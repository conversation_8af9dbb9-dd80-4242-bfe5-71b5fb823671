## # CoElec Backend Architecture (Supabase Edition)

## ## System Overview

This document outlines the comprehensive backend architecture for the CoElec electrical estimation platform, detailing service relationships, data flow patterns, and integration points. The architecture leverages **Supabase services** as the foundation, with custom **MCP (Model Context Protocol) servers** for specialized functionality like AI symbol detection and supplier integration. Development will be facilitated using the **Windsurf IDE**.

## ## Core Supabase Services

```mermaid
graph TD
    Supabase{Supabase Platform} --> Auth[Supabase Auth]
    Supabase --> DB[Supabase Database (PostgreSQL)]
    Supabase --> Storage[Supabase Storage]
    Supabase --> Functions[Supabase Edge Functions]
    Supabase --> Realtime[Supabase Realtime]
    Supabase --> Vector[Supabase Vector (pg_vector)]
```

### ### Authentication Service (Supabase Auth)

```mermaid
graph TD
    Auth[Supabase Auth] --> UserManagement[User Signup & Login]
    Auth --> RLS[RLS Integration for DB]
    Auth --> StoragePolicies[Storage Access Policies]
    UserManagement --> AuthHooks[Auth Hooks/Triggers via DB]
    AuthHooks --> UserCreationActions[User Creation Actions (e.g., Profile Setup)]
    AuthHooks --> RoleManagement[Role & Org Assignment (via custom claims/metadata)]
```

#### #### Implementation Notes

*   **Authentication Methods**: Email/password primary with optional OAuth providers (Google, Microsoft) via **Supabase Auth**.
*   **Custom Claims/User Metadata**: Implement role-based access control (Admin, Manager, Estimator, Client) using **Supabase Auth user metadata** or custom JWT claims (settable via Edge Functions or Database Triggers).
*   **Multi-Tenancy**: User-to-organization association managed in the **Supabase Database**. Data isolation enforced by **Row Level Security (RLS)** policies based on organization ID and user roles.
*   **Security**: Enforce strong password policies (Supabase Auth defaults), enable MFA for sensitive roles.
*   **Auth Triggers/Hooks**: Implement **Supabase Database Triggers** that can call **Edge Functions** in response to user creation, deletion, or role changes (e.g., to initialize user profiles, assign to organizations).
*   **Session Management**: Handled by **Supabase Auth** (JWTs with configurable lifetimes and refresh policies).

### ### Supabase Database Structure (PostgreSQL)

```mermaid
graph TD
    DB[Supabase Database (PostgreSQL)] --> UsersTbl[users Table (public.users, links to auth.users)]
    DB --> OrgsTbl[organizations Table]
    DB --> ProjectsTbl[projects Table]
    ProjectsTbl --> FloorPlansTbl[floor_plans Table]
    ProjectsTbl --> SymbolsTbl[symbols Table (detected & manual)]
    ProjectsTbl --> EstimatesTbl[estimates Table]
    ProjectsTbl --> QuotesTbl[quotes Table]
    DB --> MaterialsTbl[materials Table]
    DB --> SuppliersTbl[suppliers Table]
    DB --> AuditLogTbl[audit_log Table]
```

#### #### Implementation Notes

*   **Multi-Tenant Design**:
    *   Most tables will include an `organization_id` column.
    *   **Row Level Security (RLS)** policies will be strictly enforced on nearly all tables to ensure data isolation based on `organization_id` and user roles/permissions derived from `auth.uid()` and JWT claims/metadata.
*   **`users` Table**:
    *   Stores user profiles, preferences, linked to `auth.users` via foreign key (`id UUID REFERENCES auth.users(id)`).
    *   Role definitions (if not solely in JWT claims) and organization associations.
    *   Activity tracking and notification preferences.
*   **`organizations` Table**:
    *   Organization details, settings, team member roster (linking to `users` table via a join table like `organization_members`), role assignments within the org.
    *   Subscription and billing information.
    *   Branding assets (paths to Supabase Storage) and configuration.
*   **`projects` Table**:
    *   Core project metadata, client information, project status.
    *   Timeline and milestone tracking.
    *   Access control definitions (if more granular than organization-level).
*   **Related Tables (Examples, not exhaustive):**
    *   `floor_plans`: Metadata, processing status (for AI detection), version history, path to file in Supabase Storage.
    *   `symbols`: Detected and manually added symbol data with coordinates, types, properties, confidence scores (for AI detections).
    *   `estimates`: Material and labor calculations, pricing data, versioning.
    *   `quotes`: Generated quote versions, delivery status, client feedback, path to PDF in Supabase Storage.
*   **Indexing**: Implement appropriate indexes on foreign keys, frequently queried columns, and columns used in RLS policies for performance.
*   **Data Types**: Utilize PostgreSQL's rich data types (e.g., `JSONB` for flexible metadata, `TIMESTAMPZ` for time, `UUID` for IDs, `NUMERIC` for financials, PostGIS types via `pg_gis` extension if advanced spatial queries are needed for symbols).

### ### Storage Structure (Supabase Storage)

```mermaid
graph TD
    Storage[Supabase Storage] --> FloorPlanFiles[Floor Plan Files Bucket]
    Storage --> SymbolImages[Symbol Images Bucket (optional, e.g. for custom symbols)]
    Storage --> GeneratedQuotes[Generated Quotes Bucket (PDFs)]
    Storage --> ProjectDocuments[Project Documents Bucket]
    Storage --> OrgAssets[Organization Assets Bucket (logos, branding)]
```

#### #### Implementation Notes

*   **Structured Storage Paths (within buckets)**:
    *   Example: `floor-plan-files/{orgId}/{projectId}/{floorPlanId_version}.pdf`
    *   Example: `generated-quotes/{orgId}/{projectId}/{quoteId}.pdf`
    *   Example: `organization-assets/{orgId}/logo.png`
*   **Security Policies**: Implement granular access control policies on buckets and objects, aligned with user roles and RLS principles in the database. Policies can restrict upload, download, list, delete operations.
*   **Metadata**: Store comprehensive metadata for files in corresponding **Supabase Database** tables, linking to the storage object path.
*   **Lifecycle Management**: Configure retention policies if needed (may require custom scripts/Edge Functions for cleanup).
*   **Content Processing**: **Supabase Edge Functions** can be triggered by storage events (e.g., new file upload via Storage webhooks or database trigger on metadata table) to initiate processing pipelines (e.g., AI symbol detection).

### ### Supabase Edge Functions Architecture (Deno Runtime)

```mermaid
graph TD
    EdgeFunctions[Supabase Edge Functions] --> HttpEndpoints[HTTP API Endpoints (REST/GraphQL)]
    EdgeFunctions --> DBTriggers[Database Triggers (invoking Functions)]
    EdgeFunctions --> StorageHooks[Storage Hooks/Triggers (invoking Functions)]
    EdgeFunctions --> AuthHooks[Auth Hooks (via DB Triggers, invoking Functions)]
    EdgeFunctions --> ScheduledFunctions[Scheduled Jobs (via cron)]
    HttpEndpoints --> FrontendCommunication[Frontend Communication]
    HttpEndpoints --> MCPInteractions[Calls to MCP Servers]
    HttpEndpoints --> ThirdPartyIntegrations[Third-Party API Integrations]
```

#### #### Implementation Notes

*   **API Endpoints**: Develop RESTful (or GraphQL) API endpoints using Edge Functions for:
    *   Custom business logic not covered by PostgREST.
    *   Orchestrating calls to MCP servers (e.g., initiating symbol detection, fetching supplier prices).
    *   Integrating with third-party services (email, digital signatures, payments).
    *   Secure these endpoints by validating JWTs and checking permissions.
*   **Trigger Categories**:
    *   **Database Triggers**: PostgreSQL triggers can call Edge Functions to react to data changes (e.g., on new project creation, update user profile).
    *   **Storage Hooks/Triggers**: Use Storage webhooks or database triggers on file metadata tables to initiate processing for uploaded files (e.g., kick off floor plan analysis).
    *   **Auth Hooks**: Implement via database triggers on `auth.users` or related profile tables to respond to user lifecycle events.
    *   **Scheduled Jobs**: Use `pg_cron` extension in Supabase or an external scheduler to invoke Edge Functions for periodic tasks (e.g., price updates, report generation, cleanup).
*   **Function Organization**:
    *   Group functions by domain (e.g., `user-management`, `project-processing`, `ai-services`, `supplier-integrations`).
    *   Implement shared libraries/modules within the Deno environment for common functionality (e.g., Supabase client setup, error handling, MCP client logic).
    *   Maintain consistent error handling and logging patterns.
*   **Performance Optimization**:
    *   Optimize Edge Function code for fast execution and minimal cold starts.
    *   Use batching for bulk database operations.
    *   Implement caching within Edge Functions where appropriate (e.g., for frequently accessed external API data).
    *   Be mindful of Edge Function resource limits (memory, execution time).

## ## MCP Server Architecture

```mermaid
graph TD
    MCPServers{MCP Servers (External to Supabase, e.g., deployed on Cloud Run, Fly.io)} --> SymbolDetectionMCP[Symbol Detection MCP]
    MCPServers --> SupplierIntegrationMCP[Supplier Integration MCP]
    MCPServers --> EstimationMCP[Estimation MCP (Optional for complex logic)]
    MCPServers --> DocumentMCP[Document Generation MCP (Optional for advanced docs)]

    SupabaseEdgeFunctions[Supabase Edge Functions] -.-> SymbolDetectionMCP
    SupabaseEdgeFunctions -.-> SupplierIntegrationMCP
    SupabaseEdgeFunctions -.-> EstimationMCP
    SupabaseEdgeFunctions -.-> DocumentMCP

    SymbolDetectionMCP --> OpenRouterAI[OpenRouter AI Models]
    SupplierIntegrationMCP --> SupplierAPIs[Supplier APIs/Websites]
```

### ### Symbol Detection MCP (`SymbolDetectionMCP`)

*   **Purpose**: Centralize all AI-driven symbol detection logic, abstracting models and prompts from the core Supabase backend.
*   **Tools (Examples)**:
    *   `preprocessFloorPlan`: Handles image normalization, resizing, enhancement.
    *   `detectSymbolsInTile`: Takes an image tile, prompt version, and model preference; returns detected symbols with confidence.
    *   `aggregateTileResults`: Merges results from multiple tiles, applies NMS.
*   **Resources (Examples)**:
    *   `prompts://{prompt_name}/{version}`: Access specific versioned prompts.
    *   `symbol_knowledge_base://{category}`: (Future) Access specialized knowledge for symbol disambiguation.
*   **Implementation Notes**:
    *   Implement according to Context7 MCP server patterns.
    *   **"Vibe Coding" for Prompts**: Facilitate rapid iteration on prompts. Store prompts in a version-controlled manner (e.g., config files, database accessible by MCP).
    *   **Model Orchestration**: Handle selection of AI models via OpenRouter based on input parameters or internal logic.
    *   Implement robust error handling, retry mechanisms for AI API calls.
    *   Comprehensive logging (request, response, model used, tokens, cost).
    *   Implement rate limiting and cost management for AI API calls.
    *   Caching of AI responses where appropriate.

### ### Supplier Integration MCP (`SupplierIntegrationMCP`)

*   **Purpose**: Manage all interactions with external supplier systems for pricing, catalog access, and potentially ordering.
*   **Tools (Examples)**:
    *   `getSupplierPricing`: Fetches pricing for specific materials from a supplier.
    *   `searchSupplierCatalog`: Searches a supplier's catalog.
    *   `normalizeSupplierData`: Transforms raw supplier data into a standard CoElec format.
*   **Resources (Examples)**:
    *   `supplier_api_config://{supplierId}`: Access API configuration for a specific supplier.
*   **Implementation Notes**:
    *   Implement secure credential storage for supplier access (e.g., HashiCorp Vault, or encrypted config).
    *   Develop supplier-specific adapters (API clients or web scrapers).
    *   Robust error handling for external service failures.
    *   Rate limiting to prevent supplier API abuse.
    *   Data normalization and caching strategies.

### ### Estimation MCP (Optional)

*   If estimation logic becomes exceedingly complex or requires specialized AI/ML models beyond simple rule engines, an `EstimationMCP` could house these.
*   **Tools**: `calculateAdvancedEstimate`, `refineLaborWithAI`.

### ### Document Generation MCP (Optional)

*   For advanced document generation needs beyond what Supabase Edge Functions can handle (e.g., complex PDF layouts, integration with specialized document engines).
*   **Tools**: `generateComplexQuotePDF`, `createInteractiveExcelEstimate`.

## ## AI Services Integration (via MCP Servers)

```mermaid
graph TD
    SymbolDetectionMCP --> OpenRouter{OpenRouter}
    OpenRouter --> Claude[Claude Models]
    OpenRouter --> GPT4o[GPT-4o Models]
    OpenRouter --> Gemini[Gemini Pro Vision]
```

### ### Implementation Notes (Managed by `SymbolDetectionMCP`)

*   **Model Selection Logic**:
    *   Claude 3.5 Sonnet: Primary for high-quality symbol detection.
    *   GPT-4o: For complex floor plans with mixed elements.
    *   Gemini Pro Vision: For standard floor plans and fallback.
    *   Claude 3 Haiku/GPT-4o mini: For simple tasks (e.g., pre-classification) and secondary fallback.
*   **Context Management**: Efficient prompt construction for minimal token usage, context window optimization.
*   **Result Processing**: Consistent output parsing, confidence scoring, verification workflow for low-confidence results.
*   **Cost Management**: Track token usage by function/project, implement tiered access based on subscription level (if applicable), develop caching strategies.

### ### Supabase Vector / Vertex AI Integration (for Similarity & Advanced Search)

```mermaid
graph TD
    SupabaseDB{Supabase Database} --> pgVector[pg_vector Extension]
    SupabaseEdgeFunctions --> EmbeddingGeneration[Embedding Generation (via MCP or direct AI call)]
    EmbeddingGeneration --> pgVectorStore[Store Embeddings in pg_vector]
    pgVector --> VectorSearch[Vector Similarity Search]

    %% Optional Vertex AI path if pg_vector is insufficient
    %% SupabaseEdgeFunctions --> VertexAI{Vertex AI}
    %% VertexAI --> VertexEmbeddings[Vector Embeddings Service]
    %% VertexAI --> VertexCustomModels[Custom ML Models]
    %% VertexAI --> VertexVectorSearch[Vertex Vector Search]
```

#### #### Implementation Notes

*   **Embeddings Service**:
    *   Generate vector embeddings for floor plans (e.g., visual embeddings) and symbols (e.g., visual or semantic embeddings). This can be a tool in an MCP or an Edge Function calling an embedding model.
    *   Store these embeddings in a `vector` column within relevant **Supabase Database** tables, utilizing the **`pg_vector`** extension.
*   **Custom Models (Future)**: Train specialized models for electrical symbols (if needed beyond general multimodal models).
*   **Vector Search (via `pg_vector`)**:
    *   Implement similarity search for floor plans (e.g., "find plans similar to this one") and symbols using SQL queries with `pg_vector` operators (e.g., `<=>` for cosine distance).
    *   Develop hybrid search combining keyword search (Supabase full-text search) with vector search.

## ## Floor Plan Processing Pipeline (Orchestrated by Supabase Edge Functions & MCPs)

```mermaid
graph TD
    A[Upload to Supabase Storage] --> B{Storage Trigger / DB Metadata Trigger}
    B --> C[Supabase Edge Function: Orchestrator]
    C --> D[SymbolDetectionMCP: preprocessFloorPlan]
    D --> E[SymbolDetectionMCP: detectSymbolsInTile (iterative for tiles)]
    E --> F[SymbolDetectionMCP: aggregateTileResults]
    F --> G[Supabase Edge Function: Store Results]
    G --> H[Store Symbols & Status in Supabase DB]
    H --> I[Notify Frontend via Supabase Realtime]
```

### ### Implementation Notes

*   **Processing Initiation**: File upload to **Supabase Storage** triggers an event (e.g., webhook or database trigger on metadata table).
*   **Orchestration**: A master **Supabase Edge Function** orchestrates the pipeline.
*   **Preprocessing & Tiling**: Orchestrator calls `SymbolDetectionMCP` tools for preprocessing and tiling logic.
*   **AI Model Selection & Detection**: Orchestrator (or MCP itself) selects AI model(s); `SymbolDetectionMCP` tool(s) perform detection on tiles.
*   **Results Processing**: `SymbolDetectionMCP` tool or Edge Function merges results, applies NMS, normalizes data.
*   **Symbol Storage**: Processed symbols and analysis status are saved to **Supabase Database**.
*   **Notifications**: Frontend is notified of completion/status changes via **Supabase Realtime**.

## ## Supplier Integration Flow (Orchestrated by Supabase Edge Functions & `SupplierIntegrationMCP`)

```mermaid
graph TD
    J[Scheduled Job / User Action] --> K[Supabase Edge Function: Orchestrator]
    K --> L[SupplierIntegrationMCP: getSupplierPricing/searchCatalog]
    L --> M[SupplierIntegrationMCP: Secure Credential Access]
    M --> N[SupplierIntegrationMCP: API Call / Web Scraping]
    N --> O[SupplierIntegrationMCP: Price Extraction & Normalization]
    O --> P[Supabase Edge Function: Store/Update Prices]
    P --> Q[Store Normalized Prices in Supabase DB (materials table)]
```

### ### Implementation Notes

*   Orchestrated by **Supabase Edge Functions** (e.g., triggered by a schedule for price updates, or by user action for catalog search).
*   Edge Function calls tools on the `SupplierIntegrationMCP`.
*   `SupplierIntegrationMCP` handles secure credential management, supplier-specific logic (API/scraping), data extraction, and normalization.
*   Normalized pricing data is returned to the Edge Function to be stored/updated in the **Supabase Database**.

## ## Estimation Engine Flow (Primarily Supabase DB Logic & Edge Functions)

```mermaid
graph TD
    R[User Initiates Estimation] --> S[Supabase Edge Function: Estimation Service]
    S --> T[Fetch Symbols from Supabase DB]
    T --> U[Symbol-to-Material Mapper (DB rules/Function logic)]
    U --> V[Assembly Engine (DB rules/Function logic)]
    V --> W[Fetch Material Prices from Supabase DB (updated by Supplier Integration)]
    W --> X[Cost Calculation (Function logic)]
    X --> Y[Labor Estimation (DB rates/Function logic)]
    Y --> Z[Pricing Application (Markups, Taxes - Function logic)]
    Z --> AA[Generate Estimate Object]
    AA --> AB[Store Estimate in Supabase DB]
```

### ### Implementation Notes

*   Largely driven by logic within **Supabase Edge Functions** and data/rules stored in the **Supabase Database**.
*   An `EstimationMCP` is optional unless very complex AI-driven estimation is needed.
*   Symbol-to-Material mapping rules reside in the database.
*   Assembly definitions and rules reside in the database.
*   Material prices are fetched from the database (kept current by the Supplier Integration Flow).
*   Labor rates are stored in the database.
*   Calculation logic (costs, labor, markups, taxes) is implemented in Edge Functions.
*   Generated estimates are stored in the database.

## ## Document Generation Flow (Supabase Edge Functions)

```mermaid
graph TD
    AC[User Requests Quote/Document] --> AD[Supabase Edge Function: Document Generator]
    AD --> AE[Fetch Estimate Data from Supabase DB]
    AE --> AF[Fetch Template from Supabase DB/Storage or Predefined]
    AF --> AG[Fetch Branding Assets from Supabase Storage]
    AG --> AH[Data Merging & Formatting (Function logic)]
    AH --> AI{Output Format}
    AI --> AJ[PDF Generation (e.g., using Deno PDF library)]
    AI --> AK[Excel Generation (e.g., using Deno Excel library)]
    AJ --> AL[Store PDF in Supabase Storage]
    AK --> AM[Provide Excel for Download]
```

### ### Implementation Notes

*   Primarily handled by **Supabase Edge Functions**.
*   Templates can be stored in Supabase Storage or as part of the function code.
*   Data is fetched from Supabase DB.
*   Branding assets (logos) fetched from Supabase Storage.
*   PDF/Excel generation uses Deno-compatible libraries within the Edge Function.
*   Generated documents stored in Supabase Storage.
*   A `DocumentMCP` is optional for very complex document templating/generation needs.

## ## A2A Agent Framework (Supabase Edge Functions & DB as Orchestrator)

```mermaid
graph TD
    A2AProtocol{A2A Orchestration (Supabase DB as Task Queue)} --> UploadAgent[Upload Agent (Edge Function)]
    A2AProtocol --> DetectionAgent[Detection Agent (Edge Function calling SymbolDetectionMCP)]
    A2AProtocol --> EstimationAgent[Estimation Agent (Edge Function)]
    A2AProtocol --> SupplierAgent[Supplier Agent (Edge Function calling SupplierIntegrationMCP)]
    A2AProtocol --> DocumentAgent[Document Agent (Edge Function)]
```

### ### Agent Communication
(As per original diagram, but agents are primarily Edge Functions)

### ### Implementation Notes

*   **Agent Responsibilities**:
    *   **Upload Agent**: Handles file validation, initial metadata storage, triggers detection.
    *   **Detection Agent**: Orchestrates calls to `SymbolDetectionMCP`, manages detection status.
    *   **Estimation Agent**: Performs estimation calculations.
    *   **Supplier Agent**: Interacts with `SupplierIntegrationMCP`.
    *   **Document Agent**: Generates documents.
*   **Communication Protocol**: Standardized JSON messages. Tasks and states managed in **Supabase Database** tables (acting as a queue/state machine).
*   **Orchestration**: Central coordination logic can reside in a master Edge Function or be driven by database triggers and state changes. **Supabase Realtime** for progress updates.

## ## Event-Driven Architecture (Leveraging Supabase Triggers & Realtime)

```mermaid
graph TD
    EventSystem{Supabase Event System} --> DBTriggersEv[Database Triggers]
    EventSystem --> StorageWebhooksEv[Storage Webhooks]
    EventSystem --> AuthHooksEv[Auth Hooks (via DB Triggers)]
    EventSystem --> RealtimeEv[Supabase Realtime for Frontend Updates]

    DBTriggersEv --> EdgeFunctionHandlers[Edge Function Event Handlers]
    StorageWebhooksEv --> EdgeFunctionHandlers
    AuthHooksEv --> EdgeFunctionHandlers
```

### ### Implementation Notes

*   **Event Types**:
    *   Project Events: Creation, updates, status changes (DB Triggers).
    *   User Events: Actions, preferences, notifications (DB Triggers).
    *   System Events: Operations and monitoring (Custom Logging to DB).
    *   Integration Events: External system interactions (logged by Edge Functions).
*   **Event Handlers (Supabase Edge Functions)**:
    *   Notification generator for user alerts (e.g., sending emails).
    *   Workflow progressor for advancing project stages.
    *   Data synchronizer for consistency.
    *   Analytics recorder for metrics.
*   **Implementation Strategy**:
    *   Use **Supabase Database Triggers** to invoke Edge Functions on data changes.
    *   Use **Supabase Storage Webhooks** (or DB triggers on metadata) for file events.
    *   Implement event filtering and routing logic within Edge Functions.
    *   Use **Supabase Realtime** to push relevant event notifications to the frontend.

## ## Security Implementation (Supabase RLS & Policies)

```mermaid
graph TD
    SecurityModel{Supabase Security Model} --> RLS[Database Row Level Security]
    SecurityModel --> StoragePoliciesSec[Storage Access Policies]
    SecurityModel --> FunctionAccessSec[Edge Function Access Control (JWT Auth)]
```

### ### Implementation Notes

*   **Multi-Tenant Isolation**:
    *   Enforced primarily by **Supabase Row Level Security (RLS)** on database tables based on `organization_id` and user roles.
    *   **Supabase Storage Policies** aligned with RLS.
*   **Database RLS**:
    *   Role-based access control for all CRUD operations.
    *   Field-level security can be managed by views or careful policy writing.
    *   Validation rules using PostgreSQL CHECK constraints and RLS `USING` / `WITH CHECK` clauses.
*   **Storage Policies**:
    *   Path-based access restrictions.
    *   Content type validation (client-side and in Edge Functions before upload).
    *   Size and quota limitations (enforced by application logic/Edge Functions).
*   **Edge Function Access**:
    *   All sensitive Edge Functions must validate the JWT from the `Authorization` header.
    *   Perform role-based authorization checks within the function.
    *   Implement rate limiting (e.g., using a proxy or custom logic with Redis/Supabase DB).
*   **Credential Management**:
    *   Use Supabase project environment variables or Supabase Vault for storing API keys and secrets for Edge Functions and MCP server access.

## ## Analytics and Monitoring (Supabase & Custom)

```mermaid
graph TD
    Monitoring{CoElec Monitoring} --> SupabaseMonitoring[Supabase Platform Monitoring (DB, Auth, Functions, Storage)]
    Monitoring --> CustomAppLogging[Application-Level Logging (to Supabase DB or external service)]
    Monitoring --> MCPMonitoring[MCP Server Monitoring]
    CustomAppLogging --> PerfMonitoring[Performance Metrics]
    CustomAppLogging --> ErrorTracking[Error Tracking]
    CustomAppLogging --> UsageAnalytics[Usage Analytics]
    CustomAppLogging --> CostMonitoring[Cost Monitoring (AI, MCPs)]
```

### ### Implementation Notes

*   **Performance Monitoring**:
    *   Track Edge Function execution times and Supabase query performance (via Supabase dashboard and logs).
    *   Measure API response times.
    *   Track resource utilization.
*   **Error Tracking**:
    *   Capture detailed error information from Edge Functions and MCPs. Log to Supabase DB or an external service (Sentry, Logflare).
    *   Implement alert thresholds.
*   **Usage Analytics**:
    *   Track feature adoption and usage by logging key events to a dedicated analytics table in **Supabase DB**.
    *   Monitor user engagement metrics.
*   **Cost Monitoring**:
    *   Track Supabase resource consumption.
    *   Monitor AI API usage costs (via MCP logs and OpenRouter).
    *   Analyze cost per customer/project.

## ## Integration Points (via Supabase Edge Functions & MCPs)

```mermaid
graph TD
    ExternalIntegration{External Integrations} --> EmailService[Email Service (e.g., SendGrid, Postmark)]
    ExternalIntegration --> PaymentProvider[Payment Provider (e.g., Stripe, Paddle)]
    ExternalIntegration --> DigitalSignatureSvc[Digital Signature Service (e.g., DocuSign)]

    SupabaseEdgeFunctions -- Authenticated Calls --> EmailService
    SupabaseEdgeFunctions -- Authenticated Calls --> PaymentProvider
    SupabaseEdgeFunctions -- Authenticated Calls --> DigitalSignatureSvc
```

### ### Implementation Notes

*   All external service integrations are managed via **Supabase Edge Functions**.
*   **Email Service Integration**: Template-based email generation, delivery tracking.
*   **Payment Provider**: Secure payment processing, subscription management.
*   **Digital Signature**: Document signing workflow, compliance.
*   **Integration Strategy**:
    *   Implement adapter patterns within Edge Functions for each service.
    *   Securely store API keys/secrets.
    *   Create fallback mechanisms and robust error handling.
    *   Develop monitoring for integration health.

## ## Implementation Strategy

### ### Development Phases (Example)

1.  **Core Supabase Infrastructure Setup**:
    *   Supabase project configuration, Auth setup, core database schema, RLS policies.
    *   Base API endpoints (PostgREST + essential Edge Functions).
2.  **Data Processing Pipeline (AI Symbol Detection Focus)**:
    *   Supabase Storage setup with security policies.
    *   `SymbolDetectionMCP` server development (initial version).
    *   Floor plan processing flow (Edge Functions orchestrating MCP calls), symbol storage in DB.
    *   Frontend integration for upload and display of results (from existing UI).
3.  **Estimation Engine**:
    *   Material database implementation in Supabase.
    *   Symbol-to-material mapping logic (DB rules, Edge Functions).
    *   Cost calculation and labor estimation engine (Edge Functions).
4.  **Client-Facing Systems**:
    *   Document generation (Edge Functions).
    *   Quote delivery system.
    *   Client approval workflow.
    *   Notification system.
5.  **Supplier Integration, Analytics, and Optimization**:
    *   `SupplierIntegrationMCP` development.
    *   Monitoring and analytics implementation.
    *   Performance optimization and cost management.

### ### Critical Considerations

*   **Scalability**: Leverage Supabase's scalable architecture. Design Edge Functions and database queries efficiently.
*   **Security**: Prioritize RLS and Storage policy implementation. Secure Edge Functions and MCP servers.
*   **Error Handling**: Comprehensive error handling across all layers (Frontend, Edge Functions, MCPs, AI models).
*   **Testing**: Thorough testing strategy including RLS policies, Edge Function logic, MCP tool behavior, and AI model accuracy.
*   **Documentation**: Maintain detailed documentation for Supabase schema, RLS, Edge Functions, and MCP APIs.
*   **Cost Management**: Monitor Supabase usage and AI API costs closely. Optimize prompts and leverage caching.
*   **"Vibe Coding" Enablement for AI**: Ensure the development environment (**Windsurf IDE**) and processes support rapid iteration and visual feedback for AI prompt engineering and model testing via MCPs.

This backend architecture provides a comprehensive framework for implementing the CoElec platform with **Supabase** and custom **MCP servers**. The detailed flow diagrams and implementation notes should guide development using the **Windsurf IDE** in creating a robust backend application.