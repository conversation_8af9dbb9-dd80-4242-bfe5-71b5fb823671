
# CoElec Platform - Environment Configuration Template
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these from your Supabase project dashboard: https://supabase.com/dashboard
NEXT_PUBLIC_SUPABASE_URL=https://nzhvukfaolebykcquedd.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# AI CONFIGURATION
# =============================================================================
# OpenRouter API key for AI model access: https://openrouter.ai/
OPENROUTER_API_KEY=your_openrouter_api_key_here

# AI Feature Flags
USE_REAL_AI=true
AI_MODEL_PROVIDER=openrouter
DEFAULT_AI_MODEL=anthropic/claude-3-sonnet

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3000
API_PORT=3001

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# JWT Secret for session management
JWT_SECRET=your_jwt_secret_here_make_it_long_and_random

# Session configuration
SESSION_SECRET=your_session_secret_here_also_long_and_random

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable specific features
ENABLE_VISUAL_FEEDBACK=true
ENABLE_MCP_DISCOVERY=true
ENABLE_AI_CACHING=true
ENABLE_CONTEXT_MAPPING=true
ENABLE_FILE_OPTIMIZATION=true

# Legacy feature flags (set to 'true' to enable real services)
USE_REAL_DATABASE=true
USE_REAL_TEAM_SERVICE=true
USE_REAL_PROJECT_SERVICE=true
USE_REAL_COLUMN_SERVICE=true
USE_REAL_TASK_SERVICE=true
USE_REAL_FLOOR_PLAN_SERVICE=true
USE_REAL_QUOTE_SERVICE=true
USE_REAL_SYMBOL_SERVICE=true
USE_REAL_MATERIAL_SERVICE=true
USE_REAL_LABOR_SERVICE=true

# UAT Configuration
ENABLE_UAT_MODE=true
UAT_TEST_DATA=true

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
# SendGrid configuration for email notifications
SENDGRID_API_KEY=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================
# AI Model Configuration
SYMBOL_DETECTION_MODEL=anthropic/claude-3-sonnet
COST_CALCULATION_MODEL=openai/gpt-4-turbo
MATERIAL_MAPPING_MODEL=anthropic/claude-3-sonnet
AI_TIMEOUT_MS=30000
MAX_AI_RETRIES=3

# AI Cost Management
AI_DAILY_BUDGET_USD=50.00
AI_MONTHLY_BUDGET_USD=1000.00
AI_COST_ALERT_THRESHOLD=0.8

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# Supabase Storage bucket names
STORAGE_BUCKET_FLOOR_PLANS=floor-plans
STORAGE_BUCKET_QUOTES=quotes
STORAGE_BUCKET_DOCUMENTS=documents

# File upload limits
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Development flags
DEBUG=true
VERBOSE_LOGGING=true
ENABLE_CORS=true

# Testing configuration
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/coelec_test
CYPRESS_BASE_URL=http://localhost:3000
