# 🤖 CoElec AI Agent Implementation Guide

## **EXECUTION SEQUENCE**

Execute tasks in order. Each task must pass verification before proceeding.

**Target:** 142/142 UAT test cases passed  
**Current:** 89 passed, 53 failed/partial

---

## **T1.1: OPENROUTER AI INTEGRATION**

**Prerequisites:** OpenRouter API key in .env.local  
**UAT Tests:** SYM-1, SYM-2, SYM-3  
**Dependencies:** None

### **Files to Create**
1. `server/services/aiSymbolDetectionService.ts` - Real AI detection service
2. `server/types/symbolDetection.ts` - Type definitions

### **Files to Modify**
1. `server/mcp/symbol-detection-mcp.ts` - Replace mock with real AI calls
2. `.env.local` - Add OPENROUTER_API_KEY=your_key, USE_REAL_AI=true

### **Implementation**
```typescript
// server/services/aiSymbolDetectionService.ts
import { OpenRouterClient } from 'openrouter-api';

export class AISymbolDetectionService {
  private client: OpenRouterClient;
  
  async detectSymbols(imageBuffer: Buffer): Promise<DetectedSymbol[]> {
    const response = await this.client.chat.completions.create({
      model: "anthropic/claude-3-sonnet",
      messages: [{
        role: "user",
        content: [
          { type: "text", text: ELECTRICAL_SYMBOL_PROMPT },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${imageBuffer.toString('base64')}` }}
        ]
      }]
    });
    return this.parseSymbolResponse(response);
  }
}
```

### **Verification**
```bash
npm run test:ai
curl -X POST http://localhost:3001/api/symbol-detection/test
```

### **Success Criteria**
- [ ] OpenRouter API responds with symbol detections
- [ ] Confidence scores 0-1 for all symbols
- [ ] 15+ electrical symbol types supported

---

## **T1.2: ELECTRICAL SYMBOL DATABASE**

**Prerequisites:** T1.1 completed, Supabase access  
**UAT Tests:** SYM-2, SYM-3, MAT-1  
**Dependencies:** T1.1

### **Files to Create**
1. `server/database/migrations/electrical_symbols.sql` - Database schema
2. `scripts/populate-electrical-symbols.ts` - Data seeding script
3. `server/services/electricalSymbolService.ts` - Symbol CRUD operations

### **Implementation**
```sql
-- server/database/migrations/electrical_symbols.sql
CREATE TABLE electrical_symbols (
  id SERIAL PRIMARY KEY,
  symbol_type VARCHAR(50) NOT NULL,
  category VARCHAR(30) NOT NULL,
  material_mapping JSONB NOT NULL,
  labor_factor DECIMAL(4,2) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### **Verification**
```bash
npm run db:migrate
npm run seed:electrical-symbols
curl -X GET http://localhost:3001/api/electrical-symbols
```

### **Success Criteria**
- [ ] 50+ electrical symbols in database
- [ ] Material mappings for all symbol types
- [ ] Symbol service API endpoints working

---

## **T1.3: MATERIAL ESTIMATION ENGINE**

**Prerequisites:** T1.2 completed  
**UAT Tests:** MAT-1, MAT-2, MAT-3  
**Dependencies:** T1.2

### **Files to Create**
1. `server/services/materialEstimationEngine.ts` - Core estimation logic
2. `server/database/migrations/electrical_materials.sql` - Materials database
3. `scripts/populate-materials.ts` - Material data seeding

### **Files to Modify**
1. `server/routes/api/estimates.ts` - Add estimation endpoints
2. `client/src/pages/EstimationPage.tsx` - UI for material estimates

### **Verification**
```bash
curl -X POST http://localhost:3001/api/estimates/calculate -d '{"symbols": [...], "projectContext": {...}}'
npm run test:estimation
```

### **Success Criteria**
- [ ] Material calculations for all symbol types
- [ ] Regional pricing integration
- [ ] Detailed cost breakdown generation

---

## **T1.4: SUPPLIER INTEGRATION**

**Prerequisites:** T1.3 completed  
**UAT Tests:** Supplier pricing integration  
**Dependencies:** T1.3

### **Files to Create**
1. `server/services/supplierIntegrationService.ts` - Supplier API integration
2. `server/adapters/graybarAdapter.ts` - Graybar supplier adapter
3. `server/services/supplierCacheService.ts` - Pricing cache

### **Verification**
```bash
curl -X GET http://localhost:3001/api/suppliers/pricing
npm run test:suppliers
```

### **Success Criteria**
- [ ] Real-time pricing from 3+ suppliers
- [ ] Price comparison and recommendations
- [ ] Caching for performance

---

## **T2.1: DOCUSIGN INTEGRATION**

**Prerequisites:** DocuSign API credentials  
**UAT Tests:** SIG-1, SIG-2, SIG-3  
**Dependencies:** None

### **Files to Create**
1. `server/services/digitalSignature/docusignAdapter.ts` - DocuSign integration
2. `server/services/signatureWorkflowEngine.ts` - Workflow management

### **Files to Modify**
1. `server/services/digitalSignatureService.ts` - Replace mock with real provider
2. `.env.local` - Add DOCUSIGN_INTEGRATION_KEY, DOCUSIGN_SECRET

### **Verification**
```bash
curl -X POST http://localhost:3001/api/signatures/create
npm run test:signatures
```

### **Success Criteria**
- [ ] Real DocuSign integration
- [ ] End-to-end signature workflow
- [ ] Document upload and signing URLs

---

## **T2.2: CLIENT PORTAL**

**Prerequisites:** Client authentication system  
**UAT Tests:** CA-1, CA-3, QUOTE-3  
**Dependencies:** T2.1

### **Files to Create**
1. `client/src/pages/ClientPortal.tsx` - Client portal interface
2. `server/routes/client.ts` - Client API endpoints
3. `client/src/components/QuoteApproval.tsx` - Quote approval UI

### **Verification**
```bash
curl -X GET http://localhost:3001/api/client/quotes/123
npm run test:client-portal
```

### **Success Criteria**
- [ ] Complete client portal with authentication
- [ ] Quote viewing and approval interface
- [ ] Change request submission system

---

## **T2.3: EMAIL INTEGRATION**

**Prerequisites:** SendGrid API key  
**UAT Tests:** Email notifications  
**Dependencies:** T2.1, T2.2

### **Files to Create**
1. `server/services/emailService.ts` - Email automation
2. `server/templates/` - Email templates directory

### **Files to Modify**
1. `.env.local` - Add SENDGRID_API_KEY
2. `server/services/notificationService.ts` - Integrate email service

### **Verification**
```bash
curl -X POST http://localhost:3001/api/notifications/test-email
npm run test:email
```

### **Success Criteria**
- [ ] Real SendGrid integration
- [ ] Automated email workflows
- [ ] 99%+ delivery rate

---

## **T3.1: CRUD OPERATIONS**

**Prerequisites:** All Phase 1 & 2 tasks completed  
**UAT Tests:** KAN-4, Team/Project editing  
**Dependencies:** T1.1-T2.3

### **Files to Create**
1. `client/src/components/EditTeamDialog.tsx` - Team editing UI
2. `client/src/components/EditProjectDialog.tsx` - Project editing UI
3. `client/src/components/EditTaskDialog.tsx` - Task editing UI

### **Files to Modify**
1. `server/routes/api/teams.ts` - Add PUT/DELETE endpoints
2. `server/routes/api/projects.ts` - Add PUT/DELETE endpoints
3. `server/routes/api/tasks.ts` - Add PUT/DELETE endpoints

### **Verification**
```bash
curl -X PUT http://localhost:3001/api/teams/123
curl -X DELETE http://localhost:3001/api/tasks/456
npm run test:crud
```

### **Success Criteria**
- [ ] Edit functionality for teams, projects, tasks
- [ ] Delete operations with confirmation
- [ ] Permission-based access control

---

## **T3.2: PERFORMANCE OPTIMIZATION**

**Prerequisites:** All previous tasks completed  
**UAT Tests:** PERF-1, PERF-2, USA-2  
**Dependencies:** T1.1-T3.1

### **Files to Create**
1. `server/services/cacheService.ts` - Redis caching
2. `server/database/migrations/performance_indexes.sql` - Database indexes

### **Files to Modify**
1. `server/routes/api/*.ts` - Add caching to all routes
2. `client/src/components/*.tsx` - Performance optimizations

### **Verification**
```bash
npm run test:performance
npm run test:load
```

### **Success Criteria**
- [ ] API response time <200ms
- [ ] Support for 100+ concurrent users
- [ ] Database query optimization

---

## **FINAL VERIFICATION**

After completing all tasks, run comprehensive UAT verification:

```bash
npm run test:uat-complete
npm run test:e2e
cypress run --spec "cypress/e2e/complete-workflow.cy.ts"
```

**Target Result:** 142/142 UAT test cases passed ✅
