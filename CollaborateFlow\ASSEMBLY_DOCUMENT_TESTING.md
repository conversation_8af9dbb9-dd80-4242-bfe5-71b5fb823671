# Assembly Document Management System - Testing Guide

## Overview

The Assembly Document Management System allows electrical contractors to upload, parse, and manage their pricing documents (price books, estimate templates, material catalogs) for use in project estimations.

## Features Implemented

### ✅ Core Functionality
- **File Upload**: Support for Excel (.xlsx, .xls), PDF, and CSV files up to 25MB
- **Document Types**: Price books, estimate templates, material catalogs, assembly guides
- **Excel Parsing**: Automatic extraction of materials, pricing, and specifications
- **Document Management**: Upload tracking, processing status, metadata storage
- **UI Components**: Drag-and-drop upload, document library, processing dashboard

### ✅ Database Schema
- **assembly_documents**: Document metadata and file information
- **parsed_materials**: Extracted materials from documents
- **assembly_templates**: Reusable estimation templates
- **document_sharing**: Cross-organization document sharing
- **document_usage_analytics**: Usage tracking and analytics

### ✅ API Endpoints
- `POST /api/assembly-documents/upload` - Upload new documents
- `GET /api/assembly-documents` - List organization documents
- `POST /api/assembly-documents/:id/parse` - Parse document for materials
- Document filtering, search, and management operations

### ✅ UI Components
- **AssemblyDocumentUpload**: Main upload component with drag-and-drop
- **AssemblyDocumentsPage**: Complete management interface
- **Navigation Integration**: Added to sidebar and routing

## Testing Instructions

### 1. Manual Testing Setup

#### Prerequisites
```bash
# Ensure dependencies are installed
cd CollaborateFlow/server
npm install xlsx

# Start the development server
cd ..
npm run dev
```

#### Test Files Available
The system includes sample assembly documents in `CollaborateFlow/attached_assets/`:
- `Electrical Pricing Database.xlsx` - Comprehensive electrical pricing catalog
- `Retail Estimate Template.xlsx` - Project estimation template

### 2. Upload Testing

#### Test Case 1: Basic Document Upload
1. Navigate to `/assembly-documents` in the application
2. Select document type (e.g., "Price Book")
3. Add description: "Test electrical pricing catalog"
4. Add tags: "residential, 2024, testing"
5. Drag and drop `Electrical Pricing Database.xlsx`
6. Click "Upload"

**Expected Results:**
- File uploads successfully with progress indicator
- Document appears in upload queue with "completed" status
- Document metadata is stored correctly
- File is saved to server uploads directory

#### Test Case 2: Excel Parsing
1. After successful upload, the system should automatically parse Excel files
2. Check processing status changes from "pending" → "processing" → "completed"
3. Verify material count is extracted and displayed

**Expected Results:**
- Excel file is parsed for material data
- Materials are extracted with names, prices, categories
- Processing metadata is updated with material count
- No parsing errors occur

#### Test Case 3: Multiple File Upload
1. Select multiple files of different types (.xlsx, .csv, .pdf)
2. Upload all files simultaneously
3. Monitor processing status for each file

**Expected Results:**
- All files upload successfully
- Each file processes independently
- Status tracking works for multiple files
- No conflicts or errors occur

### 3. Document Management Testing

#### Test Case 4: Document Library
1. Navigate to "Manage Documents" tab
2. Test search functionality with document names
3. Test filtering by document type
4. Verify document details display correctly

**Expected Results:**
- Search returns relevant results
- Filters work correctly
- Document metadata displays properly
- File sizes and dates are accurate

#### Test Case 5: Document Actions
1. Test document viewing (Eye icon)
2. Test document download (Download icon)
3. Test document sharing (Share icon)
4. Test document deletion (Trash icon)

**Expected Results:**
- Actions are available for completed documents
- Buttons are properly enabled/disabled based on status
- Actions trigger appropriate responses

### 4. Error Handling Testing

#### Test Case 6: Invalid File Types
1. Try uploading unsupported file types (.txt, .doc, .jpg)
2. Verify error messages are displayed
3. Ensure files are rejected before upload

**Expected Results:**
- Clear error messages for unsupported formats
- Files are not uploaded
- User is guided to correct formats

#### Test Case 7: Large File Handling
1. Try uploading files larger than 25MB
2. Verify size limit enforcement
3. Test upload cancellation

**Expected Results:**
- Files over limit are rejected
- Clear error message about size limit
- Upload can be cancelled mid-process

#### Test Case 8: Network Error Handling
1. Simulate network disconnection during upload
2. Test server error responses
3. Verify error recovery

**Expected Results:**
- Graceful handling of network errors
- Clear error messages to user
- Ability to retry failed uploads

### 5. Integration Testing

#### Test Case 9: Material Estimation Integration
1. Upload a price book with material data
2. Navigate to project estimation
3. Verify uploaded materials are available for estimation
4. Test material mapping from assembly documents

**Expected Results:**
- Materials from uploaded documents appear in estimation
- Pricing data is correctly integrated
- Material mapping works with assembly documents

#### Test Case 10: Organization Isolation
1. Test with multiple organization contexts
2. Verify documents are isolated by organization
3. Test document sharing between organizations

**Expected Results:**
- Documents are only visible to uploading organization
- No cross-organization data leakage
- Sharing works when explicitly configured

### 6. Performance Testing

#### Test Case 11: Large Excel File Processing
1. Upload large Excel files (10MB+, 1000+ rows)
2. Monitor processing time and memory usage
3. Verify system remains responsive

**Expected Results:**
- Large files process without timeout
- System remains responsive during processing
- Memory usage is reasonable

#### Test Case 12: Concurrent Upload Testing
1. Upload multiple files simultaneously
2. Test with multiple users uploading concurrently
3. Monitor server performance

**Expected Results:**
- Concurrent uploads work correctly
- No file corruption or conflicts
- Server handles load appropriately

## Known Limitations

### Current Constraints
1. **Database Migration**: Requires Supabase configuration for full functionality
2. **File Storage**: Uses local file system (production would use Supabase Storage)
3. **Authentication**: Uses mock organization ID for testing
4. **Excel Parsing**: Basic parsing logic may need refinement for complex spreadsheets

### Future Enhancements
1. **Advanced Parsing**: AI-powered material extraction from complex documents
2. **Template Recognition**: Automatic template type detection
3. **Bulk Operations**: Mass upload and processing capabilities
4. **Analytics Dashboard**: Usage analytics and insights
5. **Document Versioning**: Version control for updated documents

## Troubleshooting

### Common Issues

#### Upload Fails
- Check file size (must be < 25MB)
- Verify file format (.xlsx, .xls, .pdf, .csv)
- Ensure document type is selected
- Check server logs for errors

#### Parsing Fails
- Verify Excel file is not corrupted
- Check for password protection
- Ensure file has recognizable data structure
- Review parsing error messages

#### Documents Not Appearing
- Verify organization ID is correct
- Check database connection
- Ensure RLS policies allow access
- Review API response for errors

### Debug Information
- Server logs: Check console for upload and parsing errors
- Network tab: Monitor API requests and responses
- Database: Verify records are created correctly
- File system: Check uploads directory for files

## Success Criteria

The Assembly Document Management System is considered successful when:

✅ **Upload Functionality**: Files upload reliably with progress tracking
✅ **Document Processing**: Excel files are parsed and materials extracted
✅ **Document Management**: Users can view, search, and manage documents
✅ **Integration**: Documents integrate with estimation system
✅ **Error Handling**: Graceful handling of errors and edge cases
✅ **Performance**: System handles typical usage loads efficiently

## Next Steps

1. **Database Setup**: Configure Supabase for full functionality
2. **Production Deployment**: Deploy to staging environment
3. **User Testing**: Conduct testing with real electricians
4. **Performance Optimization**: Optimize for production workloads
5. **Feature Enhancement**: Add advanced parsing and analytics

---

**Note**: This system provides the foundation for electricians to upload and manage their assembly documents, enabling more accurate and personalized project estimations based on their actual pricing data.
