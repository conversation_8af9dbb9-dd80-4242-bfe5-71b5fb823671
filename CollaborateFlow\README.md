
# CoElec Platform

A comprehensive platform for electrical contractors with floor plan analysis, estimation, and project management capabilities.

## Prerequisites

- Node.js (v16.x or later)
- Supabase account
- npm or yarn package manager

## Environment Setup

1. Clone the repository to your local machine
2. Create a `.env` file in the root directory with the following variables:

```
# Database Configuration
DATABASE_URL=postgres://username:password@localhost:5432/database_name

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_service_key_here

# SendGrid Email Configuration
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>

# Server Configuration
PORT=5000
NODE_ENV=development
```

## Installation

Install all dependencies:

```bash
npm install
```

## Database Setup

### Initial Setup

1. Create a Supabase project at https://supabase.com
2. Set up your Supabase URL and API key in the .env file
3. Initialize the database schema by running:

```bash
# Using the Supabase Dashboard SQL Editor is the recommended approach
# Log in to https://supabase.com/dashboard
# Navigate to your project -> SQL Editor
# Create a new query and paste the contents of server/database/schema.sql
# Execute the query
```

This approach aligns with using the Supabase REST API exclusively for all database operations, avoiding direct PostgreSQL connections that can cause issues in containerized environments.

### Database Reset and Maintenance

The application includes scripts for database maintenance and synchronization with Supabase Auth:

#### Reset Database

To clear all data while preserving the schema:

```bash
node reset-db.js
```

This script:
- Clears all existing data from the database tables
- Creates the default organization (Coelec)
- Creates the default team linked to the organization
- Does NOT affect Supabase Auth users

#### Synchronize Users

To manage users across Supabase Auth and the database:

```bash
node fix-sync-users.js
```

This interactive script provides options to:
1. Create new users in both Supabase Auth and the database
2. Sync existing Auth users to the database
3. Reset the database
4. List Supabase Auth users

## User Management

### Database-Auth Synchronization

The application uses Supabase exclusively for both authentication and database operations. The authentication flow works as follows:

1. Users are created in Supabase Auth first
2. Then matching records are created in the database users table
3. The `supabase_id` field in the database links to the Supabase Auth ID

### Creating Users

To properly create users that work with both Supabase Auth and the database:

```bash
node fix-sync-users.js
```

Then select option 1 to create a new user.

Alternatively, use the provided script for your operating system (when using Docker):

**On macOS/Linux:**
```bash
./create-user.sh <EMAIL> password "Full Name"
```

**On Windows:**
```
create-user.bat <EMAIL> password "Full Name"
```

**Important**: Always create users through these scripts to ensure proper synchronization between Supabase Auth and the database. Direct database insertions will cause authentication errors.

## Development

Start the development server:

```bash
npm run dev
```

This will start the development server on http://localhost:5000

## Building for Production

Build the application:

```bash
npm run build
```

Start the production server:

```bash
npm run start
```

## Docker Setup

### Quick Setup (Recommended)

For a simple, guided setup process, use the included setup script for your operating system:

**On macOS/Linux:**
```bash
# Make the script executable (if needed)
chmod +x setup.sh

# Run the setup script
./setup.sh
```

**On Windows:**
```
# Simply double-click setup.bat in File Explorer
# Or run from Command Prompt/PowerShell:
setup.bat
```

These scripts will:
- Check for Docker and Docker Compose installation
- Create a `.env` file if it doesn't exist
- Build and start the Docker containers
- Display access information when complete

### Manual Setup

Alternatively, you can manually set up the containers:

```bash
# Copy environment example (if needed)
cp .env.example .env

# Build the Docker images
docker-compose build

# Start the containers
docker-compose up -d
```

Access the application at http://localhost:5001

### Docker Configuration Notes

1. **Runtime Configuration**
   - The application uses a runtime configuration approach for Supabase credentials
   - A `config.js` file in `client/public` contains the Supabase URL and key
   - This file is copied to the distribution directory during build
   - This ensures client-side code can access these credentials in containerized environments

2. **Database**
   - Supabase is used exclusively for both authentication and database operations
   - All database interactions use the Supabase REST API
   - No direct PostgreSQL connections are used (avoids IPv6 connectivity issues in Docker)

3. **Port Configuration**
   - The web application is exposed on port 5001
   - The internal application runs on port 5000

## Project Structure

- `/client` - React frontend application
- `/server` - Express backend API server
- `/shared` - Shared TypeScript types and schemas
- `/uploads` - File storage for uploaded documents

## Implementation Notes

### Supabase Integration

### ⚠️ IMPORTANT: Database Access Guidelines ⚠️

**ALWAYS use the Supabase REST API exclusively for all database operations.**

- **DO NOT** use direct PostgreSQL connections - they will fail in Docker due to IPv6 issues
- **DO** use the `SupabaseStorage` implementation in `server/supabase-storage.ts`
- **IGNORE** any references to direct PostgreSQL connections in the design docs (`attached_assets/`)

This REST API approach is mandatory for cross-environment compatibility.

This approach ensures proper functioning in both Docker and non-Docker environments while maintaining compatibility across different network configurations.

## Key Dependencies

- **Frontend**: React, TypeScript, shadcn.ui, Tailwind CSS
- **Backend**: Express, Supabase REST API
- **Authentication**: Supabase Auth
- **Email**: SendGrid
- **Other**: OpenAI for AI features

## External Services

This application relies on the following external services:

1. **Supabase** - For authentication and database
2. **SendGrid** - For email functionality
3. **OpenAI** - For AI features (optional)

Make sure to register for these services and update your environment variables accordingly.
