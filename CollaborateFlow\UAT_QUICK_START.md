# 🚀 UAT Quick Start Guide

## **FOR UAT TESTERS & STAKEHOLDERS ONLY**

This is your **single entry point** for User Acceptance Testing of the CoElec platform.

### **📋 STEP 1: Read the UAT Document**
**[UAT_DOCUMENT.md](./UAT_DOCUMENT.md)** - Your complete testing guide
- 142 test cases across all features
- Clear pass/fail criteria
- Current implementation status

### **⚙️ STEP 2: Set Up Your Testing Environment**
**[SETUP_UAT.md](./SETUP_UAT.md)** - Environment setup instructions
- Docker setup for testing
- Test user accounts
- Sample data preparation

### **🎯 WHAT TO TEST**

**✅ READY FOR UAT (Fully Implemented):**
- User authentication and role management
- Team and organization management
- Project creation and Kanban boards
- Basic floor plan upload
- Quote generation (basic)
- Digital signature framework
- Client communication system

**⚠️ PARTIALLY READY (Limited Functionality):**
- Floor plan symbol detection (mock data)
- Material estimation (basic calculations)
- Supplier integration (mock suppliers)

**❌ NOT READY FOR UAT:**
- AI symbol detection (uses mock data)
- Real supplier price integration
- Complete client approval workflow

### **🚨 IMPORTANT NOTES**

1. **Focus on Core Workflows**: Test the complete project creation → estimation → quote workflow
2. **Report Issues**: Use the UAT_DOCUMENT.md status tracking
3. **Ignore Technical Docs**: All developer documentation has been moved to `/docs/` folder
4. **Ask Questions**: Contact the development team for any clarification

### **📞 UAT SUPPORT**

If you encounter issues or need clarification:
1. Check the UAT_DOCUMENT.md for known limitations
2. Verify your environment setup with SETUP_UAT.md
3. Contact the development team with specific test case IDs

---

**Remember**: This is UAT phase - focus on business functionality, not technical implementation details.
