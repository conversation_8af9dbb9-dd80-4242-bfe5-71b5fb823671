<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <!-- Runtime configuration script for Docker/containerized environments -->
    <script src="/config.js"></script>
    <!-- Initialize theme to avoid flash -->
    <script>
      (function() {
        // Check if we should use dark mode
        const storageKey = 'coelec-theme';
        const theme = localStorage.getItem(storageKey) || 'dark'; // Default to dark
        
        // Apply the theme class to the document
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>