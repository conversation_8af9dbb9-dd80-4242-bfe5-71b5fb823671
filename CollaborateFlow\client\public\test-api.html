<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    button {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px 0;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow: auto;
    }
  </style>
</head>
<body>
  <h1>CollaborateFlow API Test</h1>
  
  <h2>Create Team Test</h2>
  <button id="createTeamBtn">Create Test Team</button>
  <div>
    <strong>Response:</strong>
    <pre id="createTeamResponse">Click the button to test...</pre>
  </div>

  <h2>Get Teams Test</h2>
  <button id="getTeamsBtn">Get Teams</button>
  <div>
    <strong>Response:</strong>
    <pre id="getTeamsResponse">Click the button to test...</pre>
  </div>

  <script>
    // Create Team Test
    document.getElementById('createTeamBtn').addEventListener('click', async () => {
      const responseElement = document.getElementById('createTeamResponse');
      responseElement.textContent = 'Sending request...';
      
      try {
        // Direct API call to create a team
        const response = await fetch('/api/teams', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Test Team ' + new Date().toISOString(),
            description: 'Created via test API',
            created_by_id: 1,
            organization_id: 1
          }),
          credentials: 'include'
        });
        
        // Parse response
        const text = await response.text();
        let result;
        try {
          result = JSON.parse(text);
        } catch(e) {
          result = text;
        }
        
        // Display full response details
        responseElement.textContent = JSON.stringify({
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries([...response.headers.entries()]),
          data: result
        }, null, 2);
      } catch (error) {
        responseElement.textContent = 'Error: ' + error.message;
      }
    });

    // Get Teams Test
    document.getElementById('getTeamsBtn').addEventListener('click', async () => {
      const responseElement = document.getElementById('getTeamsResponse');
      responseElement.textContent = 'Sending request...';
      
      try {
        // Direct API call to get teams
        const response = await fetch('/api/teams', {
          method: 'GET',
          credentials: 'include'
        });
        
        // Parse response
        const text = await response.text();
        let result;
        try {
          result = JSON.parse(text);
        } catch(e) {
          result = text;
        }
        
        // Display full response details
        responseElement.textContent = JSON.stringify({
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries([...response.headers.entries()]),
          data: result
        }, null, 2);
      } catch (error) {
        responseElement.textContent = 'Error: ' + error.message;
      }
    });
  </script>
</body>
</html>
