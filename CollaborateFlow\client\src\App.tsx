import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { useMobileDetection } from "@/hooks/use-mobile-detection";
import { SupabaseAuthProvider } from "@/hooks/use-supabase-auth";
import NotFound from "@/pages/not-found";
import { AuthPage } from "@/pages/auth-page";
import HomePage from "@/pages/home-page";
import { ProjectPage } from "@/pages/project-page";
import FloorPlanPage from "@/pages/floor-plan-page";
import SymbolEditingPage from "@/pages/symbol-editing-page";
import ProjectEstimationPage from '@/pages/project-estimation-page';
import QuoteManagementPage from '@/pages/quote-management-page';
import ClientApprovalPage from '@/pages/client-approval-page';
import SettingsPage from '@/pages/settings-page';
import UserProfilePage from '@/pages/user-profile-page';
import AnalyticsDashboardPage from '@/pages/analytics-dashboard-page';
import OrganizationSetup from '@/pages/organization-setup';
import { TeamDetailsPage } from '@/pages/team-details-page';
import { OrganizationDetailsPage } from '@/pages/organization-details-page';
import AssemblyDocumentsPage from '@/pages/AssemblyDocumentsPage';
import { ClientPortal } from '@/pages/ClientPortal';
import { ProtectedRoute } from "./lib/protected-route";

// Mobile-specific components
import { MobileDashboard } from "@/components/mobile/mobile-dashboard";
import { MobileProjectView } from "@/components/mobile/mobile-project-view";
import { MobileQuoteApproval } from "@/components/mobile/mobile-quote-approval";

function Router() {
  // Use device detection to conditionally render mobile or desktop views
  const { isMobile } = useMobileDetection();

  // For mobile devices, use the mobile-specific components
  if (isMobile) {
    return (
      <Switch>
        <Route path="/auth">
          <AuthPage />
        </Route>
        <Route path="/organization-setup">
          <OrganizationSetup />
        </Route>
        <Route path="/">
          <ProtectedRoute>
            <MobileDashboard />
          </ProtectedRoute>
        </Route>
        <Route path="/project/:id">
          {(params) => (
            <ProtectedRoute>
              <MobileProjectView projectId={Number(params.id)} />
            </ProtectedRoute>
          )}
        </Route>
        <Route path="/project/:projectId/floor-plans">
          <ProtectedRoute>
            <FloorPlanPage />
          </ProtectedRoute>
        </Route>
        <Route path="/project/:projectId/symbols">
          <ProtectedRoute>
            <SymbolEditingPage />
          </ProtectedRoute>
        </Route>
        <Route path="/project/:projectId/estimation">
          <ProtectedRoute>
            <ProjectEstimationPage />
          </ProtectedRoute>
        </Route>
        <Route path="/project/:projectId/quotes">
          <ProtectedRoute>
            <QuoteManagementPage />
          </ProtectedRoute>
        </Route>
        <Route path="/quotes">
          <ProtectedRoute>
            <QuoteManagementPage />
          </ProtectedRoute>
        </Route>
        <Route path="/settings">
          <ProtectedRoute>
            <SettingsPage />
          </ProtectedRoute>
        </Route>
        <Route path="/profile">
          <ProtectedRoute>
            <UserProfilePage />
          </ProtectedRoute>
        </Route>
        <Route path="/analytics">
          <ProtectedRoute>
            <AnalyticsDashboardPage />
          </ProtectedRoute>
        </Route>
        <Route path="/assembly-documents">
          <ProtectedRoute>
            <AssemblyDocumentsPage />
          </ProtectedRoute>
        </Route>
        <Route path="/team/:id">
          <ProtectedRoute>
            <TeamDetailsPage />
          </ProtectedRoute>
        </Route>
        <Route path="/organization/:id">
          <OrganizationDetailsPage />
        </Route>
        <Route path="/quote/:quoteId/view/:token">
          {({ quoteId, token }) => <ClientApprovalPage quoteId={quoteId} token={token} />}
        </Route>
        <Route path="/mobile/quote/:quoteId/view/:token">
          {({ quoteId, token }) => <MobileQuoteApproval quoteId={quoteId} quoteToken={token} />}
        </Route>
        <Route path="/client-portal/:clientId/:accessToken">
          {({ clientId, accessToken }) => <ClientPortal clientId={clientId} accessToken={accessToken} />}
        </Route>
        <Route>
          <NotFound />
        </Route>
      </Switch>
    );
  }

  // For desktop devices, use the standard components
  return (
    <Switch>
      <Route path="/auth">
        <AuthPage />
      </Route>
      <Route path="/organization-setup">
        <OrganizationSetup />
      </Route>
      <Route path="/">
        <ProtectedRoute>
          <HomePage />
        </ProtectedRoute>
      </Route>
      <Route path="/project/:id">
        {() => (
          <ProtectedRoute>
            <ProjectPage />
          </ProtectedRoute>
        )}
      </Route>
      <Route path="/project/:projectId/floor-plans">
        <ProtectedRoute>
          <FloorPlanPage />
        </ProtectedRoute>
      </Route>
      <Route path="/project/:projectId/symbols">
        <ProtectedRoute>
          <SymbolEditingPage />
        </ProtectedRoute>
      </Route>
      <Route path="/project/:projectId/estimation">
        <ProtectedRoute>
          <ProjectEstimationPage />
        </ProtectedRoute>
      </Route>
      <Route path="/project/:projectId/quotes">
        <ProtectedRoute>
          <QuoteManagementPage />
        </ProtectedRoute>
      </Route>
      <Route path="/quotes">
        <ProtectedRoute>
          <QuoteManagementPage />
        </ProtectedRoute>
      </Route>
      <Route path="/settings">
        <ProtectedRoute>
          <SettingsPage />
        </ProtectedRoute>
      </Route>
      <Route path="/profile">
        <ProtectedRoute>
          <UserProfilePage />
        </ProtectedRoute>
      </Route>
      <Route path="/analytics">
        <ProtectedRoute>
          <AnalyticsDashboardPage />
        </ProtectedRoute>
      </Route>
      <Route path="/assembly-documents">
        <ProtectedRoute>
          <AssemblyDocumentsPage />
        </ProtectedRoute>
      </Route>
      <Route path="/team/:id">
        <ProtectedRoute>
          <TeamDetailsPage />
        </ProtectedRoute>
      </Route>
      <Route path="/organization/:id">
        <OrganizationDetailsPage />
      </Route>
      <Route path="/quote/:quoteId/view/:token">
        {({ quoteId, token }) => <ClientApprovalPage quoteId={quoteId} token={token} />}
      </Route>
      <Route path="/client-portal/:clientId/:accessToken">
        {({ clientId, accessToken }) => <ClientPortal clientId={clientId} accessToken={accessToken} />}
      </Route>
      <Route>
        <NotFound />
      </Route>
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        defaultTheme="dark"
        storageKey="coelec-theme"
        enableSystem={false}
      >
        <SupabaseAuthProvider>
          <TooltipProvider>
            <Toaster />
            <Router />
          </TooltipProvider>
        </SupabaseAuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
