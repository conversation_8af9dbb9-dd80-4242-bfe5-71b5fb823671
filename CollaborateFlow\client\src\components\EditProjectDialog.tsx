/**
 * EDIT PROJECT DIALOG COMPONENT
 * Complete project editing interface with validation and permissions
 */

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  Save, 
  Trash2, 
  Loader2, 
  FolderOpen, 
  Calendar,
  AlertTriangle,
  Users
} from "lucide-react";

// Form validation schema
const projectFormSchema = z.object({
  name: z.string().min(1, "Project name is required").max(100, "Project name must be less than 100 characters"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  status: z.enum(['planning', 'in_progress', 'completed', 'on_hold'], {
    required_error: "Please select a project status",
  }),
});

type ProjectFormValues = z.infer<typeof projectFormSchema>;

export interface Project {
  id: number;
  name: string;
  description?: string;
  status: 'planning' | 'in_progress' | 'completed' | 'on_hold';
  created_at: string;
  updated_at?: string;
  team_id: number;
  team_name?: string;
  task_count?: number;
  role?: string; // User's role in the project's team
}

interface EditProjectDialogProps {
  project: Project;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

// Status configuration
const statusConfig = {
  planning: { label: 'Planning', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' },
  in_progress: { label: 'In Progress', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' },
  completed: { label: 'Completed', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' },
  on_hold: { label: 'On Hold', color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300' },
};

export function EditProjectDialog({ 
  project, 
  open, 
  onOpenChange, 
  onSuccess 
}: EditProjectDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Form setup
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      name: project.name,
      description: project.description || "",
      status: project.status,
    },
  });

  // Check if user can edit/delete (admin or super_admin role)
  const canEdit = project.role === 'admin' || project.role === 'super_admin';
  const canDelete = project.role === 'admin' || project.role === 'super_admin';

  // Update project mutation
  const updateProjectMutation = useMutation({
    mutationFn: async (values: ProjectFormValues) => {
      return apiRequest('PUT', `/api/projects/${project.id}`, values);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/projects'] });
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${project.id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/projects/team/${project.team_id}`] });
      toast({
        title: "Project Updated",
        description: "Project details have been updated successfully.",
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update project. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete project mutation
  const deleteProjectMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('DELETE', `/api/projects/${project.id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/projects'] });
      queryClient.invalidateQueries({ queryKey: [`/api/projects/team/${project.team_id}`] });
      toast({
        title: "Project Deleted",
        description: "Project has been deleted successfully.",
      });
      onSuccess?.();
      onOpenChange(false);
      setShowDeleteDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete project. Please try again.",
        variant: "destructive",
      });
      setShowDeleteDialog(false);
    },
  });

  // Form submission
  const onSubmit = (values: ProjectFormValues) => {
    if (!canEdit) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to edit this project.",
        variant: "destructive",
      });
      return;
    }
    updateProjectMutation.mutate(values);
  };

  // Handle delete confirmation
  const handleDelete = () => {
    if (!canDelete) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to delete this project.",
        variant: "destructive",
      });
      return;
    }
    deleteProjectMutation.mutate();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FolderOpen className="h-5 w-5" />
              Edit Project
            </DialogTitle>
            <DialogDescription>
              Make changes to project details. Changes will be visible to all team members.
            </DialogDescription>
          </DialogHeader>

          {/* Project Info */}
          <div className="flex flex-wrap gap-2 py-2">
            <Badge variant="outline" className="text-xs">
              <Calendar className="h-3 w-3 mr-1" />
              Created {new Date(project.created_at).toLocaleDateString()}
            </Badge>
            {project.team_name && (
              <Badge variant="outline" className="text-xs">
                <Users className="h-3 w-3 mr-1" />
                {project.team_name}
              </Badge>
            )}
            {project.task_count !== undefined && (
              <Badge variant="outline" className="text-xs">
                {project.task_count} task{project.task_count !== 1 ? 's' : ''}
              </Badge>
            )}
            <Badge className={`text-xs ${statusConfig[project.status].color}`}>
              {statusConfig[project.status].label}
            </Badge>
            {project.role && (
              <Badge variant={project.role === 'admin' ? 'default' : 'secondary'} className="text-xs">
                {project.role}
              </Badge>
            )}
          </div>

          {/* Permission Warning */}
          {!canEdit && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
              <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                You have read-only access to this project. Contact a team admin to make changes.
              </p>
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Name</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter project name" 
                        {...field} 
                        disabled={!canEdit || updateProjectMutation.isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      A clear, descriptive name for your project.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the project goals, scope, and requirements..."
                        className="resize-none"
                        rows={3}
                        {...field}
                        disabled={!canEdit || updateProjectMutation.isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description to help team members understand the project scope.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={!canEdit || updateProjectMutation.isPending}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select project status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="planning">Planning</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="on_hold">On Hold</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Current status of the project.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter className="flex flex-col sm:flex-row gap-2">
                <div className="flex flex-1 justify-start">
                  {canDelete && (
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => setShowDeleteDialog(true)}
                      disabled={deleteProjectMutation.isPending || updateProjectMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Project
                    </Button>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={updateProjectMutation.isPending || deleteProjectMutation.isPending}
                  >
                    Cancel
                  </Button>
                  
                  {canEdit && (
                    <Button
                      type="submit"
                      disabled={updateProjectMutation.isPending || deleteProjectMutation.isPending}
                    >
                      {updateProjectMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Project
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{project.name}"? This action cannot be undone.
              {project.task_count && project.task_count > 0 && (
                <span className="block mt-2 font-medium text-destructive">
                  Warning: This project has {project.task_count} task{project.task_count !== 1 ? 's' : ''} that will be permanently deleted.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteProjectMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deleteProjectMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteProjectMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Project
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default EditProjectDialog;
