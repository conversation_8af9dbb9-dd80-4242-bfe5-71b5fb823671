/**
 * EDIT TASK DIALOG COMPONENT
 * Complete task editing interface with validation and permissions
 */

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  Save, 
  Trash2, 
  Loader2, 
  CheckSquare, 
  Calendar,
  AlertTriangle,
  User,
  FolderOpen
} from "lucide-react";

// Form validation schema
const taskFormSchema = z.object({
  title: z.string().min(1, "Task title is required").max(200, "Task title must be less than 200 characters"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  assigneeId: z.string().optional(),
});

type TaskFormValues = z.infer<typeof taskFormSchema>;

export interface Task {
  id: number;
  title: string;
  description?: string;
  created_at: string;
  updated_at?: string;
  project_id: number;
  project_name?: string;
  column_id: number;
  column_name?: string;
  assignee_id?: number;
  assignee_name?: string;
  role?: string; // User's role in the task's project team
}

export interface TeamMember {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface EditTaskDialogProps {
  task: Task;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditTaskDialog({ 
  task, 
  open, 
  onOpenChange, 
  onSuccess 
}: EditTaskDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Form setup
  const form = useForm<TaskFormValues>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: task.title,
      description: task.description || "",
      assigneeId: task.assignee_id?.toString() || "",
    },
  });

  // Check if user can edit/delete (admin or super_admin role)
  const canEdit = task.role === 'admin' || task.role === 'super_admin' || task.role === 'member';
  const canDelete = task.role === 'admin' || task.role === 'super_admin';

  // Fetch team members for assignment
  const { data: teamMembers = [] } = useQuery({
    queryKey: [`/api/projects/${task.project_id}/members`],
    queryFn: () => apiRequest('GET', `/api/projects/${task.project_id}/members`),
    enabled: open && canEdit,
  });

  // Update task mutation
  const updateTaskMutation = useMutation({
    mutationFn: async (values: TaskFormValues) => {
      const updateData = {
        title: values.title,
        description: values.description,
        assigneeId: values.assigneeId ? parseInt(values.assigneeId) : undefined,
      };
      return apiRequest('PUT', `/api/tasks/${task.id}`, updateData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/tasks'] });
      queryClient.invalidateQueries({ queryKey: [`/api/tasks?projectId=${task.project_id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${task.project_id}`] });
      toast({
        title: "Task Updated",
        description: "Task details have been updated successfully.",
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update task. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete task mutation
  const deleteTaskMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('DELETE', `/api/tasks/${task.id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/tasks'] });
      queryClient.invalidateQueries({ queryKey: [`/api/tasks?projectId=${task.project_id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${task.project_id}`] });
      toast({
        title: "Task Deleted",
        description: "Task has been deleted successfully.",
      });
      onSuccess?.();
      onOpenChange(false);
      setShowDeleteDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete task. Please try again.",
        variant: "destructive",
      });
      setShowDeleteDialog(false);
    },
  });

  // Form submission
  const onSubmit = (values: TaskFormValues) => {
    if (!canEdit) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to edit this task.",
        variant: "destructive",
      });
      return;
    }
    updateTaskMutation.mutate(values);
  };

  // Handle delete confirmation
  const handleDelete = () => {
    if (!canDelete) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to delete this task.",
        variant: "destructive",
      });
      return;
    }
    deleteTaskMutation.mutate();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckSquare className="h-5 w-5" />
              Edit Task
            </DialogTitle>
            <DialogDescription>
              Make changes to task details. Changes will be visible to all team members.
            </DialogDescription>
          </DialogHeader>

          {/* Task Info */}
          <div className="flex flex-wrap gap-2 py-2">
            <Badge variant="outline" className="text-xs">
              <Calendar className="h-3 w-3 mr-1" />
              Created {new Date(task.created_at).toLocaleDateString()}
            </Badge>
            {task.project_name && (
              <Badge variant="outline" className="text-xs">
                <FolderOpen className="h-3 w-3 mr-1" />
                {task.project_name}
              </Badge>
            )}
            {task.column_name && (
              <Badge variant="secondary" className="text-xs">
                {task.column_name}
              </Badge>
            )}
            {task.assignee_name && (
              <Badge variant="outline" className="text-xs">
                <User className="h-3 w-3 mr-1" />
                {task.assignee_name}
              </Badge>
            )}
            {task.role && (
              <Badge variant={task.role === 'admin' ? 'default' : 'secondary'} className="text-xs">
                {task.role}
              </Badge>
            )}
          </div>

          {/* Permission Warning */}
          {!canEdit && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
              <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                You have read-only access to this task. Contact a team admin to make changes.
              </p>
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Task Title</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter task title" 
                        {...field} 
                        disabled={!canEdit || updateTaskMutation.isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      A clear, actionable title for the task.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe what needs to be done, requirements, and acceptance criteria..."
                        className="resize-none"
                        rows={3}
                        {...field}
                        disabled={!canEdit || updateTaskMutation.isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description with task details and requirements.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="assigneeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assignee</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={!canEdit || updateTaskMutation.isPending}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select team member" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">Unassigned</SelectItem>
                        {teamMembers.map((member: TeamMember) => (
                          <SelectItem key={member.id} value={member.id.toString()}>
                            <div className="flex items-center gap-2">
                              <span>{member.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {member.role}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Assign this task to a team member.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter className="flex flex-col sm:flex-row gap-2">
                <div className="flex flex-1 justify-start">
                  {canDelete && (
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => setShowDeleteDialog(true)}
                      disabled={deleteTaskMutation.isPending || updateTaskMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Task
                    </Button>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={updateTaskMutation.isPending || deleteTaskMutation.isPending}
                  >
                    Cancel
                  </Button>
                  
                  {canEdit && (
                    <Button
                      type="submit"
                      disabled={updateTaskMutation.isPending || deleteTaskMutation.isPending}
                    >
                      {updateTaskMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Task
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{task.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteTaskMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deleteTaskMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteTaskMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Task
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default EditTaskDialog;
