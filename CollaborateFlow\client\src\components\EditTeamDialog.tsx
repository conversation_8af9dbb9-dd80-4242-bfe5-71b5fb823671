/**
 * EDIT TEAM DIALOG COMPONENT
 * Complete team editing interface with validation and permissions
 */

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  Save, 
  Trash2, 
  Loader2, 
  Users, 
  Calendar,
  AlertTriangle
} from "lucide-react";

// Form validation schema
const teamFormSchema = z.object({
  name: z.string().min(1, "Team name is required").max(100, "Team name must be less than 100 characters"),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
});

type TeamFormValues = z.infer<typeof teamFormSchema>;

export interface Team {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at?: string;
  created_by_id: number;
  organization_id: number;
  member_count?: number;
  project_count?: number;
  role?: string; // User's role in this team
}

interface EditTeamDialogProps {
  team: Team;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditTeamDialog({ 
  team, 
  open, 
  onOpenChange, 
  onSuccess 
}: EditTeamDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Form setup
  const form = useForm<TeamFormValues>({
    resolver: zodResolver(teamFormSchema),
    defaultValues: {
      name: team.name,
      description: team.description || "",
    },
  });

  // Check if user can edit/delete (admin or super_admin role)
  const canEdit = team.role === 'admin' || team.role === 'super_admin';
  const canDelete = team.role === 'admin' || team.role === 'super_admin';

  // Update team mutation
  const updateTeamMutation = useMutation({
    mutationFn: async (values: TeamFormValues) => {
      return apiRequest('PUT', `/api/teams/${team.id}`, values);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/teams'] });
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${team.id}`] });
      toast({
        title: "Team Updated",
        description: "Team details have been updated successfully.",
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update team. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete team mutation
  const deleteTeamMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('DELETE', `/api/teams/${team.id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/teams'] });
      toast({
        title: "Team Deleted",
        description: "Team has been deleted successfully.",
      });
      onSuccess?.();
      onOpenChange(false);
      setShowDeleteDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete team. Please try again.",
        variant: "destructive",
      });
      setShowDeleteDialog(false);
    },
  });

  // Form submission
  const onSubmit = (values: TeamFormValues) => {
    if (!canEdit) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to edit this team.",
        variant: "destructive",
      });
      return;
    }
    updateTeamMutation.mutate(values);
  };

  // Handle delete confirmation
  const handleDelete = () => {
    if (!canDelete) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to delete this team.",
        variant: "destructive",
      });
      return;
    }
    deleteTeamMutation.mutate();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Edit Team
            </DialogTitle>
            <DialogDescription>
              Make changes to team details. Changes will be visible to all team members.
            </DialogDescription>
          </DialogHeader>

          {/* Team Info */}
          <div className="flex flex-wrap gap-2 py-2">
            <Badge variant="outline" className="text-xs">
              <Calendar className="h-3 w-3 mr-1" />
              Created {new Date(team.created_at).toLocaleDateString()}
            </Badge>
            {team.member_count !== undefined && (
              <Badge variant="outline" className="text-xs">
                <Users className="h-3 w-3 mr-1" />
                {team.member_count} member{team.member_count !== 1 ? 's' : ''}
              </Badge>
            )}
            {team.project_count !== undefined && (
              <Badge variant="outline" className="text-xs">
                {team.project_count} project{team.project_count !== 1 ? 's' : ''}
              </Badge>
            )}
            {team.role && (
              <Badge variant={team.role === 'admin' ? 'default' : 'secondary'} className="text-xs">
                {team.role}
              </Badge>
            )}
          </div>

          {/* Permission Warning */}
          {!canEdit && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
              <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                You have read-only access to this team. Contact an admin to make changes.
              </p>
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Team Name</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter team name" 
                        {...field} 
                        disabled={!canEdit || updateTeamMutation.isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      A clear, descriptive name for your team.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the team's purpose and responsibilities..."
                        className="resize-none"
                        rows={3}
                        {...field}
                        disabled={!canEdit || updateTeamMutation.isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description to help team members understand the team's purpose.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter className="flex flex-col sm:flex-row gap-2">
                <div className="flex flex-1 justify-start">
                  {canDelete && (
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => setShowDeleteDialog(true)}
                      disabled={deleteTeamMutation.isPending || updateTeamMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Team
                    </Button>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={updateTeamMutation.isPending || deleteTeamMutation.isPending}
                  >
                    Cancel
                  </Button>
                  
                  {canEdit && (
                    <Button
                      type="submit"
                      disabled={updateTeamMutation.isPending || deleteTeamMutation.isPending}
                    >
                      {updateTeamMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Team
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{team.name}"? This action cannot be undone.
              {team.member_count && team.member_count > 1 && (
                <span className="block mt-2 font-medium text-destructive">
                  Warning: This team has {team.member_count} members who will lose access.
                </span>
              )}
              {team.project_count && team.project_count > 0 && (
                <span className="block mt-1 font-medium text-destructive">
                  Warning: This team has {team.project_count} project{team.project_count !== 1 ? 's' : ''} that may be affected.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteTeamMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deleteTeamMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteTeamMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Team
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default EditTeamDialog;
