/**
 * QUOTE APPROVAL COMPONENT
 * Enhanced quote approval interface with digital signature and change requests
 */

import { useState, useRef } from "react";
import { 
  Check, 
  X, 
  Download, 
  MessageSquare, 
  FileText, 
  DollarSign,
  Calendar,
  User,
  Building,
  Phone,
  Mail,
  Edit3,
  Send
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AICard } from "@/components/ai-card";
import { useToast } from "@/hooks/use-toast";

interface Quote {
  id: string;
  number: string;
  title: string;
  status: string;
  totalCost: number;
  currency: string;
  createdAt: string;
  validUntil: string;
  description?: string;
  materials?: Material[];
  laborItems?: LaborItem[];
  feedback?: Feedback[];
}

interface Material {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

interface LaborItem {
  id: string;
  description: string;
  hours: number;
  rate: number;
  totalPrice: number;
}

interface Feedback {
  id: string;
  message: string;
  section: string;
  isClient: boolean;
  createdAt: string;
}

interface QuoteApprovalProps {
  quote: Quote;
  onApprove: (signature: string) => void;
  onReject: () => void;
  onChangeRequest: (title: string, description: string) => void;
  onClose: () => void;
}

export function QuoteApproval({ 
  quote, 
  onApprove, 
  onReject, 
  onChangeRequest, 
  onClose 
}: QuoteApprovalProps) {
  const { toast } = useToast();
  const signatureCanvasRef = useRef<HTMLCanvasElement>(null);
  
  // State management
  const [activeTab, setActiveTab] = useState("overview");
  const [showSignatureDialog, setShowSignatureDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showChangeRequestDialog, setShowChangeRequestDialog] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const [signature, setSignature] = useState("");
  const [rejectReason, setRejectReason] = useState("");
  const [changeRequestTitle, setChangeRequestTitle] = useState("");
  const [changeRequestDescription, setChangeRequestDescription] = useState("");

  // Mock data for materials and labor
  const mockMaterials: Material[] = [
    {
      id: 'mat_001',
      name: 'GFCI Outlets',
      description: '20A GFCI outlets for wet locations',
      quantity: 12,
      unitPrice: 25.00,
      totalPrice: 300.00
    },
    {
      id: 'mat_002',
      name: 'LED Light Fixtures',
      description: '4ft LED linear fixtures, 4000K',
      quantity: 8,
      unitPrice: 85.00,
      totalPrice: 680.00
    },
    {
      id: 'mat_003',
      name: '12 AWG Wire',
      description: 'THHN copper wire, 12 AWG',
      quantity: 500,
      unitPrice: 1.20,
      totalPrice: 600.00
    }
  ];

  const mockLaborItems: LaborItem[] = [
    {
      id: 'lab_001',
      description: 'Electrical rough-in',
      hours: 16,
      rate: 85.00,
      totalPrice: 1360.00
    },
    {
      id: 'lab_002',
      description: 'Fixture installation',
      hours: 8,
      rate: 85.00,
      totalPrice: 680.00
    },
    {
      id: 'lab_003',
      description: 'Testing and commissioning',
      hours: 4,
      rate: 95.00,
      totalPrice: 380.00
    }
  ];

  // Signature canvas functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.beginPath();
    ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
    ctx.stroke();
  };

  const stopDrawing = () => {
    setIsDrawing(false);
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    setSignature(canvas.toDataURL());
  };

  const clearSignature = () => {
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setSignature("");
  };

  // Handle approval with signature
  const handleApprovalWithSignature = () => {
    if (!signature) {
      toast({
        title: "Signature Required",
        description: "Please provide your signature to approve this quote.",
        variant: "destructive"
      });
      return;
    }

    onApprove(signature);
    setShowSignatureDialog(false);
    onClose();
  };

  // Handle rejection
  const handleRejection = () => {
    if (!rejectReason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for rejecting this quote.",
        variant: "destructive"
      });
      return;
    }

    onReject();
    setShowRejectDialog(false);
    onClose();
  };

  // Handle change request
  const handleChangeRequestSubmission = () => {
    if (!changeRequestTitle.trim() || !changeRequestDescription.trim()) {
      toast({
        title: "Required Fields",
        description: "Please provide both a title and description for your change request.",
        variant: "destructive"
      });
      return;
    }

    onChangeRequest(changeRequestTitle, changeRequestDescription);
    setShowChangeRequestDialog(false);
    setChangeRequestTitle("");
    setChangeRequestDescription("");
    
    toast({
      title: "Change Request Submitted",
      description: "Your change request has been submitted and will be reviewed shortly."
    });
  };

  // Calculate totals
  const materialTotal = mockMaterials.reduce((sum, item) => sum + item.totalPrice, 0);
  const laborTotal = mockLaborItems.reduce((sum, item) => sum + item.totalPrice, 0);
  const subtotal = materialTotal + laborTotal;
  const tax = subtotal * 0.08; // 8% tax
  const total = subtotal + tax;

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Quote #{quote.number} - {quote.title}
          </DialogTitle>
          <DialogDescription>
            Review the quote details and take action
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="materials">Materials</TabsTrigger>
            <TabsTrigger value="labor">Labor</TabsTrigger>
            <TabsTrigger value="terms">Terms</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <AICard>
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Quote Information</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Quote Number:</span>
                        <span className="font-medium">{quote.number}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <Badge className={
                          quote.status === 'approved' ? 'bg-green-100 text-green-800' :
                          quote.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          quote.status === 'rejected' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Created:</span>
                        <span>{new Date(quote.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Valid Until:</span>
                        <span>{new Date(quote.validUntil).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Cost Summary</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Materials:</span>
                        <span>${materialTotal.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Labor:</span>
                        <span>${laborTotal.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Subtotal:</span>
                        <span>${subtotal.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax (8%):</span>
                        <span>${tax.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-lg font-semibold border-t pt-2">
                        <span>Total:</span>
                        <span>${total.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-2">Project Description</h3>
                  <p className="text-gray-700">{quote.description}</p>
                </div>
              </div>
            </AICard>
          </TabsContent>

          {/* Materials Tab */}
          <TabsContent value="materials" className="space-y-4">
            <AICard>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-4">Materials & Equipment</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Item</th>
                        <th className="text-left py-2">Description</th>
                        <th className="text-right py-2">Qty</th>
                        <th className="text-right py-2">Unit Price</th>
                        <th className="text-right py-2">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockMaterials.map((material) => (
                        <tr key={material.id} className="border-b">
                          <td className="py-3 font-medium">{material.name}</td>
                          <td className="py-3 text-gray-600">{material.description}</td>
                          <td className="py-3 text-right">{material.quantity}</td>
                          <td className="py-3 text-right">${material.unitPrice.toFixed(2)}</td>
                          <td className="py-3 text-right font-medium">${material.totalPrice.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="border-t-2 font-semibold">
                        <td colSpan={4} className="py-3 text-right">Materials Total:</td>
                        <td className="py-3 text-right">${materialTotal.toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </AICard>
          </TabsContent>

          {/* Labor Tab */}
          <TabsContent value="labor" className="space-y-4">
            <AICard>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-4">Labor & Services</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Description</th>
                        <th className="text-right py-2">Hours</th>
                        <th className="text-right py-2">Rate</th>
                        <th className="text-right py-2">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockLaborItems.map((labor) => (
                        <tr key={labor.id} className="border-b">
                          <td className="py-3 font-medium">{labor.description}</td>
                          <td className="py-3 text-right">{labor.hours}</td>
                          <td className="py-3 text-right">${labor.rate.toFixed(2)}/hr</td>
                          <td className="py-3 text-right font-medium">${labor.totalPrice.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="border-t-2 font-semibold">
                        <td colSpan={3} className="py-3 text-right">Labor Total:</td>
                        <td className="py-3 text-right">${laborTotal.toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </AICard>
          </TabsContent>

          {/* Terms Tab */}
          <TabsContent value="terms" className="space-y-4">
            <AICard>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-4">Terms & Conditions</h3>
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium mb-2">Payment Terms</h4>
                    <p className="text-gray-700">
                      50% deposit required upon approval. Remaining balance due upon completion.
                      Payment accepted via check, ACH, or credit card.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Timeline</h4>
                    <p className="text-gray-700">
                      Work will commence within 5 business days of approval and deposit receipt.
                      Estimated completion time: 3-5 business days.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Warranty</h4>
                    <p className="text-gray-700">
                      All work is guaranteed for 2 years from completion date.
                      Materials carry manufacturer warranty.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Permits & Inspections</h4>
                    <p className="text-gray-700">
                      All necessary permits and inspections are included in the quoted price.
                      Work will be performed to current electrical codes.
                    </p>
                  </div>
                </div>
              </div>
            </AICard>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <div className="flex gap-2 flex-1">
            <Button
              variant="outline"
              onClick={() => setShowChangeRequestDialog(true)}
              className="flex-1"
            >
              <Edit3 className="h-4 w-4 mr-2" />
              Request Changes
            </Button>
            
            <Button variant="outline" onClick={onClose} className="flex-1">
              Close
            </Button>
          </div>
          
          {quote.status === 'pending' && (
            <div className="flex gap-2">
              <Button
                variant="destructive"
                onClick={() => setShowRejectDialog(true)}
              >
                <X className="h-4 w-4 mr-2" />
                Reject
              </Button>
              
              <Button onClick={() => setShowSignatureDialog(true)}>
                <Check className="h-4 w-4 mr-2" />
                Approve & Sign
              </Button>
            </div>
          )}
        </DialogFooter>

        {/* Signature Dialog */}
        <Dialog open={showSignatureDialog} onOpenChange={setShowSignatureDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Digital Signature</DialogTitle>
              <DialogDescription>
                Please sign below to approve this quote
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <canvas
                  ref={signatureCanvasRef}
                  width={400}
                  height={200}
                  className="w-full h-32 border rounded cursor-crosshair"
                  onMouseDown={startDrawing}
                  onMouseMove={draw}
                  onMouseUp={stopDrawing}
                  onMouseLeave={stopDrawing}
                />
                <p className="text-sm text-gray-500 mt-2 text-center">
                  Sign above using your mouse or touch screen
                </p>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" onClick={clearSignature} className="flex-1">
                  Clear
                </Button>
                <Button onClick={handleApprovalWithSignature} className="flex-1">
                  Approve Quote
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Reject Dialog */}
        <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Quote</DialogTitle>
              <DialogDescription>
                Please provide a reason for rejecting this quote
              </DialogDescription>
            </DialogHeader>
            
            <Textarea
              placeholder="Please explain why you're rejecting this quote..."
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              rows={4}
            />
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleRejection}>
                Reject Quote
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Change Request Dialog */}
        <Dialog open={showChangeRequestDialog} onOpenChange={setShowChangeRequestDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Request Changes</DialogTitle>
              <DialogDescription>
                Describe the changes you'd like to make to this quote
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Change Request Title
                </label>
                <Input
                  placeholder="Brief description of the change..."
                  value={changeRequestTitle}
                  onChange={(e) => setChangeRequestTitle(e.target.value)}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">
                  Detailed Description
                </label>
                <Textarea
                  placeholder="Please provide detailed information about the changes you'd like..."
                  value={changeRequestDescription}
                  onChange={(e) => setChangeRequestDescription(e.target.value)}
                  rows={4}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowChangeRequestDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleChangeRequestSubmission}>
                <Send className="h-4 w-4 mr-2" />
                Submit Request
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
}

export default QuoteApproval;
