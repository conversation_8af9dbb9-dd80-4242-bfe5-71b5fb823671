import React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

interface AICardProps {
  title?: React.ReactNode;
  description?: React.ReactNode;
  children: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
}

export function AICard({
  title,
  description,
  children,
  footer,
  className,
  contentClassName,
  headerClassName,
  footerClassName,
}: AICardProps) {
  return (
    <Card className={cn(
      "border border-border/40 bg-card rounded-xl shadow-sm", 
      "backdrop-blur-[2px] dark:bg-card/95",
      "transition-all duration-200",
      className
    )}>
      {(title || description) && (
        <CardHeader className={cn("pb-2", headerClassName)}>
          {title && <CardTitle>{title}</CardTitle>}
          {description && (
            <CardDescription className="text-muted-foreground">
              {description}
            </CardDescription>
          )}
        </CardHeader>
      )}
      <CardContent className={cn("pt-0", contentClassName)}>{children}</CardContent>
      {footer && (
        <CardFooter className={cn("flex justify-between items-center border-t pt-4", footerClassName)}>
          {footer}
        </CardFooter>
      )}
    </Card>
  );
}