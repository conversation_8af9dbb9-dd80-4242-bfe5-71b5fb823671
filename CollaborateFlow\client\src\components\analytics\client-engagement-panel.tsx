import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Loader2, 
  Users, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  CheckCircle, 
  X, 
  MessageSquare,
  Building2,
  Mail,
  ThumbsUp,
  BarChart4,
  ArrowUpRight
} from "lucide-react";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from "recharts";

interface ClientEngagementPanelProps {
  timeRange: string;
  isLoading: boolean;
}

export function ClientEngagementPanel({ timeRange, isLoading }: ClientEngagementPanelProps) {
  const [viewType, setViewType] = useState<"overview" | "feedback" | "quotes">("overview");
  
  // Sample data
  const clientResponseData = [
    { month: "Jan", responseRate: 82, approvalRate: 68 },
    { month: "Feb", responseRate: 84, approvalRate: 72 },
    { month: "Mar", responseRate: 86, approvalRate: 76 },
    { month: "Apr", responseRate: 85, approvalRate: 78 },
    { month: "May", responseRate: 88, approvalRate: 80 },
    { month: "Jun", responseRate: 90, approvalRate: 84 },
    { month: "Jul", responseRate: 91, approvalRate: 86 },
    { month: "Aug", responseRate: 92, approvalRate: 88 },
    { month: "Sep", responseRate: 90, approvalRate: 85 },
    { month: "Oct", responseRate: 93, approvalRate: 89 },
    { month: "Nov", responseRate: 94, approvalRate: 90 },
    { month: "Dec", responseRate: 95, approvalRate: 92 },
  ];
  
  const clientFeedbackData = [
    { category: "Pricing", positive: 68, neutral: 22, negative: 10 },
    { category: "Quality", positive: 82, neutral: 12, negative: 6 },
    { category: "Timeline", positive: 64, neutral: 24, negative: 12 },
    { category: "Communication", positive: 76, neutral: 18, negative: 6 },
    { category: "Support", positive: 72, neutral: 20, negative: 8 },
  ];
  
  const quoteStatusData = [
    { name: "Approved", value: 42, color: "#10b981" },
    { name: "Pending", value: 28, color: "#3b82f6" },
    { name: "Rejected", value: 8, color: "#ef4444" },
    { name: "Expired", value: 6, color: "#f59e0b" },
    { name: "Revised", value: 16, color: "#6366f1" }
  ];
  
  // Calculate metrics
  const totalQuotes = quoteStatusData.reduce((sum, item) => sum + item.value, 0);
  const approvedQuotes = quoteStatusData.find(item => item.name === "Approved")?.value || 0;
  const conversionRate = Math.round((approvedQuotes / totalQuotes) * 100);
  
  const averageFeedbackScore = 
    clientFeedbackData.reduce((sum, item) => {
      const totalResponses = item.positive + item.neutral + item.negative;
      const weightedScore = (item.positive * 1 + item.neutral * 0.5 + item.negative * 0) / totalResponses;
      return sum + weightedScore;
    }, 0) / clientFeedbackData.length * 5;
  
  // Client list with engagement metrics
  const clientEngagementData = [
    { id: 1, name: "ABC Corporation", quotes: 8, approved: 6, totalValue: 240000, feedback: 4.8 },
    { id: 2, name: "XYZ Developers", quotes: 5, approved: 3, totalValue: 185000, feedback: 4.2 },
    { id: 3, name: "123 Properties", quotes: 7, approved: 4, totalValue: 210000, feedback: 4.7 },
    { id: 4, name: "Retail Ventures", quotes: 4, approved: 3, totalValue: 120000, feedback: 4.5 },
    { id: 5, name: "Coastal Living", quotes: 6, approved: 4, totalValue: 195000, feedback: 4.3 },
    { id: 6, name: "Northend Medical", quotes: 3, approved: 2, totalValue: 82000, feedback: 4.6 },
    { id: 7, name: "Eastview School District", quotes: 2, approved: 2, totalValue: 145000, feedback: 4.9 },
    { id: 8, name: "County Government", quotes: 4, approved: 3, totalValue: 175000, feedback: 4.4 },
  ];
  
  // Format functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };
  
  // Style helpers
  const getScoreColor = (score: number) => {
    if (score >= 4.5) return "text-green-600 dark:text-green-400";
    if (score >= 4.0) return "text-blue-600 dark:text-blue-400";
    if (score >= 3.5) return "text-amber-600 dark:text-amber-400";
    return "text-red-600 dark:text-red-400";
  };
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Client Engagement</h2>
          <p className="text-muted-foreground">
            Analysis of client interactions and satisfaction {timeRange === "all" ? "all time" : `for the ${getTimeRangeText(timeRange)}`}
          </p>
        </div>
        
        <Tabs value={viewType} onValueChange={(v) => setViewType(v as any)} className="w-auto">
          <TabsList className="bg-muted/50 grid grid-cols-3 h-auto p-1">
            <TabsTrigger value="overview" className="py-1.5 px-3 text-xs">Overview</TabsTrigger>
            <TabsTrigger value="feedback" className="py-1.5 px-3 text-xs">Feedback</TabsTrigger>
            <TabsTrigger value="quotes" className="py-1.5 px-3 text-xs">Quotes</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Key metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Quote Conversion</p>
                <div className="text-2xl font-bold">{conversionRate}%</div>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                8% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Response Rate</p>
                <div className="text-2xl font-bold">95%</div>
              </div>
              <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <MessageSquare className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                5% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Feedback Score</p>
                <div className="text-2xl font-bold">{averageFeedbackScore.toFixed(1)}/5</div>
              </div>
              <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                <ThumbsUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                0.3 Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Avg. Response Time</p>
                <div className="text-2xl font-bold">1.8 days</div>
              </div>
              <div className="h-12 w-12 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingDown className="h-3 w-3 mr-1" />
                0.5 day faster
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Main content based on view type */}
      <TabsContent value="overview" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Response & Approval Rate Chart */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Client Response & Approval Rates</CardTitle>
              <CardDescription>
                Monthly trends in client response and approval rates
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <LineChart data={clientResponseData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value}%`, 'Rate']}
                    />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="responseRate" 
                      name="Response Rate" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 8 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="approvalRate" 
                      name="Approval Rate" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Quote Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Quote Status Distribution</CardTitle>
              <CardDescription>
                Breakdown of quotes by current status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <PieChart>
                    <Pie
                      data={quoteStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {quoteStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value} Quotes`, 'Count']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Client Engagement Table */}
        <Card>
          <CardHeader>
            <CardTitle>Client Engagement Metrics</CardTitle>
            <CardDescription>
              Key engagement metrics by client
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md">
              <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
                <div className="col-span-4">Client</div>
                <div className="col-span-2 text-center">Quotes</div>
                <div className="col-span-2 text-center">Approved</div>
                <div className="col-span-2 text-right">Total Value</div>
                <div className="col-span-2 text-center">Feedback</div>
              </div>
              <div className="divide-y">
                {isLoading ? (
                  <div className="h-48 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  clientEngagementData.map((client) => (
                    <div key={client.id} className="grid grid-cols-12 gap-4 p-4 items-center">
                      <div className="col-span-4 font-medium">{client.name}</div>
                      <div className="col-span-2 text-center">{client.quotes}</div>
                      <div className="col-span-2 text-center">{client.approved}</div>
                      <div className="col-span-2 text-right">{formatCurrency(client.totalValue)}</div>
                      <div className={`col-span-2 text-center font-medium ${getScoreColor(client.feedback)}`}>
                        {client.feedback.toFixed(1)}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="feedback" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Feedback By Category */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Feedback By Category</CardTitle>
              <CardDescription>
                Client feedback distribution across categories
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <BarChart data={clientFeedbackData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis tickFormatter={(value) => `${value}%`} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value}%`, 'Percentage']}
                    />
                    <Legend />
                    <Bar dataKey="positive" name="Positive" stackId="a" fill="#10b981" />
                    <Bar dataKey="neutral" name="Neutral" stackId="a" fill="#f59e0b" />
                    <Bar dataKey="negative" name="Negative" stackId="a" fill="#ef4444" />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Feedback Score Trend */}
          <Card>
            <CardHeader>
              <CardTitle>Feedback Score Trend</CardTitle>
              <CardDescription>
                Monthly average feedback score
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <LineChart
                    data={[
                      { month: "Jan", score: 4.2 },
                      { month: "Feb", score: 4.3 },
                      { month: "Mar", score: 4.2 },
                      { month: "Apr", score: 4.4 },
                      { month: "May", score: 4.5 },
                      { month: "Jun", score: 4.6 },
                      { month: "Jul", score: 4.5 },
                      { month: "Aug", score: 4.7 },
                      { month: "Sep", score: 4.6 },
                      { month: "Oct", score: 4.8 },
                      { month: "Nov", score: 4.7 },
                      { month: "Dec", score: 4.9 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis domain={[4, 5]} ticks={[4, 4.2, 4.4, 4.6, 4.8, 5]} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="score" 
                      name="Average Score" 
                      stroke="#3b82f6" 
                      strokeWidth={2} 
                      dot={{ r: 4 }}
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Feedback Comments */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Client Feedback</CardTitle>
            <CardDescription>
              Notable client comments and ratings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <>
                  <div className="bg-muted/20 rounded-lg p-4">
                    <div className="flex justify-between mb-2">
                      <div className="font-medium">ABC Corporation</div>
                      <div className="flex items-center">
                        <span className="text-sm text-green-600 dark:text-green-400 font-medium mr-1">5.0</span>
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <ThumbsUp key={star} className="h-3 w-3 text-green-500" />
                          ))}
                        </div>
                      </div>
                    </div>
                    <p className="text-sm">
                      "The team at CoElec was professional and thorough. They finished the Central Plaza project on time and within budget. Their attention to detail was impressive."
                    </p>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">Received: May 10, 2025</span>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Pricing & Quality</Badge>
                    </div>
                  </div>
                  
                  <div className="bg-muted/20 rounded-lg p-4">
                    <div className="flex justify-between mb-2">
                      <div className="font-medium">XYZ Developers</div>
                      <div className="flex items-center">
                        <span className="text-sm text-blue-600 dark:text-blue-400 font-medium mr-1">4.2</span>
                        <div className="flex">
                          {[1, 2, 3, 4].map((star) => (
                            <ThumbsUp key={star} className="h-3 w-3 text-blue-500" />
                          ))}
                          <ThumbsUp className="h-3 w-3 text-muted" />
                        </div>
                      </div>
                    </div>
                    <p className="text-sm">
                      "Good work on the Westside Residential project, but there were some communication issues during the middle phase that caused minor delays."
                    </p>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">Received: May 8, 2025</span>
                      <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">Communication</Badge>
                    </div>
                  </div>
                  
                  <div className="bg-muted/20 rounded-lg p-4">
                    <div className="flex justify-between mb-2">
                      <div className="font-medium">123 Properties</div>
                      <div className="flex items-center">
                        <span className="text-sm text-green-600 dark:text-green-400 font-medium mr-1">4.7</span>
                        <div className="flex">
                          {[1, 2, 3, 4].map((star) => (
                            <ThumbsUp key={star} className="h-3 w-3 text-green-500" />
                          ))}
                          <ThumbsUp className="h-3 w-3 text-muted" />
                        </div>
                      </div>
                    </div>
                    <p className="text-sm">
                      "Very satisfied with the quality of work on the Downtown Office Retrofit. The team's expertise was evident, though the original timeline estimate was slightly optimistic."
                    </p>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">Received: May 5, 2025</span>
                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">Quality & Timeline</Badge>
                    </div>
                  </div>
                  
                  <div className="bg-muted/20 rounded-lg p-4">
                    <div className="flex justify-between mb-2">
                      <div className="font-medium">Retail Ventures</div>
                      <div className="flex items-center">
                        <span className="text-sm text-green-600 dark:text-green-400 font-medium mr-1">4.5</span>
                        <div className="flex">
                          {[1, 2, 3, 4].map((star) => (
                            <ThumbsUp key={star} className="h-3 w-3 text-green-500" />
                          ))}
                          <ThumbsUp className="h-3 w-3 text-muted" />
                        </div>
                      </div>
                    </div>
                    <p className="text-sm">
                      "The Highland Shopping Center project went smoothly. Good communication throughout, and the final results exceeded our expectations. Pricing was fair and competitive."
                    </p>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">Received: May 2, 2025</span>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Communication & Quality</Badge>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Feedback Categories Breakdown</CardTitle>
            <CardDescription>
              Detailed analysis of feedback by category and sentiment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 bg-[#10b981] rounded-full"></div>
                        <span className="font-medium">Quality of Work</span>
                      </div>
                      <span className="font-medium text-green-600 dark:text-green-400">4.8/5</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Clients consistently praise the high standard of electrical work and attention to detail.
                    </p>
                    <Separator />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 bg-[#3b82f6] rounded-full"></div>
                        <span className="font-medium">Communication</span>
                      </div>
                      <span className="font-medium text-blue-600 dark:text-blue-400">4.5/5</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Responsiveness and clarity of communication are valued, with occasional feedback about response times.
                    </p>
                    <Separator />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 bg-[#f59e0b] rounded-full"></div>
                        <span className="font-medium">Timeline Adherence</span>
                      </div>
                      <span className="font-medium text-amber-600 dark:text-amber-400">4.2/5</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Most projects completed on time, with some feedback about optimistic initial estimates.
                    </p>
                    <Separator />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 bg-[#6366f1] rounded-full"></div>
                        <span className="font-medium">Pricing</span>
                      </div>
                      <span className="font-medium text-indigo-600 dark:text-indigo-400">4.3/5</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Generally seen as fair value for quality, with some feedback about budget overruns.
                    </p>
                    <Separator />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 bg-[#8b5cf6] rounded-full"></div>
                        <span className="font-medium">Customer Support</span>
                      </div>
                      <span className="font-medium text-purple-600 dark:text-purple-400">4.6/5</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Post-project support and responsiveness to issues receives positive feedback.
                    </p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="quotes" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quote Conversion Trend */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Quote Conversion Trend</CardTitle>
              <CardDescription>
                Monthly quote approval rate
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <AreaChart
                    data={[
                      { month: "Jan", sent: 18, approved: 12, rate: 67 },
                      { month: "Feb", sent: 20, approved: 14, rate: 70 },
                      { month: "Mar", sent: 22, approved: 16, rate: 73 },
                      { month: "Apr", sent: 19, approved: 14, rate: 74 },
                      { month: "May", sent: 24, approved: 18, rate: 75 },
                      { month: "Jun", sent: 26, approved: 20, rate: 77 },
                      { month: "Jul", sent: 23, approved: 18, rate: 78 },
                      { month: "Aug", sent: 25, approved: 20, rate: 80 },
                      { month: "Sep", sent: 28, approved: 23, rate: 82 },
                      { month: "Oct", sent: 30, approved: 25, rate: 83 },
                      { month: "Nov", sent: 32, approved: 27, rate: 84 },
                      { month: "Dec", sent: 35, approved: 30, rate: 86 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" />
                    <YAxis yAxisId="right" orientation="right" domain={[60, 90]} tickFormatter={(value) => `${value}%`} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any, name) => {
                        if (name === "Conversion Rate") return [`${value}%`, name];
                        return [value, name];
                      }}
                    />
                    <Legend />
                    <Area 
                      yAxisId="left"
                      type="monotone" 
                      dataKey="sent" 
                      name="Quotes Sent" 
                      stroke="#3b82f6" 
                      fill="#3b82f640" 
                      strokeWidth={2}
                    />
                    <Area 
                      yAxisId="left"
                      type="monotone" 
                      dataKey="approved" 
                      name="Quotes Approved" 
                      stroke="#10b981" 
                      fill="#10b98140" 
                      strokeWidth={2}
                    />
                    <Line 
                      yAxisId="right"
                      type="monotone" 
                      dataKey="rate" 
                      name="Conversion Rate" 
                      stroke="#f59e0b"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Quote Value Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Quote Value Distribution</CardTitle>
              <CardDescription>
                Distribution of quotes by value range
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <PieChart>
                    <Pie
                      data={[
                        { range: "<$10k", value: 8, color: "#3b82f6" },
                        { range: "$10k-$25k", value: 14, color: "#10b981" },
                        { range: "$25k-$50k", value: 24, color: "#f59e0b" },
                        { range: "$50k-$100k", value: 32, color: "#6366f1" },
                        { range: ">$100k", value: 22, color: "#ef4444" },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { range: "<$10k", value: 8, color: "#3b82f6" },
                        { range: "$10k-$25k", value: 14, color: "#10b981" },
                        { range: "$25k-$50k", value: 24, color: "#f59e0b" },
                        { range: "$50k-$100k", value: 32, color: "#6366f1" },
                        { range: ">$100k", value: 22, color: "#ef4444" },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value} Quotes`, 'Count']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Quote Response Time Analysis */}
        <Card>
          <CardHeader>
            <CardTitle>Quote Response Time Analysis</CardTitle>
            <CardDescription>
              Distribution of client response times to quotes
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={320}>
                <BarChart
                  data={[
                    { range: "Same Day", count: 18, approved: 14, rejected: 4 },
                    { range: "1-2 Days", count: 32, approved: 24, rejected: 8 },
                    { range: "3-5 Days", count: 24, approved: 16, rejected: 8 },
                    { range: "6-10 Days", count: 12, approved: 6, rejected: 6 },
                    { range: ">10 Days", count: 14, approved: 4, rejected: 10 },
                  ]}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" />
                  <YAxis />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                  />
                  <Legend />
                  <Bar dataKey="approved" name="Approved" stackId="a" fill="#10b981" />
                  <Bar dataKey="rejected" name="Rejected" stackId="a" fill="#ef4444" />
                </BarChart>
              </ResponsiveContainer>
            )}
            <div className="mt-4 px-4 py-3 bg-amber-50 dark:bg-amber-950/50 text-amber-800 dark:text-amber-300 rounded-md text-sm">
              <div className="flex items-start gap-2">
                <Clock className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <div>
                  <strong>Insight:</strong> Quotes that receive a response within 5 days have a significantly higher approval rate (73%) compared to those with longer response times (33%).
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Quote Revision Impact */}
        <Card>
          <CardHeader>
            <CardTitle>Quote Revision Impact</CardTitle>
            <CardDescription>
              Analysis of how revisions affect quote approval
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {isLoading ? (
                <div className="md:col-span-2 h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <>
                  <div>
                    <h3 className="text-base font-medium mb-4">Approval Rate by Revision Count</h3>
                    <ResponsiveContainer width="100%" height={220}>
                      <BarChart
                        data={[
                          { revisions: "No Revisions", rate: 62 },
                          { revisions: "1 Revision", rate: 78 },
                          { revisions: "2 Revisions", rate: 85 },
                          { revisions: "3+ Revisions", rate: 72 },
                        ]}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="revisions" />
                        <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                        <Tooltip 
                          contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                          itemStyle={{ color: "hsl(var(--foreground))" }}
                          formatter={(value: any) => [`${value}%`, 'Approval Rate']}
                        />
                        <Bar dataKey="rate" name="Approval Rate" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div>
                    <h3 className="text-base font-medium mb-4">Common Revision Requests</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Pricing Adjustments</span>
                          <span className="text-sm font-medium">42%</span>
                        </div>
                        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-blue-500" style={{ width: "42%" }}></div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Material Specifications</span>
                          <span className="text-sm font-medium">28%</span>
                        </div>
                        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-green-500" style={{ width: "28%" }}></div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Timeline Changes</span>
                          <span className="text-sm font-medium">16%</span>
                        </div>
                        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-amber-500" style={{ width: "16%" }}></div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Scope Modifications</span>
                          <span className="text-sm font-medium">14%</span>
                        </div>
                        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                          <div className="h-full bg-purple-500" style={{ width: "14%" }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className="mt-6 px-4 py-3 bg-blue-50 dark:bg-blue-950/50 text-blue-800 dark:text-blue-300 rounded-md text-sm">
              <div className="flex items-start gap-2">
                <ArrowUpRight className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <div>
                  <strong>Insight:</strong> Quotes that undergo 1-2 revisions have the highest approval rate, suggesting that collaboration on refinements leads to better outcomes.
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  );
}

// Helper functions
function getTimeRangeText(timeRange: string) {
  switch (timeRange) {
    case "7days": return "last 7 days";
    case "30days": return "last 30 days";
    case "90days": return "last 90 days";
    case "year": return "last 12 months";
    case "ytd": return "year to date";
    default: return "selected time period";
  }
}