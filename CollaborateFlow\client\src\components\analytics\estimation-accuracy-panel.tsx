import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  TrendingDown, 
  Loader2, 
  Target, 
  BarChart4, 
  Scale,
  Clock,
  Percent,
  AlertTriangle,
  Package
} from "lucide-react";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  LineChart,
  Line,
  Pie<PERSON>hart,
  Pie,
  Cell,
  ScatterChart,
  Scatter,
  ZAxis
} from "recharts";

interface EstimationAccuracyPanelProps {
  timeRange: string;
  isLoading: boolean;
}

export function EstimationAccuracyPanel({ timeRange, isLoading }: EstimationAccuracyPanelProps) {
  const [viewType, setViewType] = useState<"overview" | "materials" | "labor">("overview");
  
  // Sample data
  const overallAccuracyData = [
    { name: "Jan", accuracy: 88 },
    { name: "Feb", accuracy: 86 },
    { name: "Mar", accuracy: 89 },
    { name: "Apr", accuracy: 91 },
    { name: "May", accuracy: 92 },
    { name: "Jun", accuracy: 90 },
    { name: "Jul", accuracy: 93 },
    { name: "Aug", accuracy: 94 },
    { name: "Sep", accuracy: 93 },
    { name: "Oct", accuracy: 95 },
    { name: "Nov", accuracy: 94 },
    { name: "Dec", accuracy: 96 },
  ];
  
  const materialEstimationData = [
    { category: "Lighting Fixtures", estimated: 28000, actual: 30800, variance: 10 },
    { category: "Wiring/Cables", estimated: 42000, actual: 39900, variance: -5 },
    { category: "Switches/Outlets", estimated: 18000, actual: 17100, variance: -5 },
    { category: "Circuit Breakers", estimated: 24000, actual: 26400, variance: 10 },
    { category: "Junction Boxes", estimated: 12000, actual: 11400, variance: -5 },
    { category: "Conduit/Raceways", estimated: 32000, actual: 33600, variance: 5 },
    { category: "Other Materials", estimated: 15000, actual: 16500, variance: 10 },
  ];
  
  const laborEstimationData = [
    { category: "Rough-In", estimated: 120, actual: 132, variance: 10 },
    { category: "Wiring", estimated: 160, actual: 152, variance: -5 },
    { category: "Fixture Install", estimated: 80, actual: 76, variance: -5 },
    { category: "Panel Work", estimated: 60, actual: 66, variance: 10 },
    { category: "Testing", estimated: 40, actual: 38, variance: -5 },
    { category: "Final Connections", estimated: 50, actual: 52.5, variance: 5 },
    { category: "Finishing", estimated: 30, actual: 33, variance: 10 },
  ];
  
  const projectAccuracyData = [
    { project: "Central Plaza Lighting", estimated: 110000, actual: 108900, accuracy: 99 },
    { project: "Westside Residential", estimated: 85000, actual: 93500, accuracy: 91 },
    { project: "Downtown Office Retrofit", estimated: 120000, actual: 127200, accuracy: 94 },
    { project: "Highland Shopping Center", estimated: 220000, actual: 198000, accuracy: 90 },
    { project: "County Court Renovation", estimated: 175000, actual: 166250, accuracy: 95 },
    { project: "Bayside Apartments", estimated: 68000, actual: 74800, accuracy: 90 },
    { project: "Northend Medical Office", estimated: 92000, actual: 95680, accuracy: 96 },
    { project: "Eastview School", estimated: 145000, actual: 159500, accuracy: 90 },
  ];
  
  // Calculate metrics
  const averageAccuracy = Math.round(overallAccuracyData.reduce((sum, item) => sum + item.accuracy, 0) / overallAccuracyData.length);
  const averageMaterialAccuracy = 1 - (materialEstimationData.reduce((sum, item) => sum + Math.abs(item.variance), 0) / materialEstimationData.length) / 100;
  const averageLaborAccuracy = 1 - (laborEstimationData.reduce((sum, item) => sum + Math.abs(item.variance), 0) / laborEstimationData.length) / 100;
  
  // Format functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };
  
  const formatPercentage = (value: number) => {
    return `${Math.round(value * 100)}%`;
  };
  
  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 95) return "text-green-600 dark:text-green-400";
    if (accuracy >= 90) return "text-blue-600 dark:text-blue-400";
    if (accuracy >= 85) return "text-amber-600 dark:text-amber-400";
    return "text-red-600 dark:text-red-400";
  };
  
  const getVarianceColor = (variance: number) => {
    if (Math.abs(variance) <= 5) return "text-green-600 dark:text-green-400";
    if (Math.abs(variance) <= 10) return "text-amber-600 dark:text-amber-400";
    return "text-red-600 dark:text-red-400";
  };
  
  const getVarianceBadge = (variance: number) => {
    const isOverestimated = variance < 0;
    
    if (Math.abs(variance) <= 5) {
      return (
        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
          {isOverestimated ? <TrendingDown className="h-3 w-3 mr-1" /> : <TrendingUp className="h-3 w-3 mr-1" />}
          {Math.abs(variance)}%
        </Badge>
      );
    }
    
    if (Math.abs(variance) <= 10) {
      return (
        <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
          {isOverestimated ? <TrendingDown className="h-3 w-3 mr-1" /> : <TrendingUp className="h-3 w-3 mr-1" />}
          {Math.abs(variance)}%
        </Badge>
      );
    }
    
    return (
      <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
        {isOverestimated ? <TrendingDown className="h-3 w-3 mr-1" /> : <TrendingUp className="h-3 w-3 mr-1" />}
        {Math.abs(variance)}%
      </Badge>
    );
  };
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Estimation Accuracy</h2>
          <p className="text-muted-foreground">
            Analysis of estimation accuracy {timeRange === "all" ? "all time" : `for the ${getTimeRangeText(timeRange)}`}
          </p>
        </div>
        
        <Tabs value={viewType} onValueChange={(v) => setViewType(v as any)} className="w-auto">
          <TabsList className="bg-muted/50 grid grid-cols-3 h-auto p-1">
            <TabsTrigger value="overview" className="py-1.5 px-3 text-xs">Overview</TabsTrigger>
            <TabsTrigger value="materials" className="py-1.5 px-3 text-xs">Materials</TabsTrigger>
            <TabsTrigger value="labor" className="py-1.5 px-3 text-xs">Labor</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Key metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Overall Accuracy</p>
                <div className="text-2xl font-bold">{averageAccuracy}%</div>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Target className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                4% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Material Accuracy</p>
                <div className="text-2xl font-bold">{formatPercentage(averageMaterialAccuracy)}</div>
              </div>
              <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                3% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Labor Accuracy</p>
                <div className="text-2xl font-bold">{formatPercentage(averageLaborAccuracy)}</div>
              </div>
              <div className="h-12 w-12 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                5% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Main content based on view type */}
      <TabsContent value="overview" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Overall Accuracy Chart */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Estimation Accuracy Trend</CardTitle>
              <CardDescription>
                Monthly estimation accuracy percentage
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <LineChart data={overallAccuracyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis domain={[80, 100]} tickFormatter={(value) => `${value}%`} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value}%`, 'Accuracy']}
                    />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="accuracy" 
                      stroke="#10b981" 
                      strokeWidth={3}
                      dot={{ r: 4 }}
                      activeDot={{ r: 8 }}
                      name="Accuracy Percentage"
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Accuracy Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Accuracy by Category</CardTitle>
              <CardDescription>
                Distribution of estimation accuracy
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: "Excellent (≥95%)", value: 35, color: "#10b981" },
                        { name: "Good (90-94%)", value: 42, color: "#3b82f6" },
                        { name: "Fair (85-89%)", value: 18, color: "#f59e0b" },
                        { name: "Poor (<85%)", value: 5, color: "#ef4444" }
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: "Excellent (≥95%)", value: 35, color: "#10b981" },
                        { name: "Good (90-94%)", value: 42, color: "#3b82f6" },
                        { name: "Fair (85-89%)", value: 18, color: "#f59e0b" },
                        { name: "Poor (<85%)", value: 5, color: "#ef4444" }
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value} Projects`, 'Count']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Project Accuracy Table */}
        <Card>
          <CardHeader>
            <CardTitle>Project Estimation Accuracy</CardTitle>
            <CardDescription>
              Comparison of estimated vs. actual costs by project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md">
              <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
                <div className="col-span-4">Project</div>
                <div className="col-span-3 text-right">Estimated</div>
                <div className="col-span-3 text-right">Actual</div>
                <div className="col-span-2 text-center">Accuracy</div>
              </div>
              <div className="divide-y">
                {isLoading ? (
                  <div className="h-48 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  projectAccuracyData.map((project, index) => (
                    <div key={index} className="grid grid-cols-12 gap-4 p-4 items-center">
                      <div className="col-span-4 font-medium">{project.project}</div>
                      <div className="col-span-3 text-right">{formatCurrency(project.estimated)}</div>
                      <div className="col-span-3 text-right">{formatCurrency(project.actual)}</div>
                      <div className={`col-span-2 text-center font-medium ${getAccuracyColor(project.accuracy)}`}>
                        {project.accuracy}%
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="materials" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Material Estimation Accuracy</CardTitle>
            <CardDescription>
              Comparison of estimated vs. actual material costs
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={materialEstimationData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="category" />
                  <YAxis tickFormatter={(value) => `$${(value / 1000)}k`} />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                    formatter={(value: any) => [formatCurrency(value), 'Amount']}
                  />
                  <Legend />
                  <Bar dataKey="estimated" name="Estimated Cost" fill="#3b82f6" />
                  <Bar dataKey="actual" name="Actual Cost" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
        
        {/* Material Variance Table */}
        <Card>
          <CardHeader>
            <CardTitle>Material Cost Variance</CardTitle>
            <CardDescription>
              Detailed breakdown of material estimation variance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md">
              <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
                <div className="col-span-4">Material Category</div>
                <div className="col-span-3 text-right">Estimated</div>
                <div className="col-span-3 text-right">Actual</div>
                <div className="col-span-2 text-center">Variance</div>
              </div>
              <div className="divide-y">
                {isLoading ? (
                  <div className="h-48 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  materialEstimationData.map((material, index) => (
                    <div key={index} className="grid grid-cols-12 gap-4 p-4 items-center">
                      <div className="col-span-4 font-medium">{material.category}</div>
                      <div className="col-span-3 text-right">{formatCurrency(material.estimated)}</div>
                      <div className="col-span-3 text-right">{formatCurrency(material.actual)}</div>
                      <div className="col-span-2 flex justify-center">
                        {getVarianceBadge(material.variance)}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Material Estimation Factors */}
        <Card>
          <CardHeader>
            <CardTitle>Material Estimation Factors</CardTitle>
            <CardDescription>
              Factors affecting material estimation accuracy
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                        <div>
                          <h3 className="font-medium">Price Volatility</h3>
                          <p className="text-sm text-muted-foreground">
                            Rapid changes in material pricing contributed to a 3.2% decline in estimation accuracy for lighting fixtures.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                        <div>
                          <h3 className="font-medium">Supply Chain Issues</h3>
                          <p className="text-sm text-muted-foreground">
                            Delays and substitutions caused a 4.8% variance in circuit breaker cost estimations.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                        <div>
                          <h3 className="font-medium">Quantity Miscalculations</h3>
                          <p className="text-sm text-muted-foreground">
                            Incorrect quantity estimates for wiring and cables resulted in a 2.5% discrepancy in overall material costs.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                        <div>
                          <h3 className="font-medium">Specification Changes</h3>
                          <p className="text-sm text-muted-foreground">
                            Mid-project specification changes contributed to a 5.1% increase in actual costs versus estimates.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="labor" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Labor Estimation Accuracy</CardTitle>
            <CardDescription>
              Comparison of estimated vs. actual labor hours
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={laborEstimationData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="category" />
                  <YAxis label={{ value: 'Hours', angle: -90, position: 'insideLeft' }} />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                    formatter={(value: any) => [`${value} Hours`, 'Amount']}
                  />
                  <Legend />
                  <Bar dataKey="estimated" name="Estimated Hours" fill="#6366f1" />
                  <Bar dataKey="actual" name="Actual Hours" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
        
        {/* Labor Variance Table */}
        <Card>
          <CardHeader>
            <CardTitle>Labor Hours Variance</CardTitle>
            <CardDescription>
              Detailed breakdown of labor estimation variance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md">
              <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
                <div className="col-span-4">Labor Category</div>
                <div className="col-span-3 text-right">Estimated (hrs)</div>
                <div className="col-span-3 text-right">Actual (hrs)</div>
                <div className="col-span-2 text-center">Variance</div>
              </div>
              <div className="divide-y">
                {isLoading ? (
                  <div className="h-48 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  laborEstimationData.map((labor, index) => (
                    <div key={index} className="grid grid-cols-12 gap-4 p-4 items-center">
                      <div className="col-span-4 font-medium">{labor.category}</div>
                      <div className="col-span-3 text-right">{labor.estimated}</div>
                      <div className="col-span-3 text-right">{labor.actual}</div>
                      <div className="col-span-2 flex justify-center">
                        {getVarianceBadge(labor.variance)}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Project Complexity vs Accuracy */}
        <Card>
          <CardHeader>
            <CardTitle>Project Complexity vs. Accuracy</CardTitle>
            <CardDescription>
              Correlation between project complexity and estimation accuracy
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={320}>
                <ScatterChart
                  margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" dataKey="complexity" name="Complexity Score" unit="" domain={[0, 10]} />
                  <YAxis type="number" dataKey="accuracy" name="Accuracy" unit="%" domain={[80, 100]} />
                  <ZAxis type="number" dataKey="size" range={[50, 200]} />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                    formatter={(value: any, name) => {
                      if (name === "Complexity Score") return [`${value}/10`, name];
                      if (name === "Accuracy") return [`${value}%`, name];
                      return [value, name];
                    }}
                    cursor={{ strokeDasharray: '3 3' }}
                  />
                  <Legend />
                  <Scatter 
                    name="Projects" 
                    data={[
                      { project: "Central Plaza Lighting", complexity: 5.2, accuracy: 99, size: 110 },
                      { project: "Westside Residential", complexity: 6.8, accuracy: 91, size: 85 },
                      { project: "Downtown Office Retrofit", complexity: 7.5, accuracy: 94, size: 120 },
                      { project: "Highland Shopping Center", complexity: 8.2, accuracy: 90, size: 220 },
                      { project: "County Court Renovation", complexity: 7.8, accuracy: 95, size: 175 },
                      { project: "Bayside Apartments", complexity: 4.5, accuracy: 90, size: 68 },
                      { project: "Northend Medical Office", complexity: 6.2, accuracy: 96, size: 92 },
                      { project: "Eastview School", complexity: 7.4, accuracy: 90, size: 145 },
                    ]} 
                    fill="#8884d8"
                  />
                </ScatterChart>
              </ResponsiveContainer>
            )}
            <div className="mt-4 text-xs text-muted-foreground text-center">
              Bubble size represents project budget. Horizontal axis is complexity score (1-10), vertical axis is accuracy percentage.
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  );
}

// Helper functions
function getTimeRangeText(timeRange: string) {
  switch (timeRange) {
    case "7days": return "last 7 days";
    case "30days": return "last 30 days";
    case "90days": return "last 90 days";
    case "year": return "last 12 months";
    case "ytd": return "year to date";
    default: return "selected time period";
  }
}