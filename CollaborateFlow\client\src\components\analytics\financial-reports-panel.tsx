import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  ArrowUpRight, 
  ArrowDownRight,
  Loader2,
  Receipt,
  Wallet,
  Calculator,
  CheckSquare2,
  Package2
} from "lucide-react";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell
} from "recharts";

interface FinancialReportsPanelProps {
  timeRange: string;
  isLoading: boolean;
}

export function FinancialReportsPanel({ timeRange, isLoading }: FinancialReportsPanelProps) {
  const [viewType, setViewType] = useState<"revenue" | "expenses" | "profit">("revenue");
  
  // Sample data for charts
  const monthlyRevenueData = [
    { month: "Jan", revenue: 42500, target: 40000 },
    { month: "Feb", revenue: 48200, target: 42000 },
    { month: "Mar", revenue: 46800, target: 45000 },
    { month: "Apr", revenue: 52000, target: 48000 },
    { month: "May", revenue: 58700, target: 50000 },
    { month: "Jun", revenue: 61200, target: 55000 },
    { month: "Jul", revenue: 59800, target: 57000 },
    { month: "Aug", revenue: 64300, target: 60000 },
    { month: "Sep", revenue: 68500, target: 63000 },
    { month: "Oct", revenue: 72100, target: 65000 },
    { month: "Nov", revenue: 74600, target: 68000 },
    { month: "Dec", revenue: 79200, target: 70000 },
  ];
  
  const expenseBreakdownData = [
    { name: "Materials", value: 328000, color: "#3b82f6" },
    { name: "Labor", value: 420000, color: "#10b981" },
    { name: "Overhead", value: 184000, color: "#f59e0b" },
    { name: "Equipment", value: 96000, color: "#6366f1" },
    { name: "Other", value: 72000, color: "#ef4444" },
  ];
  
  const revenueByProjectTypeData = [
    { name: "Residential", value: 350000, color: "#3b82f6" },
    { name: "Commercial", value: 480000, color: "#10b981" },
    { name: "Industrial", value: 210000, color: "#f59e0b" },
    { name: "Government", value: 160000, color: "#6366f1" },
  ];
  
  const profitMarginData = [
    { month: "Jan", margin: 18 },
    { month: "Feb", margin: 19 },
    { month: "Mar", margin: 17 },
    { month: "Apr", margin: 20 },
    { month: "May", margin: 22 },
    { month: "Jun", margin: 21 },
    { month: "Jul", margin: 19 },
    { month: "Aug", margin: 23 },
    { month: "Sep", margin: 24 },
    { month: "Oct", margin: 22 },
    { month: "Nov", margin: 25 },
    { month: "Dec", margin: 26 },
  ];
  
  // Calculate overall metrics
  const totalRevenue = monthlyRevenueData.reduce((sum, item) => sum + item.revenue, 0);
  const totalExpenses = expenseBreakdownData.reduce((sum, item) => sum + item.value, 0);
  const totalProfit = totalRevenue - totalExpenses;
  const averageProfitMargin = Math.round(profitMarginData.reduce((sum, item) => sum + item.margin, 0) / profitMarginData.length);
  
  // Recent invoices
  const recentInvoices = [
    { id: "INV-2025-042", client: "ABC Corporation", amount: 24500, status: "Paid", date: "2025-05-02" },
    { id: "INV-2025-041", client: "XYZ Developers", amount: 18750, status: "Pending", date: "2025-04-28" },
    { id: "INV-2025-040", client: "123 Properties", amount: 32000, status: "Overdue", date: "2025-04-15" },
    { id: "INV-2025-039", client: "Retail Ventures", amount: 12800, status: "Paid", date: "2025-04-10" },
    { id: "INV-2025-038", client: "Coastal Living", amount: 9600, status: "Paid", date: "2025-04-05" }
  ];
  
  // Formatter functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
  };
  
  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Paid": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "Pending": return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300";
      case "Overdue": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Financial Reports</h2>
          <p className="text-muted-foreground">
            Financial performance overview {timeRange === "all" ? "all time" : `for the ${getTimeRangeText(timeRange)}`}
          </p>
        </div>
        
        <Tabs value={viewType} onValueChange={(v) => setViewType(v as any)} className="w-auto">
          <TabsList className="bg-muted/50 grid grid-cols-3 h-auto p-1">
            <TabsTrigger value="revenue" className="py-1.5 px-3 text-xs">Revenue</TabsTrigger>
            <TabsTrigger value="expenses" className="py-1.5 px-3 text-xs">Expenses</TabsTrigger>
            <TabsTrigger value="profit" className="py-1.5 px-3 text-xs">Profit</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Key financial metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Total Revenue</p>
                <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
              </div>
              <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                15% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Total Expenses</p>
                <div className="text-2xl font-bold">{formatCurrency(totalExpenses)}</div>
              </div>
              <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                <Wallet className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                8% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Net Profit</p>
                <div className="text-2xl font-bold">{formatCurrency(totalProfit)}</div>
              </div>
              <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <Calculator className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                12% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Profit Margin</p>
                <div className="text-2xl font-bold">{averageProfitMargin}%</div>
              </div>
              <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                <Receipt className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                2% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Main content based on view type */}
      <TabsContent value="revenue" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Monthly Revenue Chart */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Monthly Revenue</CardTitle>
              <CardDescription>
                Revenue performance over time with targets
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <ComposedChart data={monthlyRevenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `$${(value / 1000)}k`} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [formatCurrency(value), 'Amount']}
                    />
                    <Legend />
                    <Bar dataKey="revenue" name="Actual Revenue" fill="#10b981" radius={[4, 4, 0, 0]} />
                    <Line type="monotone" dataKey="target" name="Target Revenue" stroke="#f59e0b" strokeWidth={2} dot={{ r: 4 }} />
                  </ComposedChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Revenue by Project Type */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue by Project Type</CardTitle>
              <CardDescription>
                Revenue distribution across project categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <PieChart>
                    <Pie
                      data={revenueByProjectTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value, percent }) => 
                        `${name}: ${formatCurrency(value).replace("$", "")}k (${(percent * 100).toFixed(0)}%)`
                      }
                    >
                      {revenueByProjectTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [formatCurrency(value), 'Revenue']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Recent Invoices */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div className="space-y-0.5">
              <CardTitle>Recent Invoices</CardTitle>
              <CardDescription>
                Most recent invoices and their payment status
              </CardDescription>
            </div>
            <Button size="sm" variant="outline">View All</Button>
          </CardHeader>
          <CardContent>
            <div className="rounded-md">
              <div className="grid grid-cols-12 bg-muted/50 p-4 text-sm font-medium">
                <div className="col-span-2">Invoice #</div>
                <div className="col-span-3">Client</div>
                <div className="col-span-2 text-right">Amount</div>
                <div className="col-span-3 text-center">Date</div>
                <div className="col-span-2 text-center">Status</div>
              </div>
              <div className="divide-y">
                {isLoading ? (
                  <div className="h-48 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  recentInvoices.map((invoice) => (
                    <div key={invoice.id} className="grid grid-cols-12 p-4 items-center">
                      <div className="col-span-2 font-medium">{invoice.id}</div>
                      <div className="col-span-3 text-muted-foreground">{invoice.client}</div>
                      <div className="col-span-2 text-right">{formatCurrency(invoice.amount)}</div>
                      <div className="col-span-3 text-center">{formatDate(invoice.date)}</div>
                      <div className="col-span-2 flex justify-center">
                        <Badge className={getStatusColor(invoice.status)}>{invoice.status}</Badge>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="expenses" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Expense Breakdown */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Expense Breakdown</CardTitle>
              <CardDescription>
                Distribution of expenses by category
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <BarChart 
                    data={expenseBreakdownData}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" tickFormatter={(value) => `$${(value / 1000)}k`} />
                    <YAxis dataKey="name" type="category" width={80} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [formatCurrency(value), 'Amount']}
                    />
                    <Legend />
                    <Bar dataKey="value" name="Expenses" radius={[0, 4, 4, 0]}>
                      {expenseBreakdownData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Expense Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Expense Metrics</CardTitle>
              <CardDescription>
                Key expense indicators and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {isLoading ? (
                  <div className="h-80 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="h-4 w-4 rounded-full bg-[#3b82f6] mr-2"></div>
                          <span className="text-sm">Materials</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{formatCurrency(328000)}</span>
                          <Badge className="ml-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            12%
                          </Badge>
                        </div>
                      </div>
                      <Separator />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="h-4 w-4 rounded-full bg-[#10b981] mr-2"></div>
                          <span className="text-sm">Labor</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{formatCurrency(420000)}</span>
                          <Badge className="ml-1 bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            6%
                          </Badge>
                        </div>
                      </div>
                      <Separator />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="h-4 w-4 rounded-full bg-[#f59e0b] mr-2"></div>
                          <span className="text-sm">Overhead</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{formatCurrency(184000)}</span>
                          <Badge className="ml-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <TrendingDown className="h-3 w-3 mr-1" />
                            3%
                          </Badge>
                        </div>
                      </div>
                      <Separator />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="h-4 w-4 rounded-full bg-[#6366f1] mr-2"></div>
                          <span className="text-sm">Equipment</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{formatCurrency(96000)}</span>
                          <Badge className="ml-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            15%
                          </Badge>
                        </div>
                      </div>
                      <Separator />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="h-4 w-4 rounded-full bg-[#ef4444] mr-2"></div>
                          <span className="text-sm">Other</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{formatCurrency(72000)}</span>
                          <Badge className="ml-1 bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            8%
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Expense Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Expense Trends</CardTitle>
            <CardDescription>
              Monthly expense trends over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={320}>
                <AreaChart
                  data={[
                    { month: "Jan", materials: 24000, labor: 32000, overhead: 14000, equipment: 7000, other: 5000 },
                    { month: "Feb", materials: 26000, labor: 33000, overhead: 15000, equipment: 8000, other: 6000 },
                    { month: "Mar", materials: 25000, labor: 34000, overhead: 16000, equipment: 7500, other: 5500 },
                    { month: "Apr", materials: 27000, labor: 35000, overhead: 15500, equipment: 8000, other: 6000 },
                    { month: "May", materials: 28000, labor: 36000, overhead: 16000, equipment: 8200, other: 6300 },
                    { month: "Jun", materials: 29000, labor: 37000, overhead: 15800, equipment: 8500, other: 6200 },
                    { month: "Jul", materials: 28500, labor: 36000, overhead: 16200, equipment: 8300, other: 6100 },
                    { month: "Aug", materials: 30000, labor: 38000, overhead: 16500, equipment: 8800, other: 6500 },
                    { month: "Sep", materials: 31000, labor: 39000, overhead: 16800, equipment: 9000, other: 6800 },
                    { month: "Oct", materials: 32000, labor: 40000, overhead: 17000, equipment: 9200, other: 7000 },
                    { month: "Nov", materials: 33000, labor: 41000, overhead: 17200, equipment: 9500, other: 7200 },
                    { month: "Dec", materials: 34500, labor: 42000, overhead: 17500, equipment: 9800, other: 7400 },
                  ]}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${(value / 1000)}k`} />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                    formatter={(value: any) => [formatCurrency(value), 'Amount']}
                  />
                  <Legend />
                  <Area type="monotone" dataKey="materials" stackId="1" stroke="#3b82f6" fill="#3b82f680" name="Materials" />
                  <Area type="monotone" dataKey="labor" stackId="1" stroke="#10b981" fill="#10b98180" name="Labor" />
                  <Area type="monotone" dataKey="overhead" stackId="1" stroke="#f59e0b" fill="#f59e0b80" name="Overhead" />
                  <Area type="monotone" dataKey="equipment" stackId="1" stroke="#6366f1" fill="#6366f180" name="Equipment" />
                  <Area type="monotone" dataKey="other" stackId="1" stroke="#ef4444" fill="#ef444480" name="Other" />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="profit" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profit Margin Chart */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Profit Margin Trend</CardTitle>
              <CardDescription>
                Monthly profit margin percentage
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <LineChart data={profitMarginData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `${value}%`} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value}%`, 'Profit Margin']}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="margin" 
                      stroke="#10b981" 
                      strokeWidth={3}
                      dot={{ r: 4 }}
                      activeDot={{ r: 8 }}
                      name="Profit Margin"
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Top Profitable Projects */}
          <Card>
            <CardHeader>
              <CardTitle>Most Profitable Projects</CardTitle>
              <CardDescription>
                Projects with highest profit margins
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoading ? (
                  <div className="h-80 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="text-sm font-medium">Central Plaza Lighting</div>
                        <div className="font-medium text-green-600 dark:text-green-400">32%</div>
                      </div>
                      <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-500" style={{ width: "32%" }}></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Revenue: {formatCurrency(128000)}</span>
                        <span>Profit: {formatCurrency(41000)}</span>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="text-sm font-medium">Highland Shopping Center</div>
                        <div className="font-medium text-green-600 dark:text-green-400">28%</div>
                      </div>
                      <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-500" style={{ width: "28%" }}></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Revenue: {formatCurrency(210000)}</span>
                        <span>Profit: {formatCurrency(58800)}</span>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="text-sm font-medium">Downtown Office Retrofit</div>
                        <div className="font-medium text-green-600 dark:text-green-400">25%</div>
                      </div>
                      <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-500" style={{ width: "25%" }}></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Revenue: {formatCurrency(180000)}</span>
                        <span>Profit: {formatCurrency(45000)}</span>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="text-sm font-medium">County Court Renovation</div>
                        <div className="font-medium text-green-600 dark:text-green-400">24%</div>
                      </div>
                      <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-500" style={{ width: "24%" }}></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Revenue: {formatCurrency(240000)}</span>
                        <span>Profit: {formatCurrency(57600)}</span>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="text-sm font-medium">Westview Hotel</div>
                        <div className="font-medium text-green-600 dark:text-green-400">22%</div>
                      </div>
                      <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                        <div className="h-full bg-green-500" style={{ width: "22%" }}></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Revenue: {formatCurrency(320000)}</span>
                        <span>Profit: {formatCurrency(70400)}</span>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Revenue vs Expenses */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue vs Expenses</CardTitle>
            <CardDescription>
              Comparative analysis of revenue and expenses
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={320}>
                <BarChart
                  data={[
                    { month: "Jan", revenue: 42500, expenses: 35000, profit: 7500 },
                    { month: "Feb", revenue: 48200, expenses: 38000, profit: 10200 },
                    { month: "Mar", revenue: 46800, expenses: 39000, profit: 7800 },
                    { month: "Apr", revenue: 52000, expenses: 41000, profit: 11000 },
                    { month: "May", revenue: 58700, expenses: 45000, profit: 13700 },
                    { month: "Jun", revenue: 61200, expenses: 47000, profit: 14200 },
                    { month: "Jul", revenue: 59800, expenses: 48000, profit: 11800 },
                    { month: "Aug", revenue: 64300, expenses: 50000, profit: 14300 },
                    { month: "Sep", revenue: 68500, expenses: 52000, profit: 16500 },
                    { month: "Oct", revenue: 72100, expenses: 56000, profit: 16100 },
                    { month: "Nov", revenue: 74600, expenses: 55000, profit: 19600 },
                    { month: "Dec", revenue: 79200, expenses: 58000, profit: 21200 },
                  ]}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${(value / 1000)}k`} />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                    formatter={(value: any) => [formatCurrency(value), 'Amount']}
                  />
                  <Legend />
                  <Bar dataKey="revenue" name="Revenue" fill="#10b981" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="expenses" name="Expenses" fill="#ef4444" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="profit" name="Profit" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  );
}

// Helper functions
function getTimeRangeText(timeRange: string) {
  switch (timeRange) {
    case "7days": return "last 7 days";
    case "30days": return "last 30 days";
    case "90days": return "last 90 days";
    case "year": return "last 12 months";
    case "ytd": return "year to date";
    default: return "selected time period";
  }
}

// Workaround for ComposedChart that isn't exported by recharts
function ComposedChart({ children, data, ...props }: any) {
  return (
    <BarChart data={data} {...props}>
      {children}
    </BarChart>
  );
}