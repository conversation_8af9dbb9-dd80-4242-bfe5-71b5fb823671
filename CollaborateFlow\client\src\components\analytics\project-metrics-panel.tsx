import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Loader2, TrendingUp, TrendingDown, ClipboardList, Calendar, Clock, CheckCircle2, AlertTriangle } from "lucide-react";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";

interface ProjectMetricsPanelProps {
  timeRange: string;
  isLoading: boolean;
}

export function ProjectMetricsPanel({ timeRange, isLoading }: ProjectMetricsPanelProps) {
  const [viewType, setViewType] = useState<"overview" | "timeline" | "status">("overview");
  
  // Sample data for charts
  const projectStatusData = [
    { name: "Completed", value: 18, color: "#10b981" },
    { name: "In Progress", value: 24, color: "#3b82f6" },
    { name: "Planning", value: 12, color: "#6366f1" },
    { name: "On Hold", value: 4, color: "#f59e0b" },
    { name: "Cancelled", value: 2, color: "#ef4444" }
  ];

  const projectTimelineData = [
    { month: 'Jan', planned: 4, actual: 3 },
    { month: 'Feb', planned: 5, actual: 6 },
    { month: 'Mar', planned: 7, actual: 5 },
    { month: 'Apr', planned: 6, actual: 8 },
    { month: 'May', planned: 9, actual: 10 },
    { month: 'Jun', planned: 8, actual: 7 },
    { month: 'Jul', planned: 10, actual: 9 },
    { month: 'Aug', planned: 12, actual: 11 },
    { month: 'Sep', planned: 11, actual: 12 },
    { month: 'Oct', planned: 14, actual: 13 },
    { month: 'Nov', planned: 13, actual: 15 },
    { month: 'Dec', planned: 15, actual: 14 }
  ];

  const projectTypeData = [
    { type: 'Residential', count: 28 },
    { type: 'Commercial', count: 22 },
    { type: 'Industrial', count: 16 },
    { type: 'Government', count: 8 },
    { type: 'Other', count: 6 }
  ];

  // Calculate metrics
  const totalProjects = projectStatusData.reduce((sum, item) => sum + item.value, 0);
  const completedProjects = projectStatusData.find(item => item.name === "Completed")?.value || 0;
  const inProgressProjects = projectStatusData.find(item => item.name === "In Progress")?.value || 0;
  const completionRate = Math.round((completedProjects / totalProjects) * 100);
  
  // Recent projects data
  const recentProjects = [
    { id: 1, name: "Central Plaza Lighting", client: "ABC Corporation", status: "Completed", dueDate: "2025-05-10", progress: 100 },
    { id: 2, name: "Westside Residential Complex", client: "XYZ Developers", status: "In Progress", dueDate: "2025-06-15", progress: 68 },
    { id: 3, name: "Downtown Office Retrofit", client: "123 Properties", status: "In Progress", dueDate: "2025-07-12", progress: 42 },
    { id: 4, name: "Highland Shopping Center", client: "Retail Ventures", status: "Planning", dueDate: "2025-08-20", progress: 10 },
    { id: 5, name: "Bayside Apartments", client: "Coastal Living", status: "On Hold", dueDate: "2025-09-05", progress: 25 }
  ];
  
  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "In Progress": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "Planning": return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300";
      case "On Hold": return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300";
      case "Cancelled": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };
  
  // Get progress color
  const getProgressColor = (progress: number) => {
    if (progress < 25) return "bg-red-500";
    if (progress < 50) return "bg-amber-500";
    if (progress < 75) return "bg-blue-500";
    return "bg-green-500";
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
  };
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Project Metrics</h2>
          <p className="text-muted-foreground">
            Performance overview for all projects {timeRange === "all" ? "all time" : `in the ${getTimeRangeText(timeRange)}`}
          </p>
        </div>
        
        <Tabs value={viewType} onValueChange={(v) => setViewType(v as any)} className="w-auto">
          <TabsList className="bg-muted/50 grid grid-cols-3 h-auto p-1">
            <TabsTrigger value="overview" className="py-1.5 px-3 text-xs">Overview</TabsTrigger>
            <TabsTrigger value="timeline" className="py-1.5 px-3 text-xs">Timeline</TabsTrigger>
            <TabsTrigger value="status" className="py-1.5 px-3 text-xs">Status</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Key metrics cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Total Projects</p>
                <div className="text-2xl font-bold">{totalProjects}</div>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                <ClipboardList className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                12% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Completion Rate</p>
                <div className="text-2xl font-bold">{completionRate}%</div>
              </div>
              <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                <CheckCircle2 className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                <TrendingDown className="h-3 w-3 mr-1" />
                3% Decrease
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Avg. Project Duration</p>
                <div className="text-2xl font-bold">48 days</div>
              </div>
              <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                5% Improvement
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">On-time Delivery</p>
                <div className="text-2xl font-bold">82%</div>
              </div>
              <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <TrendingUp className="h-3 w-3 mr-1" />
                8% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last {timeRange === "all" ? "year" : timeRange}</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Main content based on view type */}
      <TabsContent value="overview" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Project Status Distribution */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Project Status Distribution</CardTitle>
              <CardDescription>
                Breakdown of all projects by their current status
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <BarChart data={projectStatusData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                    />
                    <Legend />
                    <Bar dataKey="value" name="Projects" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]}>
                      {projectStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Project Type Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Project Type Distribution</CardTitle>
              <CardDescription>
                Breakdown of projects by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <PieChart>
                    <Pie
                      data={projectTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {projectTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getColorForIndex(index)} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value} Projects`, 'Count']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Recent Projects */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Projects</CardTitle>
            <CardDescription>
              Overview of the most recently updated projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md">
              <div className="grid grid-cols-12 bg-muted/50 p-4 text-sm font-medium">
                <div className="col-span-4">Project Name</div>
                <div className="col-span-3">Client</div>
                <div className="col-span-2 text-center">Status</div>
                <div className="col-span-2 text-center">Due Date</div>
                <div className="col-span-1 text-center">Progress</div>
              </div>
              <div className="divide-y">
                {recentProjects.map((project) => (
                  <div key={project.id} className="grid grid-cols-12 p-4 items-center">
                    <div className="col-span-4 font-medium">{project.name}</div>
                    <div className="col-span-3 text-muted-foreground">{project.client}</div>
                    <div className="col-span-2 flex justify-center">
                      <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                    </div>
                    <div className="col-span-2 text-center">{formatDate(project.dueDate)}</div>
                    <div className="col-span-1">
                      <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                        <div 
                          className={`h-full ${getProgressColor(project.progress)}`}
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="timeline" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Project Timeline Comparison</CardTitle>
            <CardDescription>
              Comparison of planned vs. actual project timelines
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={projectTimelineData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="planned" 
                    stroke="#3b82f6" 
                    activeDot={{ r: 8 }} 
                    name="Planned Timeline"
                    strokeWidth={2}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="actual" 
                    stroke="#10b981" 
                    name="Actual Timeline"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Projects by Phase</CardTitle>
              <CardDescription>
                Current distribution of projects across phases
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Planning (15%)</span>
                      <span>9 Projects</span>
                    </div>
                    <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-indigo-500" style={{ width: "15%" }}></div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Design (28%)</span>
                      <span>17 Projects</span>
                    </div>
                    <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-blue-500" style={{ width: "28%" }}></div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Implementation (40%)</span>
                      <span>24 Projects</span>
                    </div>
                    <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-purple-500" style={{ width: "40%" }}></div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Testing (10%)</span>
                      <span>6 Projects</span>
                    </div>
                    <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-amber-500" style={{ width: "10%" }}></div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Completed (7%)</span>
                      <span>4 Projects</span>
                    </div>
                    <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-green-500" style={{ width: "7%" }}></div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Project Delays</CardTitle>
              <CardDescription>
                Analysis of delayed projects by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={240}>
                  <BarChart
                    data={[
                      { category: "Supply Chain", delays: 8 },
                      { category: "Client Changes", delays: 12 },
                      { category: "Technical Issues", delays: 7 },
                      { category: "Resource Issues", delays: 5 },
                      { category: "Permitting", delays: 9 }
                    ]}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="category" type="category" width={120} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value} Projects`, 'Delayed']}
                    />
                    <Bar dataKey="delays" fill="#ef4444" radius={[0, 4, 4, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
      </TabsContent>
      
      <TabsContent value="status" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Projects Status Breakdown</CardTitle>
              <CardDescription>
                Current status distribution of all projects
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={240}>
                  <PieChart>
                    <Pie
                      data={projectStatusData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      fill="#8884d8"
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {projectStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value} Projects`, 'Count']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
              
              <div className="grid grid-cols-2 gap-4 mt-4">
                {projectStatusData.map((status) => (
                  <div key={status.name} className="flex items-center">
                    <div className="h-3 w-3 rounded-full mr-2" style={{ backgroundColor: status.color }}></div>
                    <div className="flex justify-between w-full text-sm">
                      <span>{status.name}:</span>
                      <span className="font-medium">{status.value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Status Changes</CardTitle>
              <CardDescription>
                Recent project status transitions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {isLoading ? (
                  <div className="h-60 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <>
                    <div className="relative pl-6 border-l border-muted-foreground/20">
                      <div className="absolute top-0 left-0 transform -translate-x-1/2 w-3 h-3 rounded-full bg-green-500"></div>
                      <div className="mb-1">
                        <span className="text-sm font-medium">Central Plaza Lighting</span>
                        <Badge className="ml-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Completed</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Changed from <span className="font-medium">In Progress</span> to <span className="font-medium">Completed</span> • 2 days ago
                      </p>
                    </div>
                    
                    <div className="relative pl-6 border-l border-muted-foreground/20">
                      <div className="absolute top-0 left-0 transform -translate-x-1/2 w-3 h-3 rounded-full bg-blue-500"></div>
                      <div className="mb-1">
                        <span className="text-sm font-medium">Westside Residential Complex</span>
                        <Badge className="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">In Progress</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Changed from <span className="font-medium">Planning</span> to <span className="font-medium">In Progress</span> • 5 days ago
                      </p>
                    </div>
                    
                    <div className="relative pl-6 border-l border-muted-foreground/20">
                      <div className="absolute top-0 left-0 transform -translate-x-1/2 w-3 h-3 rounded-full bg-amber-500"></div>
                      <div className="mb-1">
                        <span className="text-sm font-medium">Bayside Apartments</span>
                        <Badge className="ml-2 bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">On Hold</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Changed from <span className="font-medium">In Progress</span> to <span className="font-medium">On Hold</span> • 1 week ago
                      </p>
                    </div>
                    
                    <div className="relative pl-6 border-l border-muted-foreground/20">
                      <div className="absolute top-0 left-0 transform -translate-x-1/2 w-3 h-3 rounded-full bg-indigo-500"></div>
                      <div className="mb-1">
                        <span className="text-sm font-medium">Highland Shopping Center</span>
                        <Badge className="ml-2 bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300">Planning</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Changed from <span className="font-medium">New</span> to <span className="font-medium">Planning</span> • 2 weeks ago
                      </p>
                    </div>
                    
                    <div className="relative pl-6">
                      <div className="absolute top-0 left-0 transform -translate-x-1/2 w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="mb-1">
                        <span className="text-sm font-medium">Northend Medical Office</span>
                        <Badge className="ml-2 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">Cancelled</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Changed from <span className="font-medium">On Hold</span> to <span className="font-medium">Cancelled</span> • 3 weeks ago
                      </p>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div className="space-y-0.5">
              <CardTitle>At-Risk Projects</CardTitle>
              <CardDescription>
                Projects with schedule or budget issues requiring attention
              </CardDescription>
            </div>
            <Button size="sm" variant="outline">View All</Button>
          </CardHeader>
          <CardContent>
            <div className="rounded-md">
              <div className="grid grid-cols-12 bg-muted/50 p-4 text-sm font-medium">
                <div className="col-span-5 md:col-span-4">Project Name</div>
                <div className="col-span-3 md:col-span-2 text-center">Risk Level</div>
                <div className="hidden md:block md:col-span-2 text-center">Due Date</div>
                <div className="col-span-4 text-right">Issues</div>
              </div>
              <div className="divide-y">
                {isLoading ? (
                  <div className="h-48 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-12 p-4 items-center">
                      <div className="col-span-5 md:col-span-4 font-medium">Westside Residential Complex</div>
                      <div className="col-span-3 md:col-span-2 text-center">
                        <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">High</Badge>
                      </div>
                      <div className="hidden md:block md:col-span-2 text-center">June 15, 2025</div>
                      <div className="col-span-4 text-right space-y-1">
                        <div className="inline-flex items-center text-xs px-2 py-1 rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Budget overrun
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-12 p-4 items-center">
                      <div className="col-span-5 md:col-span-4 font-medium">Downtown Office Retrofit</div>
                      <div className="col-span-3 md:col-span-2 text-center">
                        <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">Medium</Badge>
                      </div>
                      <div className="hidden md:block md:col-span-2 text-center">July 12, 2025</div>
                      <div className="col-span-4 text-right space-y-1">
                        <div className="inline-flex items-center text-xs px-2 py-1 rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                          <Clock className="h-3 w-3 mr-1" />
                          Schedule delay
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-12 p-4 items-center">
                      <div className="col-span-5 md:col-span-4 font-medium">Highland Shopping Center</div>
                      <div className="col-span-3 md:col-span-2 text-center">
                        <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">Medium</Badge>
                      </div>
                      <div className="hidden md:block md:col-span-2 text-center">Aug 20, 2025</div>
                      <div className="col-span-4 text-right space-y-1">
                        <div className="inline-flex items-center text-xs px-2 py-1 rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Resource shortage
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  );
}

// Helper functions
function getTimeRangeText(timeRange: string) {
  switch (timeRange) {
    case "7days": return "last 7 days";
    case "30days": return "last 30 days";
    case "90days": return "last 90 days";
    case "year": return "last 12 months";
    case "ytd": return "year to date";
    default: return "selected time period";
  }
}

function getColorForIndex(index: number) {
  const colors = [
    "#3b82f6", // blue
    "#10b981", // green
    "#6366f1", // indigo
    "#f59e0b", // amber
    "#ef4444", // red
    "#8b5cf6", // purple
    "#ec4899", // pink
    "#14b8a6", // teal
    "#f97316", // orange
    "#84cc16"  // lime
  ];
  
  return colors[index % colors.length];
}