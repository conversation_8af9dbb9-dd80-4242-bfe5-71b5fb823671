import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Loader2, 
  Users, 
  Clock, 
  CheckCircle, 
  Search, 
  Calendar,
  BarChart4,
  Activity,
  User,
  ArrowUpRight,
  Filter,
  FileInput,
  ClipboardList
} from "lucide-react";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell
} from "recharts";

interface UserActivityPanelProps {
  timeRange: string;
  isLoading: boolean;
}

export function UserActivityPanel({ timeRange, isLoading }: UserActivityPanelProps) {
  const [viewType, setViewType] = useState<"summary" | "team" | "individual">("summary");
  const [searchQuery, setSearchQuery] = useState("");
  
  // Sample data
  const activityByUserData = [
    { name: "John Smith", tasks: 42, projects: 8, hours: 165, completion: 92 },
    { name: "Jane Doe", tasks: 38, projects: 6, hours: 152, completion: 88 },
    { name: "Michael Brown", tasks: 34, projects: 5, hours: 128, completion: 94 },
    { name: "Emily Wilson", tasks: 28, projects: 4, hours: 116, completion: 86 },
    { name: "David Lee", tasks: 22, projects: 3, hours: 92, completion: 90 },
    { name: "Susan Miller", tasks: 18, projects: 4, hours: 78, completion: 82 },
    { name: "Robert Johnson", tasks: 16, projects: 2, hours: 64, completion: 96 },
    { name: "Amanda Taylor", tasks: 12, projects: 2, hours: 48, completion: 92 },
  ];
  
  const activityByTypeData = [
    { type: "Project Creation", count: 18 },
    { type: "Task Updates", count: 124 },
    { type: "Document Uploads", count: 56 },
    { type: "Quote Generation", count: 32 },
    { type: "Client Communication", count: 42 },
    { type: "Team Collaboration", count: 78 },
  ];
  
  const activityTimeData = [
    { hour: "9 AM", activities: 28 },
    { hour: "10 AM", activities: 42 },
    { hour: "11 AM", activities: 56 },
    { hour: "12 PM", activities: 38 },
    { hour: "1 PM", activities: 32 },
    { hour: "2 PM", activities: 48 },
    { hour: "3 PM", activities: 62 },
    { hour: "4 PM", activities: 58 },
    { hour: "5 PM", activities: 36 },
  ];
  
  const dailyActiveUsersData = [
    { date: "05/01", users: 12 },
    { date: "05/02", users: 14 },
    { date: "05/03", users: 15 },
    { date: "05/04", users: 12 },
    { date: "05/05", users: 13 },
    { date: "05/06", users: 11 },
    { date: "05/07", users: 10 },
    { date: "05/08", users: 15 },
    { date: "05/09", users: 16 },
    { date: "05/10", users: 14 },
    { date: "05/11", users: 12 },
    { date: "05/12", users: 13 },
    { date: "05/13", users: 15 },
    { date: "05/14", users: 16 },
  ];
  
  // Activity feed
  const activityFeed = [
    { id: 1, user: "John Smith", action: "Completed task", details: "Finalized electrical panel layout for Central Plaza", timestamp: "2025-05-13T13:45:00Z", type: "Task" },
    { id: 2, user: "Jane Doe", action: "Created quote", details: "Generated quote #QT-2025-042 for Highland Shopping Center", timestamp: "2025-05-13T11:20:00Z", type: "Quote" },
    { id: 3, user: "Michael Brown", action: "Uploaded document", details: "Added final inspection report for Westside Residential Complex", timestamp: "2025-05-13T10:05:00Z", type: "Document" },
    { id: 4, user: "Emily Wilson", action: "Updated project", details: "Changed timeline for Downtown Office Retrofit", timestamp: "2025-05-12T16:30:00Z", type: "Project" },
    { id: 5, user: "David Lee", action: "Sent client message", details: "Contacted ABC Corporation about quote approval", timestamp: "2025-05-12T14:15:00Z", type: "Communication" },
    { id: 6, user: "Susan Miller", action: "Created project", details: "Set up new project for Eastside Medical Center", timestamp: "2025-05-12T09:50:00Z", type: "Project" },
    { id: 7, user: "Robert Johnson", action: "Updated task", details: "Changed due date for wiring installation at Bayside Apartments", timestamp: "2025-05-11T15:40:00Z", type: "Task" },
    { id: 8, user: "Amanda Taylor", action: "Generated report", details: "Created monthly progress report for County Court Renovation", timestamp: "2025-05-11T11:25:00Z", type: "Report" },
  ];
  
  // Team performance data
  const teamPerformanceData = [
    { team: "Engineering", tasks: 86, completed: 78, members: 4, efficiency: 91 },
    { team: "Installation", tasks: 64, completed: 56, members: 5, efficiency: 88 },
    { team: "Estimation", tasks: 42, completed: 38, members: 3, efficiency: 90 },
    { team: "Project Management", tasks: 38, completed: 35, members: 2, efficiency: 92 },
    { team: "Client Relations", tasks: 28, completed: 24, members: 2, efficiency: 86 },
  ];
  
  // Format functions
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(date);
  };
  
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    }
    
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
    }
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
    }
    
    return formatTimestamp(timestamp);
  };
  
  // Get action icon
  const getActionIcon = (type: string) => {
    switch (type) {
      case "Task":
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case "Quote":
        return <FileInput className="h-4 w-4 text-green-600" />;
      case "Document":
        return <ClipboardList className="h-4 w-4 text-amber-600" />;
      case "Project":
        return <BarChart4 className="h-4 w-4 text-purple-600" />;
      case "Communication":
        return <Users className="h-4 w-4 text-red-600" />;
      case "Report":
        return <Activity className="h-4 w-4 text-indigo-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };
  
  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };
  
  // Filter activity feed based on search query
  const filteredActivity = searchQuery 
    ? activityFeed.filter(activity => 
        activity.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.details.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : activityFeed;
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">User Activity</h2>
          <p className="text-muted-foreground">
            Analysis of user activity and engagement {timeRange === "all" ? "all time" : `in the ${getTimeRangeText(timeRange)}`}
          </p>
        </div>
        
        <Tabs value={viewType} onValueChange={(v) => setViewType(v as any)} className="w-auto">
          <TabsList className="bg-muted/50 grid grid-cols-3 h-auto p-1">
            <TabsTrigger value="summary" className="py-1.5 px-3 text-xs">Summary</TabsTrigger>
            <TabsTrigger value="team" className="py-1.5 px-3 text-xs">Team</TabsTrigger>
            <TabsTrigger value="individual" className="py-1.5 px-3 text-xs">Individual</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Key metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Active Users</p>
                <div className="text-2xl font-bold">16</div>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                25% of team
              </Badge>
              <span className="ml-2 text-muted-foreground">today</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Total Activities</p>
                <div className="text-2xl font-bold">352</div>
              </div>
              <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <Activity className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                18% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last week</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Task Completion</p>
                <div className="text-2xl font-bold">90%</div>
              </div>
              <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                5% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last month</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">Avg. Time on Platform</p>
                <div className="text-2xl font-bold">5.2 hrs</div>
              </div>
              <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                2% Increase
              </Badge>
              <span className="ml-2 text-muted-foreground">from last week</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Main content based on view type */}
      <TabsContent value="summary" className="mt-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Daily Active Users */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Daily Active Users</CardTitle>
              <CardDescription>
                Number of active users per day
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <AreaChart data={dailyActiveUsersData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="users" 
                      stroke="#3b82f6" 
                      fill="#3b82f680" 
                      name="Active Users"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Activity by Type */}
          <Card>
            <CardHeader>
              <CardTitle>Activity by Type</CardTitle>
              <CardDescription>
                Distribution of activities by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <PieChart>
                    <Pie
                      data={activityByTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {activityByTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getColorForIndex(index)} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                      formatter={(value: any) => [`${value} Activities`, 'Count']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Activity by Time of Day */}
        <Card>
          <CardHeader>
            <CardTitle>Activity by Time of Day</CardTitle>
            <CardDescription>
              Distribution of user activities throughout the day
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={320}>
                <BarChart data={activityTimeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip 
                    contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                    itemStyle={{ color: "hsl(var(--foreground))" }}
                  />
                  <Legend />
                  <Bar dataKey="activities" name="Activities" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
        
        {/* Activity Feed */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest actions performed by users
            </CardDescription>
            <div className="mt-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search activities..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : filteredActivity.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 mx-auto text-muted-foreground opacity-50 mb-4" />
                  <h3 className="text-lg font-medium">No Activities Found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search query
                  </p>
                </div>
              ) : (
                filteredActivity.map((activity) => (
                  <div key={activity.id} className="flex gap-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={`https://avatar.vercel.sh/${activity.user}`} alt={activity.user} />
                      <AvatarFallback>{getInitials(activity.user)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{activity.user}</span>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">{formatTimeAgo(activity.timestamp)}</span>
                      </div>
                      <p>
                        <span className="inline-flex items-center gap-1 text-sm">
                          {getActionIcon(activity.type)}
                          <span className="font-medium">{activity.action}</span>
                        </span>
                        <span className="text-sm text-muted-foreground"> - {activity.details}</span>
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="team" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Team Performance</CardTitle>
            <CardDescription>
              Activity and efficiency metrics by team
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="rounded-md">
                <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
                  <div className="col-span-3">Team</div>
                  <div className="col-span-2 text-center">Members</div>
                  <div className="col-span-2 text-center">Tasks</div>
                  <div className="col-span-2 text-center">Completed</div>
                  <div className="col-span-3 text-center">Efficiency</div>
                </div>
                <div className="divide-y">
                  {teamPerformanceData.map((team, index) => (
                    <div key={index} className="grid grid-cols-12 gap-4 p-4 items-center">
                      <div className="col-span-3 font-medium">{team.team}</div>
                      <div className="col-span-2 text-center">{team.members}</div>
                      <div className="col-span-2 text-center">{team.tasks}</div>
                      <div className="col-span-2 text-center">{team.completed}</div>
                      <div className="col-span-3">
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-full max-w-24 bg-muted rounded-full h-2 overflow-hidden">
                            <div 
                              className={`h-full ${team.efficiency >= 90 ? 'bg-green-500' : team.efficiency >= 85 ? 'bg-blue-500' : 'bg-amber-500'}`}
                              style={{ width: `${team.efficiency}%` }}
                            ></div>
                          </div>
                          <span className={`text-sm font-medium ${
                            team.efficiency >= 90 ? 'text-green-600 dark:text-green-400' : 
                            team.efficiency >= 85 ? 'text-blue-600 dark:text-blue-400' : 
                            'text-amber-600 dark:text-amber-400'
                          }`}>
                            {team.efficiency}%
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Team Activity Breakdown */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Team Activity Breakdown</CardTitle>
              <CardDescription>
                Activity distribution by team
              </CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <BarChart
                    data={[
                      { team: "Engineering", project: 12, task: 38, document: 18, quote: 8, communication: 12 },
                      { team: "Installation", project: 8, task: 24, document: 14, quote: 4, communication: 8 },
                      { team: "Estimation", project: 4, task: 12, document: 8, quote: 16, communication: 6 },
                      { team: "Project Management", project: 10, task: 16, document: 10, quote: 6, communication: 14 },
                      { team: "Client Relations", project: 2, task: 6, document: 6, quote: 4, communication: 18 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="team" />
                    <YAxis />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                    />
                    <Legend />
                    <Bar dataKey="project" name="Projects" stackId="a" fill="#6366f1" />
                    <Bar dataKey="task" name="Tasks" stackId="a" fill="#3b82f6" />
                    <Bar dataKey="document" name="Documents" stackId="a" fill="#10b981" />
                    <Bar dataKey="quote" name="Quotes" stackId="a" fill="#f59e0b" />
                    <Bar dataKey="communication" name="Communication" stackId="a" fill="#ef4444" />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Team Activity Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Team Activity Trends</CardTitle>
              <CardDescription>
                Weekly activity trends by team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={320}>
                  <LineChart
                    data={[
                      { week: "Week 1", engineering: 68, installation: 42, estimation: 38, pmTeam: 56, clientTeam: 32 },
                      { week: "Week 2", engineering: 72, installation: 48, estimation: 35, pmTeam: 62, clientTeam: 36 },
                      { week: "Week 3", engineering: 76, installation: 52, estimation: 42, pmTeam: 58, clientTeam: 38 },
                      { week: "Week 4", engineering: 82, installation: 56, estimation: 46, pmTeam: 64, clientTeam: 42 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="week" />
                    <YAxis />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                    />
                    <Legend />
                    <Line type="monotone" dataKey="engineering" name="Engineering" stroke="#6366f1" />
                    <Line type="monotone" dataKey="installation" name="Installation" stroke="#3b82f6" />
                    <Line type="monotone" dataKey="estimation" name="Estimation" stroke="#10b981" />
                    <Line type="monotone" dataKey="pmTeam" name="Project Mgmt" stroke="#f59e0b" />
                    <Line type="monotone" dataKey="clientTeam" name="Client Relations" stroke="#ef4444" />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Team Collaboration Network */}
        <Card>
          <CardHeader>
            <CardTitle>Team Collaboration Stats</CardTitle>
            <CardDescription>
              Key collaboration metrics between teams
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {isLoading ? (
                <div className="md:col-span-3 h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <>
                  <div className="space-y-4">
                    <h3 className="font-medium text-center">Most Active Collaboration</h3>
                    <div className="bg-muted/10 rounded-lg p-4 text-center">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">Engineering</Badge>
                        <span>+</span>
                        <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">Project Management</Badge>
                      </div>
                      <div className="text-2xl font-bold">42</div>
                      <p className="text-sm text-muted-foreground">Shared activities</p>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-medium text-center">Average Cross-Team Activities</h3>
                    <div className="bg-muted/10 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold">24.5</div>
                      <p className="text-sm text-muted-foreground">Activities per team pair</p>
                      <div className="mt-2 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-full inline-flex items-center">
                        <ArrowUpRight className="h-3 w-3 mr-1" />
                        18% increase
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-medium text-center">Team Isolation Index</h3>
                    <div className="bg-muted/10 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold">12%</div>
                      <p className="text-sm text-muted-foreground">Activities without collaboration</p>
                      <div className="mt-2 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-full inline-flex items-center">
                        <ArrowUpRight className="h-3 w-3 mr-1" />
                        5% decrease
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="individual" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Individual Activity Metrics</CardTitle>
            <CardDescription>
              Activity and performance metrics by user
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="rounded-md">
                <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
                  <div className="col-span-3">Name</div>
                  <div className="col-span-2 text-center">Projects</div>
                  <div className="col-span-2 text-center">Tasks</div>
                  <div className="col-span-2 text-center">Hours</div>
                  <div className="col-span-3 text-center">Completion Rate</div>
                </div>
                <div className="divide-y">
                  {activityByUserData.map((user, index) => (
                    <div key={index} className="grid grid-cols-12 gap-4 p-4 items-center">
                      <div className="col-span-3 font-medium">{user.name}</div>
                      <div className="col-span-2 text-center">{user.projects}</div>
                      <div className="col-span-2 text-center">{user.tasks}</div>
                      <div className="col-span-2 text-center">{user.hours}</div>
                      <div className="col-span-3">
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-full max-w-24 bg-muted rounded-full h-2 overflow-hidden">
                            <div 
                              className={`h-full ${user.completion >= 90 ? 'bg-green-500' : user.completion >= 85 ? 'bg-blue-500' : 'bg-amber-500'}`}
                              style={{ width: `${user.completion}%` }}
                            ></div>
                          </div>
                          <span className={`text-sm font-medium ${
                            user.completion >= 90 ? 'text-green-600 dark:text-green-400' : 
                            user.completion >= 85 ? 'text-blue-600 dark:text-blue-400' : 
                            'text-amber-600 dark:text-amber-400'
                          }`}>
                            {user.completion}%
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Contributors */}
          <Card>
            <CardHeader>
              <CardTitle>Top Contributors</CardTitle>
              <CardDescription>
                Most active users based on total activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={activityByUserData.sort((a, b) => (b.tasks + b.projects * 5) - (a.tasks + a.projects * 5)).slice(0, 5)}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" width={100} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                    />
                    <Legend />
                    <Bar dataKey="tasks" name="Tasks" fill="#3b82f6" />
                    <Bar dataKey="projects" name="Projects" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          {/* Activity Spikes */}
          <Card>
            <CardHeader>
              <CardTitle>User Engagement Over Time</CardTitle>
              <CardDescription>
                Daily activity patterns by hour
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart
                    data={[
                      { time: "8 AM", john: 2, jane: 1, michael: 0, emily: 3, david: 1 },
                      { time: "9 AM", john: 4, jane: 3, michael: 2, emily: 5, david: 3 },
                      { time: "10 AM", john: 6, jane: 5, michael: 4, emily: 4, david: 2 },
                      { time: "11 AM", john: 5, jane: 6, michael: 5, emily: 3, david: 4 },
                      { time: "12 PM", john: 3, jane: 4, michael: 3, emily: 2, david: 3 },
                      { time: "1 PM", john: 2, jane: 2, michael: 1, emily: 1, david: 2 },
                      { time: "2 PM", john: 4, jane: 5, michael: 3, emily: 4, david: 4 },
                      { time: "3 PM", john: 7, jane: 6, michael: 5, emily: 5, david: 3 },
                      { time: "4 PM", john: 6, jane: 7, michael: 6, emily: 4, david: 4 },
                      { time: "5 PM", john: 4, jane: 5, michael: 4, emily: 3, david: 2 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis label={{ value: 'Activities', angle: -90, position: 'insideLeft' }} />
                    <Tooltip 
                      contentStyle={{ backgroundColor: "hsl(var(--background))", borderColor: "hsl(var(--border))" }}
                      itemStyle={{ color: "hsl(var(--foreground))" }}
                    />
                    <Legend />
                    <Line type="monotone" dataKey="john" name="John" stroke="#3b82f6" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="jane" name="Jane" stroke="#10b981" />
                    <Line type="monotone" dataKey="michael" name="Michael" stroke="#f59e0b" />
                    <Line type="monotone" dataKey="emily" name="Emily" stroke="#6366f1" />
                    <Line type="monotone" dataKey="david" name="David" stroke="#ef4444" />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Individual Performance Metrics */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div className="space-y-0.5">
              <CardTitle>Individual Efficiency Metrics</CardTitle>
              <CardDescription>
                Detailed performance metrics by user
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {isLoading ? (
                <div className="md:col-span-3 h-60 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <>
                  <div className="space-y-3">
                    <div className="border rounded-md p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="https://avatar.vercel.sh/John%20Smith" alt="John Smith" />
                          <AvatarFallback>JS</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">John Smith</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Task Completion:</span>
                          <span className="font-medium text-green-600 dark:text-green-400">92%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Time per Task:</span>
                          <span className="font-medium">3.9 hours</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span className="font-medium">42 minutes</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="border rounded-md p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="https://avatar.vercel.sh/Jane%20Doe" alt="Jane Doe" />
                          <AvatarFallback>JD</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">Jane Doe</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Task Completion:</span>
                          <span className="font-medium text-blue-600 dark:text-blue-400">88%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Time per Task:</span>
                          <span className="font-medium">4.0 hours</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span className="font-medium">37 minutes</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="border rounded-md p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="https://avatar.vercel.sh/Michael%20Brown" alt="Michael Brown" />
                          <AvatarFallback>MB</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">Michael Brown</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Task Completion:</span>
                          <span className="font-medium text-green-600 dark:text-green-400">94%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Time per Task:</span>
                          <span className="font-medium">3.8 hours</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span className="font-medium">48 minutes</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="border rounded-md p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="https://avatar.vercel.sh/Emily%20Wilson" alt="Emily Wilson" />
                          <AvatarFallback>EW</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">Emily Wilson</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Task Completion:</span>
                          <span className="font-medium text-blue-600 dark:text-blue-400">86%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Time per Task:</span>
                          <span className="font-medium">4.1 hours</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span className="font-medium">52 minutes</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="border rounded-md p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="https://avatar.vercel.sh/David%20Lee" alt="David Lee" />
                          <AvatarFallback>DL</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">David Lee</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Task Completion:</span>
                          <span className="font-medium text-blue-600 dark:text-blue-400">90%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Time per Task:</span>
                          <span className="font-medium">4.2 hours</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span className="font-medium">45 minutes</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="border rounded-md p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="https://avatar.vercel.sh/Susan%20Miller" alt="Susan Miller" />
                          <AvatarFallback>SM</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">Susan Miller</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Task Completion:</span>
                          <span className="font-medium text-amber-600 dark:text-amber-400">82%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Time per Task:</span>
                          <span className="font-medium">4.3 hours</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span className="font-medium">58 minutes</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  );
}

// Helper functions
function getTimeRangeText(timeRange: string) {
  switch (timeRange) {
    case "7days": return "last 7 days";
    case "30days": return "last 30 days";
    case "90days": return "last 90 days";
    case "year": return "last 12 months";
    case "ytd": return "year to date";
    default: return "selected time period";
  }
}

function getColorForIndex(index: number) {
  const colors = [
    "#3b82f6", // blue
    "#10b981", // green
    "#6366f1", // indigo
    "#f59e0b", // amber
    "#ef4444", // red
    "#8b5cf6", // purple
    "#ec4899", // pink
    "#14b8a6", // teal
    "#f97316", // orange
    "#84cc16"  // lime
  ];
  
  return colors[index % colors.length];
}