import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface Member {
  id: number;
  fullName?: string;
  username?: string;
  avatarUrl?: string;
}

interface AvatarGroupProps {
  members: Member[];
  limit?: number;
  size?: "sm" | "md" | "lg";
}

export function AvatarGroup({ members, limit = 3, size = "md" }: AvatarGroupProps) {
  const sizeClasses = {
    sm: "h-6 w-6 text-xs",
    md: "h-8 w-8 text-sm",
    lg: "h-10 w-10 text-base",
  };
  
  const visibleMembers = members.slice(0, limit);
  const overflow = members.length - limit;

  return (
    <div className="flex -space-x-2">
      {visibleMembers.map((member) => {
        const initials = member.fullName 
          ? member.fullName.split(" ").map(n => n[0]).join("").toUpperCase()
          : member.username
            ? member.username.substring(0, 2).toUpperCase()
            : "U";
        
        return (
          <Avatar key={member.id} className={`${sizeClasses[size]} border-2 border-background`}>
            {member.avatarUrl ? (
              <AvatarImage src={member.avatarUrl} alt={member.fullName || member.username || `User ${member.id}`} />
            ) : null}
            <AvatarFallback className="bg-primary text-primary-foreground text-xs">
              {initials}
            </AvatarFallback>
          </Avatar>
        );
      })}
      
      {overflow > 0 && (
        <Avatar className={`${sizeClasses[size]} border-2 border-background bg-muted`}>
          <AvatarFallback className="bg-muted text-muted-foreground">
            +{overflow}
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}
