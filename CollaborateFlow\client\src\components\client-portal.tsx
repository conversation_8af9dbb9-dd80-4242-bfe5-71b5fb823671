import { useState, useRef, useEffect } from "react";
import { 
  Check, 
  X, 
  MessageSquare, 
  Download, 
  ArrowLeft, 
  ArrowRight, 
  Edit, 
  Send, 
  CheckCircle, 
  XCircle,
  Pencil,
  Clock
} from "lucide-react";
import { format } from "date-fns";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { AICard } from "@/components/ai-card";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface ClientPortalProps {
  quoteId: string;
  quoteToken?: string;
  onApprove?: () => void;
  onReject?: () => void;
  onFeedback?: (feedback: FeedbackItem) => void;
  onClose?: () => void;
  readOnly?: boolean;
}

interface QuoteData {
  id: string;
  number: string;
  projectName: string;
  clientName: string;
  company: {
    name: string;
    logo: string;
    contact: string;
  };
  issueDate: string;
  expiryDate: string;
  totalAmount: number;
  items: {
    category: string;
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }[];
  paymentTerms: string;
  notes: string;
  status: 'draft' | 'sent' | 'viewed' | 'approved' | 'rejected' | 'expired';
}

interface FeedbackItem {
  id: string;
  message: string;
  timestamp: string;
  section?: string;
  itemIndex?: number;
  isClient: boolean;
}

export function ClientPortal({ 
  quoteId, 
  quoteToken,
  onApprove, 
  onReject, 
  onFeedback,
  onClose,
  readOnly = false
}: ClientPortalProps) {
  const { toast } = useToast();
  const signatureCanvasRef = useRef<HTMLCanvasElement>(null);
  const [activeTab, setActiveTab] = useState("details");
  const [loading, setLoading] = useState(true);
  const [signature, setSignature] = useState("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState("");
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [showSignatureForm, setShowSignatureForm] = useState(false);
  const [signatureAgreement, setSignatureAgreement] = useState(false);
  
  // State for quote data
  const [quote, setQuote] = useState<QuoteData | null>(null);
  const [feedbackHistory, setFeedbackHistory] = useState<FeedbackItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch quote data
  useEffect(() => {
    const fetchQuote = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/quotes/${quoteId}/view${quoteToken ? `/${quoteToken}` : ''}`);
        
        if (!response.ok) {
          throw new Error('Failed to load quote. Please check the URL and try again.');
        }
        
        const data = await response.json();
        setQuote(data);
        
        // Fetch feedback history if available
        try {
          const feedbackResponse = await fetch(`/api/quotes/${quoteId}/feedback${quoteToken ? `?token=${quoteToken}` : ''}`);
          if (feedbackResponse.ok) {
            const feedbackData = await feedbackResponse.json();
            setFeedbackHistory(feedbackData);
          }
        } catch (feedbackError) {
          console.error('Error fetching feedback:', feedbackError);
        }
        
        // Update the quote status to 'viewed' if it was previously 'sent'
        if (data.status === 'sent') {
          updateQuoteStatus('viewed');
        }
        
      } catch (error) {
        console.error('Error fetching quote:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    if (quoteId) {
      fetchQuote();
    }
  }, [quoteId, quoteToken]);
  
  // Update quote status
  const updateQuoteStatus = async (status: 'viewed' | 'approved' | 'rejected') => {
    try {
      const response = await fetch(`/api/quotes/${quoteId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          token: quoteToken
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update quote status');
      }
      
      // Update local state
      if (quote) {
        setQuote({
          ...quote,
          status
        });
      }
    } catch (error) {
      console.error('Error updating quote status:', error);
      toast({
        title: "Error",
        description: "Failed to update quote status. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Submit feedback
  const submitFeedback = async () => {
    if (!feedbackMessage.trim()) {
      toast({
        title: "Error",
        description: "Please enter feedback message",
        variant: "destructive"
      });
      return;
    }

    // Create feedback item
    const newFeedback: FeedbackItem = {
      id: `temp-${Date.now()}`, // This will be replaced by a server-generated ID
      message: feedbackMessage,
      timestamp: new Date().toISOString(),
      section: selectedSection || undefined,
      isClient: true
    };

    try {
      const response = await fetch(`/api/quotes/${quoteId}/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: feedbackMessage,
          section: selectedSection,
          token: quoteToken
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }

      // Add feedback to local state
      setFeedbackHistory([...feedbackHistory, newFeedback]);
      
      // Reset form
      setFeedbackMessage("");
      setSelectedSection(null);
      setShowFeedbackForm(false);

      toast({
        title: "Feedback Submitted",
        description: "Your feedback has been sent to the contractor",
      });

      if (onFeedback) {
        onFeedback(newFeedback);
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle approval
  const handleApproval = async () => {
    if (!signatureAgreement || !signature) {
      toast({
        title: "Error",
        description: "Please sign and agree to the terms before approving",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/quotes/${quoteId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: quoteToken,
          signature: signature
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve quote');
      }

      setLoading(false);
      setQuote(prev => prev ? { ...prev, status: 'approved' } : null);
      setShowSignatureForm(false);

      toast({
        title: "Quote Approved",
        description: "Thank you for approving this quote. We'll be in touch soon."
      });

      if (onApprove) {
        onApprove();
      }
    } catch (error) {
      console.error('Error approving quote:', error);
      toast({
        title: "Error",
        description: "Failed to approve quote. Please try again.",
        variant: "destructive"
      });
      setLoading(false);
    }
  };

  // Handle rejection
  const handleRejection = async () => {
    if (!rejectReason.trim()) {
      toast({
        title: "Error",
        description: "Please provide a reason for rejection",
        variant: "destructive"
      });
      return;
    }
    
    setLoading(true);
    try {
      const response = await fetch(`/api/quotes/${quoteId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: quoteToken,
          reason: rejectReason
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject quote');
      }

      setLoading(false);
      setQuote(prev => prev ? { ...prev, status: 'rejected' } : null);
      setShowRejectDialog(false);

      // Add rejection reason as feedback
      const rejectionFeedback: FeedbackItem = {
        id: `temp-${Date.now()}`,
        message: `Rejection reason: ${rejectReason}`,
        timestamp: new Date().toISOString(),
        isClient: true
      };
      
      setFeedbackHistory([...feedbackHistory, rejectionFeedback]);

      toast({
        title: "Quote Rejected",
        description: "The quote has been rejected. You can request revisions."
      });
      
      if (onReject) {
        onReject();
      }
    } catch (error) {
      console.error('Error rejecting quote:', error);
      setLoading(false);
    }
  };
  
  // Clear signature canvas
  const clearSignature = () => {
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
    setSignature("");
  };
  
  // Start drawing
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    setIsDrawing(true);
    
    const rect = canvas.getBoundingClientRect();
    const x = e instanceof MouseEvent 
      ? e.clientX - rect.left 
      : e.touches[0].clientX - rect.left;
    const y = e instanceof MouseEvent 
      ? e.clientY - rect.top 
      : e.touches[0].clientY - rect.top;
    
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.strokeStyle = '#000000';
  };
  
  // Draw signature
  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = e instanceof MouseEvent 
      ? e.clientX - rect.left 
      : e.touches[0].clientX - rect.left;
    const y = e instanceof MouseEvent 
      ? e.clientY - rect.top 
      : e.touches[0].clientY - rect.top;
    
    ctx.lineTo(x, y);
    ctx.stroke();
  };
  
  // End drawing
  const endDrawing = () => {
    setIsDrawing(false);
    
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      setSignature(canvas.toDataURL());
    }
  };
  
  // If loading, show spinner
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin h-8 w-8 border-2 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }
  
  // If error, show error message
  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
          <XCircle className="h-8 w-8 text-red-500" />
        </div>
        <h3 className="text-lg font-medium mb-2">Error Loading Quote</h3>
        <p className="text-muted-foreground mb-4">{error}</p>
        <Button onClick={onClose}>Close</Button>
      </div>
    );
  }
  
  // If no quote data is loaded yet, show placeholder
  if (!quote) {
    return (
      <div className="p-8 text-center">
        <p>No quote data available.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      {/* Quote Header */}
      <div className="p-6 border-b">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-bold">Quote #{quote.number}</h2>
            <p className="text-sm text-muted-foreground">
              Project: {quote.projectName}
            </p>
          </div>
          <Badge 
            className={cn(
              "text-sm",
              quote.status === 'approved' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
              quote.status === 'rejected' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
              quote.status === 'viewed' && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
              quote.status === 'sent' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
              quote.status === 'expired' && "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
            )}
          >
            {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div>
            <p className="text-sm text-muted-foreground">Issue Date</p>
            <p className="font-medium">{new Date(quote.issueDate).toLocaleDateString()}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Expiry Date</p>
            <p className="font-medium">{new Date(quote.expiryDate).toLocaleDateString()}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Client</p>
            <p className="font-medium">{quote.clientName}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Total Amount</p>
            <p className="font-medium">${quote.totalAmount.toFixed(2)}</p>
          </div>
        </div>
      </div>
      
      {/* Quote Tabs */}
      <Tabs defaultValue="details" className="flex-1" value={activeTab} onValueChange={setActiveTab}>
        <div className="border-b px-4">
          <TabsList className="py-2">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
          </TabsList>
        </div>
        
        {/* Details Tab */}
        <TabsContent value="details" className="p-6 flex-1">
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medium mb-4">Quote Items</h3>
              <div className="space-y-4">
                {/* Header */}
                <div className="grid grid-cols-12 gap-4 py-2 font-medium text-sm">
                  <div className="col-span-5">Description</div>
                  <div className="col-span-2">Category</div>
                  <div className="col-span-1 text-right">Qty</div>
                  <div className="col-span-2 text-right">Unit Price</div>
                  <div className="col-span-2 text-right">Total</div>
                </div>
                
                <Separator />
                
                {/* Items */}
                {quote.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-12 gap-4 py-3 text-sm">
                    <div className="col-span-5">
                      {item.description}
                    </div>
                    <div className="col-span-2">
                      <Badge variant="outline">{item.category}</Badge>
                    </div>
                    <div className="col-span-1 text-right">{item.quantity}</div>
                    <div className="col-span-2 text-right">${item.unitPrice.toFixed(2)}</div>
                    <div className="col-span-2 text-right font-medium">${item.total.toFixed(2)}</div>
                  </div>
                ))}
                
                <Separator />
                
                {/* Total */}
                <div className="grid grid-cols-12 gap-4 py-2 font-medium">
                  <div className="col-span-10 text-right">Total Amount</div>
                  <div className="col-span-2 text-right">${quote.totalAmount.toFixed(2)}</div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Payment Terms</h3>
              <p>{quote.paymentTerms}</p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Notes</h3>
              <p className="text-muted-foreground">{quote.notes}</p>
            </div>
            
            {/* Actions */}
            {!readOnly && quote.status !== 'approved' && quote.status !== 'rejected' && (
              <div className="flex flex-col sm:flex-row gap-4 mt-8">
                <Button 
                  className="flex-1" 
                  onClick={() => setShowSignatureForm(true)}
                  disabled={loading}
                >
                  <Check className="mr-2 h-4 w-4" />
                  Approve Quote
                </Button>
                
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setShowRejectDialog(true)}
                  disabled={loading}
                >
                  <X className="mr-2 h-4 w-4" />
                  Reject Quote
                </Button>
              </div>
            )}
            
            {/* Status message for approved/rejected quotes */}
            {(quote.status === 'approved' || quote.status === 'rejected') && (
              <div className={cn(
                "p-4 rounded-lg",
                quote.status === 'approved' ? "bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-200" : 
                "bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-200"
              )}>
                <div className="flex items-start">
                  {quote.status === 'approved' ? (
                    <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
                  ) : (
                    <XCircle className="h-5 w-5 mr-2 mt-0.5" />
                  )}
                  <div>
                    <p className="font-medium">
                      {quote.status === 'approved' ? 'Quote Approved' : 'Quote Rejected'}
                    </p>
                    <p className="text-sm mt-1">
                      {quote.status === 'approved' 
                        ? 'You have approved this quote. The contractor will be in touch soon to schedule work.'
                        : 'You have rejected this quote. Please provide feedback so the contractor can address your concerns.'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
        
        {/* Feedback Tab */}
        <TabsContent value="feedback" className="p-6">
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Feedback & Communication</h3>
              
              {!readOnly && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setShowFeedbackForm(true)}
                  disabled={loading}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Add Feedback
                </Button>
              )}
            </div>
            
            {/* Feedback Thread */}
            <div className="space-y-4">
              {feedbackHistory.length === 0 ? (
                <div className="text-center py-12">
                  <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground opacity-20 mb-4" />
                  <p className="text-muted-foreground">No feedback yet</p>
                  {!readOnly && (
                    <Button 
                      variant="link" 
                      onClick={() => setShowFeedbackForm(true)}
                      disabled={loading}
                    >
                      Add your first comment
                    </Button>
                  )}
                </div>
              ) : (
                feedbackHistory.map((item) => (
                  <div key={item.id} className={cn(
                    "p-4 rounded-lg",
                    item.isClient 
                      ? "bg-primary/10 ml-6" 
                      : "bg-muted mr-6"
                  )}>
                    <div className="flex justify-between mb-2">
                      <div className="font-medium text-sm">
                        {item.isClient ? 'You' : 'Contractor'}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(item.timestamp).toLocaleString()}
                      </div>
                    </div>
                    
                    {item.section && (
                      <div className="mb-2">
                        <Badge variant="outline" className="text-xs">
                          {item.section}
                          {item.itemIndex !== undefined && ` - Item ${item.itemIndex + 1}`}
                        </Badge>
                      </div>
                    )}
                    
                    <p className="text-sm">{item.message}</p>
                  </div>
                ))
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Feedback Dialog */}
      <Dialog open={showFeedbackForm} onOpenChange={setShowFeedbackForm}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Feedback</DialogTitle>
            <DialogDescription>
              Provide your comments or questions about the quote.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="section">Section (Optional)</Label>
              <select 
                id="section"
                className="w-full px-3 py-2 border rounded-md text-sm"
                value={selectedSection || ''}
                onChange={(e) => setSelectedSection(e.target.value || null)}
              >
                <option value="">General Feedback</option>
                <option value="items">Quote Items</option>
                <option value="payment">Payment Terms</option>
                <option value="notes">Notes</option>
              </select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="feedback">Your Feedback</Label>
              <Textarea 
                id="feedback"
                placeholder="Enter your question or comment here..."
                className="min-h-[100px]"
                value={feedbackMessage}
                onChange={(e) => setFeedbackMessage(e.target.value)}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowFeedbackForm(false)}>
              Cancel
            </Button>
            <Button onClick={submitFeedback} disabled={!feedbackMessage.trim()}>
              <Send className="mr-2 h-4 w-4" />
              Submit Feedback
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Approve Dialog with Signature */}
      <Dialog open={showSignatureForm} onOpenChange={setShowSignatureForm}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Approve Quote</DialogTitle>
            <DialogDescription>
              Please sign below to approve this quote and authorize the work.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="border rounded-md p-2 bg-gray-50 dark:bg-gray-900">
              <canvas
                ref={signatureCanvasRef}
                width={400}
                height={150}
                className="w-full bg-white dark:bg-black cursor-crosshair touch-none"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={endDrawing}
                onMouseLeave={endDrawing}
                onTouchStart={startDrawing}
                onTouchMove={draw}
                onTouchEnd={endDrawing}
              />
            </div>
            
            <Button variant="outline" size="sm" onClick={clearSignature}>
              Clear Signature
            </Button>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="agreement"
                  checked={signatureAgreement}
                  onChange={(e) => setSignatureAgreement(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="agreement" className="text-sm">
                  I agree to the terms and conditions of this quote and authorize the work to proceed.
                </Label>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSignatureForm(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleApproval} 
              disabled={!signatureAgreement || !signature || loading}
            >
              {loading ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Processing
                </>
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Approve Quote
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Reject Quote</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this quote. This will help the contractor understand your concerns.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="reason">Reason for Rejection</Label>
              <Textarea 
                id="reason"
                placeholder="Enter your reason here..."
                className="min-h-[100px]"
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleRejection} 
              disabled={!rejectReason.trim() || loading}
            >
              {loading ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Processing
                </>
              ) : (
                <>
                  <X className="mr-2 h-4 w-4" />
                  Reject Quote
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}