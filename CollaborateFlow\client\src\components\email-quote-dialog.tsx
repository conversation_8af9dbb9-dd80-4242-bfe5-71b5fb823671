import { useState } from "react";
import { <PERSON>, Co<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Link } from "lucide-react";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  <PERSON><PERSON>Title, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";

interface EmailQuoteDialogProps {
  quoteId: string;
  quoteNumber: string;
  clientName: string;
  clientEmail: string;
  projectName: string;
  amount: number;
  expiryDate: string;
  onSend?: () => void;
  trigger?: React.ReactNode;
}

export function EmailQuoteDialog({
  quoteId,
  quoteNumber,
  clientName,
  clientEmail,
  projectName,
  amount,
  expiryDate,
  onSend,
  trigger
}: EmailQuoteDialogProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<"email" | "link">("email");
  const [isSending, setIsSending] = useState(false);
  const [emailData, setEmailData] = useState({
    to: clientEmail,
    cc: "",
    bcc: "",
    subject: `Quote ${quoteNumber} for ${projectName}`,
    message: `Dear ${clientName},

I'm pleased to provide you with the attached quote for the ${projectName} project.

The total amount for this project is $${amount.toLocaleString()}.

This quote is valid until ${expiryDate}.

To review, provide feedback, or approve this quote, please click the secure client portal link below. No account creation is required.

[Quote Link]

If you have any questions or need clarification on any aspect of the quote, you can respond directly through the client portal or contact us.

Thank you for your business. We look forward to working with you!

Best regards,
[Your Name]
CoElec Electrical Services
(*************`
  });
  
  // Generate a quote link
  const quoteLink = `${window.location.origin}/quote/${quoteId}/view/${generateQuoteToken()}`;
  
  // Generate a unique token (in a real app, this would be generated on the server)
  function generateQuoteToken() {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
  
  const handleInputChange = (field: keyof typeof emailData, value: string) => {
    setEmailData(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const handleSendEmail = () => {
    setIsSending(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      setIsSending(false);
      
      toast({
        title: "Quote Sent",
        description: `Quote has been sent to ${emailData.to}${emailData.cc ? ` and cc'd to ${emailData.cc}` : ''}`
      });
      
      if (onSend) {
        onSend();
      }
    }, 2000);
  };
  
  const handleCopyLink = () => {
    navigator.clipboard.writeText(quoteLink);
    
    toast({
      title: "Link Copied",
      description: "The quote link has been copied to clipboard."
    });
  };
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Mail className="mr-2 h-4 w-4" />
            Send Quote
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Send Quote to Client</DialogTitle>
          <DialogDescription>
            Send the quote via email or get a shareable link
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "email" | "link")} className="mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="link">Get Link</TabsTrigger>
          </TabsList>
          
          <TabsContent value="email" className="mt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-3">
                  <Label htmlFor="to">To</Label>
                  <Input
                    id="to"
                    value={emailData.to}
                    onChange={(e) => handleInputChange("to", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="cc">CC</Label>
                  <Input
                    id="cc"
                    value={emailData.cc}
                    onChange={(e) => handleInputChange("cc", e.target.value)}
                  />
                </div>
                <div className="col-span-2">
                  <Label htmlFor="bcc">BCC</Label>
                  <Input
                    id="bcc"
                    value={emailData.bcc}
                    onChange={(e) => handleInputChange("bcc", e.target.value)}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="subject">Subject</Label>
                <Input
                  id="subject"
                  value={emailData.subject}
                  onChange={(e) => handleInputChange("subject", e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  rows={10}
                  value={emailData.message}
                  onChange={(e) => handleInputChange("message", e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  The quote will be attached as a PDF and a secure link will be included.
                </p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="link" className="mt-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="link" className="mb-2 block">Shareable Quote Link</Label>
                <div className="flex gap-2">
                  <Input
                    id="link"
                    value={quoteLink}
                    readOnly
                    className="flex-1"
                  />
                  <Button variant="outline" onClick={handleCopyLink}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  This secure link allows your client to view and approve the quote without creating an account.
                </p>
              </div>
              
              <div className="border rounded-md p-4 bg-muted/10">
                <h4 className="text-sm font-medium mb-2 flex items-center">
                  <Link className="h-4 w-4 mr-2 text-muted-foreground" />
                  Link Details
                </h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Quote Number:</span>
                    <span>{quoteNumber}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Client:</span>
                    <span>{clientName}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Amount:</span>
                    <span>${amount.toLocaleString()}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Expires:</span>
                    <span>{expiryDate}</span>
                  </li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="mt-4">
          {activeTab === "email" ? (
            <Button onClick={handleSendEmail} disabled={isSending || !emailData.to.trim()}>
              {isSending ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" />
                  Send Email
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleCopyLink}>
              <Copy className="mr-2 h-4 w-4" />
              Copy Link
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}