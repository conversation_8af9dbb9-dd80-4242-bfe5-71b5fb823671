import { useState, useRef, useEffect } from "react";
import { 
  Pencil, 
  Square, 
  Circle, 
  Type, 
  Eraser, 
  Layers, 
  Plus, 
  Move, 
  Hand, 
  RotateCcw, 
  Download, 
  Trash, 
  Save, 
  ArrowUpDown, 
  Eye,
  EyeOff,
  MoreVertical,
  Minus
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Toggle } from "@/components/ui/toggle";
import { Input } from "@/components/ui/input";
import { AICard } from "@/components/ai-card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { SketchPicker } from "react-color";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

// Types for annotations
type AnnotationTool = 
  | "select" 
  | "move" 
  | "pen" 
  | "rectangle" 
  | "circle" 
  | "text" 
  | "eraser" 
  | "none";

export type AnnotationLayer = {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  zIndex: number;
};

export type AnnotationObject = {
  id: string;
  type: "path" | "rectangle" | "circle" | "text";
  layerId: string;
  points?: { x: number; y: number }[];
  text?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  radius?: number;
  color: string;
  strokeWidth: number;
  filled: boolean;
  selected: boolean;
  createdBy?: string;
  createdAt?: Date;
};

interface FloorPlanAnnotationProps {
  imageUrl: string;
  initialAnnotations?: AnnotationObject[];
  initialLayers?: AnnotationLayer[];
  collaborators?: { id: string; name: string; color: string }[];
  readOnly?: boolean;
  onSave?: (annotations: AnnotationObject[], layers: AnnotationLayer[]) => void;
}

export function FloorPlanAnnotation({
  imageUrl,
  initialAnnotations = [],
  initialLayers = [],
  collaborators = [],
  readOnly = false,
  onSave
}: FloorPlanAnnotationProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);
  
  // State for the canvas
  const [activeTool, setActiveTool] = useState<AnnotationTool>("select");
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [annotations, setAnnotations] = useState<AnnotationObject[]>(initialAnnotations);
  const [selectedAnnotation, setSelectedAnnotation] = useState<string | null>(null);
  const [strokeColor, setStrokeColor] = useState<string>("#1a73e8");
  const [strokeWidth, setStrokeWidth] = useState<number>(2);
  const [isFilled, setIsFilled] = useState<boolean>(false);
  const [currentPath, setCurrentPath] = useState<{ x: number; y: number }[]>([]);
  const [layers, setLayers] = useState<AnnotationLayer[]>(
    initialLayers.length > 0 
      ? initialLayers 
      : [
          { 
            id: "default", 
            name: "Default Layer", 
            visible: true, 
            locked: false, 
            zIndex: 0 
          }
        ]
  );
  const [activeLayer, setActiveLayer] = useState<string>(
    layers.length > 0 ? layers[0].id : "default"
  );
  const [textValue, setTextValue] = useState<string>("");
  const [textPosition, setTextPosition] = useState<{ x: number; y: number } | null>(null);
  const [isAddingText, setIsAddingText] = useState<boolean>(false);
  const [scale, setScale] = useState<number>(1);
  const [offset, setOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [colorPickerOpen, setColorPickerOpen] = useState<boolean>(false);
  
  // Load the floor plan image
  useEffect(() => {
    if (!imageUrl) return;
    
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      imageRef.current = img;
      drawCanvas();
    };
  }, [imageUrl]);
  
  // Redraw canvas when annotations, layers, or other visual properties change
  useEffect(() => {
    drawCanvas();
  }, [annotations, layers, selectedAnnotation, scale, offset, activeTool, isAddingText, textPosition]);
  
  // Generate a unique ID
  const generateId = () => {
    return Math.random().toString(36).substring(2, 15);
  };
  
  // Draw everything on the canvas
  const drawCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    const img = imageRef.current;
    
    if (!canvas || !ctx || !img) return;
    
    // Set canvas size to match container
    if (containerRef.current) {
      canvas.width = containerRef.current.clientWidth;
      canvas.height = containerRef.current.clientHeight;
    }
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background image with scaling and offset
    const imgWidth = img.width * scale;
    const imgHeight = img.height * scale;
    
    ctx.save();
    ctx.translate(offset.x, offset.y);
    ctx.drawImage(img, 0, 0, imgWidth, imgHeight);
    ctx.restore();
    
    // Draw annotations based on layer visibility
    const visibleLayers = layers.filter(layer => layer.visible);
    const sortedLayers = [...visibleLayers].sort((a, b) => a.zIndex - b.zIndex);
    
    sortedLayers.forEach(layer => {
      const layerAnnotations = annotations.filter(anno => anno.layerId === layer.id);
      
      layerAnnotations.forEach(annotation => {
        ctx.save();
        ctx.translate(offset.x, offset.y);
        ctx.scale(scale, scale);
        
        // Set common properties
        ctx.strokeStyle = annotation.color;
        ctx.lineWidth = annotation.strokeWidth;
        ctx.fillStyle = annotation.color;
        
        // Draw selection indicator if selected
        if (annotation.selected || annotation.id === selectedAnnotation) {
          ctx.setLineDash([5, 5]);
          ctx.strokeStyle = "#ffffff";
          ctx.lineWidth = 2;
          
          if (annotation.type === "rectangle" && annotation.x !== undefined && annotation.y !== undefined && annotation.width !== undefined && annotation.height !== undefined) {
            ctx.strokeRect(
              annotation.x - 5, 
              annotation.y - 5, 
              annotation.width + 10, 
              annotation.height + 10
            );
          } else if (annotation.type === "circle" && annotation.x !== undefined && annotation.y !== undefined && annotation.radius !== undefined) {
            ctx.beginPath();
            ctx.arc(
              annotation.x, 
              annotation.y, 
              annotation.radius + 5, 
              0, 
              2 * Math.PI
            );
            ctx.stroke();
          } else if (annotation.type === "text" && annotation.x !== undefined && annotation.y !== undefined) {
            // For text, draw a bounding box
            const textWidth = ctx.measureText(annotation.text || "").width;
            ctx.strokeRect(
              annotation.x - 5, 
              annotation.y - 20, 
              textWidth + 10, 
              30
            );
          }
          
          ctx.setLineDash([]);
        }
        
        // Reset styles for actual drawing
        ctx.strokeStyle = annotation.color;
        ctx.lineWidth = annotation.strokeWidth;
        ctx.fillStyle = annotation.color;
        
        // Draw the annotation based on its type
        switch (annotation.type) {
          case "path":
            if (annotation.points && annotation.points.length > 0) {
              ctx.beginPath();
              ctx.moveTo(annotation.points[0].x, annotation.points[0].y);
              
              for (let i = 1; i < annotation.points.length; i++) {
                ctx.lineTo(annotation.points[i].x, annotation.points[i].y);
              }
              
              ctx.stroke();
            }
            break;
            
          case "rectangle":
            if (annotation.x !== undefined && annotation.y !== undefined && annotation.width !== undefined && annotation.height !== undefined) {
              ctx.beginPath();
              ctx.rect(annotation.x, annotation.y, annotation.width, annotation.height);
              
              if (annotation.filled) {
                ctx.globalAlpha = 0.3;
                ctx.fill();
                ctx.globalAlpha = 1;
              }
              
              ctx.stroke();
            }
            break;
            
          case "circle":
            if (annotation.x !== undefined && annotation.y !== undefined && annotation.radius !== undefined) {
              ctx.beginPath();
              ctx.arc(annotation.x, annotation.y, annotation.radius, 0, 2 * Math.PI);
              
              if (annotation.filled) {
                ctx.globalAlpha = 0.3;
                ctx.fill();
                ctx.globalAlpha = 1;
              }
              
              ctx.stroke();
            }
            break;
            
          case "text":
            if (annotation.x !== undefined && annotation.y !== undefined && annotation.text) {
              ctx.font = `${annotation.strokeWidth * 5}px Arial`;
              ctx.fillStyle = annotation.color;
              ctx.fillText(annotation.text, annotation.x, annotation.y);
            }
            break;
        }
        
        ctx.restore();
      });
    });
    
    // Draw current path if drawing
    if (isDrawing && activeTool === "pen" && currentPath.length > 0) {
      ctx.save();
      ctx.translate(offset.x, offset.y);
      ctx.scale(scale, scale);
      
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth;
      ctx.beginPath();
      ctx.moveTo(currentPath[0].x, currentPath[0].y);
      
      for (let i = 1; i < currentPath.length; i++) {
        ctx.lineTo(currentPath[i].x, currentPath[i].y);
      }
      
      ctx.stroke();
      ctx.restore();
    }
    
    // Draw rectangle or circle preview if drawing
    if (isDrawing && startPoint && (activeTool === "rectangle" || activeTool === "circle")) {
      ctx.save();
      ctx.translate(offset.x, offset.y);
      ctx.scale(scale, scale);
      
      const currentPoint = getCanvasPoint(
        canvas.width / 2, 
        canvas.height / 2
      );
      
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth;
      
      if (activeTool === "rectangle") {
        const width = currentPoint.x - startPoint.x;
        const height = currentPoint.y - startPoint.y;
        
        ctx.beginPath();
        ctx.rect(startPoint.x, startPoint.y, width, height);
        
        if (isFilled) {
          ctx.globalAlpha = 0.3;
          ctx.fillStyle = strokeColor;
          ctx.fill();
          ctx.globalAlpha = 1;
        }
        
        ctx.stroke();
      } else if (activeTool === "circle") {
        const radius = Math.sqrt(
          Math.pow(currentPoint.x - startPoint.x, 2) +
          Math.pow(currentPoint.y - startPoint.y, 2)
        );
        
        ctx.beginPath();
        ctx.arc(startPoint.x, startPoint.y, radius, 0, 2 * Math.PI);
        
        if (isFilled) {
          ctx.globalAlpha = 0.3;
          ctx.fillStyle = strokeColor;
          ctx.fill();
          ctx.globalAlpha = 1;
        }
        
        ctx.stroke();
      }
      
      ctx.restore();
    }
    
    // Show text input position
    if (isAddingText && textPosition) {
      ctx.save();
      ctx.translate(offset.x, offset.y);
      ctx.scale(scale, scale);
      
      // Draw cursor
      ctx.strokeStyle = strokeColor;
      ctx.fillStyle = strokeColor;
      ctx.fillRect(textPosition.x, textPosition.y - 15, 2, 20);
      
      // Preview text if available
      if (textValue) {
        ctx.font = `${strokeWidth * 5}px Arial`;
        ctx.fillText(textValue, textPosition.x, textPosition.y);
      }
      
      ctx.restore();
    }
  };
  
  // Convert screen coordinates to canvas coordinates
  const getCanvasPoint = (clientX: number, clientY: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    
    const rect = canvas.getBoundingClientRect();
    const x = (clientX - rect.left - offset.x) / scale;
    const y = (clientY - rect.top - offset.y) / scale;
    
    return { x, y };
  };
  
  // Handle mouse down event
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (readOnly) return;
    
    const point = getCanvasPoint(e.clientX, e.clientY);
    
    switch (activeTool) {
      case "select":
        // Check if we clicked on an annotation
        const clickedAnnotation = findAnnotationAtPoint(point);
        setSelectedAnnotation(clickedAnnotation ? clickedAnnotation.id : null);
        break;
        
      case "move":
        setIsDragging(true);
        setDragStart({ x: e.clientX, y: e.clientY });
        break;
        
      case "pen":
        setIsDrawing(true);
        setCurrentPath([point]);
        break;
        
      case "rectangle":
      case "circle":
        setIsDrawing(true);
        setStartPoint(point);
        break;
        
      case "text":
        setIsAddingText(true);
        setTextPosition(point);
        setTextValue("");
        break;
        
      case "eraser":
        eraseAnnotationAtPoint(point);
        break;
    }
  };
  
  // Handle mouse move event
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (readOnly) return;
    
    const point = getCanvasPoint(e.clientX, e.clientY);
    
    if (activeTool === "move" && isDragging) {
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;
      
      setOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      
      setDragStart({ x: e.clientX, y: e.clientY });
    } else if (activeTool === "pen" && isDrawing) {
      setCurrentPath(prev => [...prev, point]);
    } else if ((activeTool === "rectangle" || activeTool === "circle") && isDrawing) {
      // Just redraw with current mouse position
      drawCanvas();
    } else if (activeTool === "select") {
      // Highlight annotation under cursor
      const annotationUnderCursor = findAnnotationAtPoint(point);
      if (annotationUnderCursor) {
        canvasRef.current!.style.cursor = "pointer";
      } else {
        canvasRef.current!.style.cursor = "default";
      }
    } else if (activeTool === "eraser") {
      canvasRef.current!.style.cursor = "not-allowed";
    }
  };
  
  // Handle mouse up event
  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (readOnly) return;
    
    const point = getCanvasPoint(e.clientX, e.clientY);
    
    if (activeTool === "pen" && isDrawing && currentPath.length > 0) {
      const newAnnotation: AnnotationObject = {
        id: generateId(),
        type: "path",
        layerId: activeLayer,
        points: currentPath,
        color: strokeColor,
        strokeWidth,
        filled: false,
        selected: false,
        createdBy: "user", // Replace with actual user
        createdAt: new Date()
      };
      
      setAnnotations(prev => [...prev, newAnnotation]);
      setCurrentPath([]);
    } else if (activeTool === "rectangle" && isDrawing && startPoint) {
      const width = point.x - startPoint.x;
      const height = point.y - startPoint.y;
      
      const newAnnotation: AnnotationObject = {
        id: generateId(),
        type: "rectangle",
        layerId: activeLayer,
        x: startPoint.x,
        y: startPoint.y,
        width,
        height,
        color: strokeColor,
        strokeWidth,
        filled: isFilled,
        selected: false,
        createdBy: "user", // Replace with actual user
        createdAt: new Date()
      };
      
      setAnnotations(prev => [...prev, newAnnotation]);
    } else if (activeTool === "circle" && isDrawing && startPoint) {
      const radius = Math.sqrt(
        Math.pow(point.x - startPoint.x, 2) +
        Math.pow(point.y - startPoint.y, 2)
      );
      
      const newAnnotation: AnnotationObject = {
        id: generateId(),
        type: "circle",
        layerId: activeLayer,
        x: startPoint.x,
        y: startPoint.y,
        radius,
        color: strokeColor,
        strokeWidth,
        filled: isFilled,
        selected: false,
        createdBy: "user", // Replace with actual user
        createdAt: new Date()
      };
      
      setAnnotations(prev => [...prev, newAnnotation]);
    }
    
    setIsDrawing(false);
    setStartPoint(null);
    
    if (activeTool === "move") {
      setIsDragging(false);
    }
  };
  
  // Handle key press events (for text input)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!isAddingText || !textPosition) return;
    
    if (e.key === "Enter") {
      // Add text annotation
      const newAnnotation: AnnotationObject = {
        id: generateId(),
        type: "text",
        layerId: activeLayer,
        x: textPosition.x,
        y: textPosition.y,
        text: textValue,
        color: strokeColor,
        strokeWidth,
        filled: false,
        selected: false,
        createdBy: "user", // Replace with actual user
        createdAt: new Date()
      };
      
      setAnnotations(prev => [...prev, newAnnotation]);
      setIsAddingText(false);
      setTextPosition(null);
      setTextValue("");
    } else if (e.key === "Escape") {
      setIsAddingText(false);
      setTextPosition(null);
      setTextValue("");
    }
  };
  
  // Find annotation at a specific point
  const findAnnotationAtPoint = (point: { x: number; y: number }) => {
    // Check in reverse order (top to bottom in layers)
    const visibleLayers = layers.filter(layer => layer.visible && !layer.locked);
    const sortedLayers = [...visibleLayers].sort((a, b) => b.zIndex - a.zIndex);
    const layerIds = sortedLayers.map(layer => layer.id);
    
    // Filter annotations in visible layers and sort by layers
    const visibleAnnotations = annotations.filter(annotation => 
      layerIds.includes(annotation.layerId)
    );
    
    // Check each annotation
    for (const annotation of visibleAnnotations) {
      switch (annotation.type) {
        case "rectangle":
          if (
            annotation.x !== undefined && 
            annotation.y !== undefined && 
            annotation.width !== undefined && 
            annotation.height !== undefined && 
            point.x >= annotation.x && 
            point.x <= annotation.x + annotation.width &&
            point.y >= annotation.y && 
            point.y <= annotation.y + annotation.height
          ) {
            return annotation;
          }
          break;
          
        case "circle":
          if (
            annotation.x !== undefined && 
            annotation.y !== undefined && 
            annotation.radius !== undefined
          ) {
            const distance = Math.sqrt(
              Math.pow(point.x - annotation.x, 2) + 
              Math.pow(point.y - annotation.y, 2)
            );
            
            if (distance <= annotation.radius) {
              return annotation;
            }
          }
          break;
          
        case "text":
          if (
            annotation.x !== undefined && 
            annotation.y !== undefined && 
            annotation.text
          ) {
            const canvas = canvasRef.current;
            const ctx = canvas?.getContext("2d");
            
            if (ctx) {
              ctx.font = `${annotation.strokeWidth * 5}px Arial`;
              const textWidth = ctx.measureText(annotation.text).width;
              
              if (
                point.x >= annotation.x && 
                point.x <= annotation.x + textWidth &&
                point.y >= annotation.y - 20 && 
                point.y <= annotation.y + 10
              ) {
                return annotation;
              }
            }
          }
          break;
          
        case "path":
          if (annotation.points && annotation.points.length > 0) {
            // Check if point is near any line segment
            for (let i = 0; i < annotation.points.length - 1; i++) {
              const p1 = annotation.points[i];
              const p2 = annotation.points[i + 1];
              
              // Calculate distance from point to line segment
              const distance = distanceToSegment(point, p1, p2);
              
              if (distance <= annotation.strokeWidth + 2) {
                return annotation;
              }
            }
          }
          break;
      }
    }
    
    return null;
  };
  
  // Calculate distance from point to line segment
  const distanceToSegment = (
    point: { x: number; y: number },
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ) => {
    const A = point.x - p1.x;
    const B = point.y - p1.y;
    const C = p2.x - p1.x;
    const D = p2.y - p1.y;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;
    
    if (lenSq !== 0) {
      param = dot / lenSq;
    }
    
    let xx, yy;
    
    if (param < 0) {
      xx = p1.x;
      yy = p1.y;
    } else if (param > 1) {
      xx = p2.x;
      yy = p2.y;
    } else {
      xx = p1.x + param * C;
      yy = p1.y + param * D;
    }
    
    const dx = point.x - xx;
    const dy = point.y - yy;
    
    return Math.sqrt(dx * dx + dy * dy);
  };
  
  // Erase annotation at point
  const eraseAnnotationAtPoint = (point: { x: number; y: number }) => {
    const annotationToErase = findAnnotationAtPoint(point);
    
    if (annotationToErase) {
      setAnnotations(prev => 
        prev.filter(annotation => annotation.id !== annotationToErase.id)
      );
    }
  };
  
  // Add a new layer
  const addNewLayer = () => {
    const newLayer: AnnotationLayer = {
      id: generateId(),
      name: `Layer ${layers.length + 1}`,
      visible: true,
      locked: false,
      zIndex: layers.length
    };
    
    setLayers(prev => [...prev, newLayer]);
    setActiveLayer(newLayer.id);
  };
  
  // Toggle layer visibility
  const toggleLayerVisibility = (layerId: string) => {
    setLayers(prev => 
      prev.map(layer => 
        layer.id === layerId 
          ? { ...layer, visible: !layer.visible } 
          : layer
      )
    );
  };
  
  // Toggle layer lock
  const toggleLayerLock = (layerId: string) => {
    setLayers(prev => 
      prev.map(layer => 
        layer.id === layerId 
          ? { ...layer, locked: !layer.locked } 
          : layer
      )
    );
  };
  
  // Delete a layer and its annotations
  const deleteLayer = (layerId: string) => {
    if (layers.length <= 1) return; // Prevent deleting the last layer
    
    setLayers(prev => prev.filter(layer => layer.id !== layerId));
    setAnnotations(prev => prev.filter(annotation => annotation.layerId !== layerId));
    
    // Set active layer to the first available layer
    if (activeLayer === layerId) {
      const remainingLayers = layers.filter(layer => layer.id !== layerId);
      if (remainingLayers.length > 0) {
        setActiveLayer(remainingLayers[0].id);
      }
    }
  };
  
  // Delete selected annotation
  const deleteSelectedAnnotation = () => {
    if (selectedAnnotation) {
      setAnnotations(prev => 
        prev.filter(annotation => annotation.id !== selectedAnnotation)
      );
      setSelectedAnnotation(null);
    }
  };
  
  // Save annotations
  const saveAnnotations = () => {
    if (onSave) {
      onSave(annotations, layers);
    }
  };
  
  // Reset annotations
  const resetAnnotations = () => {
    if (window.confirm("Are you sure you want to clear all annotations?")) {
      setAnnotations([]);
      setSelectedAnnotation(null);
    }
  };
  
  // Handle mouse wheel for zooming
  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    const newScale = Math.max(0.1, Math.min(5, scale + delta));
    setScale(newScale);
  };
  
  // Handle text input changes
  const handleTextInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTextValue(e.target.value);
  };
  
  // Get the cursor style based on active tool
  const getCursorStyle = () => {
    switch (activeTool) {
      case "select":
        return "default";
      case "move":
        return isDragging ? "grabbing" : "grab";
      case "pen":
        return "crosshair";
      case "rectangle":
      case "circle":
        return "crosshair";
      case "text":
        return "text";
      case "eraser":
        return "not-allowed";
      default:
        return "default";
    }
  };
  
  // Define stroke width options
  const strokeWidthOptions = [
    { value: "1", label: "Thin (1px)" },
    { value: "2", label: "Medium (2px)" },
    { value: "4", label: "Thick (4px)" },
    { value: "6", label: "Extra Thick (6px)" }
  ];
  
  return (
    <AICard className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Floor Plan Annotations</h3>
        
        <div className="flex space-x-2">
          {!readOnly && (
            <>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={resetAnnotations}
              >
                Reset
              </Button>
              <Button 
                size="sm" 
                onClick={saveAnnotations}
              >
                Save Annotations
              </Button>
            </>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 flex-1">
        {/* Sidebar with annotation tools */}
        <div className="space-y-4">
          {!readOnly && (
            <>
              {/* Drawing tools */}
              <div className="border border-border rounded-lg p-4">
                <h4 className="text-sm font-medium mb-3">Tools</h4>
                <div className="grid grid-cols-4 gap-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Toggle 
                          pressed={activeTool === "select"} 
                          onPressedChange={() => setActiveTool("select")}
                          className="flex flex-col items-center justify-center h-14 data-[state=on]:bg-primary/10"
                          aria-label="Select tool"
                        >
                          <Move className="h-5 w-5" />
                          <span className="text-xs mt-1">Select</span>
                        </Toggle>
                      </TooltipTrigger>
                      <TooltipContent>Select annotations</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Toggle 
                          pressed={activeTool === "move"} 
                          onPressedChange={() => setActiveTool("move")}
                          className="flex flex-col items-center justify-center h-14 data-[state=on]:bg-primary/10"
                          aria-label="Pan tool"
                        >
                          <Hand className="h-5 w-5" />
                          <span className="text-xs mt-1">Pan</span>
                        </Toggle>
                      </TooltipTrigger>
                      <TooltipContent>Pan the floor plan</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Toggle 
                          pressed={activeTool === "pen"} 
                          onPressedChange={() => setActiveTool("pen")}
                          className="flex flex-col items-center justify-center h-14 data-[state=on]:bg-primary/10"
                          aria-label="Pen tool"
                        >
                          <Pencil className="h-5 w-5" />
                          <span className="text-xs mt-1">Pen</span>
                        </Toggle>
                      </TooltipTrigger>
                      <TooltipContent>Draw freehand</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Toggle 
                          pressed={activeTool === "rectangle"} 
                          onPressedChange={() => setActiveTool("rectangle")}
                          className="flex flex-col items-center justify-center h-14 data-[state=on]:bg-primary/10"
                          aria-label="Rectangle tool"
                        >
                          <Square className="h-5 w-5" />
                          <span className="text-xs mt-1">Rectangle</span>
                        </Toggle>
                      </TooltipTrigger>
                      <TooltipContent>Draw rectangle</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Toggle 
                          pressed={activeTool === "circle"} 
                          onPressedChange={() => setActiveTool("circle")}
                          className="flex flex-col items-center justify-center h-14 data-[state=on]:bg-primary/10"
                          aria-label="Circle tool"
                        >
                          <Circle className="h-5 w-5" />
                          <span className="text-xs mt-1">Circle</span>
                        </Toggle>
                      </TooltipTrigger>
                      <TooltipContent>Draw circle</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Toggle 
                          pressed={activeTool === "text"} 
                          onPressedChange={() => setActiveTool("text")}
                          className="flex flex-col items-center justify-center h-14 data-[state=on]:bg-primary/10"
                          aria-label="Text tool"
                        >
                          <Type className="h-5 w-5" />
                          <span className="text-xs mt-1">Text</span>
                        </Toggle>
                      </TooltipTrigger>
                      <TooltipContent>Add text</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Toggle 
                          pressed={activeTool === "eraser"} 
                          onPressedChange={() => setActiveTool("eraser")}
                          className="flex flex-col items-center justify-center h-14 data-[state=on]:bg-primary/10"
                          aria-label="Eraser tool"
                        >
                          <Eraser className="h-5 w-5" />
                          <span className="text-xs mt-1">Eraser</span>
                        </Toggle>
                      </TooltipTrigger>
                      <TooltipContent>Erase annotations</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
              
              {/* Stroke properties */}
              <div className="border border-border rounded-lg p-4">
                <h4 className="text-sm font-medium mb-3">Properties</h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="text-xs text-muted-foreground block mb-2">
                      Color
                    </label>
                    <Popover open={colorPickerOpen} onOpenChange={setColorPickerOpen}>
                      <PopoverTrigger asChild>
                        <Button 
                          variant="outline"
                          className="w-full h-8 flex justify-between items-center"
                        >
                          <div className="flex items-center">
                            <div 
                              className="w-4 h-4 rounded-full mr-2" 
                              style={{ backgroundColor: strokeColor }}
                            ></div>
                            <span>{strokeColor}</span>
                          </div>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <SketchPicker
                          color={strokeColor}
                          onChange={(color) => setStrokeColor(color.hex)}
                          disableAlpha
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  
                  <div>
                    <label className="text-xs text-muted-foreground block mb-2">
                      Stroke Width
                    </label>
                    <Select 
                      value={strokeWidth.toString()} 
                      onValueChange={(value) => setStrokeWidth(parseInt(value))}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Select width" />
                      </SelectTrigger>
                      <SelectContent>
                        {strokeWidthOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex items-center">
                    <Toggle 
                      pressed={isFilled} 
                      onPressedChange={setIsFilled}
                      className="rounded-md h-8"
                      aria-label="Fill shape"
                    >
                      <span className="text-xs">Fill Shape</span>
                    </Toggle>
                  </div>
                </div>
              </div>
              
              {/* Layers */}
              <div className="border border-border rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-sm font-medium">Layers</h4>
                  <Button 
                    size="icon" 
                    variant="ghost" 
                    className="h-6 w-6" 
                    onClick={addNewLayer}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {layers.map(layer => (
                    <div 
                      key={layer.id} 
                      className={`
                        flex items-center justify-between p-2 text-sm rounded-md
                        ${activeLayer === layer.id ? 'bg-primary/10' : 'hover:bg-muted/50'}
                        ${layer.locked ? 'opacity-50' : ''}
                      `}
                      onClick={() => !layer.locked && setActiveLayer(layer.id)}
                    >
                      <div className="flex items-center">
                        <Button 
                          size="icon" 
                          variant="ghost" 
                          className="h-5 w-5 mr-2" 
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleLayerVisibility(layer.id);
                          }}
                        >
                          {layer.visible ? (
                            <Eye className="h-3 w-3" />
                          ) : (
                            <EyeOff className="h-3 w-3" />
                          )}
                        </Button>
                        <span className="truncate max-w-[100px]">{layer.name}</span>
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            size="icon" 
                            variant="ghost" 
                            className="h-5 w-5"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => toggleLayerLock(layer.id)}>
                            {layer.locked ? "Unlock Layer" : "Lock Layer"}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => deleteLayer(layer.id)}>
                            Delete Layer
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
          
          {/* Annotations list */}
          <div className="border border-border rounded-lg p-4">
            <h4 className="text-sm font-medium mb-3">Annotations</h4>
            
            {annotations.length === 0 ? (
              <p className="text-xs text-muted-foreground">No annotations yet</p>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {annotations.map(annotation => {
                  const layer = layers.find(l => l.id === annotation.layerId);
                  
                  if (!layer || !layer.visible) return null;
                  
                  let annotationLabel = "";
                  switch (annotation.type) {
                    case "path":
                      annotationLabel = "Freehand Line";
                      break;
                    case "rectangle":
                      annotationLabel = "Rectangle";
                      break;
                    case "circle":
                      annotationLabel = "Circle";
                      break;
                    case "text":
                      annotationLabel = `Text: "${annotation.text}"`;
                      break;
                  }
                  
                  return (
                    <div 
                      key={annotation.id} 
                      className={`
                        flex items-center justify-between p-2 text-xs rounded-md
                        ${selectedAnnotation === annotation.id ? 'bg-primary/10' : 'hover:bg-muted/50'}
                        cursor-pointer
                      `}
                      onClick={() => setSelectedAnnotation(annotation.id)}
                    >
                      <div className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-2" 
                          style={{ backgroundColor: annotation.color }}
                        ></div>
                        <span className="truncate max-w-[120px]">{annotationLabel}</span>
                      </div>
                      
                      {selectedAnnotation === annotation.id && !readOnly && (
                        <Button 
                          size="icon" 
                          variant="ghost" 
                          className="h-5 w-5" 
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteSelectedAnnotation();
                          }}
                        >
                          <Trash className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          
          {/* Collaborators */}
          {collaborators.length > 0 && (
            <div className="border border-border rounded-lg p-4">
              <h4 className="text-sm font-medium mb-3">Collaborators</h4>
              <div className="space-y-2">
                {collaborators.map(collaborator => (
                  <div 
                    key={collaborator.id} 
                    className="flex items-center p-2 text-xs"
                  >
                    <div 
                      className="w-6 h-6 rounded-full mr-2 flex items-center justify-center text-white"
                      style={{ backgroundColor: collaborator.color }}
                    >
                      {collaborator.name.charAt(0).toUpperCase()}
                    </div>
                    <span>{collaborator.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Canvas area */}
        <div className="md:col-span-3">
          <div 
            className="relative border border-border rounded-lg overflow-hidden"
            style={{ height: "600px" }}
            ref={containerRef}
            tabIndex={0}
            onKeyDown={handleKeyDown}
          >
            <canvas
              ref={canvasRef}
              style={{ cursor: getCursorStyle() }}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onWheel={handleWheel}
              className="w-full h-full"
            />
            
            {isAddingText && (
              <div className="absolute bottom-4 left-0 right-0 mx-auto w-64 bg-background border border-border rounded-md p-2">
                <Input
                  type="text"
                  placeholder="Type text and press Enter"
                  value={textValue}
                  onChange={handleTextInputChange}
                  className="text-sm"
                  autoFocus
                />
              </div>
            )}
            
            {/* View controls */}
            <div className="absolute bottom-4 right-4 bg-background/80 backdrop-blur-sm border border-border rounded-md flex p-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8"
                      onClick={() => setScale(prev => Math.min(5, prev + 0.1))}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom in</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <span className="flex items-center justify-center w-12 text-xs">
                {Math.round(scale * 100)}%
              </span>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8"
                      onClick={() => setScale(prev => Math.max(0.1, prev - 0.1))}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom out</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 ml-2"
                      onClick={() => {
                        setScale(1);
                        setOffset({ x: 0, y: 0 });
                      }}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Reset view</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          {/* Status bar */}
          <div className="mt-2 flex justify-between items-center text-xs text-muted-foreground">
            <div>
              {activeTool !== "none" && (
                <Badge variant="outline" className="text-[10px]">
                  {activeTool.charAt(0).toUpperCase() + activeTool.slice(1)} Tool
                </Badge>
              )}
            </div>
            <div>
              {layers.find(layer => layer.id === activeLayer)?.name || "Default Layer"}
            </div>
            <div>
              {annotations.length} annotations
            </div>
          </div>
        </div>
      </div>
    </AICard>
  );
}