import { useState, useEffect } from "react";
import { FileText, Image } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { FloorPlanViewer } from "@/components/floor-plan-viewer";

interface FloorPlanPreviewProps {
  file: File;
  imageUrl: string;
}

export function FloorPlanPreview({ file, imageUrl }: FloorPlanPreviewProps) {
  const [fileType, setFileType] = useState<"image" | "pdf" | "unknown">("unknown");
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  
  useEffect(() => {
    // Determine file type
    if (file.type.startsWith("image/")) {
      setFileType("image");
    } else if (file.type === "application/pdf") {
      setFileType("pdf");
      // Create object URL for PDF
      setPdfUrl(URL.createObjectURL(file));
    } else {
      setFileType("unknown");
    }
    
    // Cleanup object URL on unmount
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [file]);
  
  return (
    <div className="space-y-4">
      {/* File info */}
      <div className="flex items-center space-x-4 p-4 bg-muted/30 border border-border rounded-lg">
        <div className="h-12 w-12 bg-primary/10 rounded-md flex items-center justify-center">
          {fileType === "image" ? (
            <Image className="h-6 w-6 text-primary" />
          ) : (
            <FileText className="h-6 w-6 text-primary" />
          )}
        </div>
        
        <div>
          <h4 className="text-sm font-medium">{file.name}</h4>
          <p className="text-xs text-muted-foreground">
            {(file.size / 1024 / 1024).toFixed(2)} MB • {file.type || "Unknown type"}
          </p>
        </div>
      </div>
      
      {/* Preview */}
      <div className="border border-border rounded-lg overflow-hidden">
        {fileType === "image" && (
          <div className="h-[400px]">
            <FloorPlanViewer imageUrl={imageUrl} readOnly />
          </div>
        )}
        
        {fileType === "pdf" && pdfUrl && (
          <div className="h-[600px]">
            <iframe
              src={`${pdfUrl}#toolbar=0&navpanes=0`}
              className="w-full h-full"
              title={file.name}
            ></iframe>
          </div>
        )}
        
        {fileType === "unknown" && (
          <div className="flex items-center justify-center h-[300px] bg-muted/20">
            <p className="text-muted-foreground">Preview not available for this file type</p>
          </div>
        )}
      </div>
      
      {/* Metadata */}
      <div className="grid grid-cols-2 gap-4 text-xs p-4 bg-muted/30 border border-border rounded-lg">
        <div>
          <p className="text-muted-foreground mb-1">File name</p>
          <p className="font-medium">{file.name}</p>
        </div>
        
        <div>
          <p className="text-muted-foreground mb-1">File type</p>
          <p className="font-medium">{file.type || "Unknown"}</p>
        </div>
        
        <div>
          <p className="text-muted-foreground mb-1">File size</p>
          <p className="font-medium">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
        </div>
        
        <div>
          <p className="text-muted-foreground mb-1">Last modified</p>
          <p className="font-medium">
            {file.lastModified
              ? new Date(file.lastModified).toLocaleString()
              : "Unknown"}
          </p>
        </div>
      </div>
    </div>
  );
}