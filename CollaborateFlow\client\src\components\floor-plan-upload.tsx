import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { useMutation } from "@tanstack/react-query";
import { UploadCloud, X, File, CheckCircle, AlertCircle, Image, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AICard } from "@/components/ai-card";
import { apiRequest } from "@/lib/queryClient";
import { FloorPlanPreview } from "@/components/floor-plan-preview";

interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: "pending" | "uploading" | "success" | "error";
  error?: string;
  previewUrl?: string;
}

interface FloorPlanUploadProps {
  onFileUploaded?: (file: File, imageUrl: string) => void;
}

export function FloorPlanUpload({ onFileUploaded }: FloorPlanUploadProps) {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState<UploadFile | null>(null);
  const { toast } = useToast();
  
  // File upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append("file", file);
      
      const response = await apiRequest("POST", "/api/floor-plans/upload", formData);
      
      if (!response.ok) {
        throw new Error("Failed to upload file");
      }
      
      return await response.json();
    },
    onError: (error) => {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  // Dropzone setup
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => ({
      file,
      id: Math.random().toString(36).substring(2, 15),
      progress: 0,
      status: "pending" as const,
      previewUrl: URL.createObjectURL(file)
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
    
    // Auto start upload for the first file
    if (newFiles.length > 0) {
      handleFileUpload(newFiles[0]);
    }
  }, []);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.pdf', '.svg'],
    },
    maxFiles: 1,
  });
  
  // Handle file upload
  const handleFileUpload = async (uploadFile: UploadFile) => {
    // Update file status to uploading
    setFiles(prev => 
      prev.map(file => 
        file.id === uploadFile.id 
          ? { ...file, status: "uploading", progress: 0 } 
          : file
      )
    );
    
    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setFiles(prev => 
          prev.map(file => 
            file.id === uploadFile.id && file.status === "uploading" && file.progress < 90
              ? { ...file, progress: file.progress + 10 }
              : file
          )
        );
      }, 500);
      
      // Upload the file
      const result = await uploadMutation.mutateAsync(uploadFile.file);
      
      // Clear the progress interval
      clearInterval(progressInterval);
      
      // Update file status to success
      setFiles(prev => 
        prev.map(file => 
          file.id === uploadFile.id 
            ? { 
                ...file, 
                status: "success", 
                progress: 100,
                // Using the mock file URL since we don't have a real server to upload to
                previewUrl: file.previewUrl || URL.createObjectURL(file.file)
              } 
            : file
        )
      );
      
      toast({
        title: "Upload successful",
        description: "Floor plan has been uploaded successfully.",
      });
      
      // Call the callback
      if (onFileUploaded) {
        const successFile = files.find(f => f.id === uploadFile.id);
        if (successFile && successFile.previewUrl) {
          onFileUploaded(successFile.file, successFile.previewUrl);
        }
      }
    } catch (error) {
      console.error("Upload error:", error);
      
      // Update file status to error
      setFiles(prev => 
        prev.map(file => 
          file.id === uploadFile.id 
            ? { 
                ...file, 
                status: "error", 
                progress: 0,
                error: error instanceof Error ? error.message : "Unknown error" 
              } 
            : file
        )
      );
    }
  };
  
  // Remove file from the list
  const removeFile = (id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id);
      if (fileToRemove?.previewUrl) {
        URL.revokeObjectURL(fileToRemove.previewUrl);
      }
      return prev.filter(file => file.id !== id);
    });
  };
  
  // Retry uploading a file
  const retryUpload = (uploadFile: UploadFile) => {
    handleFileUpload(uploadFile);
  };
  
  // View file preview
  const viewFilePreview = (file: UploadFile) => {
    if (file.previewUrl) {
      setPreviewFile(file);
      setPreviewOpen(true);
    }
  };
  
  return (
    <AICard>
      <div className="space-y-6">
        <div className="flex flex-col items-center justify-center space-y-3">
          <h3 className="text-lg font-medium">Upload Floor Plan</h3>
          <p className="text-sm text-muted-foreground">
            Upload floor plan images in JPG, PNG, or PDF format
          </p>
        </div>
        
        {/* Dropzone area */}
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-8
            flex flex-col items-center justify-center space-y-3
            cursor-pointer transition-colors
            ${isDragActive ? 'border-primary bg-primary/5' : 'border-border'}
            hover:border-primary/50 hover:bg-primary/5
          `}
        >
          <input {...getInputProps()} />
          
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            <UploadCloud className="h-6 w-6 text-primary" />
          </div>
          
          <div className="text-center">
            <p className="text-sm font-medium mb-1">
              {isDragActive ? "Drop the files here..." : "Drag & drop files here"}
            </p>
            <p className="text-xs text-muted-foreground">
              or click to browse
            </p>
          </div>
        </div>
        
        {/* File list */}
        {files.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Uploaded Files</h4>
            
            <div className="space-y-3">
              {files.map((file) => (
                <div 
                  key={file.id} 
                  className="flex items-center justify-between p-3 border border-border rounded-lg"
                >
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="h-10 w-10 bg-primary/10 rounded-md flex items-center justify-center">
                      <File className="h-5 w-5 text-primary" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{file.file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {(file.file.size / 1024 / 1024).toFixed(2)} MB • {file.status}
                      </p>
                      
                      {file.status === "uploading" && (
                        <Progress value={file.progress} className="h-1 mt-2" />
                      )}
                      
                      {file.status === "error" && (
                        <p className="text-xs text-destructive mt-1">{file.error}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    {file.status === "success" && (
                      <Button 
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => viewFilePreview(file)}
                      >
                        <Image className="h-4 w-4" />
                      </Button>
                    )}
                    
                    {file.status === "error" && (
                      <Button 
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => retryUpload(file)}
                      >
                        <AlertCircle className="h-4 w-4 text-destructive" />
                      </Button>
                    )}
                    
                    {file.status === "uploading" ? (
                      <div className="h-8 w-8 flex items-center justify-center">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </div>
                    ) : (
                      <Button 
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => removeFile(file.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* File preview dialog */}
        <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
          <DialogContent className="sm:max-w-3xl">
            <DialogHeader>
              <DialogTitle>
                {previewFile?.file.name}
              </DialogTitle>
            </DialogHeader>
            
            {previewFile && previewFile.previewUrl && (
              <div className="mt-4">
                <FloorPlanPreview 
                  file={previewFile.file} 
                  imageUrl={previewFile.previewUrl} 
                />
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AICard>
  );
}