import { useState } from "react";
import { format } from "date-fns";
import { Check, Clock, FileText, History, Layers, Calendar, MoreVertical, Eye, Download, FileEdit, Copy, Trash } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { FloorPlanViewer } from "@/components/floor-plan-viewer";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export interface FloorPlanVersion {
  id: string;
  name: string;
  description?: string;
  fileUrl: string;
  thumbnailUrl: string;
  createdBy: string;
  createdAt: Date;
  isLatest: boolean;
  changes?: string[];
}

interface FloorPlanVersionsProps {
  projectId: number;
  versions: FloorPlanVersion[];
  onSelectVersion?: (version: FloorPlanVersion) => void;
  onUploadNewVersion?: () => void;
  onCompareVersions?: (version1: FloorPlanVersion, version2: FloorPlanVersion) => void;
}

export function FloorPlanVersions({ projectId, versions, onSelectVersion, onUploadNewVersion, onCompareVersions }: FloorPlanVersionsProps) {
  const [selectedVersion, setSelectedVersion] = useState<FloorPlanVersion | null>(
    versions.length > 0 ? versions.find(v => v.isLatest) || versions[0] : null
  );
  const [compareVersion, setCompareVersion] = useState<FloorPlanVersion | null>(null);
  const [isComparing, setIsComparing] = useState(false);
  const [showVersionDetail, setShowVersionDetail] = useState(false);
  
  // Sort versions by date (newest first)
  const sortedVersions = [...versions].sort((a, b) => 
    b.createdAt.getTime() - a.createdAt.getTime()
  );
  
  const handleSelectVersion = (version: FloorPlanVersion) => {
    setSelectedVersion(version);
    
    if (onSelectVersion) {
      onSelectVersion(version);
    }
  };
  
  const handleSetCompareVersion = (version: FloorPlanVersion) => {
    if (compareVersion && compareVersion.id === version.id) {
      setCompareVersion(null);
    } else {
      setCompareVersion(version);
    }
  };
  
  const handleStartCompare = () => {
    if (selectedVersion && compareVersion && onCompareVersions) {
      onCompareVersions(selectedVersion, compareVersion);
      setIsComparing(true);
    }
  };
  
  const handleCancelCompare = () => {
    setCompareVersion(null);
    setIsComparing(false);
  };
  
  const formatDate = (date: Date) => {
    return format(date, "MMM d, yyyy 'at' h:mm a");
  };
  
  // When no versions are available
  if (versions.length === 0) {
    return (
      <AICard>
        <div className="flex flex-col items-center justify-center p-12 text-center">
          <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <FileText className="h-8 w-8 text-primary" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Floor Plans</h3>
          <p className="text-sm text-muted-foreground mb-6 max-w-md">
            No floor plans have been uploaded for this project yet. Upload a floor plan to get started with electrical estimation.
          </p>
          <Button onClick={onUploadNewVersion}>Upload Floor Plan</Button>
        </div>
      </AICard>
    );
  }
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-2">
        <AICard className="h-full flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">
              {isComparing 
                ? "Compare Floor Plans" 
                : selectedVersion 
                  ? selectedVersion.name 
                  : "Floor Plan Viewer"
              }
            </h3>
            <div className="flex space-x-2">
              {compareVersion && (
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleCancelCompare}
                  >
                    Cancel
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={handleStartCompare}
                  >
                    Compare Versions
                  </Button>
                </div>
              )}
              
              {!isComparing && selectedVersion && (
                <Dialog open={showVersionDetail} onOpenChange={setShowVersionDetail}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">Version Details</Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[550px]">
                    <DialogHeader>
                      <DialogTitle>{selectedVersion.name}</DialogTitle>
                      <DialogDescription>
                        Version details and metadata
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Description</h4>
                        <p className="text-sm text-muted-foreground">
                          {selectedVersion.description || "No description provided"}
                        </p>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium">Created By</h4>
                          <p className="text-sm text-muted-foreground">{selectedVersion.createdBy}</p>
                        </div>
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium">Created At</h4>
                          <p className="text-sm text-muted-foreground">{formatDate(selectedVersion.createdAt)}</p>
                        </div>
                      </div>
                      
                      {selectedVersion.changes && selectedVersion.changes.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium">Changes</h4>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {selectedVersion.changes.map((change, index) => (
                              <li key={index} className="flex items-start">
                                <Check className="h-4 w-4 mr-2 mt-0.5 text-green-500" />
                                <span>{change}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowVersionDetail(false)}>Close</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
          
          {/* Floor Plan Viewer */}
          <div className="flex-1">
            {isComparing && selectedVersion && compareVersion ? (
              <Tabs defaultValue="side-by-side">
                <TabsList className="mb-4">
                  <TabsTrigger value="side-by-side">Side by Side</TabsTrigger>
                  <TabsTrigger value="overlay">Overlay</TabsTrigger>
                </TabsList>
                
                <TabsContent value="side-by-side" className="mt-0">
                  <div className="grid grid-cols-2 gap-4 h-full">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Badge variant="outline" className="font-normal">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(selectedVersion.createdAt)}
                        </Badge>
                        <p className="text-xs font-medium">{selectedVersion.name}</p>
                      </div>
                      <div className="h-[400px]">
                        <FloorPlanViewer imageUrl={selectedVersion.fileUrl} readOnly />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Badge variant="outline" className="font-normal">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(compareVersion.createdAt)}
                        </Badge>
                        <p className="text-xs font-medium">{compareVersion.name}</p>
                      </div>
                      <div className="h-[400px]">
                        <FloorPlanViewer imageUrl={compareVersion.fileUrl} readOnly />
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="overlay" className="mt-0 h-full">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-4">
                        <Badge variant="outline" className="font-normal flex items-center">
                          <div className="h-3 w-3 mr-1 bg-primary rounded-full"></div>
                          {selectedVersion.name}
                        </Badge>
                        
                        <Badge variant="outline" className="font-normal flex items-center">
                          <div className="h-3 w-3 mr-1 bg-secondary rounded-full"></div>
                          {compareVersion.name}
                        </Badge>
                      </div>
                      
                      <Button variant="outline" size="sm" onClick={handleCancelCompare}>
                        Exit Comparison
                      </Button>
                    </div>
                    
                    <div className="h-[400px] relative border border-border rounded-md overflow-hidden">
                      {/* Overlay visualization would go here */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <p className="text-muted-foreground">Overlay view is in development</p>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            ) : selectedVersion ? (
              <FloorPlanViewer imageUrl={selectedVersion.fileUrl} />
            ) : (
              <div className="h-full flex items-center justify-center">
                <p className="text-muted-foreground">Select a floor plan version to view</p>
              </div>
            )}
          </div>
        </AICard>
      </div>
      
      <div>
        <AICard>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Version History</h3>
            <Button size="sm" onClick={onUploadNewVersion}>
              Upload New Version
            </Button>
          </div>
          
          <div className="space-y-2 max-h-[500px] overflow-y-auto pr-2">
            {sortedVersions.map((version) => (
              <div 
                key={version.id}
                className={`
                  p-3 rounded-lg border 
                  ${selectedVersion?.id === version.id ? 'border-primary bg-primary/5' : 'border-border'} 
                  ${compareVersion?.id === version.id ? 'ring-2 ring-secondary' : ''}
                  hover:bg-muted/50 cursor-pointer transition-colors
                `}
                onClick={() => handleSelectVersion(version)}
              >
                <div className="flex justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <FileText className="h-5 w-5 text-primary" />
                      {version.isLatest && (
                        <div className="absolute -top-1 -right-1 bg-primary rounded-full w-3 h-3 border-2 border-background"></div>
                      )}
                    </div>
                    <h4 className="font-medium text-sm truncate max-w-[150px]">{version.name}</h4>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Options</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleSelectVersion(version)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSetCompareVersion(version)}>
                        <Layers className="h-4 w-4 mr-2" />
                        {compareVersion?.id === version.id ? "Cancel Compare" : "Compare"}
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <FileEdit className="h-4 w-4 mr-2" />
                        Edit Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      {!version.isLatest && (
                        <DropdownMenuItem className="text-destructive">
                          <Trash className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <div className="flex items-center text-xs text-muted-foreground mb-2">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>{formatDate(version.createdAt)}</span>
                </div>
                
                {version.isLatest && (
                  <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 text-[10px]">Latest</Badge>
                )}
                
                {compareVersion && selectedVersion && version.id !== selectedVersion.id && version.id !== compareVersion.id && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs h-6 mt-2 w-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSetCompareVersion(version);
                    }}
                  >
                    <Layers className="h-3 w-3 mr-1" />
                    Compare with this
                  </Button>
                )}
              </div>
            ))}
          </div>
        </AICard>
      </div>
    </div>
  );
}