import { useState, useRef, useEffect } from "react";
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  RotateCw, 
  Maximize, 
  Move, 
  Ruler, 
  Hand, 
  Crosshair, 
  Download
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Toggle } from "@/components/ui/toggle";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Slider } from "@/components/ui/slider";
import { AICard } from "@/components/ai-card";

export interface FloorPlanViewerProps {
  imageUrl: string;
  onMeasure?: (start: { x: number; y: number }, end: { x: number; y: number }, distance: number) => void;
  readOnly?: boolean;
}

type ViewerTool = "pan" | "zoom" | "measure" | "none";

export function FloorPlanViewer({ imageUrl, onMeasure, readOnly = false }: FloorPlanViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);
  
  const [activeTool, setActiveTool] = useState<ViewerTool>("none");
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [measureStart, setMeasureStart] = useState<{ x: number; y: number } | null>(null);
  const [measureEnd, setMeasureEnd] = useState<{ x: number; y: number } | null>(null);
  const [measurements, setMeasurements] = useState<Array<{
    start: { x: number; y: number };
    end: { x: number; y: number };
    distance: number;
  }>>([]);
  
  // Load the image
  useEffect(() => {
    if (!imageUrl) return;
    
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      imageRef.current = img;
      drawCanvas();
    };
  }, [imageUrl]);
  
  // Update canvas when any view parameters change
  useEffect(() => {
    drawCanvas();
  }, [scale, rotation, position, measureStart, measureEnd, measurements]);
  
  // Draw the floor plan and measurements on the canvas
  const drawCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    const img = imageRef.current;
    
    if (!canvas || !ctx || !img) return;
    
    // Resize canvas to fit container
    if (containerRef.current) {
      canvas.width = containerRef.current.clientWidth;
      canvas.height = containerRef.current.clientHeight;
    }
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Save context state
    ctx.save();
    
    // Apply transformations
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    ctx.translate(centerX + position.x, centerY + position.y);
    ctx.rotate((rotation * Math.PI) / 180);
    ctx.scale(scale, scale);
    
    // Calculate image positioning to center it
    const imgX = -img.width / 2;
    const imgY = -img.height / 2;
    
    // Draw the image
    ctx.drawImage(img, imgX, imgY);
    
    // Restore context state
    ctx.restore();
    
    // Draw measurement line if in progress
    if (measureStart && measureEnd) {
      ctx.save();
      
      // Set line style
      ctx.strokeStyle = "#1a73e8";
      ctx.lineWidth = 2;
      
      // Draw line
      ctx.beginPath();
      ctx.moveTo(measureStart.x, measureStart.y);
      ctx.lineTo(measureEnd.x, measureEnd.y);
      ctx.stroke();
      
      // Draw start and end points
      ctx.fillStyle = "#1a73e8";
      ctx.beginPath();
      ctx.arc(measureStart.x, measureStart.y, 4, 0, 2 * Math.PI);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(measureEnd.x, measureEnd.y, 4, 0, 2 * Math.PI);
      ctx.fill();
      
      // Draw distance text
      const distance = Math.sqrt(
        Math.pow(measureEnd.x - measureStart.x, 2) + 
        Math.pow(measureEnd.y - measureStart.y, 2)
      );
      const textX = (measureStart.x + measureEnd.x) / 2;
      const textY = (measureStart.y + measureEnd.y) / 2 - 10;
      
      ctx.font = "14px Arial";
      ctx.fillStyle = "#ffffff";
      ctx.textAlign = "center";
      ctx.fillRect(textX - 40, textY - 15, 80, 20);
      ctx.fillStyle = "#1a73e8";
      ctx.fillText(`${(distance / 50).toFixed(2)} m`, textX, textY);
      
      ctx.restore();
    }
    
    // Draw saved measurements
    measurements.forEach(({ start, end, distance }) => {
      ctx.save();
      
      // Set line style
      ctx.strokeStyle = "#34a853";
      ctx.lineWidth = 2;
      
      // Draw line
      ctx.beginPath();
      ctx.moveTo(start.x, start.y);
      ctx.lineTo(end.x, end.y);
      ctx.stroke();
      
      // Draw start and end points
      ctx.fillStyle = "#34a853";
      ctx.beginPath();
      ctx.arc(start.x, start.y, 4, 0, 2 * Math.PI);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(end.x, end.y, 4, 0, 2 * Math.PI);
      ctx.fill();
      
      // Draw distance text
      const textX = (start.x + end.x) / 2;
      const textY = (start.y + end.y) / 2 - 10;
      
      ctx.font = "14px Arial";
      ctx.fillStyle = "#ffffff";
      ctx.textAlign = "center";
      ctx.fillRect(textX - 40, textY - 15, 80, 20);
      ctx.fillStyle = "#34a853";
      ctx.fillText(`${(distance / 50).toFixed(2)} m`, textX, textY);
      
      ctx.restore();
    });
  };
  
  // Handle mouse down event
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    if (activeTool === "pan") {
      setIsDragging(true);
      setDragStart({ x, y });
    } else if (activeTool === "measure") {
      setMeasureStart({ x, y });
      setMeasureEnd({ x, y });
    }
  };
  
  // Handle mouse move event
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    if (activeTool === "pan" && isDragging) {
      const deltaX = x - dragStart.x;
      const deltaY = y - dragStart.y;
      
      setPosition(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      
      setDragStart({ x, y });
    } else if (activeTool === "measure" && measureStart) {
      setMeasureEnd({ x, y });
    }
  };
  
  // Handle mouse up event
  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (activeTool === "pan") {
      setIsDragging(false);
    } else if (activeTool === "measure" && measureStart && measureEnd) {
      const distance = Math.sqrt(
        Math.pow(measureEnd.x - measureStart.x, 2) + 
        Math.pow(measureEnd.y - measureStart.y, 2)
      );
      
      // Save the measurement
      setMeasurements(prev => [
        ...prev,
        { start: measureStart, end: measureEnd, distance }
      ]);
      
      // Call the onMeasure callback if provided
      if (onMeasure) {
        onMeasure(measureStart, measureEnd, distance);
      }
      
      // Reset measurement points
      setMeasureStart(null);
      setMeasureEnd(null);
    }
  };
  
  // Handle wheel event for zooming
  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    
    if (activeTool === "zoom" || e.ctrlKey) {
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      const newScale = Math.max(0.1, Math.min(5, scale + delta));
      setScale(newScale);
    }
  };
  
  // Handle zoom in button
  const handleZoomIn = () => {
    const newScale = Math.min(5, scale + 0.1);
    setScale(newScale);
  };
  
  // Handle zoom out button
  const handleZoomOut = () => {
    const newScale = Math.max(0.1, scale - 0.1);
    setScale(newScale);
  };
  
  // Handle rotate clockwise button
  const handleRotateCw = () => {
    setRotation(prev => (prev + 90) % 360);
  };
  
  // Handle rotate counter-clockwise button
  const handleRotateCcw = () => {
    setRotation(prev => (prev - 90 + 360) % 360);
  };
  
  // Handle reset view button
  const handleResetView = () => {
    setScale(1);
    setRotation(0);
    setPosition({ x: 0, y: 0 });
    setMeasurements([]);
  };
  
  // Handle download button
  const handleDownload = () => {
    if (!canvasRef.current) return;
    
    const link = document.createElement("a");
    link.download = "floor-plan.png";
    link.href = canvasRef.current.toDataURL("image/png");
    link.click();
  };
  
  // Set cursor style based on active tool
  const getCursorStyle = (): string => {
    switch (activeTool) {
      case "pan":
        return "grab";
      case "measure":
        return "crosshair";
      case "zoom":
        return "zoom-in";
      default:
        return "default";
    }
  };
  
  return (
    <AICard>
      <div className="space-y-4">
        {/* Toolbar */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-1">
            {/* Tool toggles */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Toggle 
                    pressed={activeTool === "pan"} 
                    onPressedChange={() => setActiveTool(activeTool === "pan" ? "none" : "pan")}
                    variant="outline" 
                    size="sm"
                  >
                    <Hand className="h-4 w-4" />
                  </Toggle>
                </TooltipTrigger>
                <TooltipContent>Pan tool</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Toggle 
                    pressed={activeTool === "zoom"} 
                    onPressedChange={() => setActiveTool(activeTool === "zoom" ? "none" : "zoom")}
                    variant="outline" 
                    size="sm"
                  >
                    <Crosshair className="h-4 w-4" />
                  </Toggle>
                </TooltipTrigger>
                <TooltipContent>Zoom tool</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Toggle 
                    pressed={activeTool === "measure"} 
                    onPressedChange={() => setActiveTool(activeTool === "measure" ? "none" : "measure")}
                    variant="outline" 
                    size="sm"
                    disabled={readOnly}
                  >
                    <Ruler className="h-4 w-4" />
                  </Toggle>
                </TooltipTrigger>
                <TooltipContent>Measure tool</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          <div className="flex space-x-1">
            {/* View controls */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleZoomIn}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Zoom in</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleZoomOut}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Zoom out</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleRotateCw}>
                    <RotateCw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Rotate clockwise</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleRotateCcw}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Rotate counter-clockwise</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleResetView}>
                    <Maximize className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Reset view</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleDownload}>
                    <Download className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Download</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        
        {/* Zoom slider */}
        <div className="flex items-center space-x-2">
          <ZoomOut className="h-4 w-4 text-muted-foreground" />
          <Slider
            className="flex-1"
            min={0.1}
            max={5}
            step={0.1}
            value={[scale]}
            onValueChange={(vals) => setScale(vals[0])}
          />
          <ZoomIn className="h-4 w-4 text-muted-foreground" />
          <div className="w-16 text-xs text-muted-foreground">
            {(scale * 100).toFixed(0)}%
          </div>
        </div>
        
        {/* Canvas container */}
        <div 
          ref={containerRef} 
          className="border border-border rounded-md overflow-hidden"
          style={{ height: "500px" }}
        >
          <canvas
            ref={canvasRef}
            style={{ cursor: getCursorStyle() }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onWheel={handleWheel}
          />
        </div>
        
        {/* Status bar */}
        <div className="flex justify-between text-xs text-muted-foreground">
          <div>{`Zoom: ${(scale * 100).toFixed(0)}%`}</div>
          <div>{`Rotation: ${rotation}°`}</div>
          <div>{`Measurements: ${measurements.length}`}</div>
        </div>
      </div>
    </AICard>
  );
}