import { useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Menu, 
  Search, 
  Settings,
  HelpCircle, 
  User,
  LogOut,
  BarChart 
} from "lucide-react";
import { useSupabaseAuth } from "@/hooks/use-supabase-auth";
import { SignOutButton } from "@/components/sign-out-button";
import { ThemeToggle } from "@/components/theme-toggle";

export function Header() {
  const [location, setLocation] = useLocation();
  const { user } = useSupabaseAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const fullName = user?.full_name || "";
  const initials = fullName
    ? fullName.split(" ").map((n: string) => n[0]).join("").toUpperCase()
    : "U";

  return (
    <header className="bg-background/95 backdrop-blur-sm border-b border-border/40 z-10 sticky top-0">
      <div className="flex items-center justify-between px-4 py-2">
        <div className="flex items-center">
          <Button 
            variant="ghost" 
            size="icon" 
            className="md:hidden mr-2 rounded-full hover:bg-muted/30"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          <h1 className="text-lg font-normal ml-2 md:ml-0">
            {location === "/" 
              ? "Dashboard" 
              : location.startsWith("/project/")
                ? "Project Details"
                : location.includes("estimation")
                  ? "Project Estimation"
                  : "CoElec"}
          </h1>
        </div>
        
        <div className="flex items-center">
          <div className="relative hidden md:block mr-2">
            <div className="flex items-center h-9 rounded-full bg-muted/30 px-3 focus-within:ring-1 focus-within:ring-primary">
              <Search className="h-4 w-4 text-muted-foreground mr-2" />
              <Input 
                placeholder="Search..." 
                className="border-0 focus-visible:ring-0 bg-transparent h-9 w-48 lg:w-64 pl-0" 
              />
            </div>
          </div>
          
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-muted-foreground rounded-full hover:bg-muted/30 mx-1"
            onClick={() => setLocation("/analytics")}
          >
            <BarChart className="h-5 w-5" />
          </Button>
          
          <Button variant="ghost" size="icon" className="text-muted-foreground rounded-full hover:bg-muted/30 mx-1">
            <Settings className="h-5 w-5" />
          </Button>
          
          <Button variant="ghost" size="icon" className="text-muted-foreground rounded-full hover:bg-muted/30 mx-1">
            <HelpCircle className="h-5 w-5" />
          </Button>
          
          <div className="mx-1">
            <ThemeToggle />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-9 w-9 rounded-full bg-primary/10 text-primary ml-1 hover:bg-primary/20"
              >
                {initials}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 mt-1 rounded-xl bg-popover/95 backdrop-blur-sm" sideOffset={8}>
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="cursor-pointer" onClick={() => setLocation("/profile")}>
                <User className="mr-2 h-4 w-4" /> Profile
              </DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer" onClick={() => setLocation("/settings")}>
                <Settings className="mr-2 h-4 w-4" /> Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="cursor-pointer text-destructive focus:text-destructive" 
                asChild
              >
                <SignOutButton variant="ghost" />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      {isMobileMenuOpen && (
        <div className="md:hidden border-t border-border/40">
          <div className="p-4">
            <div className="flex items-center h-9 rounded-full bg-muted/30 px-3 mb-4">
              <Search className="h-4 w-4 text-muted-foreground mr-2" />
              <Input 
                placeholder="Search..." 
                className="border-0 focus-visible:ring-0 bg-transparent h-9 pl-0" 
              />
            </div>
            
            <div className="space-y-2">
              <Button variant="ghost" className="w-full justify-start rounded-lg" onClick={() => setLocation("/")}>
                <LayoutDashboard className="h-4 w-4 mr-2" /> Dashboard
              </Button>
              <Button variant="ghost" className="w-full justify-start rounded-lg" onClick={() => setLocation("/analytics")}>
                <BarChart className="h-4 w-4 mr-2" /> Analytics Dashboard
              </Button>
              <Button variant="ghost" className="w-full justify-start rounded-lg" onClick={() => setLocation("/project-estimation")}>
                <PencilRuler className="h-4 w-4 mr-2" /> Project Estimation
              </Button>
              <Button variant="ghost" className="w-full justify-start rounded-lg" onClick={() => setLocation("/floor-plans")}>
                <FileImage className="h-4 w-4 mr-2" /> Floor Plans
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}

// Import this at the top of the file
import { LayoutDashboard, PencilRuler, FileImage } from "lucide-react";
