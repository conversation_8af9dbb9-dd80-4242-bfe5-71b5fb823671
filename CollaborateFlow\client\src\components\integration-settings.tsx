import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { 
  Link, 
  Check, 
  X, 
  Loader2, 
  ExternalLink, 
  Plus,
  RefreshCw
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const integrationFormSchema = z.object({
  apiKey: z.string().min(1, {
    message: "API key is required.",
  }),
  webhookUrl: z.string().url({
    message: "Please enter a valid URL.",
  }).optional().or(z.literal("")),
});

interface IntegrationSettingsProps {
  onIntegrationStatusChange?: (integration: string, status: boolean) => void;
}

export function IntegrationSettings({ onIntegrationStatusChange }: IntegrationSettingsProps) {
  const { toast } = useToast();
  const [activeSyncItems, setActiveSyncItems] = useState<Record<string, boolean>>({
    customers: true,
    invoices: true,
    estimates: false,
    expenses: false,
  });
  
  const [activeIntegrations, setActiveIntegrations] = useState<Record<string, boolean>>({
    quickbooks: true,
    stripe: true,
    google: false,
    twilio: false,
    sendgrid: false,
    zapier: false,
  });
  
  const [isApiKeyDialogOpen, setIsApiKeyDialogOpen] = useState(false);
  const [currentIntegration, setCurrentIntegration] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState(false);
  
  const form = useForm<z.infer<typeof integrationFormSchema>>({
    resolver: zodResolver(integrationFormSchema),
    defaultValues: {
      apiKey: "",
      webhookUrl: "",
    },
  });
  
  const toggleIntegration = (integration: string) => {
    const newStatus = !activeIntegrations[integration];
    
    if (newStatus && !integration.includes("_connected")) {
      // If turning on, open the API key dialog
      setCurrentIntegration(integration);
      setIsApiKeyDialogOpen(true);
    } else {
      // If turning off, just update the state
      updateIntegrationStatus(integration, newStatus);
    }
  };
  
  const updateIntegrationStatus = (integration: string, status: boolean) => {
    setActiveIntegrations(prev => ({
      ...prev,
      [integration]: status,
    }));
    
    if (onIntegrationStatusChange) {
      onIntegrationStatusChange(integration, status);
    }
    
    toast({
      title: status ? "Integration Enabled" : "Integration Disabled",
      description: `${integration.charAt(0).toUpperCase() + integration.slice(1)} integration has been ${status ? "enabled" : "disabled"}.`,
    });
  };
  
  const handleSyncToggle = (item: string) => {
    setActiveSyncItems(prev => ({
      ...prev,
      [item]: !prev[item],
    }));
    
    toast({
      title: activeSyncItems[item] ? "Sync Disabled" : "Sync Enabled",
      description: `${item.charAt(0).toUpperCase() + item.slice(1)} synchronization has been ${activeSyncItems[item] ? "disabled" : "enabled"}.`,
    });
  };
  
  const onSubmitApiKey = (data: z.infer<typeof integrationFormSchema>) => {
    setIsVerifying(true);
    
    // Simulate API verification
    setTimeout(() => {
      setIsVerifying(false);
      setIsApiKeyDialogOpen(false);
      updateIntegrationStatus(currentIntegration, true);
      form.reset();
      
      toast({
        title: "API Key Verified",
        description: `Your ${currentIntegration.charAt(0).toUpperCase() + currentIntegration.slice(1)} integration has been set up successfully.`,
      });
    }, 1500);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Integration Settings</h3>
        <p className="text-sm text-muted-foreground">
          Connect CoElec with your favorite tools and services
        </p>
      </div>
      
      <Separator />
      
      {/* Active Integrations */}
      <div>
        <h4 className="text-md font-medium mb-4">Active Integrations</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* QuickBooks Integration */}
          {activeIntegrations.quickbooks && (
            <div className="border rounded-md p-4">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-[#2CA01C]/10 p-2 rounded-md">
                    <div className="w-8 h-8 bg-[#2CA01C] rounded-md flex items-center justify-center text-white font-bold">
                      QB
                    </div>
                  </div>
                  <div>
                    <h5 className="font-medium">QuickBooks</h5>
                    <p className="text-xs text-muted-foreground">Financial accounting</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-xs flex items-center text-green-600">
                    <Check className="h-3 w-3 mr-1" />
                    Connected
                  </div>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="sync-customers" className="text-sm flex-1">Customers</Label>
                  <Switch
                    id="sync-customers"
                    checked={activeSyncItems.customers}
                    onCheckedChange={() => handleSyncToggle("customers")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="sync-invoices" className="text-sm flex-1">Invoices</Label>
                  <Switch
                    id="sync-invoices"
                    checked={activeSyncItems.invoices}
                    onCheckedChange={() => handleSyncToggle("invoices")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="sync-estimates" className="text-sm flex-1">Estimates</Label>
                  <Switch
                    id="sync-estimates"
                    checked={activeSyncItems.estimates}
                    onCheckedChange={() => handleSyncToggle("estimates")}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="sync-expenses" className="text-sm flex-1">Expenses</Label>
                  <Switch
                    id="sync-expenses"
                    checked={activeSyncItems.expenses}
                    onCheckedChange={() => handleSyncToggle("expenses")}
                  />
                </div>
              </div>
              
              <div className="mt-4 flex justify-between">
                <Button variant="outline" size="sm">
                  Settings
                </Button>
                <Button variant="outline" size="sm" className="text-destructive">
                  Disconnect
                </Button>
              </div>
            </div>
          )}
          
          {/* Stripe Integration */}
          {activeIntegrations.stripe && (
            <div className="border rounded-md p-4">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-[#6772E5]/10 p-2 rounded-md">
                    <div className="w-8 h-8 bg-[#6772E5] rounded-md flex items-center justify-center text-white font-bold">
                      S
                    </div>
                  </div>
                  <div>
                    <h5 className="font-medium">Stripe</h5>
                    <p className="text-xs text-muted-foreground">Payment processing</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-xs flex items-center text-green-600">
                    <Check className="h-3 w-3 mr-1" />
                    Connected
                  </div>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="text-sm">
                <p className="mb-2">
                  <span className="font-medium">Account:</span>{" "}
                  <span className="text-muted-foreground"><EMAIL></span>
                </p>
                <p>
                  <span className="font-medium">Mode:</span>{" "}
                  <span className="px-2 py-0.5 bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300 rounded-full text-xs">Test Mode</span>
                </p>
                <Button variant="link" className="text-xs px-0 h-auto mt-1" asChild>
                  <a href="#" target="_blank" rel="noopener noreferrer">
                    View Stripe Dashboard
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </Button>
              </div>
              
              <div className="mt-4 flex justify-between">
                <Button variant="outline" size="sm">
                  Settings
                </Button>
                <Button variant="outline" size="sm" className="text-destructive">
                  Disconnect
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Available Integrations */}
      <div>
        <h4 className="text-md font-medium mb-4">Available Integrations</h4>
        <div className="border rounded-md overflow-hidden">
          <Accordion type="single" collapsible className="w-full">
            {/* Accounting & Finance */}
            <AccordionItem value="accounting">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <span className="font-medium">Accounting & Finance</span>
              </AccordionTrigger>
              <AccordionContent className="border-t px-0 pb-0">
                <div className="divide-y">
                  {/* QuickBooks - Already Connected, hidden if not active */}
                  {!activeIntegrations.quickbooks && (
                    <div className="flex items-center justify-between p-4">
                      <div className="flex items-center gap-3">
                        <div className="bg-[#2CA01C]/10 p-2 rounded-md">
                          <div className="w-8 h-8 bg-[#2CA01C] rounded-md flex items-center justify-center text-white font-bold">
                            QB
                          </div>
                        </div>
                        <div>
                          <h5 className="font-medium">QuickBooks</h5>
                          <p className="text-xs text-muted-foreground">Sync customers, invoices and payments</p>
                        </div>
                      </div>
                      <Button onClick={() => toggleIntegration("quickbooks")} variant="outline">
                        Connect
                      </Button>
                    </div>
                  )}
                  
                  {/* Xero */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#13B5EA]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#13B5EA] rounded-md flex items-center justify-center text-white font-bold">
                          X
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">Xero</h5>
                        <p className="text-xs text-muted-foreground">Accounting software alternative to QuickBooks</p>
                      </div>
                    </div>
                    <Button variant="outline">Connect</Button>
                  </div>
                  
                  {/* FreshBooks */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#0D827E]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#0D827E] rounded-md flex items-center justify-center text-white font-bold">
                          FB
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">FreshBooks</h5>
                        <p className="text-xs text-muted-foreground">Invoicing and expense tracking</p>
                      </div>
                    </div>
                    <Button variant="outline">Connect</Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            {/* Payment Processing */}
            <AccordionItem value="payments">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <span className="font-medium">Payment Processing</span>
              </AccordionTrigger>
              <AccordionContent className="border-t px-0 pb-0">
                <div className="divide-y">
                  {/* Stripe - Already Connected, hidden if not active */}
                  {!activeIntegrations.stripe && (
                    <div className="flex items-center justify-between p-4">
                      <div className="flex items-center gap-3">
                        <div className="bg-[#6772E5]/10 p-2 rounded-md">
                          <div className="w-8 h-8 bg-[#6772E5] rounded-md flex items-center justify-center text-white font-bold">
                            S
                          </div>
                        </div>
                        <div>
                          <h5 className="font-medium">Stripe</h5>
                          <p className="text-xs text-muted-foreground">Payment processing for invoices</p>
                        </div>
                      </div>
                      <Button onClick={() => toggleIntegration("stripe")} variant="outline">
                        Connect
                      </Button>
                    </div>
                  )}
                  
                  {/* PayPal */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#003087]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#003087] rounded-md flex items-center justify-center text-white font-bold">
                          PP
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">PayPal</h5>
                        <p className="text-xs text-muted-foreground">Additional payment option for clients</p>
                      </div>
                    </div>
                    <Button variant="outline">Connect</Button>
                  </div>
                  
                  {/* Square */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#3E4348]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#3E4348] rounded-md flex items-center justify-center text-white font-bold">
                          SQ
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">Square</h5>
                        <p className="text-xs text-muted-foreground">In-person and online payments</p>
                      </div>
                    </div>
                    <Button variant="outline">Connect</Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            {/* Communication */}
            <AccordionItem value="communication">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <span className="font-medium">Communication</span>
              </AccordionTrigger>
              <AccordionContent className="border-t px-0 pb-0">
                <div className="divide-y">
                  {/* SendGrid */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#1A82E2]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#1A82E2] rounded-md flex items-center justify-center text-white font-bold">
                          SG
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">SendGrid</h5>
                        <p className="text-xs text-muted-foreground">Enhanced email delivery and tracking</p>
                      </div>
                    </div>
                    <Button onClick={() => toggleIntegration("sendgrid")} variant="outline">
                      {activeIntegrations.sendgrid ? "Configured" : "Connect"}
                    </Button>
                  </div>
                  
                  {/* Twilio */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#F22F46]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#F22F46] rounded-md flex items-center justify-center text-white font-bold">
                          TW
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">Twilio</h5>
                        <p className="text-xs text-muted-foreground">SMS notifications and reminders</p>
                      </div>
                    </div>
                    <Button onClick={() => toggleIntegration("twilio")} variant="outline">
                      {activeIntegrations.twilio ? "Configured" : "Connect"}
                    </Button>
                  </div>
                  
                  {/* Mailchimp */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#FFE01B]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#FFE01B] rounded-md flex items-center justify-center font-bold">
                          MC
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">Mailchimp</h5>
                        <p className="text-xs text-muted-foreground">Email campaigns and marketing</p>
                      </div>
                    </div>
                    <Button variant="outline">Connect</Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            {/* Other Integrations */}
            <AccordionItem value="other">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <span className="font-medium">Other Integrations</span>
              </AccordionTrigger>
              <AccordionContent className="border-t px-0 pb-0">
                <div className="divide-y">
                  {/* Google Workspace */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#4285F4]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#4285F4] rounded-md flex items-center justify-center text-white font-bold">
                          G
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">Google Workspace</h5>
                        <p className="text-xs text-muted-foreground">Calendar, Drive, and email integration</p>
                      </div>
                    </div>
                    <Button onClick={() => toggleIntegration("google")} variant="outline">
                      {activeIntegrations.google ? "Configured" : "Connect"}
                    </Button>
                  </div>
                  
                  {/* Zapier */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#FF4A00]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#FF4A00] rounded-md flex items-center justify-center text-white font-bold">
                          Z
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">Zapier</h5>
                        <p className="text-xs text-muted-foreground">Connect to 3,000+ apps and automate workflows</p>
                      </div>
                    </div>
                    <Button onClick={() => toggleIntegration("zapier")} variant="outline">
                      {activeIntegrations.zapier ? "Configured" : "Connect"}
                    </Button>
                  </div>
                  
                  {/* Slack */}
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#4A154B]/10 p-2 rounded-md">
                        <div className="w-8 h-8 bg-[#4A154B] rounded-md flex items-center justify-center text-white font-bold">
                          SL
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium">Slack</h5>
                        <p className="text-xs text-muted-foreground">Team notifications and updates</p>
                      </div>
                    </div>
                    <Button variant="outline">Connect</Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
      
      {/* Webhook Configuration */}
      <div>
        <h4 className="text-md font-medium mb-4">Webhook Configuration</h4>
        <div className="border rounded-md p-4">
          <p className="text-sm mb-4">
            Set up webhooks to receive real-time updates when events occur in your CoElec account.
          </p>
          
          <div className="space-y-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <div className="flex gap-2">
                <Input 
                  id="webhook-url" 
                  placeholder="https://your-server.com/webhook" 
                  className="flex-1"
                />
                <Button variant="outline">Save</Button>
              </div>
              <p className="text-xs text-muted-foreground">
                This URL will receive POST requests for events like new quotes, client approvals, and project updates.
              </p>
            </div>
            
            <div>
              <h5 className="text-sm font-medium mb-2">Available Events</h5>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch id="event-quotes" />
                  <Label htmlFor="event-quotes">Quote Events</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="event-projects" />
                  <Label htmlFor="event-projects">Project Events</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="event-clients" />
                  <Label htmlFor="event-clients">Client Events</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="event-payments" />
                  <Label htmlFor="event-payments">Payment Events</Label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* API Key Dialog */}
      <Dialog open={isApiKeyDialogOpen} onOpenChange={setIsApiKeyDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Connect {currentIntegration.charAt(0).toUpperCase() + currentIntegration.slice(1)}</DialogTitle>
            <DialogDescription>
              Enter your API key to connect to {currentIntegration.charAt(0).toUpperCase() + currentIntegration.slice(1)}.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitApiKey)} className="space-y-4">
              <FormField
                control={form.control}
                name="apiKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" placeholder="Enter your API key" />
                    </FormControl>
                    <FormDescription>
                      You can find your API key in your {currentIntegration.charAt(0).toUpperCase() + currentIntegration.slice(1)} dashboard.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {(currentIntegration === "zapier" || currentIntegration === "webhook") && (
                <FormField
                  control={form.control}
                  name="webhookUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Webhook URL (Optional)</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://your-server.com/webhook" />
                      </FormControl>
                      <FormDescription>
                        Specify a URL to receive webhook notifications.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsApiKeyDialogOpen(false)}
                  disabled={isVerifying}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isVerifying}>
                  {isVerifying ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    "Connect"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}