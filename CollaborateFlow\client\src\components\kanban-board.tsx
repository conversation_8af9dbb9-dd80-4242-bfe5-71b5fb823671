import { useMutation } from "@tanstack/react-query";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AddTaskDialog } from "@/components/add-task-dialog";
import { TaskCard } from "@/components/task-card";
import { Column, Task } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Plus } from "lucide-react";
import { useState } from "react";

interface KanbanBoardProps {
  columns: Column[];
  tasks: Task[];
  projectId: number;
}

export function KanbanBoard({ columns, tasks, projectId }: KanbanBoardProps) {
  const [selectedColumn, setSelectedColumn] = useState<number | null>(null);
  const [openAddTask, setOpenAddTask] = useState(false);

  const updateTaskMutation = useMutation({
    mutationFn: async ({ taskId, columnId }: { taskId: number; columnId: number }) => {
      const res = await apiRequest("PATCH", `/api/tasks/${taskId}`, { columnId });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/tasks`] });
    },
  });

  const handleAddTask = (columnId: number) => {
    setSelectedColumn(columnId);
    setOpenAddTask(true);
  };

  const handleDragStart = (e: React.DragEvent, taskId: number) => {
    e.dataTransfer.setData('taskId', taskId.toString());
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, columnId: number) => {
    e.preventDefault();
    const taskId = parseInt(e.dataTransfer.getData('taskId'));
    
    if (taskId) {
      updateTaskMutation.mutate({ taskId, columnId });
    }
  };

  // Sort columns by order
  const sortedColumns = [...columns].sort((a, b) => a.order - b.order);

  return (
    <div className="flex space-x-6 pb-8 overflow-x-auto h-full">
      {sortedColumns.map((column) => {
        const columnTasks = tasks.filter((task) => task.columnId === column.id);
        
        return (
          <div 
            key={column.id} 
            className="kanban-column bg-muted rounded-xl"
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, column.id)}
          >
            <div className="p-4 bg-background rounded-t-xl border border-border">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-foreground">{column.name}</h3>
                <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-full">
                  {columnTasks.length}
                </span>
              </div>
            </div>
            
            <div className="p-3 border-l border-r border-border bg-muted min-h-[200px]">
              {/* Task Cards */}
              {columnTasks.map((task) => (
                <TaskCard 
                  key={task.id} 
                  task={task} 
                  onDragStart={(e) => handleDragStart(e, task.id)}
                />
              ))}
              
              {/* Add Task Button */}
              <Button 
                variant="outline" 
                className="w-full py-2 px-3 border-dashed text-muted-foreground text-sm hover:bg-background mt-2 h-auto"
                onClick={() => handleAddTask(column.id)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Task
              </Button>
            </div>
          </div>
        );
      })}
      
      {/* Add Column Button */}
      <div className="flex-shrink-0 w-64">
        <Button 
          variant="outline" 
          className="w-full p-4 border-dashed text-muted-foreground hover:bg-background h-auto"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Column
        </Button>
      </div>
      
      <AddTaskDialog 
        open={openAddTask} 
        onOpenChange={setOpenAddTask} 
        projectId={projectId} 
        columnId={selectedColumn} 
      />
    </div>
  );
}
