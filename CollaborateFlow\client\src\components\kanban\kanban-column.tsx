import { useState } from "react";
import { Droppable } from "@hello-pangea/dnd";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusIcon, MoreHorizontalIcon } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { KanbanTask } from "./kanban-task";
import { IColumn } from "./kanban-board";
import { AddTaskDialog } from "../add-task-dialog";

interface KanbanColumnProps {
  column: IColumn;
  projectId: number;
}

export function KanbanColumn({ column, projectId }: KanbanColumnProps) {
  const [showAddTask, setShowAddTask] = useState(false);

  // Get column color based on name
  const getColumnColor = (columnName: string) => {
    const colors: Record<string, string> = {
      "New Project": "bg-gray-100 border-gray-300",
      "Symbol Detection": "bg-blue-50 border-blue-300",
      "Symbol Review": "bg-purple-50 border-purple-300",
      "Material Estimation": "bg-yellow-50 border-yellow-300",
      "Labor Estimation": "bg-orange-50 border-orange-300",
      "Quote Generation": "bg-green-50 border-green-300",
      "Client Approval": "bg-red-50 border-red-300",
    };
    
    return colors[columnName] || "bg-gray-100 border-gray-300";
  };

  return (
    <Card className={`flex-shrink-0 w-80 ${getColumnColor(column.name)} border-t-4`}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-md font-medium">{column.name}</CardTitle>
          <div className="flex items-center gap-1">
            <div className="bg-gray-200 rounded-full px-2 py-0.5 text-xs font-medium">
              {column.tasks.length}
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Edit Column</DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">Delete Column</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-2">
        <Droppable droppableId={column.id.toString()}>
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`min-h-[calc(70vh-3.5rem)] rounded-md p-2 ${
                snapshot.isDraggingOver ? "bg-gray-100" : ""
              }`}
            >
              {column.tasks.map((task, index) => (
                <KanbanTask key={task.id} task={task} index={index} />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
        
        <Button
          variant="ghost"
          size="sm"
          className="w-full mt-2 text-gray-500"
          onClick={() => setShowAddTask(true)}
        >
          <PlusIcon className="mr-1 h-4 w-4" /> Add Task
        </Button>
        
        <AddTaskDialog
          open={showAddTask}
          onOpenChange={setShowAddTask}
          projectId={projectId}
          columnId={column.id}
        />
      </CardContent>
    </Card>
  );
}