import { Draggable } from "@hello-pangea/dnd";
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ITask } from "./kanban-board";
import {
  AlertCircleIcon,
  CalendarIcon,
  ClipboardIcon,
  MoreVerticalIcon,
  UserIcon,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";

interface KanbanTaskProps {
  task: ITask;
  index: number;
}

export function KanbanTask({ task, index }: KanbanTaskProps) {
  const [showDetails, setShowDetails] = useState(false);
  
  // Get priority color
  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      low: "bg-green-100 text-green-800 hover:bg-green-100",
      medium: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
      high: "bg-red-100 text-red-800 hover:bg-red-100",
    };
    
    return colors[priority] || "bg-gray-100 text-gray-800";
  };
  
  // Get initials from name
  const getInitials = (name: string) => {
    if (!name) return "?";
    const parts = name.split(" ");
    if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
    return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
  };
  
  // Format due date
  const formatDueDate = (dateString?: string | null) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  return (
    <Draggable draggableId={`task-${task.id}`} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-2 border shadow-sm ${
            snapshot.isDragging ? "shadow-md" : ""
          }`}
          style={{ ...provided.draggableProps.style }}
        >
          <CardContent className="p-3">
            <div className="flex justify-between">
              <h3 className="font-medium text-sm">{task.title}</h3>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-6 w-6">
                    <MoreVerticalIcon className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Edit Task</DropdownMenuItem>
                  <DropdownMenuItem>View Details</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-red-600">Delete Task</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            {task.description && (
              <p className="text-xs text-gray-500 mt-1 line-clamp-2">{task.description}</p>
            )}
            
            <div className="flex justify-between items-center mt-3">
              <div className="flex gap-1">
                <Badge variant="outline" className={getPriorityColor(task.priority)}>
                  {task.priority}
                </Badge>
                
                {task.dueDate && (
                  <Badge variant="outline" className="text-xs">
                    <CalendarIcon className="mr-1 h-3 w-3" />
                    {formatDueDate(task.dueDate)}
                  </Badge>
                )}
              </div>
              
              {task.assignedTo && (
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="text-xs">
                    {getInitials("User")}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </Draggable>
  );
}