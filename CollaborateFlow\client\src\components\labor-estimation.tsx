import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Users, Clock, DollarSign, Plus, Minus, FileDown, Search, Trash, Edit, RefreshCw, User, Calendar } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useToast } from "@/hooks/use-toast";
import { SymbolType, SYMBOL_TYPES } from "./symbol-editor";

interface DetectedSymbol {
  id: string;
  type: string;
  position?: { x: number; y: number };
  rotation?: number;
  scale?: number;
  selected?: boolean;
  count?: number;
}

interface LaborTask {
  id: string;
  name: string;
  category: string;
  hours: number;
  rate: number;
  level: "apprentice" | "journeyman" | "master";
  workers: number;
}

interface LaborEstimationProps {
  detectedSymbols?: DetectedSymbol[];
  onLaborTasksUpdated?: (laborTasks: LaborTask[]) => void;
}

// Labor categories
const LABOR_CATEGORIES = [
  "Outlets & Switches",
  "Light Fixtures",
  "Panels & Distribution",
  "Wiring & Conduit",
  "Data & Communications",
  "Safety & Protection",
  "Inspection & Permits",
  "Custom Tasks"
];

// Labor skill levels with hourly rates
const SKILL_LEVELS = {
  "apprentice": {
    name: "Apprentice",
    baseRate: 35
  },
  "journeyman": {
    name: "Journeyman",
    baseRate: 55
  },
  "master": {
    name: "Master Electrician",
    baseRate: 85
  }
};

export function LaborEstimation({ detectedSymbols = [], onLaborTasksUpdated }: LaborEstimationProps) {
  const [laborTasks, setLaborTasks] = useState<LaborTask[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<LaborTask[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<LaborTask | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | "all">("all");
  const [sortField, setSortField] = useState<keyof LaborTask>("category");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [isRateUpdateLoading, setIsRateUpdateLoading] = useState(false);
  const [lastRateUpdate, setLastRateUpdate] = useState<Date | null>(null);
  const { toast } = useToast();
  
  // Generate initial labor tasks from detected symbols
  useEffect(() => {
    if (detectedSymbols.length === 0) return;
    
    const generateLaborTasks = async () => {
      try {
        // Call the API endpoint to generate labor estimates
        const response = await fetch('/api/ai/estimate-labor', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ symbols: detectedSymbols }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate labor estimates');
        }
        
        const data = await response.json();
        
        if (data.success && data.laborTasks) {
          // Set the labor tasks
          setLaborTasks(data.laborTasks);
          setLastRateUpdate(new Date());
          
          if (onLaborTasksUpdated) {
            onLaborTasksUpdated(data.laborTasks);
          }
          
          toast({
            title: "Labor Tasks Generated",
            description: `${data.laborTasks.length} labor tasks have been estimated for your project.`,
          });
        } else {
          throw new Error(data.message || 'Failed to generate labor estimates');
        }
      } catch (error) {
        console.error('Labor estimation error:', error);
        toast({
          title: "Estimation Error",
          description: `Failed to generate labor estimates: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
        
        // Fallback to locally generated estimates
        generateLocalEstimates();
      }
    };
    
    // Fallback function to generate estimates locally
    const generateLocalEstimates = () => {
      const initialTasks: LaborTask[] = [];
      
      // Map symbols to labor tasks
      detectedSymbols.forEach(symbol => {
        const count = symbol.count || 1;
        
        switch(symbol.type) {
          case "outlet":
            initialTasks.push({
              id: `task-${Math.random().toString(36).substring(2, 9)}`,
              name: "Install Standard Outlets",
              category: "Outlets & Switches",
              hours: count * 0.5, // 30 min per outlet
              rate: SKILL_LEVELS.journeyman.baseRate,
              level: "journeyman",
              workers: 1
            });
            break;
            
          case "switch":
            initialTasks.push({
              id: `task-${Math.random().toString(36).substring(2, 9)}`,
              name: "Install Wall Switches",
              category: "Outlets & Switches",
              hours: count * 0.5, // 30 min per switch
              rate: SKILL_LEVELS.journeyman.baseRate,
              level: "journeyman",
              workers: 1
            });
            break;
            
          case "light":
            initialTasks.push({
              id: `task-${Math.random().toString(36).substring(2, 9)}`,
              name: "Install Light Fixtures",
              category: "Light Fixtures",
              hours: count * 1.0, // 1 hour per fixture
              rate: SKILL_LEVELS.journeyman.baseRate,
              level: "journeyman",
              workers: 1
            });
            break;
            
          case "panel":
            initialTasks.push({
              id: `task-${Math.random().toString(36).substring(2, 9)}`,
              name: "Install Electrical Panels",
              category: "Panels & Distribution",
              hours: count * 4.0, // 4 hours per panel
              rate: SKILL_LEVELS.master.baseRate,
              level: "master",
              workers: 2
            });
            break;
            
          case "data":
            initialTasks.push({
              id: `task-${Math.random().toString(36).substring(2, 9)}`,
              name: "Install Data Outlets",
              category: "Data & Communications",
              hours: count * 0.75, // 45 min per data outlet
              rate: SKILL_LEVELS.journeyman.baseRate,
              level: "journeyman",
              workers: 1
            });
            break;
        }
      });
      
      // Add general tasks
      const totalComponents = detectedSymbols.reduce((sum, symbol) => sum + (symbol.count || 1), 0);
      const roomCount = Math.ceil(totalComponents / 5); // Rough estimate: 5 components per room
      
      // Add wiring task
      initialTasks.push({
        id: `task-${Math.random().toString(36).substring(2, 9)}`,
        name: "Run Electrical Wiring",
        category: "Wiring & Conduit",
        hours: totalComponents * 0.75, // 45 min per component for wiring
        rate: SKILL_LEVELS.journeyman.baseRate,
        level: "journeyman",
        workers: 2 // Typically done in pairs
      });
      
      // Add inspection task
      initialTasks.push({
        id: `task-${Math.random().toString(36).substring(2, 9)}`,
        name: "Electrical Inspection",
        category: "Inspection & Permits",
        hours: 3.0, // Fixed 3 hours for inspection
        rate: SKILL_LEVELS.master.baseRate,
        level: "master",
        workers: 1
      });
      
      // Add apprentice task for support
      initialTasks.push({
        id: `task-${Math.random().toString(36).substring(2, 9)}`,
        name: "General Electrical Support",
        category: "Custom Tasks",
        hours: Math.ceil(totalComponents * 0.5), // Support hours
        rate: SKILL_LEVELS.apprentice.baseRate,
        level: "apprentice",
        workers: 1
      });
      
      // Set the tasks
      setLaborTasks(initialTasks);
      setLastRateUpdate(new Date());
      
      if (onLaborTasksUpdated) {
        onLaborTasksUpdated(initialTasks);
      }
    };
    
    // Call the function to generate estimates
    generateLaborTasks();
    
  }, [detectedSymbols, toast]);
  
  // Filter and sort tasks when any filter/sort criteria changes
  useEffect(() => {
    let filtered = [...laborTasks];
    
    // Apply category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter(task => task.category === selectedCategory);
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(task => 
        task.name.toLowerCase().includes(query) || 
        task.category.toLowerCase().includes(query) ||
        SKILL_LEVELS[task.level].name.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      }
      return 0;
    });
    
    setFilteredTasks(filtered);
  }, [laborTasks, selectedCategory, searchQuery, sortField, sortDirection]);
  
  // Handle hours change
  const handleHoursChange = (id: string, newHours: number) => {
    if (newHours < 0) return;
    
    setLaborTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === id
          ? { ...task, hours: newHours }
          : task
      )
    );
    
    if (onLaborTasksUpdated) {
      const updatedTasks = laborTasks.map(task => 
        task.id === id ? { ...task, hours: newHours } : task
      );
      onLaborTasksUpdated(updatedTasks);
    }
  };
  
  // Handle rate change
  const handleRateChange = (id: string, newRate: number) => {
    if (newRate < 0) return;
    
    setLaborTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === id
          ? { ...task, rate: newRate }
          : task
      )
    );
    
    if (onLaborTasksUpdated) {
      const updatedTasks = laborTasks.map(task => 
        task.id === id ? { ...task, rate: newRate } : task
      );
      onLaborTasksUpdated(updatedTasks);
    }
  };
  
  // Handle workers change
  const handleWorkersChange = (id: string, newWorkers: number) => {
    if (newWorkers < 1) return;
    
    setLaborTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === id
          ? { ...task, workers: newWorkers }
          : task
      )
    );
    
    if (onLaborTasksUpdated) {
      const updatedTasks = laborTasks.map(task => 
        task.id === id ? { ...task, workers: newWorkers } : task
      );
      onLaborTasksUpdated(updatedTasks);
    }
  };
  
  // Handle skill level change
  const handleLevelChange = (id: string, newLevel: "apprentice" | "journeyman" | "master") => {
    setLaborTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === id
          ? { 
              ...task, 
              level: newLevel,
              rate: SKILL_LEVELS[newLevel].baseRate // Update rate based on skill level
            }
          : task
      )
    );
    
    if (onLaborTasksUpdated) {
      const updatedTasks = laborTasks.map(task => 
        task.id === id 
          ? { 
              ...task, 
              level: newLevel,
              rate: SKILL_LEVELS[newLevel].baseRate
            } 
          : task
      );
      onLaborTasksUpdated(updatedTasks);
    }
  };
  
  // Add new labor task
  const handleAddTask = (task: Omit<LaborTask, 'id'>) => {
    const newTask = {
      ...task,
      id: `task-${Math.random().toString(36).substring(2, 9)}`
    };
    
    setLaborTasks(prev => [...prev, newTask]);
    
    if (onLaborTasksUpdated) {
      onLaborTasksUpdated([...laborTasks, newTask]);
    }
    
    setIsAddDialogOpen(false);
    
    toast({
      title: "Task Added",
      description: `${task.name} has been added to labor tasks.`
    });
  };
  
  // Edit existing task
  const handleEditTask = () => {
    if (!editingTask) return;
    
    setLaborTasks(prev => 
      prev.map(task => 
        task.id === editingTask.id ? editingTask : task
      )
    );
    
    if (onLaborTasksUpdated) {
      onLaborTasksUpdated(
        laborTasks.map(task => 
          task.id === editingTask.id ? editingTask : task
        )
      );
    }
    
    setEditingTask(null);
    
    toast({
      title: "Task Updated",
      description: `${editingTask.name} has been updated.`
    });
  };
  
  // Delete task
  const handleDeleteTask = (id: string) => {
    setLaborTasks(prev => prev.filter(task => task.id !== id));
    
    if (onLaborTasksUpdated) {
      onLaborTasksUpdated(laborTasks.filter(task => task.id !== id));
    }
    
    toast({
      title: "Task Removed",
      description: "The labor task has been removed from the list."
    });
  };
  
  // Bulk delete selected tasks
  const handleBulkDelete = () => {
    if (selectedTasks.length === 0) return;
    
    setLaborTasks(prev => prev.filter(task => !selectedTasks.includes(task.id)));
    setSelectedTasks([]);
    
    if (onLaborTasksUpdated) {
      onLaborTasksUpdated(laborTasks.filter(task => !selectedTasks.includes(task.id)));
    }
    
    toast({
      title: "Tasks Removed",
      description: `${selectedTasks.length} labor tasks have been removed from the list.`
    });
  };
  
  // Toggle task selection
  const toggleTaskSelection = (id: string) => {
    setSelectedTasks(prev => 
      prev.includes(id)
        ? prev.filter(taskId => taskId !== id)
        : [...prev, id]
    );
  };
  
  // Toggle select all tasks
  const toggleSelectAll = () => {
    if (selectedTasks.length === filteredTasks.length) {
      setSelectedTasks([]);
    } else {
      setSelectedTasks(filteredTasks.map(task => task.id));
    }
  };
  
  // Update all rates (simulate market rate updates)
  const updateAllRates = () => {
    setIsRateUpdateLoading(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      const updatedTasks = laborTasks.map(task => {
        // Apply a small random rate fluctuation (±5%)
        const fluctuation = 0.95 + (Math.random() * 0.1);
        return {
          ...task,
          rate: parseFloat((task.rate * fluctuation).toFixed(2))
        };
      });
      
      setLaborTasks(updatedTasks);
      setLastRateUpdate(new Date());
      setIsRateUpdateLoading(false);
      
      if (onLaborTasksUpdated) {
        onLaborTasksUpdated(updatedTasks);
      }
      
      toast({
        title: "Labor Rates Updated",
        description: "All labor rates have been updated to current market rates."
      });
    }, 2000);
  };
  
  // Calculate totals
  const totalHours = filteredTasks.reduce(
    (sum, task) => sum + (task.hours * task.workers), 
    0
  );
  
  const totalLaborCost = filteredTasks.reduce(
    (sum, task) => sum + (task.hours * task.rate * task.workers), 
    0
  );
  
  // Calculate labor distribution by skill level
  const laborDistribution = filteredTasks.reduce((acc, task) => {
    const level = task.level;
    const hours = task.hours * task.workers;
    
    acc[level] = (acc[level] || 0) + hours;
    return acc;
  }, {} as Record<string, number>);
  
  // Labor task form for adding/editing
  const LaborTaskForm = ({ task, onSubmit, isEdit = false }: { 
    task?: Partial<LaborTask>, 
    onSubmit: (task: Omit<LaborTask, 'id'>) => void,
    isEdit?: boolean
  }) => {
    const [formState, setFormState] = useState<Omit<LaborTask, 'id'>>({
      name: task?.name || "",
      category: task?.category || LABOR_CATEGORIES[0],
      hours: task?.hours || 1,
      rate: task?.rate || SKILL_LEVELS.journeyman.baseRate,
      level: task?.level || "journeyman",
      workers: task?.workers || 1
    });
    
    const handleChange = (field: keyof typeof formState, value: any) => {
      if (field === "level") {
        // Update rate when level changes
        setFormState(prev => ({ 
          ...prev, 
          [field]: value,
          rate: SKILL_LEVELS[value as "apprentice" | "journeyman" | "master"].baseRate
        }));
      } else {
        setFormState(prev => ({ ...prev, [field]: value }));
      }
    };
    
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit(formState);
    };
    
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Task Name</label>
          <Input 
            value={formState.name} 
            onChange={e => handleChange("name", e.target.value)}
            placeholder="Enter task name"
            required
          />
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Category</label>
          <Select 
            value={formState.category} 
            onValueChange={value => handleChange("category", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {LABOR_CATEGORIES.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Hours</label>
            <Input 
              type="number" 
              value={formState.hours} 
              onChange={e => handleChange("hours", parseFloat(e.target.value) || 0)}
              min={0.25}
              step={0.25}
              required
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Workers</label>
            <Input 
              type="number" 
              value={formState.workers} 
              onChange={e => handleChange("workers", parseInt(e.target.value) || 1)}
              min={1}
              required
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Skill Level</label>
          <Select 
            value={formState.level} 
            onValueChange={value => handleChange("level", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select skill level" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(SKILL_LEVELS).map(([level, data]) => (
                <SelectItem key={level} value={level}>
                  {data.name} (${data.baseRate}/hr)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Hourly Rate ($)</label>
          <Input 
            type="number" 
            value={formState.rate} 
            onChange={e => handleChange("rate", parseFloat(e.target.value) || 0)}
            min={1}
            step={0.01}
            required
          />
        </div>
        
        <DialogFooter>
          <Button type="submit">
            {isEdit ? "Save Changes" : "Add Task"}
          </Button>
        </DialogFooter>
      </form>
    );
  };
  
  const getSortIcon = (field: keyof LaborTask) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };
  
  const handleSort = (field: keyof LaborTask) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  return (
    <div className="space-y-6">
      <AICard>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-lg font-medium">Labor Estimation</h2>
            <p className="text-sm text-muted-foreground">
              Manage labor tasks, hours, and pricing for your project
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <Button onClick={updateAllRates} disabled={isRateUpdateLoading}>
              {isRateUpdateLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <DollarSign className="mr-2 h-4 w-4" />
                  Update Rates
                </>
              )}
            </Button>
            
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Task
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Labor Task</DialogTitle>
                  <DialogDescription>
                    Add a new labor task to your estimation.
                  </DialogDescription>
                </DialogHeader>
                <LaborTaskForm onSubmit={handleAddTask} />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </AICard>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-3">
          <AICard>
            <div className="space-y-4">
              {/* Filters and Search */}
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search tasks..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                
                <Select
                  value={selectedCategory}
                  onValueChange={(value) => setSelectedCategory(value)}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {LABOR_CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Button variant="outline" size="icon" title="Download as CSV">
                  <FileDown className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Labor Tasks Table */}
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox 
                          checked={
                            filteredTasks.length > 0 && 
                            selectedTasks.length === filteredTasks.length
                          }
                          onCheckedChange={toggleSelectAll}
                          aria-label="Select all"
                        />
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('name')}
                      >
                        Task{getSortIcon('name')}
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('category')}
                      >
                        Category{getSortIcon('category')}
                      </TableHead>
                      <TableHead 
                        className="text-right cursor-pointer"
                        onClick={() => handleSort('hours')}
                      >
                        Hours{getSortIcon('hours')}
                      </TableHead>
                      <TableHead 
                        className="text-right cursor-pointer"
                        onClick={() => handleSort('workers')}
                      >
                        Workers{getSortIcon('workers')}
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('level')}
                      >
                        Skill Level{getSortIcon('level')}
                      </TableHead>
                      <TableHead 
                        className="text-right cursor-pointer"
                        onClick={() => handleSort('rate')}
                      >
                        Rate{getSortIcon('rate')}
                      </TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead className="w-[120px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTasks.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-10 text-muted-foreground">
                          No labor tasks found. Add some tasks or adjust your filters.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredTasks.map(task => (
                        <TableRow key={task.id}>
                          <TableCell>
                            <Checkbox 
                              checked={selectedTasks.includes(task.id)}
                              onCheckedChange={() => toggleTaskSelection(task.id)}
                              aria-label={`Select ${task.name}`}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{task.name}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{task.category}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end">
                              <Input
                                type="number"
                                value={task.hours}
                                onChange={(e) => handleHoursChange(task.id, parseFloat(e.target.value) || 0)}
                                className="w-16 h-8 text-center mx-1"
                                min={0.25}
                                step={0.25}
                              />
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6 rounded-full"
                                onClick={() => handleWorkersChange(task.id, Math.max(1, task.workers - 1))}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <Input
                                type="number"
                                value={task.workers}
                                onChange={(e) => handleWorkersChange(task.id, parseInt(e.target.value) || 1)}
                                className="w-12 h-8 text-center mx-1"
                                min={1}
                              />
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6 rounded-full"
                                onClick={() => handleWorkersChange(task.id, task.workers + 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Select
                              value={task.level}
                              onValueChange={(value: "apprentice" | "journeyman" | "master") => 
                                handleLevelChange(task.id, value)
                              }
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="Select level" />
                              </SelectTrigger>
                              <SelectContent>
                                {Object.entries(SKILL_LEVELS).map(([level, data]) => (
                                  <SelectItem key={level} value={level}>
                                    {data.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end">
                              <span className="mr-1">$</span>
                              <Input
                                type="number"
                                value={task.rate.toFixed(2)}
                                onChange={(e) => handleRateChange(task.id, parseFloat(e.target.value) || 0)}
                                className="w-20 h-8 text-right"
                                step={0.01}
                                min={0}
                              />
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            ${(task.hours * task.rate * task.workers).toFixed(2)}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="h-8 w-8"
                                    onClick={() => setEditingTask(task)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Edit Labor Task</DialogTitle>
                                    <DialogDescription>
                                      Make changes to the labor task details.
                                    </DialogDescription>
                                  </DialogHeader>
                                  {editingTask && (
                                    <LaborTaskForm 
                                      task={editingTask} 
                                      onSubmit={(updatedTask) => {
                                        setEditingTask({
                                          ...updatedTask,
                                          id: editingTask.id,
                                        });
                                        handleEditTask();
                                      }}
                                      isEdit
                                    />
                                  )}
                                </DialogContent>
                              </Dialog>
                              
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-destructive"
                                onClick={() => handleDeleteTask(task.id)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
              
              {/* Batch Actions */}
              {selectedTasks.length > 0 && (
                <div className="flex items-center justify-between bg-muted/20 p-2 rounded-md">
                  <span className="text-sm">
                    {selectedTasks.length} {selectedTasks.length === 1 ? 'task' : 'tasks'} selected
                  </span>
                  <div className="flex space-x-2">
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={handleBulkDelete}
                    >
                      <Trash className="h-4 w-4 mr-2" />
                      Delete Selected
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </AICard>
        </div>
        
        <div className="md:col-span-1">
          <div className="space-y-6">
            <AICard>
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Labor Summary</h3>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Tasks:</span>
                    <span className="font-medium">{filteredTasks.length}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Hours:</span>
                    <span className="font-medium">{totalHours.toFixed(1)}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Average Rate:</span>
                    <span className="font-medium">
                      ${totalHours > 0 
                        ? (totalLaborCost / totalHours).toFixed(2)
                        : '0.00'}/hr
                    </span>
                  </div>
                  
                  <div className="pt-2 mt-2 border-t">
                    <div className="flex justify-between">
                      <span className="font-medium">Total Labor Cost:</span>
                      <span className="font-bold text-lg">${totalLaborCost.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
                
                {lastRateUpdate && (
                  <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 mt-2 border-t">
                    <span className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" /> 
                      Last Rate Update:
                    </span>
                    <span>
                      {lastRateUpdate.toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric', 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </span>
                  </div>
                )}
              </div>
            </AICard>
            
            <AICard>
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Labor Distribution</h3>
                
                <div className="space-y-3">
                  {Object.entries(SKILL_LEVELS).map(([level, data]) => {
                    const levelHours = laborDistribution[level] || 0;
                    const percentage = totalHours > 0 
                      ? (levelHours / totalHours) * 100 
                      : 0;
                    
                    return (
                      <div key={level} className="space-y-1">
                        <div className="flex justify-between items-center text-sm">
                          <span className="font-medium">{data.name}</span>
                          <span>{levelHours.toFixed(1)} hrs ({percentage.toFixed(0)}%)</span>
                        </div>
                        <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className={`h-full rounded-full ${
                              level === 'apprentice' ? 'bg-blue-500' :
                              level === 'journeyman' ? 'bg-green-500' :
                              'bg-purple-500'
                            }`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                <Accordion type="single" collapsible>
                  <AccordionItem value="crew-requirement">
                    <AccordionTrigger className="text-sm">
                      Crew Requirements
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3 text-sm">
                        {/* Calculate needed workers by skill level */}
                        {Object.entries(SKILL_LEVELS).map(([level, data]) => {
                          const levelHours = laborDistribution[level] || 0;
                          // Assuming 8-hour workdays
                          const workersNeeded = Math.ceil(levelHours / 8);
                          
                          return workersNeeded > 0 ? (
                            <div key={level} className="flex items-center space-x-3">
                              <div 
                                className={`h-8 w-8 flex items-center justify-center rounded-full ${
                                  level === 'apprentice' ? 'bg-blue-100 text-blue-500' :
                                  level === 'journeyman' ? 'bg-green-100 text-green-500' :
                                  'bg-purple-100 text-purple-500'
                                }`}
                              >
                                <User className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="font-medium">{workersNeeded} {data.name}{workersNeeded > 1 ? 's' : ''}</p>
                                <p className="text-xs text-muted-foreground">
                                  {levelHours.toFixed(1)} hours total
                                </p>
                              </div>
                            </div>
                          ) : null;
                        })}
                        
                        <div className="pt-2 mt-2 border-t flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span>Estimated Duration:</span>
                          </div>
                          <span className="font-medium">
                            {Math.ceil(totalHours / 8)} days
                          </span>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            </AICard>
          </div>
        </div>
      </div>
    </div>
  );
}