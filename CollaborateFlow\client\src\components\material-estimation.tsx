import { useState, useEffect } from "react";
import { Filter, Plus, Minus, FileDown, Calculator, Search, Trash, Edit, DollarSign, RefreshC<PERSON>, Clock, CheckCircle2 } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { SymbolType, SYMBOL_TYPES } from "./symbol-editor";

interface DetectedSymbol {
  id: string;
  type: string;
  position?: { x: number; y: number };
  rotation?: number;
  scale?: number;
  selected?: boolean;
  count?: number;
}

interface MaterialItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unitPrice: number;
  supplier: string;
  alternativeIds: string[];
}

interface MaterialEstimationProps {
  detectedSymbols?: DetectedSymbol[];
  onMaterialsUpdated?: (materials: MaterialItem[]) => void;
}

// Material categories
const MATERIAL_CATEGORIES = [
  "Outlets & Switches",
  "Light Fixtures",
  "Panels & Distribution",
  "Wiring & Conduit",
  "Data & Communications",
  "Safety & Protection",
  "Accessories & Hardware",
  "Custom Items"
];

// Supplier options
const SUPPLIERS = [
  "ElectraMart",
  "PowerSupply Inc.",
  "CircuitWorld",
  "WireHouse Distributors",
  "Contractor Warehouse",
  "Local Vendor",
  "Other"
];

export function MaterialEstimation({ detectedSymbols = [], onMaterialsUpdated }: MaterialEstimationProps) {
  const [materials, setMaterials] = useState<MaterialItem[]>([]);
  const [filteredMaterials, setFilteredMaterials] = useState<MaterialItem[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingMaterial, setEditingMaterial] = useState<MaterialItem | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | "all">("all");
  const [sortField, setSortField] = useState<keyof MaterialItem>("category");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([]);
  const [isPriceUpdateLoading, setIsPriceUpdateLoading] = useState(false);
  const [lastPriceUpdate, setLastPriceUpdate] = useState<Date | null>(null);
  const { toast } = useToast();
  
  // Generate initial materials from detected symbols
  useEffect(() => {
    if (detectedSymbols.length === 0) return;
    
    const generateMaterials = async () => {
      try {
        // Call the API endpoint to generate material estimates
        const response = await fetch('/api/ai/estimate-materials', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ symbols: detectedSymbols }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate material estimates');
        }
        
        const data = await response.json();
        
        if (data.success && data.materials) {
          // Set the materials
          setMaterials(data.materials);
          setLastPriceUpdate(new Date());
          
          if (onMaterialsUpdated) {
            onMaterialsUpdated(data.materials);
          }
          
          toast({
            title: "Materials Generated",
            description: `${data.materials.length} material items have been estimated for your project.`,
          });
        } else {
          throw new Error(data.message || 'Failed to generate material estimates');
        }
      } catch (error) {
        console.error('Material estimation error:', error);
        toast({
          title: "Estimation Error",
          description: `Failed to generate material estimates: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
        
        // Fallback to locally generated estimates
        generateLocalEstimates();
      }
    };
    
    // Fallback function to generate estimates locally
    const generateLocalEstimates = () => {
      const initialMaterials: MaterialItem[] = [];
      
      // Map symbols to materials
      detectedSymbols.forEach(symbol => {
        const count = symbol.count || 1;
        
        switch(symbol.type) {
          case "outlet":
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Standard Outlet",
              category: "Outlets & Switches",
              quantity: count,
              unitPrice: 8.99,
              supplier: "ElectraMart",
              alternativeIds: []
            });
            
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Outlet Box",
              category: "Accessories & Hardware",
              quantity: count,
              unitPrice: 3.49,
              supplier: "ElectraMart",
              alternativeIds: []
            });
            break;
            
          case "switch":
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Toggle Switch",
              category: "Outlets & Switches",
              quantity: count,
              unitPrice: 6.99,
              supplier: "ElectraMart",
              alternativeIds: []
            });
            
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Switch Box",
              category: "Accessories & Hardware",
              quantity: count,
              unitPrice: 3.49,
              supplier: "ElectraMart",
              alternativeIds: []
            });
            break;
            
          case "light":
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Ceiling Light Fixture",
              category: "Light Fixtures",
              quantity: count,
              unitPrice: 45.99,
              supplier: "PowerSupply Inc.",
              alternativeIds: []
            });
            
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Light Bulb (LED)",
              category: "Light Fixtures",
              quantity: count * 2, // Assuming 2 bulbs per fixture
              unitPrice: 8.99,
              supplier: "PowerSupply Inc.",
              alternativeIds: []
            });
            break;
            
          case "panel":
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Electrical Panel (100A)",
              category: "Panels & Distribution",
              quantity: count,
              unitPrice: 189.99,
              supplier: "CircuitWorld",
              alternativeIds: []
            });
            
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Circuit Breakers",
              category: "Panels & Distribution",
              quantity: count * 12, // Assuming 12 breakers per panel
              unitPrice: 12.99,
              supplier: "CircuitWorld",
              alternativeIds: []
            });
            break;
            
          case "data":
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "Data Outlet",
              category: "Data & Communications",
              quantity: count,
              unitPrice: 14.99,
              supplier: "WireHouse Distributors",
              alternativeIds: []
            });
            
            initialMaterials.push({
              id: `mat-${Math.random().toString(36).substring(2, 9)}`,
              name: "CAT6 Cable (ft)",
              category: "Data & Communications",
              quantity: count * 15, // Assuming 15 ft per outlet
              unitPrice: 0.79,
              supplier: "WireHouse Distributors",
              alternativeIds: []
            });
            break;
        }
      });
      
      // Add some general materials
      initialMaterials.push({
        id: `mat-${Math.random().toString(36).substring(2, 9)}`,
        name: "Electrical Wire 12 AWG (ft)",
        category: "Wiring & Conduit",
        quantity: calculateTotalWireLength(initialMaterials),
        unitPrice: 0.89,
        supplier: "WireHouse Distributors",
        alternativeIds: []
      });
      
      initialMaterials.push({
        id: `mat-${Math.random().toString(36).substring(2, 9)}`,
        name: "Wire Nuts",
        category: "Accessories & Hardware",
        quantity: initialMaterials.reduce((sum, item) => 
          ["Outlets & Switches", "Light Fixtures"].includes(item.category) ? sum + item.quantity * 3 : sum, 0),
        unitPrice: 0.25,
        supplier: "ElectraMart",
        alternativeIds: []
      });
      
      // Set the materials
      setMaterials(initialMaterials);
      setLastPriceUpdate(new Date());
      
      if (onMaterialsUpdated) {
        onMaterialsUpdated(initialMaterials);
      }
    };
    
    // Call the function to generate estimates
    generateMaterials();
    
  }, [detectedSymbols, toast]);
  
  // Calculate an estimated total wire length based on number of electrical components
  const calculateTotalWireLength = (items: MaterialItem[]) => {
    const outletCount = items.reduce((sum, item) => 
      item.name === "Standard Outlet" ? sum + item.quantity : sum, 0);
    
    const switchCount = items.reduce((sum, item) => 
      item.name === "Toggle Switch" ? sum + item.quantity : sum, 0);
    
    const lightCount = items.reduce((sum, item) => 
      item.name === "Ceiling Light Fixture" ? sum + item.quantity : sum, 0);
    
    // Rough estimation: 20ft per outlet, 15ft per switch, 25ft per light
    return (outletCount * 20) + (switchCount * 15) + (lightCount * 25);
  };
  
  // Filter and sort materials when any filter/sort criteria changes
  useEffect(() => {
    let filtered = [...materials];
    
    // Apply category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(query) || 
        item.supplier.toLowerCase().includes(query) ||
        item.category.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      }
      return 0;
    });
    
    setFilteredMaterials(filtered);
  }, [materials, selectedCategory, searchQuery, sortField, sortDirection]);
  
  // Handle quantity change
  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 0) return;
    
    setMaterials(prevMaterials => 
      prevMaterials.map(item => 
        item.id === id
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
    
    // Debounced callback to avoid too many updates
    if (onMaterialsUpdated) {
      const updatedMaterials = materials.map(item => 
        item.id === id ? { ...item, quantity: newQuantity } : item
      );
      onMaterialsUpdated(updatedMaterials);
    }
  };
  
  // Handle unit price change
  const handleUnitPriceChange = (id: string, newPrice: number) => {
    if (newPrice < 0) return;
    
    setMaterials(prevMaterials => 
      prevMaterials.map(item => 
        item.id === id
          ? { ...item, unitPrice: newPrice }
          : item
      )
    );
    
    // Debounced callback to avoid too many updates
    if (onMaterialsUpdated) {
      const updatedMaterials = materials.map(item => 
        item.id === id ? { ...item, unitPrice: newPrice } : item
      );
      onMaterialsUpdated(updatedMaterials);
    }
  };
  
  // Handle supplier change
  const handleSupplierChange = (id: string, newSupplier: string) => {
    setMaterials(prevMaterials => 
      prevMaterials.map(item => 
        item.id === id
          ? { ...item, supplier: newSupplier }
          : item
      )
    );
    
    if (onMaterialsUpdated) {
      const updatedMaterials = materials.map(item => 
        item.id === id ? { ...item, supplier: newSupplier } : item
      );
      onMaterialsUpdated(updatedMaterials);
    }
  };
  
  // Add new material
  const handleAddMaterial = (material: Omit<MaterialItem, 'id'>) => {
    const newMaterial = {
      ...material,
      id: `mat-${Math.random().toString(36).substring(2, 9)}`,
      alternativeIds: []
    };
    
    setMaterials(prev => [...prev, newMaterial]);
    
    if (onMaterialsUpdated) {
      onMaterialsUpdated([...materials, newMaterial]);
    }
    
    setIsAddDialogOpen(false);
    
    toast({
      title: "Material Added",
      description: `${material.name} has been added to the list.`
    });
  };
  
  // Edit existing material
  const handleEditMaterial = () => {
    if (!editingMaterial) return;
    
    setMaterials(prev => 
      prev.map(item => 
        item.id === editingMaterial.id ? editingMaterial : item
      )
    );
    
    if (onMaterialsUpdated) {
      onMaterialsUpdated(
        materials.map(item => 
          item.id === editingMaterial.id ? editingMaterial : item
        )
      );
    }
    
    setEditingMaterial(null);
    
    toast({
      title: "Material Updated",
      description: `${editingMaterial.name} has been updated.`
    });
  };
  
  // Delete material
  const handleDeleteMaterial = (id: string) => {
    setMaterials(prev => prev.filter(item => item.id !== id));
    
    if (onMaterialsUpdated) {
      onMaterialsUpdated(materials.filter(item => item.id !== id));
    }
    
    toast({
      title: "Material Removed",
      description: "The material has been removed from the list."
    });
  };
  
  // Bulk delete selected materials
  const handleBulkDelete = () => {
    if (selectedMaterials.length === 0) return;
    
    setMaterials(prev => prev.filter(item => !selectedMaterials.includes(item.id)));
    setSelectedMaterials([]);
    
    if (onMaterialsUpdated) {
      onMaterialsUpdated(materials.filter(item => !selectedMaterials.includes(item.id)));
    }
    
    toast({
      title: "Materials Removed",
      description: `${selectedMaterials.length} materials have been removed from the list.`
    });
  };
  
  // Toggle material selection
  const toggleMaterialSelection = (id: string) => {
    setSelectedMaterials(prev => 
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };
  
  // Toggle select all materials
  const toggleSelectAll = () => {
    if (selectedMaterials.length === filteredMaterials.length) {
      setSelectedMaterials([]);
    } else {
      setSelectedMaterials(filteredMaterials.map(item => item.id));
    }
  };
  
  // Update all prices (simulate fetching from supplier APIs)
  const updateAllPrices = () => {
    setIsPriceUpdateLoading(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      const updatedMaterials = materials.map(item => {
        // Apply a small random price fluctuation (±5%)
        const fluctuation = 0.9 + (Math.random() * 0.2);
        return {
          ...item,
          unitPrice: parseFloat((item.unitPrice * fluctuation).toFixed(2))
        };
      });
      
      setMaterials(updatedMaterials);
      setLastPriceUpdate(new Date());
      setIsPriceUpdateLoading(false);
      
      if (onMaterialsUpdated) {
        onMaterialsUpdated(updatedMaterials);
      }
      
      toast({
        title: "Prices Updated",
        description: "All material prices have been updated to current market rates."
      });
    }, 2000);
  };
  
  // Calculate total cost
  const totalCost = filteredMaterials.reduce(
    (sum, item) => sum + (item.quantity * item.unitPrice), 
    0
  );
  
  // Material form for adding/editing
  const MaterialForm = ({ material, onSubmit, isEdit = false }: { 
    material?: Partial<MaterialItem>, 
    onSubmit: (material: Omit<MaterialItem, 'id'>) => void,
    isEdit?: boolean
  }) => {
    const [formState, setFormState] = useState<Omit<MaterialItem, 'id'>>({
      name: material?.name || "",
      category: material?.category || MATERIAL_CATEGORIES[0],
      quantity: material?.quantity || 1,
      unitPrice: material?.unitPrice || 0,
      supplier: material?.supplier || SUPPLIERS[0],
      alternativeIds: material?.alternativeIds || []
    });
    
    const handleChange = (field: keyof typeof formState, value: any) => {
      setFormState(prev => ({ ...prev, [field]: value }));
    };
    
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit(formState);
    };
    
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Material Name</label>
          <Input 
            value={formState.name} 
            onChange={e => handleChange("name", e.target.value)}
            placeholder="Enter material name"
            required
          />
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Category</label>
          <Select 
            value={formState.category} 
            onValueChange={value => handleChange("category", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {MATERIAL_CATEGORIES.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Quantity</label>
            <Input 
              type="number" 
              value={formState.quantity} 
              onChange={e => handleChange("quantity", parseInt(e.target.value) || 0)}
              min={1}
              required
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Unit Price ($)</label>
            <Input 
              type="number" 
              value={formState.unitPrice} 
              onChange={e => handleChange("unitPrice", parseFloat(e.target.value) || 0)}
              min={0}
              step={0.01}
              required
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Supplier</label>
          <Select 
            value={formState.supplier} 
            onValueChange={value => handleChange("supplier", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select supplier" />
            </SelectTrigger>
            <SelectContent>
              {SUPPLIERS.map(supplier => (
                <SelectItem key={supplier} value={supplier}>
                  {supplier}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <DialogFooter>
          <Button type="submit">
            {isEdit ? "Save Changes" : "Add Material"}
          </Button>
        </DialogFooter>
      </form>
    );
  };
  
  const getSortIcon = (field: keyof MaterialItem) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };
  
  const handleSort = (field: keyof MaterialItem) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  return (
    <div className="space-y-6">
      <AICard>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-lg font-medium">Material Estimation</h2>
            <p className="text-sm text-muted-foreground">
              Manage materials, quantities, and pricing for your project
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <Button onClick={updateAllPrices} disabled={isPriceUpdateLoading}>
              {isPriceUpdateLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <DollarSign className="mr-2 h-4 w-4" />
                  Update Prices
                </>
              )}
            </Button>
            
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Material
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Material</DialogTitle>
                  <DialogDescription>
                    Add a new material to your estimation.
                  </DialogDescription>
                </DialogHeader>
                <MaterialForm onSubmit={handleAddMaterial} />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </AICard>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-3">
          <AICard>
            <div className="space-y-4">
              {/* Filters and Search */}
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search materials..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                
                <Select
                  value={selectedCategory}
                  onValueChange={(value) => setSelectedCategory(value)}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {MATERIAL_CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Button variant="outline" size="icon" title="Download as CSV">
                  <FileDown className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Materials Table */}
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox 
                          checked={
                            filteredMaterials.length > 0 && 
                            selectedMaterials.length === filteredMaterials.length
                          }
                          onCheckedChange={toggleSelectAll}
                          aria-label="Select all"
                        />
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('name')}
                      >
                        Material{getSortIcon('name')}
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('category')}
                      >
                        Category{getSortIcon('category')}
                      </TableHead>
                      <TableHead 
                        className="text-right cursor-pointer"
                        onClick={() => handleSort('quantity')}
                      >
                        Quantity{getSortIcon('quantity')}
                      </TableHead>
                      <TableHead 
                        className="text-right cursor-pointer"
                        onClick={() => handleSort('unitPrice')}
                      >
                        Unit Price{getSortIcon('unitPrice')}
                      </TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('supplier')}
                      >
                        Supplier{getSortIcon('supplier')}
                      </TableHead>
                      <TableHead className="w-[120px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMaterials.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-10 text-muted-foreground">
                          No materials found. Add some materials or adjust your filters.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredMaterials.map(material => (
                        <TableRow key={material.id}>
                          <TableCell>
                            <Checkbox 
                              checked={selectedMaterials.includes(material.id)}
                              onCheckedChange={() => toggleMaterialSelection(material.id)}
                              aria-label={`Select ${material.name}`}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{material.name}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{material.category}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6 rounded-full"
                                onClick={() => handleQuantityChange(material.id, Math.max(0, material.quantity - 1))}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <Input
                                type="number"
                                value={material.quantity}
                                onChange={(e) => handleQuantityChange(material.id, parseInt(e.target.value) || 0)}
                                className="w-16 h-8 text-center mx-1"
                                min={0}
                              />
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6 rounded-full"
                                onClick={() => handleQuantityChange(material.id, material.quantity + 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end">
                              <span className="mr-1">$</span>
                              <Input
                                type="number"
                                value={material.unitPrice.toFixed(2)}
                                onChange={(e) => handleUnitPriceChange(material.id, parseFloat(e.target.value) || 0)}
                                className="w-20 h-8 text-right"
                                step={0.01}
                                min={0}
                              />
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            ${(material.quantity * material.unitPrice).toFixed(2)}
                          </TableCell>
                          <TableCell>
                            <Select
                              value={material.supplier}
                              onValueChange={(value) => handleSupplierChange(material.id, value)}
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="Select supplier" />
                              </SelectTrigger>
                              <SelectContent>
                                {SUPPLIERS.map(supplier => (
                                  <SelectItem key={supplier} value={supplier}>
                                    {supplier}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="h-8 w-8"
                                    onClick={() => setEditingMaterial(material)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Edit Material</DialogTitle>
                                    <DialogDescription>
                                      Make changes to the material details.
                                    </DialogDescription>
                                  </DialogHeader>
                                  {editingMaterial && (
                                    <MaterialForm 
                                      material={editingMaterial} 
                                      onSubmit={(updatedMaterial) => {
                                        setEditingMaterial({
                                          ...updatedMaterial,
                                          id: editingMaterial.id,
                                        });
                                        handleEditMaterial();
                                      }}
                                      isEdit
                                    />
                                  )}
                                </DialogContent>
                              </Dialog>
                              
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-destructive"
                                onClick={() => handleDeleteMaterial(material.id)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
              
              {/* Batch Actions */}
              {selectedMaterials.length > 0 && (
                <div className="flex items-center justify-between bg-muted/20 p-2 rounded-md">
                  <span className="text-sm">
                    {selectedMaterials.length} {selectedMaterials.length === 1 ? 'item' : 'items'} selected
                  </span>
                  <div className="flex space-x-2">
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={handleBulkDelete}
                    >
                      <Trash className="h-4 w-4 mr-2" />
                      Delete Selected
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </AICard>
        </div>
        
        <div className="md:col-span-1">
          <div className="space-y-6">
            <AICard>
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Summary</h3>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Items:</span>
                    <span className="font-medium">{filteredMaterials.reduce((sum, item) => sum + item.quantity, 0)}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Unique Materials:</span>
                    <span className="font-medium">{filteredMaterials.length}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Categories:</span>
                    <span className="font-medium">
                      {new Set(filteredMaterials.map(item => item.category)).size}
                    </span>
                  </div>
                  
                  <div className="pt-2 mt-2 border-t">
                    <div className="flex justify-between">
                      <span className="font-medium">Total Cost:</span>
                      <span className="font-bold text-lg">${totalCost.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
                
                {lastPriceUpdate && (
                  <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 mt-2 border-t">
                    <span className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" /> 
                      Last Price Update:
                    </span>
                    <span>
                      {lastPriceUpdate.toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric', 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </span>
                  </div>
                )}
              </div>
            </AICard>
            
            <AICard>
              <div className="space-y-4">
                <h3 className="text-lg font-medium">What-If Analysis</h3>
                
                <div className="space-y-3">
                  <div className="p-3 bg-muted/20 rounded-lg">
                    <h4 className="text-sm font-medium mb-1">Price Increase 10%</h4>
                    <p className="text-xl font-bold">${(totalCost * 1.1).toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">
                      +${(totalCost * 0.1).toFixed(2)} from current
                    </p>
                  </div>
                  
                  <div className="p-3 bg-primary/5 rounded-lg">
                    <h4 className="text-sm font-medium mb-1">Quantity +20%</h4>
                    <p className="text-xl font-bold">${(totalCost * 1.2).toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">
                      +${(totalCost * 0.2).toFixed(2)} from current
                    </p>
                  </div>
                  
                  <div className="p-3 bg-muted/20 rounded-lg">
                    <h4 className="text-sm font-medium mb-1">Premium Suppliers</h4>
                    <p className="text-xl font-bold">${(totalCost * 1.15).toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">
                      +${(totalCost * 0.15).toFixed(2)} from current
                    </p>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full">
                  <Calculator className="h-4 w-4 mr-2" />
                  Run Advanced Analysis
                </Button>
              </div>
            </AICard>
          </div>
        </div>
      </div>
    </div>
  );
}