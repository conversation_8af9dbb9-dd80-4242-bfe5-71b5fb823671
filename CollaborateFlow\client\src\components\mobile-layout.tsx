import { useState } from "react";
import { useLocation } from "wouter";
import { 
  ChevronLeft, 
  Menu,
  Home,
  BarChart,
  FileText,
  Settings,
  User,
  PencilRuler,
  Search,
  Plus,
  MoreVertical
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { 
  Sheet, 
  <PERSON>et<PERSON>ontent, 
  SheetTrigger, 
  SheetClose 
} from "@/components/ui/sheet";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { useSupabaseAuth } from "@/hooks/use-supabase-auth";
import { SignOutButton } from "@/components/sign-out-button";

interface MobileLayoutProps {
  title: string;
  children: React.ReactNode;
  showBackButton?: boolean;
  actions?: React.ReactNode;
  hideNav?: boolean;
}

export function MobileLayout({
  title,
  children,
  showBackButton = false,
  actions,
  hideNav = false
}: MobileLayoutProps) {
  const [location, setLocation] = useLocation();
  const { user } = useSupabaseAuth();
  
  const navigateBack = () => {
    window.history.back();
  };
  
  const initials = user?.full_name 
    ? user.full_name.split(" ").map((n: string) => n[0]).join("").toUpperCase()
    : user?.email?.charAt(0).toUpperCase() || "U";
  
  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Mobile Header */}
      <header className="sticky top-0 z-10 w-full bg-background/95 backdrop-blur-sm border-b border-border/40 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {showBackButton ? (
              <Button 
                variant="ghost" 
                size="icon" 
                className="mr-2 rounded-full hover:bg-muted/30"
                onClick={navigateBack}
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
            ) : (
              <Sheet>
                <SheetTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="mr-2 rounded-full hover:bg-muted/30"
                  >
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-64 p-0">
                  <div className="border-b border-border/40 p-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex items-center justify-center rounded-full bg-primary/10 text-primary">
                        {initials}
                      </div>
                      <div className="ml-3">
                        <p className="font-medium">{user?.full_name || user?.email?.split('@')[0]}</p>
                        <p className="text-xs text-muted-foreground">{user?.email}</p>
                      </div>
                    </div>
                  </div>
                  <nav className="p-2">
                    <ul className="space-y-1">
                      <li>
                        <SheetClose asChild>
                          <Button 
                            variant="ghost" 
                            className="w-full justify-start" 
                            onClick={() => setLocation("/")}
                          >
                            <Home className="mr-2 h-4 w-4" /> Dashboard
                          </Button>
                        </SheetClose>
                      </li>
                      <li>
                        <SheetClose asChild>
                          <Button 
                            variant="ghost" 
                            className="w-full justify-start"
                            onClick={() => setLocation("/analytics")}
                          >
                            <BarChart className="mr-2 h-4 w-4" /> Analytics
                          </Button>
                        </SheetClose>
                      </li>
                      <li>
                        <SheetClose asChild>
                          <Button 
                            variant="ghost" 
                            className="w-full justify-start"
                            onClick={() => setLocation("/quotes")}
                          >
                            <FileText className="mr-2 h-4 w-4" /> Quotes
                          </Button>
                        </SheetClose>
                      </li>
                      <li>
                        <SheetClose asChild>
                          <Button 
                            variant="ghost" 
                            className="w-full justify-start"
                            onClick={() => setLocation("/project-estimation")}
                          >
                            <PencilRuler className="mr-2 h-4 w-4" /> Estimation
                          </Button>
                        </SheetClose>
                      </li>
                    </ul>
                  </nav>
                  <div className="absolute bottom-0 left-0 right-0 border-t border-border/40 p-4">
                    <ul className="space-y-1">
                      <li>
                        <SheetClose asChild>
                          <Button 
                            variant="ghost" 
                            className="w-full justify-start"
                            onClick={() => setLocation("/profile")}
                          >
                            <User className="mr-2 h-4 w-4" /> Profile
                          </Button>
                        </SheetClose>
                      </li>
                      <li>
                        <SheetClose asChild>
                          <Button 
                            variant="ghost" 
                            className="w-full justify-start"
                            onClick={() => setLocation("/settings")}
                          >
                            <Settings className="mr-2 h-4 w-4" /> Settings
                          </Button>
                        </SheetClose>
                      </li>
                      <li>
                        <SheetClose asChild>
                          <Button 
                              variant="ghost" 
                              className="w-full justify-start text-destructive hover:text-destructive"
                              onClick={async () => {
                                try {
                                  await user?.signOut();
                                } catch (error) {
                                  console.error("Error signing out:", error);
                                }
                              }}
                            >
                              <span className="mr-2">🚪</span> Logout
                            </Button>
                        </SheetClose>
                      </li>
                    </ul>
                  </div>
                </SheetContent>
              </Sheet>
            )}
            <h1 className="text-lg font-medium">{title}</h1>
          </div>
          
          <div className="flex items-center">
            {actions}
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="flex-1">
        <div className="container mx-auto px-4 py-4">
          {children}
        </div>
      </main>
      
      {/* Bottom Navigation */}
      {!hideNav && (
        <div className="sticky bottom-0 border-t border-border/40 bg-background/95 backdrop-blur-sm p-2">
          <nav className="flex justify-around">
            <Button 
              variant="ghost" 
              size="sm" 
              className="flex flex-col items-center gap-1 h-auto py-2"
              onClick={() => setLocation("/")}
            >
              <Home className="h-5 w-5" />
              <span className="text-xs">Home</span>
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="flex flex-col items-center gap-1 h-auto py-2"
              onClick={() => setLocation("/analytics")}
            >
              <BarChart className="h-5 w-5" />
              <span className="text-xs">Analytics</span>
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="flex flex-col items-center gap-1 h-auto py-2"
              onClick={() => setLocation("/quotes")}
            >
              <FileText className="h-5 w-5" />
              <span className="text-xs">Quotes</span>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="flex flex-col items-center gap-1 h-auto py-2"
                >
                  <MoreVertical className="h-5 w-5" />
                  <span className="text-xs">More</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setLocation("/profile")}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLocation("/settings")}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>
        </div>
      )}
    </div>
  );
}