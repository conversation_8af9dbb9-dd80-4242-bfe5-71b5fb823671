import { useState } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Project } from "@shared/schema";
import { MobileLayout } from "@/components/mobile-layout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { ChevronRight, Plus, Clock, CheckCircle, UserCircle2, FileText, CircleSlash } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { queryClient } from "@/lib/queryClient";

export function MobileDashboard() {
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState("projects");
  
  // Fetch projects
  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ["/api/projects"],
  });
  
  // Helper functions for display
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "in-progress": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "planning": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "on-hold": return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };
  
  const truncate = (text: string, length: number) => {
    if (text.length <= length) return text;
    return text.slice(0, length) + "...";
  };
  
  // Sample data for dashboard
  const recentActivities = [
    { id: 1, type: "task", description: "Updated floor plan for Project Alpha", user: "John S.", time: "2 hours ago" },
    { id: 2, type: "quote", description: "Sent quote to ABC Corporation", user: "Maria L.", time: "4 hours ago" },
    { id: 3, type: "project", description: "Created new project: Downtown Office", user: "You", time: "1 day ago" },
    { id: 4, type: "task", description: "Completed circuit design for Project Beta", user: "David K.", time: "1 day ago" },
    { id: 5, type: "approval", description: "Client approved quote #QT-2025-042", user: "System", time: "2 days ago" }
  ];
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "task": return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case "quote": return <FileText className="h-4 w-4 text-green-500" />;
      case "project": return <Plus className="h-4 w-4 text-purple-500" />;
      case "approval": return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };
  
  // Sample stats cards
  const dashboardStats = [
    { title: "Active Projects", value: "12", change: "+2", trend: "up" },
    { title: "Pending Quotes", value: "8", change: "-1", trend: "down" },
    { title: "This Month", value: "$48.5k", change: "+12%", trend: "up" },
    { title: "Team Utilization", value: "87%", change: "+5%", trend: "up" },
  ];
  
  return (
    <MobileLayout 
      title="Dashboard" 
      actions={
        <Button 
          size="icon" 
          variant="ghost" 
          className="rounded-full"
          onClick={() => setLocation("/project/new")}
        >
          <Plus className="h-5 w-5" />
        </Button>
      }
    >
      {/* Stats Cards (Horizontal Scroll) */}
      <ScrollArea className="w-full whitespace-nowrap">
        <div className="flex gap-3 pb-4">
          {dashboardStats.map((stat, index) => (
            <Card key={index} className="min-w-[130px] bg-muted/30">
              <CardContent className="p-4">
                <p className="text-xs text-muted-foreground">{stat.title}</p>
                <div className="flex items-end justify-between mt-1">
                  <p className="text-xl font-semibold">{stat.value}</p>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${
                      stat.trend === "up" ? "text-green-500" : "text-red-500"
                    }`}
                  >
                    {stat.change}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
      
      {/* Tabs for Projects/Activity */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
        <TabsList className="grid grid-cols-2 w-full">
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>
        
        <TabsContent value="projects" className="mt-4">
          <div className="space-y-4">
            {projectsLoading ? (
              // Loading skeletons
              Array.from({ length: 3 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                      <Skeleton className="h-6 w-20 rounded-full" />
                    </div>
                    <div className="mt-4">
                      <Skeleton className="h-3 w-full" />
                      <Skeleton className="h-3 w-2/3 mt-2" />
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : projects && projects.length > 0 ? (
              // Project cards
              projects.map(project => (
                <Card 
                  key={project.id} 
                  className="relative overflow-hidden"
                  onClick={() => setLocation(`/project/${project.id}`)}
                >
                  <div className={`absolute left-0 top-0 bottom-0 w-1 bg-blue-500`}></div>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{project.name}</h3>
                        <p className="text-xs text-muted-foreground">{project.description || "No description"}</p>
                      </div>
                      <Badge className={getStatusColor("in-progress")}>In Progress</Badge>
                    </div>
                    
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex -space-x-2">
                        {Array.from({ length: 3 }).map((_, i) => (
                          <Avatar key={i} className="h-6 w-6 border-2 border-background">
                            <AvatarFallback className="text-[10px]">
                              {["JS", "ML", "DK"][i]}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                      </div>
                      
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>Updated 2 days ago</span>
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              // Empty state
              <div className="text-center py-8">
                <CircleSlash className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                <h3 className="text-lg font-medium mb-1">No projects yet</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Create your first project to get started
                </p>
                <Button onClick={() => setLocation("/project/new")}>
                  <Plus className="h-4 w-4 mr-2" /> New Project
                </Button>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="activity" className="mt-4">
          <div className="space-y-3">
            {recentActivities.map(activity => (
              <Card key={activity.id} className="overflow-hidden">
                <CardContent className="p-4">
                  <div className="flex gap-3">
                    <div className="h-8 w-8 rounded-full bg-muted/50 flex items-center justify-center">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm">{activity.description}</p>
                      <div className="flex items-center mt-1 text-xs text-muted-foreground">
                        <UserCircle2 className="h-3 w-3 mr-1" />
                        <span>{activity.user}</span>
                        <span className="mx-1.5">•</span>
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{activity.time}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Quick Actions */}
      <div className="mt-6">
        <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-3">
          <Button 
            variant="outline" 
            className="h-auto py-3 justify-start"
            onClick={() => setLocation("/project-estimation")}
          >
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium">New Estimate</span>
              <span className="text-xs text-muted-foreground">Create estimate</span>
            </div>
          </Button>
          <Button 
            variant="outline" 
            className="h-auto py-3 justify-start"
            onClick={() => setLocation("/quotes")}
          >
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium">Manage Quotes</span>
              <span className="text-xs text-muted-foreground">View all quotes</span>
            </div>
          </Button>
        </div>
      </div>
    </MobileLayout>
  );
}