import { useState } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Project, Task } from "@shared/schema";
import { MobileLayout } from "@/components/mobile-layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  MoreVertical, 
  Plus, 
  Clock, 
  Calendar, 
  CheckCircle2, 
  FileText, 
  LayoutDashboard,
  PencilRuler,
  Image,
  ListTodo,
  FileBarChart,
  CalendarClock,
  ChevronRight,
  FileEdit,
  Trash,
  Share
} from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

interface MobileProjectViewProps {
  projectId: number;
}

export function MobileProjectView({ projectId }: MobileProjectViewProps) {
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState("overview");
  
  // Fetch project
  const { data: project, isLoading: projectLoading } = useQuery<Project>({
    queryKey: ["/api/projects", projectId],
  });
  
  // Fetch tasks
  const { data: tasks, isLoading: tasksLoading } = useQuery<Task[]>({
    queryKey: ["/api/tasks", { projectId }],
  });
  
  // Helper functions for display
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "in-progress": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "planning": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "on-hold": return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };
  
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
  };
  
  // Sample project milestones
  const milestones = [
    { id: 1, name: "Project Kickoff", date: "2025-04-10", status: "completed" },
    { id: 2, name: "Floor Plans Approved", date: "2025-04-20", status: "completed" },
    { id: 3, name: "Electrical Design", date: "2025-05-15", status: "in-progress" },
    { id: 4, name: "Material Delivery", date: "2025-06-01", status: "planning" },
    { id: 5, name: "Installation Complete", date: "2025-06-20", status: "planning" },
    { id: 6, name: "Final Inspection", date: "2025-06-30", status: "planning" }
  ];
  
  // Project menu actions
  const projectActions = [
    { label: "Edit Project", icon: <FileEdit className="h-4 w-4 mr-2" />, action: () => setLocation(`/project/${projectId}/edit`) },
    { label: "Share Project", icon: <Share className="h-4 w-4 mr-2" />, action: () => alert("Share Project") },
    { label: "Delete Project", icon: <Trash className="h-4 w-4 mr-2" />, action: () => alert("Delete Project"), destructive: true }
  ];
  
  return (
    <MobileLayout 
      title={projectLoading ? "Loading..." : project?.name || "Project Details"}
      showBackButton
      actions={
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="ghost" className="rounded-full">
              <MoreVertical className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {projectActions.map((action, index) => (
              <DropdownMenuItem 
                key={index} 
                onClick={action.action}
                className={action.destructive ? "text-destructive" : ""}
              >
                {action.icon}
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      }
    >
      {projectLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-1/2" />
          <div className="grid grid-cols-2 gap-4 mt-6">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </div>
      ) : (
        <>
          {/* Project Header */}
          <div className="mb-6">
            <Badge className={getStatusColor("in-progress")}>In Progress</Badge>
            <p className="text-sm text-muted-foreground mt-3">
              {project?.description || "No description provided"}
            </p>
            
            <div className="flex items-center mt-4 text-sm">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                <span className="text-muted-foreground">Created: </span>
                <span className="ml-1">{formatDate(project?.createdAt?.toString())}</span>
              </div>
              
              <Separator orientation="vertical" className="mx-2 h-4" />
              
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                <span className="text-muted-foreground">Due: </span>
                <span className="ml-1">{formatDate("2025-06-30")}</span>
              </div>
            </div>
            
            <div className="mt-4 flex items-center">
              <span className="text-xs font-medium mr-2">Project Progress:</span>
              <div className="flex-1">
                <Progress value={65} className="h-2" />
              </div>
              <span className="text-xs ml-2">65%</span>
            </div>
          </div>
          
          {/* Project Navigation */}
          <ScrollArea className="w-full">
            <div className="flex gap-2 pb-4">
              <Button 
                variant="outline" 
                size="sm" 
                className="whitespace-nowrap"
                onClick={() => setLocation(`/project/${projectId}/floor-plans`)}
              >
                <Image className="h-4 w-4 mr-1.5" /> Floor Plans
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="whitespace-nowrap"
                onClick={() => setLocation(`/project/${projectId}/symbols`)}
              >
                <PencilRuler className="h-4 w-4 mr-1.5" /> Symbols
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="whitespace-nowrap"
                onClick={() => setLocation(`/project/${projectId}/estimation`)}
              >
                <FileBarChart className="h-4 w-4 mr-1.5" /> Estimation
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="whitespace-nowrap"
                onClick={() => setLocation(`/project/${projectId}/quotes`)}
              >
                <FileText className="h-4 w-4 mr-1.5" /> Quotes
              </Button>
            </div>
          </ScrollArea>
          
          {/* Tabs for project details */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="mt-4 space-y-4">
              {/* Summary Cards */}
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-muted/30">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2.5 rounded-full bg-blue-100 dark:bg-blue-900/50">
                        <ListTodo className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Tasks</p>
                        <p className="text-2xl font-semibold">{tasks?.length || 0}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-muted/30">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2.5 rounded-full bg-green-100 dark:bg-green-900/50">
                        <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Quotes</p>
                        <p className="text-2xl font-semibold">2</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Milestones */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Project Milestones</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {milestones.map((milestone, index) => (
                      <div key={milestone.id} className="relative">
                        {/* Timeline connector */}
                        {index < milestones.length - 1 && (
                          <div className="absolute top-6 bottom-0 left-3.5 w-px bg-muted-foreground/20"></div>
                        )}
                        
                        <div className="flex gap-3">
                          <div className={`
                            h-7 w-7 rounded-full flex-shrink-0 flex items-center justify-center
                            ${milestone.status === 'completed' 
                              ? 'bg-green-100 dark:bg-green-900/50' 
                              : milestone.status === 'in-progress'
                                ? 'bg-blue-100 dark:bg-blue-900/50'
                                : 'bg-muted'
                            }
                          `}>
                            {milestone.status === 'completed' ? (
                              <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
                            ) : (
                              <CalendarClock className={`h-4 w-4 ${
                                milestone.status === 'in-progress' 
                                  ? 'text-blue-600 dark:text-blue-400' 
                                  : 'text-muted-foreground'
                              }`} />
                            )}
                          </div>
                          
                          <div>
                            <p className="text-sm font-medium">{milestone.name}</p>
                            <div className="flex items-center mt-1">
                              <p className="text-xs text-muted-foreground">{formatDate(milestone.date)}</p>
                              <Badge className={`ml-2 text-xs ${getStatusColor(milestone.status)}`}>
                                {milestone.status.charAt(0).toUpperCase() + milestone.status.slice(1)}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              
              {/* Team Members */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Team Members</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { name: "John Smith", role: "Project Manager", initials: "JS" },
                      { name: "Maria Lopez", role: "Electrical Engineer", initials: "ML" },
                      { name: "David Kim", role: "Designer", initials: "DK" }
                    ].map((member, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <Avatar className="h-9 w-9">
                          <AvatarFallback>{member.initials}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{member.name}</p>
                          <p className="text-xs text-muted-foreground">{member.role}</p>
                        </div>
                      </div>
                    ))}
                    
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full mt-2"
                      onClick={() => alert("Add team member")}
                    >
                      <Plus className="h-3.5 w-3.5 mr-1" /> Add Team Member
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="tasks" className="mt-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium">Project Tasks</h3>
                <Button 
                  size="sm" 
                  onClick={() => alert("Add task")}
                >
                  <Plus className="h-3.5 w-3.5 mr-1" /> Add Task
                </Button>
              </div>
              
              <div className="space-y-3">
                {tasksLoading ? (
                  Array.from({ length: 3 }).map((_, i) => (
                    <Card key={i}>
                      <CardContent className="p-4">
                        <Skeleton className="h-5 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-1/2" />
                      </CardContent>
                    </Card>
                  ))
                ) : tasks && tasks.length > 0 ? (
                  tasks.map(task => (
                    <Card key={task.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">{task.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">{task.description}</p>
                          </div>
                          <Badge className={getStatusColor("in-progress")}>
                            In Progress
                          </Badge>
                        </div>
                        
                        <div className="flex items-center justify-between mt-3">
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Calendar className="h-3.5 w-3.5 mr-1" />
                            <span>Due: {formatDate(task.dueDate?.toString())}</span>
                          </div>
                          
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-[10px]">
                              {task.assignees ? "JS" : "?"}
                            </AvatarFallback>
                          </Avatar>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <ListTodo className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                    <h3 className="text-lg font-medium mb-1">No tasks yet</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Add tasks to track project progress
                    </p>
                    <Button onClick={() => alert("Add task")}>
                      <Plus className="h-4 w-4 mr-2" /> Add Task
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </MobileLayout>
  );
}