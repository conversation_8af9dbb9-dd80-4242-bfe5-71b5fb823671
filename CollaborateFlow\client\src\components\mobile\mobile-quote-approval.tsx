import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { MobileLayout } from "@/components/mobile-layout";
import { <PERSON>, CardContent, CardHeader, CardFooter, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  Calendar, 
  Building, 
  Mail, 
  Phone, 
  MapPin,
  Download,
  Plus,
  Send,
  Loader2
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";

interface MobileQuoteApprovalProps {
  quoteId: string;
  quoteToken?: string;
}

interface QuoteItem {
  category: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface QuoteData {
  id: number;
  quoteNumber: string;
  projectId: number;
  projectName: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  clientAddress?: string;
  clientCompany?: string;
  issueDate: string;
  expiryDate: string;
  subtotal: number;
  tax: number;
  taxRate: number;
  total: number;
  laborTotal: number;
  materialsTotal: number;
  status: string;
  paymentTerms?: string;
  notes?: string;
  items: QuoteItem[];
  logoUrl?: string;
  token?: string;
  organizationId?: number;
  signatureData?: string;
  approvedAt?: string;
  rejectedAt?: string;
  viewedAt?: string;
}

interface FeedbackMessage {
  id: number;
  quoteId: number;
  message: string;
  section?: string;
  itemIndex?: number;
  isClient: boolean;
  createdAt: string;
}

export function MobileQuoteApproval({ quoteId, quoteToken }: MobileQuoteApprovalProps) {
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState("quote");
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quoteData, setQuoteData] = useState<QuoteData | null>(null);
  const [feedbackMessages, setFeedbackMessages] = useState<FeedbackMessage[]>([]);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  
  const { toast } = useToast();
  
  // Load quote data
  useEffect(() => {
    const fetchQuote = async () => {
      try {
        setIsLoading(true);
        
        if (!quoteId || !quoteToken) {
          setError("Invalid quote link. Please check your email for the correct link.");
          return;
        }
        
        // Fetch quote data
        const response = await fetch(`/api/quotes/${quoteId}/view/${quoteToken}`);
        
        if (!response.ok) {
          throw new Error("Failed to load quote. The link may be expired or invalid.");
        }
        
        const data = await response.json();
        setQuoteData(data);
        
        // Fetch feedback
        const feedbackResponse = await fetch(`/api/quotes/${quoteId}/feedback?token=${quoteToken}`);
        
        if (feedbackResponse.ok) {
          const feedbackData = await feedbackResponse.json();
          setFeedbackMessages(feedbackData);
        }
        
        // Update quote status to viewed if it's currently "sent"
        if (data.status === "sent") {
          updateQuoteStatus("viewed");
        }
      } catch (err) {
        console.error("Error loading quote:", err);
        setError(err instanceof Error ? err.message : "An unexpected error occurred");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchQuote();
  }, [quoteId, quoteToken]);
  
  // Update quote status
  const updateQuoteStatus = async (status: string) => {
    try {
      const response = await fetch(`/api/quotes/${quoteId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          status,
          token: quoteToken
        })
      });
      
      if (!response.ok) {
        throw new Error("Failed to update quote status");
      }
      
      // Update local state
      setQuoteData(prev => prev ? { ...prev, status } : null);
    } catch (err) {
      console.error("Error updating status:", err);
    }
  };
  
  // Format functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };
  
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
  };
  
  const formatTimestamp = (timestamp: string) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true
    });
  };
  
  // Submit feedback
  const handleSendFeedback = async () => {
    if (!feedback.trim()) return;
    
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/quotes/${quoteId}/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: feedback,
          token: quoteToken
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to submit feedback');
      }
      
      const newFeedback = await response.json();
      
      // Add to feedback list
      setFeedbackMessages(prev => [...prev, newFeedback]);
      setFeedback('');
      
      toast({
        title: "Feedback Sent",
        description: "Your feedback has been submitted successfully"
      });
    } catch (err) {
      console.error('Error sending feedback:', err);
      
      toast({
        title: "Error",
        description: "Failed to send feedback. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle approve action
  const handleApprove = async () => {
    setShowApproveDialog(true);
  };
  
  const confirmApproval = async () => {
    if (!agreeToTerms) {
      toast({
        title: "Action Required",
        description: "Please agree to the terms before approving",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/quotes/${quoteId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: quoteToken,
          signature: "mobile-signature"  // In a real app, we would capture a real signature
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to approve quote');
      }
      
      // Update quote data locally
      setQuoteData(prev => prev ? { ...prev, status: 'approved' } : null);
      setShowApproveDialog(false);
      
      toast({
        title: "Quote Approved",
        description: "Thank you for approving this quote. We'll be in touch soon."
      });
    } catch (err) {
      console.error('Error approving quote:', err);
      
      toast({
        title: "Error",
        description: "Failed to approve quote. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle reject action
  const handleReject = () => {
    setShowRejectDialog(true);
  };
  
  const confirmRejection = async () => {
    if (!rejectReason.trim()) {
      toast({
        title: "Action Required",
        description: "Please provide a reason for rejecting the quote",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/quotes/${quoteId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: quoteToken,
          reason: rejectReason
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to reject quote');
      }
      
      // Update quote data locally
      setQuoteData(prev => prev ? { ...prev, status: 'rejected' } : null);
      setShowRejectDialog(false);
      
      toast({
        title: "Quote Rejected",
        description: "The quote has been rejected. You can continue to provide feedback."
      });
    } catch (err) {
      console.error('Error rejecting quote:', err);
      
      toast({
        title: "Error",
        description: "Failed to reject quote. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Loading state
  if (isLoading) {
    return (
      <MobileLayout 
        title="Quote Review" 
        showBackButton={false}
        hideNav={true}
      >
        <div className="flex flex-col items-center justify-center h-[70vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="mt-4 text-muted-foreground">Loading quote...</p>
        </div>
      </MobileLayout>
    );
  }
  
  // Error state
  if (error || !quoteData) {
    return (
      <MobileLayout 
        title="Quote Review" 
        showBackButton={false}
        hideNav={true}
      >
        <div className="flex flex-col items-center justify-center h-[70vh]">
          <XCircle className="h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-xl font-bold mb-2">Error Loading Quote</h2>
          <p className="text-center text-muted-foreground mb-6">{error || "Quote not found"}</p>
          <Button onClick={() => window.location.href = "/"}>
            Return to Homepage
          </Button>
        </div>
      </MobileLayout>
    );
  }
  
  return (
    <MobileLayout 
      title="Quote Review" 
      showBackButton={!quoteToken}
      hideNav={!!quoteToken}
    >
      {/* Approve Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Quote</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-4">By approving this quote, you agree to the terms and conditions outlined in the quote.</p>
            
            <div className="flex items-start space-x-2 mb-6">
              <Checkbox 
                id="terms" 
                checked={agreeToTerms}
                onCheckedChange={(checked) => setAgreeToTerms(checked === true)}
              />
              <Label 
                htmlFor="terms" 
                className="text-sm font-normal"
              >
                I agree to the terms and accept this quote for a total of {formatCurrency(quoteData.total)}
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowApproveDialog(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={confirmApproval}
              disabled={isSubmitting || !agreeToTerms}
            >
              {isSubmitting ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              Confirm Approval
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Quote</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-4">Please provide a reason for rejecting this quote:</p>
            <Textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="Reason for rejection..."
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowRejectDialog(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={confirmRejection}
              disabled={isSubmitting || !rejectReason.trim()}
            >
              {isSubmitting ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              Confirm Rejection
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Quote header */}
      <div className="space-y-4 mb-6">
        <div className="flex justify-between items-start">
          <div>
            <Badge className={cn(
              "mb-2",
              quoteData.status === 'approved' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
              quoteData.status === 'rejected' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
              quoteData.status === 'viewed' && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
              quoteData.status === 'sent' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
            )}>
              {quoteData.status === 'sent' ? 'Pending Approval' : 
               quoteData.status === 'viewed' ? 'Under Review' : 
               quoteData.status.charAt(0).toUpperCase() + quoteData.status.slice(1)}
            </Badge>
            <h1 className="text-xl font-bold">{quoteData.projectName}</h1>
            <p className="text-sm text-muted-foreground">Quote #{quoteData.quoteNumber}</p>
          </div>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
            <span>Issued: {formatDate(quoteData.issueDate)}</span>
          </div>
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
            <span>Expires: {formatDate(quoteData.expiryDate)}</span>
          </div>
        </div>
      </div>
      
      {/* Tabs for quote/feedback */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-2 w-full">
          <TabsTrigger value="quote">Quote Details</TabsTrigger>
          <TabsTrigger value="feedback">Discussion</TabsTrigger>
        </TabsList>
        
        <TabsContent value="quote" className="space-y-4 pt-2">
          {/* Company & Client Info */}
          <Card>
            <CardContent className="p-4 space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">From</h3>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center text-primary mr-2">
                    EL
                  </div>
                  <div>
                    <p className="font-medium">CoElec</p>
                    <p className="text-xs text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-sm font-medium mb-2">To</h3>
                <div className="space-y-1">
                  <div className="flex items-start">
                    <Building className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                    <p>{quoteData.clientName}</p>
                  </div>
                  <div className="flex items-start">
                    <Mail className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                    <p>{quoteData.clientEmail}</p>
                  </div>
                  {quoteData.clientPhone && (
                    <div className="flex items-start">
                      <Phone className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                      <p>{quoteData.clientPhone}</p>
                    </div>
                  )}
                  {quoteData.clientAddress && (
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                      <p>{quoteData.clientAddress}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Quote Items */}
          <Card>
            <CardHeader className="px-4 py-3">
              <CardTitle className="text-base">Quote Items</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="max-h-96">
                <div className="divide-y">
                  {quoteData.items.map((item, index) => (
                    <div key={index} className="p-4 space-y-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{item.description}</h4>
                          <Badge variant="outline" className="mt-1">
                            {item.category}
                          </Badge>
                        </div>
                        <p className="font-medium">{formatCurrency(item.total)}</p>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        {item.quantity} x {formatCurrency(item.unitPrice)}
                      </p>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
            <CardFooter className="border-t p-4 space-y-3">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{formatCurrency(quoteData.subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax</span>
                <span>{formatCurrency(quoteData.tax)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>{formatCurrency(quoteData.total)}</span>
              </div>
            </CardFooter>
          </Card>
          
          {/* Terms & Notes */}
          <Card>
            <CardContent className="p-4 space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-1">Payment Terms</h3>
                <p className="text-sm">{quoteData.paymentTerms}</p>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-sm font-medium mb-1">Notes</h3>
                <p className="text-sm">{quoteData.notes}</p>
              </div>
            </CardContent>
          </Card>
          
          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3 mt-6">
            <Button 
              variant="outline" 
              size="lg" 
              className="w-full"
              onClick={handleReject}
              disabled={isSubmitting}
            >
              {isSubmitting ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <XCircle className="h-4 w-4 mr-2" />}
              Reject
            </Button>
            <Button 
              variant="default" 
              size="lg" 
              className="w-full"
              onClick={handleApprove}
              disabled={isSubmitting}
            >
              {isSubmitting ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <CheckCircle className="h-4 w-4 mr-2" />}
              Approve
            </Button>
          </div>
        </TabsContent>
        
        <TabsContent value="feedback" className="space-y-4 pt-2">
          {/* Feedback Messages */}
          <Card className="mb-4">
            <CardContent className="p-4">
              <div className="space-y-4">
                {feedbackMessages.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No feedback messages yet</p>
                  </div>
                ) : (
                  feedbackMessages.map((msg) => (
                    <div 
                      key={msg.id} 
                      className={`flex flex-col ${msg.isClient ? 'items-end' : 'items-start'}`}
                    >
                      <div className="flex items-center mb-1">
                        <p className="text-xs text-muted-foreground">
                          {!msg.isClient && "Contractor • "}
                          {formatTimestamp(msg.createdAt)}
                        </p>
                      </div>
                      <div 
                        className={`
                          px-4 py-3 rounded-lg max-w-[85%]
                          ${msg.isClient 
                            ? 'bg-primary text-primary-foreground' 
                            : 'bg-muted'
                          }
                        `}
                      >
                        {msg.section && (
                          <Badge variant="outline" className="mb-2">
                            {msg.section}
                          </Badge>
                        )}
                        <p className="text-sm">{msg.message}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
          
          {/* Send Message Form */}
          <div className="flex gap-2">
            <Textarea 
              placeholder="Type your message here..." 
              className="min-h-[80px]"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
            />
            <Button 
              className="flex-shrink-0 self-end" 
              size="icon"
              disabled={!feedback.trim() || isSubmitting}
              onClick={handleSendFeedback}
            >
              {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </MobileLayout>
  );
}