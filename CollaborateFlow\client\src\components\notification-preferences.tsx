import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  AlertCircle, 
  Mail, 
  MessageSquare, 
  Bell, 
  Clock, 
  Calendar,
  FileText,
  DollarSign,
  Settings,
  UserPlus,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface NotificationSetting {
  id: string;
  label: string;
  description: string;
  email: boolean;
  push: boolean;
  sms: boolean;
  icon?: React.ReactNode;
  category: string;
}

export function NotificationPreferences() {
  const { toast } = useToast();
  const [notificationSettings, setNotificationSettings] = useState<NotificationSetting[]>([
    // Project Notifications
    {
      id: "project_created",
      label: "Project Created",
      description: "When a new project is created",
      email: true,
      push: true,
      sms: false,
      icon: <FileText className="h-4 w-4" />,
      category: "projects"
    },
    {
      id: "project_updated",
      label: "Project Updated",
      description: "When project details are changed",
      email: false,
      push: true,
      sms: false,
      icon: <FileText className="h-4 w-4" />,
      category: "projects"
    },
    {
      id: "task_assigned",
      label: "Task Assigned",
      description: "When you are assigned to a task",
      email: true,
      push: true,
      sms: false,
      icon: <CheckCircle className="h-4 w-4" />,
      category: "projects"
    },
    {
      id: "task_due",
      label: "Task Due Soon",
      description: "When a task is approaching its due date",
      email: true,
      push: true,
      sms: false,
      icon: <Clock className="h-4 w-4" />,
      category: "projects"
    },
    
    // Quote Notifications
    {
      id: "quote_created",
      label: "Quote Created",
      description: "When a new quote is created",
      email: true,
      push: true,
      sms: false,
      icon: <DollarSign className="h-4 w-4" />,
      category: "quotes"
    },
    {
      id: "quote_sent",
      label: "Quote Sent",
      description: "When a quote is sent to a client",
      email: true,
      push: true,
      sms: false,
      icon: <Mail className="h-4 w-4" />,
      category: "quotes"
    },
    {
      id: "quote_viewed",
      label: "Quote Viewed",
      description: "When a client views a quote",
      email: false,
      push: true,
      sms: false,
      icon: <MessageSquare className="h-4 w-4" />,
      category: "quotes"
    },
    {
      id: "quote_approved",
      label: "Quote Approved",
      description: "When a client approves a quote",
      email: true,
      push: true,
      sms: true,
      icon: <CheckCircle className="h-4 w-4" />,
      category: "quotes"
    },
    {
      id: "quote_rejected",
      label: "Quote Rejected",
      description: "When a client rejects a quote",
      email: true,
      push: true,
      sms: true,
      icon: <AlertTriangle className="h-4 w-4" />,
      category: "quotes"
    },
    {
      id: "quote_expiring",
      label: "Quote Expiring",
      description: "When a quote is about to expire",
      email: true,
      push: true,
      sms: false,
      icon: <Calendar className="h-4 w-4" />,
      category: "quotes"
    },
    
    // Team Notifications
    {
      id: "member_invited",
      label: "Member Invited",
      description: "When a new team member is invited",
      email: true,
      push: true,
      sms: false,
      icon: <UserPlus className="h-4 w-4" />,
      category: "team"
    },
    {
      id: "member_joined",
      label: "Member Joined",
      description: "When someone joins your team",
      email: true,
      push: true,
      sms: false,
      icon: <UserPlus className="h-4 w-4" />,
      category: "team"
    },
    
    // System Notifications
    {
      id: "account_update",
      label: "Account Updates",
      description: "Important account information and updates",
      email: true,
      push: true,
      sms: true,
      icon: <Settings className="h-4 w-4" />,
      category: "system"
    },
    {
      id: "security_alert",
      label: "Security Alerts",
      description: "Important security notifications",
      email: true,
      push: true,
      sms: true,
      icon: <AlertCircle className="h-4 w-4" />,
      category: "system"
    },
  ]);
  
  const handleToggle = (id: string, channel: "email" | "push" | "sms") => {
    setNotificationSettings(
      notificationSettings.map((setting) => {
        if (setting.id === id) {
          return {
            ...setting,
            [channel]: !setting[channel],
          };
        }
        return setting;
      })
    );
    
    toast({
      title: "Notification settings updated",
      description: "Your notification preferences have been saved.",
    });
  };
  
  // Group notifications by category
  const getNotificationsByCategory = (category: string) => {
    return notificationSettings.filter((setting) => setting.category === category);
  };
  
  // Bulk toggle all notifications in a category and channel
  const toggleAll = (category: string, channel: "email" | "push" | "sms", value: boolean) => {
    setNotificationSettings(
      notificationSettings.map((setting) => {
        if (setting.category === category) {
          return {
            ...setting,
            [channel]: value,
          };
        }
        return setting;
      })
    );
    
    toast({
      title: "Notification settings updated",
      description: `All ${category} ${channel} notifications have been ${value ? "enabled" : "disabled"}.`,
    });
  };
  
  // Check if all notifications in a category and channel are enabled
  const areAllEnabled = (category: string, channel: "email" | "push" | "sms") => {
    const categorySettings = getNotificationsByCategory(category);
    return categorySettings.every((setting) => setting[channel]);
  };
  
  // Check if some (but not all) notifications in a category and channel are enabled
  const areSomeEnabled = (category: string, channel: "email" | "push" | "sms") => {
    const categorySettings = getNotificationsByCategory(category);
    const enabledCount = categorySettings.filter((setting) => setting[channel]).length;
    return enabledCount > 0 && enabledCount < categorySettings.length;
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Notification Preferences</h3>
        <p className="text-sm text-muted-foreground">
          Control how and when you receive notifications
        </p>
      </div>
      
      <Separator />
      
      {/* Project Notifications */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-md font-medium">Project Notifications</h4>
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="projects-email-all" 
                checked={areAllEnabled("projects", "email")}
                onCheckedChange={(checked) => toggleAll("projects", "email", checked)}
                aria-label="Toggle all project email notifications"
              />
              <Label htmlFor="projects-email-all" className="text-xs">Email</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="projects-push-all" 
                checked={areAllEnabled("projects", "push")}
                onCheckedChange={(checked) => toggleAll("projects", "push", checked)}
                aria-label="Toggle all project push notifications"
              />
              <Label htmlFor="projects-push-all" className="text-xs">Push</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="projects-sms-all" 
                checked={areAllEnabled("projects", "sms")}
                onCheckedChange={(checked) => toggleAll("projects", "sms", checked)}
                aria-label="Toggle all project SMS notifications"
              />
              <Label htmlFor="projects-sms-all" className="text-xs">SMS</Label>
            </div>
          </div>
        </div>
        
        <div className="border rounded-md overflow-hidden">
          <div className="grid grid-cols-4 gap-4 p-4 border-b bg-muted/10 text-sm font-medium">
            <div className="col-span-1">Notification</div>
            <div className="col-span-3 grid grid-cols-3 md:grid-cols-6">
              <div className="col-span-1 md:col-span-3">Description</div>
              <div className="text-center">Email</div>
              <div className="text-center">Push</div>
              <div className="text-center">SMS</div>
            </div>
          </div>
          
          <div className="divide-y">
            {getNotificationsByCategory("projects").map((setting) => (
              <div key={setting.id} className="grid grid-cols-4 gap-4 p-4 items-center text-sm">
                <div className="col-span-1 flex items-center gap-2">
                  {setting.icon}
                  <span className="font-medium">{setting.label}</span>
                </div>
                <div className="col-span-3 grid grid-cols-3 md:grid-cols-6 items-center">
                  <div className="col-span-1 md:col-span-3 text-muted-foreground">
                    {setting.description}
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-email`}
                      checked={setting.email}
                      onCheckedChange={() => handleToggle(setting.id, "email")}
                      aria-label={`Toggle ${setting.label} email notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-push`}
                      checked={setting.push}
                      onCheckedChange={() => handleToggle(setting.id, "push")}
                      aria-label={`Toggle ${setting.label} push notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-sms`}
                      checked={setting.sms}
                      onCheckedChange={() => handleToggle(setting.id, "sms")}
                      aria-label={`Toggle ${setting.label} SMS notifications`}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Quote Notifications */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-md font-medium">Quote Notifications</h4>
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="quotes-email-all" 
                checked={areAllEnabled("quotes", "email")}
                onCheckedChange={(checked) => toggleAll("quotes", "email", checked)}
                aria-label="Toggle all quote email notifications"
              />
              <Label htmlFor="quotes-email-all" className="text-xs">Email</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="quotes-push-all" 
                checked={areAllEnabled("quotes", "push")}
                onCheckedChange={(checked) => toggleAll("quotes", "push", checked)}
                aria-label="Toggle all quote push notifications"
              />
              <Label htmlFor="quotes-push-all" className="text-xs">Push</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="quotes-sms-all" 
                checked={areAllEnabled("quotes", "sms")}
                onCheckedChange={(checked) => toggleAll("quotes", "sms", checked)}
                aria-label="Toggle all quote SMS notifications"
              />
              <Label htmlFor="quotes-sms-all" className="text-xs">SMS</Label>
            </div>
          </div>
        </div>
        
        <div className="border rounded-md overflow-hidden">
          <div className="grid grid-cols-4 gap-4 p-4 border-b bg-muted/10 text-sm font-medium">
            <div className="col-span-1">Notification</div>
            <div className="col-span-3 grid grid-cols-3 md:grid-cols-6">
              <div className="col-span-1 md:col-span-3">Description</div>
              <div className="text-center">Email</div>
              <div className="text-center">Push</div>
              <div className="text-center">SMS</div>
            </div>
          </div>
          
          <div className="divide-y">
            {getNotificationsByCategory("quotes").map((setting) => (
              <div key={setting.id} className="grid grid-cols-4 gap-4 p-4 items-center text-sm">
                <div className="col-span-1 flex items-center gap-2">
                  {setting.icon}
                  <span className="font-medium">{setting.label}</span>
                </div>
                <div className="col-span-3 grid grid-cols-3 md:grid-cols-6 items-center">
                  <div className="col-span-1 md:col-span-3 text-muted-foreground">
                    {setting.description}
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-email`}
                      checked={setting.email}
                      onCheckedChange={() => handleToggle(setting.id, "email")}
                      aria-label={`Toggle ${setting.label} email notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-push`}
                      checked={setting.push}
                      onCheckedChange={() => handleToggle(setting.id, "push")}
                      aria-label={`Toggle ${setting.label} push notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-sms`}
                      checked={setting.sms}
                      onCheckedChange={() => handleToggle(setting.id, "sms")}
                      aria-label={`Toggle ${setting.label} SMS notifications`}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Team Notifications */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-md font-medium">Team Notifications</h4>
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="team-email-all" 
                checked={areAllEnabled("team", "email")}
                onCheckedChange={(checked) => toggleAll("team", "email", checked)}
                aria-label="Toggle all team email notifications"
              />
              <Label htmlFor="team-email-all" className="text-xs">Email</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="team-push-all" 
                checked={areAllEnabled("team", "push")}
                onCheckedChange={(checked) => toggleAll("team", "push", checked)}
                aria-label="Toggle all team push notifications"
              />
              <Label htmlFor="team-push-all" className="text-xs">Push</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="team-sms-all" 
                checked={areAllEnabled("team", "sms")}
                onCheckedChange={(checked) => toggleAll("team", "sms", checked)}
                aria-label="Toggle all team SMS notifications"
              />
              <Label htmlFor="team-sms-all" className="text-xs">SMS</Label>
            </div>
          </div>
        </div>
        
        <div className="border rounded-md overflow-hidden">
          <div className="grid grid-cols-4 gap-4 p-4 border-b bg-muted/10 text-sm font-medium">
            <div className="col-span-1">Notification</div>
            <div className="col-span-3 grid grid-cols-3 md:grid-cols-6">
              <div className="col-span-1 md:col-span-3">Description</div>
              <div className="text-center">Email</div>
              <div className="text-center">Push</div>
              <div className="text-center">SMS</div>
            </div>
          </div>
          
          <div className="divide-y">
            {getNotificationsByCategory("team").map((setting) => (
              <div key={setting.id} className="grid grid-cols-4 gap-4 p-4 items-center text-sm">
                <div className="col-span-1 flex items-center gap-2">
                  {setting.icon}
                  <span className="font-medium">{setting.label}</span>
                </div>
                <div className="col-span-3 grid grid-cols-3 md:grid-cols-6 items-center">
                  <div className="col-span-1 md:col-span-3 text-muted-foreground">
                    {setting.description}
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-email`}
                      checked={setting.email}
                      onCheckedChange={() => handleToggle(setting.id, "email")}
                      aria-label={`Toggle ${setting.label} email notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-push`}
                      checked={setting.push}
                      onCheckedChange={() => handleToggle(setting.id, "push")}
                      aria-label={`Toggle ${setting.label} push notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-sms`}
                      checked={setting.sms}
                      onCheckedChange={() => handleToggle(setting.id, "sms")}
                      aria-label={`Toggle ${setting.label} SMS notifications`}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* System Notifications */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-md font-medium">System Notifications</h4>
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="system-email-all" 
                checked={areAllEnabled("system", "email")}
                onCheckedChange={(checked) => toggleAll("system", "email", checked)}
                aria-label="Toggle all system email notifications"
              />
              <Label htmlFor="system-email-all" className="text-xs">Email</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="system-push-all" 
                checked={areAllEnabled("system", "push")}
                onCheckedChange={(checked) => toggleAll("system", "push", checked)}
                aria-label="Toggle all system push notifications"
              />
              <Label htmlFor="system-push-all" className="text-xs">Push</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                id="system-sms-all" 
                checked={areAllEnabled("system", "sms")}
                onCheckedChange={(checked) => toggleAll("system", "sms", checked)}
                aria-label="Toggle all system SMS notifications"
              />
              <Label htmlFor="system-sms-all" className="text-xs">SMS</Label>
            </div>
          </div>
        </div>
        
        <div className="border rounded-md overflow-hidden">
          <div className="grid grid-cols-4 gap-4 p-4 border-b bg-muted/10 text-sm font-medium">
            <div className="col-span-1">Notification</div>
            <div className="col-span-3 grid grid-cols-3 md:grid-cols-6">
              <div className="col-span-1 md:col-span-3">Description</div>
              <div className="text-center">Email</div>
              <div className="text-center">Push</div>
              <div className="text-center">SMS</div>
            </div>
          </div>
          
          <div className="divide-y">
            {getNotificationsByCategory("system").map((setting) => (
              <div key={setting.id} className="grid grid-cols-4 gap-4 p-4 items-center text-sm">
                <div className="col-span-1 flex items-center gap-2">
                  {setting.icon}
                  <span className="font-medium">{setting.label}</span>
                </div>
                <div className="col-span-3 grid grid-cols-3 md:grid-cols-6 items-center">
                  <div className="col-span-1 md:col-span-3 text-muted-foreground">
                    {setting.description}
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-email`}
                      checked={setting.email}
                      onCheckedChange={() => handleToggle(setting.id, "email")}
                      aria-label={`Toggle ${setting.label} email notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-push`}
                      checked={setting.push}
                      onCheckedChange={() => handleToggle(setting.id, "push")}
                      aria-label={`Toggle ${setting.label} push notifications`}
                    />
                  </div>
                  <div className="flex justify-center">
                    <Switch 
                      id={`${setting.id}-sms`}
                      checked={setting.sms}
                      onCheckedChange={() => handleToggle(setting.id, "sms")}
                      aria-label={`Toggle ${setting.label} SMS notifications`}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <Button>Save Preferences</Button>
      </div>
    </div>
  );
}