import { useState, useMemo } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { useSupabaseAuth } from "@/hooks/use-supabase-auth";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Project, insertProjectSchema } from "@shared/schema";
import { 
  CalendarDays, 
  ArrowUpRight, 
  Plus, 
  Search, 
  Filter, 
  X,
  ArrowUpDown,
  CheckCircle2, 
  Clock,
  PauseCircle,
  XCircle
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AICard } from "@/components/ai-card";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { AddProjectDialog } from "@/components/add-project-dialog";
import { Input } from "@/components/ui/input";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuGroup,
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem
} from "@/components/ui/dropdown-menu";

// Helper function to format date string
const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric"
  });
};

// Status badge component
const StatusBadge = ({ status }: { status?: string | null }) => {
  switch (status) {
    case "planning":
      return <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">Planning</Badge>;
    case "in-progress":
      return <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">In Progress</Badge>;
    case "completed":
      return <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">Completed</Badge>;
    case "on-hold":
      return <Badge variant="outline" className="bg-orange-500/10 text-orange-500 border-orange-500/20">On Hold</Badge>;
    case "cancelled":
      return <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/20">Cancelled</Badge>;
    default:
      return <Badge variant="outline" className="bg-muted text-muted-foreground">Unknown</Badge>;
  }
};

// Status icon component
const StatusIcon = ({ status }: { status?: string | null }) => {
  switch (status) {
    case "planning":
      return <Clock className="h-4 w-4 text-blue-500" />;
    case "in-progress":
      return <ArrowUpRight className="h-4 w-4 text-yellow-500" />;
    case "completed":
      return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    case "on-hold":
      return <PauseCircle className="h-4 w-4 text-orange-500" />;
    case "cancelled":
      return <XCircle className="h-4 w-4 text-red-500" />;
    default:
      return null;
  }
};

// Available sorting options
type SortField = "name" | "startDate" | "createdAt" | "clientName";
type SortDirection = "asc" | "desc";

export function ProjectHub() {
  const { user } = useSupabaseAuth();
  const [_, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [sortField, setSortField] = useState<SortField>("createdAt");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  
  // Fetch projects
  const { data: projects, isLoading } = useQuery<Project[]>({
    queryKey: ["/api/projects"],
  });

  // Delete project mutation
  const deleteProjectMutation = useMutation({
    mutationFn: async (projectId: number) => {
      await apiRequest("DELETE", `/api/projects/${projectId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/projects"] });
    },
  });

  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    if (!projects) return [];

    let filtered = [...projects];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        project => 
          project.name.toLowerCase().includes(query) ||
          project.description?.toLowerCase().includes(query) ||
          project.clientName?.toLowerCase().includes(query)
      );
    }
    
    // Apply status filters
    if (statusFilters.length > 0) {
      filtered = filtered.filter(project => 
        statusFilters.includes(project.status || "planning")
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      let valA: any = a[sortField];
      let valB: any = b[sortField];
      
      // Handle dates
      if (sortField === "startDate" || sortField === "createdAt") {
        valA = valA ? new Date(valA).getTime() : 0;
        valB = valB ? new Date(valB).getTime() : 0;
      }
      
      // Handle strings
      if (typeof valA === "string") {
        valA = valA.toLowerCase();
      }
      if (typeof valB === "string") {
        valB = valB.toLowerCase();
      }
      
      // For falsy values, always sort to the end
      if (!valA && valB) return sortDirection === "asc" ? 1 : -1;
      if (valA && !valB) return sortDirection === "asc" ? -1 : 1;
      
      // Standard comparison
      if (valA < valB) return sortDirection === "asc" ? -1 : 1;
      if (valA > valB) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });
    
    return filtered;
  }, [projects, searchQuery, statusFilters, sortField, sortDirection]);

  // Toggle a status filter
  const toggleStatusFilter = (status: string) => {
    setStatusFilters(prev => 
      prev.includes(status) 
        ? prev.filter(s => s !== status) 
        : [...prev, status]
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilters([]);
    setSortField("createdAt");
    setSortDirection("desc");
  };

  // Change sort field and direction
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(prev => prev === "asc" ? "desc" : "asc");
    } else {
      // Set new field with default direction
      setSortField(field);
      setSortDirection("asc");
    }
  };

  return (
    <div className="space-y-6">
      {/* Project Hub header with search, filters, and sort */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="relative flex-1 max-w-lg">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects by name, description or client..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9 rounded-full bg-muted/40 border-muted/30 focus-visible:ring-primary/20"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Status filter dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="rounded-full">
                <Filter className="h-4 w-4 mr-2" />
                {statusFilters.length > 0 ? `Filters (${statusFilters.length})` : "Filters"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={statusFilters.includes("planning")}
                onCheckedChange={() => toggleStatusFilter("planning")}
              >
                <Clock className="h-4 w-4 mr-2 text-blue-500" />
                Planning
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={statusFilters.includes("in-progress")}
                onCheckedChange={() => toggleStatusFilter("in-progress")}
              >
                <ArrowUpRight className="h-4 w-4 mr-2 text-yellow-500" />
                In Progress
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={statusFilters.includes("completed")}
                onCheckedChange={() => toggleStatusFilter("completed")}
              >
                <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                Completed
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={statusFilters.includes("on-hold")}
                onCheckedChange={() => toggleStatusFilter("on-hold")}
              >
                <PauseCircle className="h-4 w-4 mr-2 text-orange-500" />
                On Hold
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={statusFilters.includes("cancelled")}
                onCheckedChange={() => toggleStatusFilter("cancelled")}
              >
                <XCircle className="h-4 w-4 mr-2 text-red-500" />
                Cancelled
              </DropdownMenuCheckboxItem>
              {(statusFilters.length > 0 || searchQuery) && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onSelect={clearFilters}>
                    <X className="h-4 w-4 mr-2" />
                    Clear all filters
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Sort dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="rounded-full">
                <ArrowUpDown className="h-4 w-4 mr-2" />
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Sort Projects</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onSelect={() => handleSort("name")}>
                <span className="flex items-center">
                  Name
                  {sortField === "name" && (
                    <ArrowUpDown className={`h-4 w-4 ml-2 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleSort("clientName")}>
                <span className="flex items-center">
                  Client
                  {sortField === "clientName" && (
                    <ArrowUpDown className={`h-4 w-4 ml-2 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleSort("startDate")}>
                <span className="flex items-center">
                  Start Date
                  {sortField === "startDate" && (
                    <ArrowUpDown className={`h-4 w-4 ml-2 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleSort("createdAt")}>
                <span className="flex items-center">
                  Created Date
                  {sortField === "createdAt" && (
                    <ArrowUpDown className={`h-4 w-4 ml-2 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Create new project button */}
          <Dialog>
            <DialogTrigger asChild>
              <Button className="rounded-full bg-primary/90 hover:bg-primary">
                <Plus className="h-4 w-4 mr-1" /> New Project
              </Button>
            </DialogTrigger>
            <AddProjectDialog />
          </Dialog>
        </div>
      </div>

      {/* Projects list */}
      <AICard>
        {isLoading ? (
          <div className="h-64 flex items-center justify-center">
            <p className="text-muted-foreground">Loading projects...</p>
          </div>
        ) : !filteredProjects.length ? (
          <div className="h-64 flex flex-col items-center justify-center text-center">
            <div className="p-4 rounded-full bg-primary/10 mb-4">
              <ArrowUpRight className="h-6 w-6 text-primary" />
            </div>
            <h3 className="font-medium mb-1">
              {projects?.length ? "No matching projects found" : "No projects yet"}
            </h3>
            <p className="text-sm text-muted-foreground mb-4 max-w-md">
              {projects?.length 
                ? "Try adjusting your search terms or filters"
                : "Create your first electrical project to get started"
              }
            </p>
            {!projects?.length && (
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="rounded-full">
                    <Plus className="h-4 w-4 mr-1" /> New Project
                  </Button>
                </DialogTrigger>
                <AddProjectDialog />
              </Dialog>
            )}
            {(statusFilters.length > 0 || searchQuery) && (
              <Button variant="ghost" onClick={clearFilters} className="mt-2">
                <X className="h-4 w-4 mr-2" /> Clear filters
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-3 p-1">
            {filteredProjects.map((project) => (
              <div 
                key={project.id} 
                className="flex items-center justify-between p-3 rounded-lg hover:bg-muted cursor-pointer transition-colors" 
                onClick={() => navigate(`/project/${project.id}`)}
              >
                <div className="flex items-center flex-1 min-w-0">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3 shrink-0">
                    <StatusIcon status={project.status} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center flex-wrap gap-2">
                      <h4 className="font-medium text-base truncate mr-auto">{project.name}</h4>
                      <StatusBadge status={project.status} />
                    </div>
                    <div className="flex flex-wrap items-center gap-x-4 text-xs text-muted-foreground mt-1">
                      {project.clientName && (
                        <span className="truncate">Client: {project.clientName}</span>
                      )}
                      <span className="flex items-center">
                        <CalendarDays className="h-3 w-3 mr-1" />
                        {formatDate(project.startDate || undefined)} - {project.endDate ? formatDate(project.endDate) : "Ongoing"}
                      </span>
                      {project.estimatedBudget && (
                        <span>Budget: ${project.estimatedBudget}</span>
                      )}
                    </div>
                  </div>
                </div>
                <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </AICard>
    </div>
  );
}