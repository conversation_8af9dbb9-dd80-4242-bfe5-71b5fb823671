import { useState, useRef, useEffect } from "react";
import { 
  FileText, 
  Download, 
  Mail, 
  Copy, 
  Check, 
  Edit, 
  CreditCard, 
  Settings, 
  Send, 
  Image, 
  CalendarDays,
  Clock,
  Percent,
  DollarSign,
  Save,
  FileSearch,
  MessageSquare,
  Building,
  User,
  Phone
} from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { AICard } from "@/components/ai-card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";

interface Material {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unitPrice: number;
  supplier: string;
  alternativeIds: string[];
}

interface LaborTask {
  id: string;
  name: string;
  category: string;
  hours: number;
  rate: number;
  level: "apprentice" | "journeyman" | "master";
  workers: number;
}

interface QuoteItem {
  id: string;
  description: string;
  category: string;
  quantity: number;
  unitPrice: number;
  total: number;
  type: "material" | "labor" | "other";
}

interface QuoteData {
  id?: number;
  projectName: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  clientCompany?: string;
  clientAddress?: string;
  quoteNumber: string;
  issueDate: Date;
  expiryDate: Date;
  subtotal: number;
  tax: number;
  taxRate: number;
  markupPercentage: number;
  discount: number;
  laborTotal: number;
  materialsTotal: number;
  notes?: string;
  paymentTerms: string;
  logoUrl: string;
  status: "draft" | "sent" | "viewed" | "approved" | "declined";
  company: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
    license: string;
  };
  additionalOptions: {
    permitFees: boolean;
    inspectionFees: boolean;
    cleanup: boolean;
    warranty: boolean;
  };
  items: QuoteItem[];
}

const defaultCompany = {
  name: "CoElec Electrical Services",
  address: "123 Circuit Ave, Voltageville, CA 90210",
  phone: "(*************",
  email: "<EMAIL>",
  website: "www.coelec.com",
  license: "LIC# EL-12345"
};

interface QuoteGenerationProps {
  materials?: Material[];
  laborTasks?: LaborTask[];
  projectId?: string;
  projectName?: string;
  clientName?: string;
  existingQuote?: QuoteData | null;
}

// Quote schema for form validation
const quoteSchema = z.object({
  id: z.number().optional(),
  projectName: z.string().min(1, "Project name is required"),
  clientName: z.string().min(1, "Client name is required"),
  clientEmail: z.string().email("Invalid email address"),
  clientPhone: z.string().optional(),
  clientCompany: z.string().optional(),
  clientAddress: z.string().optional(),
  quoteNumber: z.string().min(1, "Quote number is required"),
  issueDate: z.date(),
  expiryDate: z.date(),
  taxRate: z.number().min(0).max(100),
  markupPercentage: z.number().min(0).max(100),
  discount: z.number().min(0),
  notes: z.string().optional(),
  paymentTerms: z.string(),
  company: z.object({
    name: z.string().min(1, "Company name is required"),
    address: z.string().min(1, "Company address is required"),
    phone: z.string().min(1, "Company phone is required"),
    email: z.string().email("Invalid email address"),
    website: z.string().optional(),
    license: z.string().optional(),
  }),
  additionalOptions: z.object({
    permitFees: z.boolean().default(false),
    inspectionFees: z.boolean().default(false),
    cleanup: z.boolean().default(true),
    warranty: z.boolean().default(true),
  }),
});

// Payment terms options
const PAYMENT_TERMS = [
  "Due on receipt",
  "Net 15",
  "Net 30",
  "50% deposit, 50% on completion",
  "30% deposit, 70% on completion",
  "Custom"
];

// Quote templates
const QUOTE_TEMPLATES = [
  { id: "standard", name: "Standard", description: "Professional standard template" },
  { id: "modern", name: "Modern", description: "Clean, modern design" },
  { id: "detailed", name: "Detailed", description: "Comprehensive breakdown with details" },
  { id: "minimal", name: "Minimal", description: "Simple, minimal design" },
];

// Quote statuses with colors
const QUOTE_STATUSES = {
  draft: { label: "Draft", color: "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300" },
  sent: { label: "Sent", color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300" },
  viewed: { label: "Viewed", color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300" },
  approved: { label: "Approved", color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" },
  declined: { label: "Declined", color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300" },
};

export function QuoteGeneration({ 
  materials = [], 
  laborTasks = [],
  projectId = "",
  projectName = "New Project",
  clientName = "",
  existingQuote = null
}: QuoteGenerationProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("details");
  const [selectedTemplate, setSelectedTemplate] = useState("standard");
  const [previewMode, setPreviewMode] = useState(false);
  const [quoteItems, setQuoteItems] = useState<QuoteItem[]>([]);
  const [quoteStatus, setQuoteStatus] = useState<"draft" | "sent" | "viewed" | "approved" | "declined">(
    existingQuote?.status || "draft"
  );
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [showSendDialog, setShowSendDialog] = useState(false);
  const [emailDetails, setEmailDetails] = useState({
    to: "",
    cc: "",
    subject: "",
    message: ""
  });
  const [selectedAdditionalFeatures, setSelectedAdditionalFeatures] = useState({
    permitFees: existingQuote?.additionalOptions.permitFees || false,
    inspectionFees: existingQuote?.additionalOptions.inspectionFees || false,
    cleanup: existingQuote?.additionalOptions.cleanup || true,
    warranty: existingQuote?.additionalOptions.warranty || true,
  });
  const quotePreviewRef = useRef<HTMLDivElement>(null);

  // Initialize form
  const form = useForm<z.infer<typeof quoteSchema>>({
    resolver: zodResolver(quoteSchema),
    defaultValues: existingQuote ? {
      ...existingQuote,
      issueDate: new Date(existingQuote.issueDate),
      expiryDate: new Date(existingQuote.expiryDate),
    } : {
      projectName: projectName,
      clientName: clientName,
      clientEmail: "",
      clientPhone: "",
      clientCompany: "",
      clientAddress: "",
      quoteNumber: generateQuoteNumber(),
      issueDate: new Date(),
      expiryDate: new Date(new Date().setDate(new Date().getDate() + 30)),
      taxRate: 8.5,
      markupPercentage: 20,
      discount: 0,
      notes: "Thank you for your business. We look forward to working with you!",
      paymentTerms: "50% deposit, 50% on completion",
      company: defaultCompany,
      additionalOptions: {
        permitFees: false,
        inspectionFees: false,
        cleanup: true,
        warranty: true,
      },
    }
  });

  // Generate a unique quote number
  function generateQuoteNumber() {
    const date = new Date();
    return `Q-${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}-${Math.floor(1000 + Math.random() * 9000)}`;
  }

  // Convert materials to quote items
  useEffect(() => {
    if (materials.length === 0 && laborTasks.length === 0) return;
    
    if (existingQuote && existingQuote.items.length > 0) {
      setQuoteItems(existingQuote.items);
      return;
    }

    const materialItems: QuoteItem[] = materials.map(material => ({
      id: material.id,
      description: material.name,
      category: material.category,
      quantity: material.quantity,
      unitPrice: material.unitPrice * (1 + form.getValues().markupPercentage / 100), // Apply markup
      total: material.quantity * material.unitPrice * (1 + form.getValues().markupPercentage / 100),
      type: "material"
    }));

    const laborItems: QuoteItem[] = laborTasks.map(task => ({
      id: task.id,
      description: task.name,
      category: task.category,
      quantity: task.hours * task.workers,
      unitPrice: task.rate,
      total: task.hours * task.workers * task.rate,
      type: "labor"
    }));

    // Add additional options if selected
    const additionalItems: QuoteItem[] = [];
    
    if (selectedAdditionalFeatures.permitFees) {
      additionalItems.push({
        id: `permit-${Date.now()}`,
        description: "Permit Fees",
        category: "Additional Fees",
        quantity: 1,
        unitPrice: 350,
        total: 350,
        type: "other"
      });
    }
    
    if (selectedAdditionalFeatures.inspectionFees) {
      additionalItems.push({
        id: `inspection-${Date.now()}`,
        description: "Inspection Fees",
        category: "Additional Fees",
        quantity: 1,
        unitPrice: 250,
        total: 250,
        type: "other"
      });
    }
    
    if (selectedAdditionalFeatures.cleanup) {
      additionalItems.push({
        id: `cleanup-${Date.now()}`,
        description: "Cleanup and Disposal",
        category: "Additional Services",
        quantity: 1,
        unitPrice: 200,
        total: 200,
        type: "other"
      });
    }

    setQuoteItems([...materialItems, ...laborItems, ...additionalItems]);
  }, [materials, laborTasks, form.getValues().markupPercentage, selectedAdditionalFeatures, existingQuote]);

  // Update item totals when markup changes
  useEffect(() => {
    const markup = form.getValues().markupPercentage;
    
    setQuoteItems(prevItems => 
      prevItems.map(item => {
        if (item.type === "material") {
          const originalPrice = item.unitPrice / (1 + (form.getValues().markupPercentage - markup) / 100);
          return {
            ...item,
            unitPrice: originalPrice * (1 + markup / 100),
            total: item.quantity * originalPrice * (1 + markup / 100)
          };
        }
        return item;
      })
    );
  }, [form.watch("markupPercentage")]);

  // Calculate totals
  const materialsTotal = quoteItems
    .filter(item => item.type === "material")
    .reduce((sum, item) => sum + item.total, 0);
  
  const laborTotal = quoteItems
    .filter(item => item.type === "labor")
    .reduce((sum, item) => sum + item.total, 0);
  
  const otherTotal = quoteItems
    .filter(item => item.type === "other")
    .reduce((sum, item) => sum + item.total, 0);
  
  const subtotal = materialsTotal + laborTotal + otherTotal;
  const discount = form.watch("discount") || 0;
  const taxRate = form.watch("taxRate") || 0;
  const tax = (subtotal - discount) * (taxRate / 100);
  const total = subtotal - discount + tax;

  // Save quote to backend
  const saveMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save quote');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Quote Saved",
        description: "Your quote has been saved successfully.",
      });
      
      // If we have an ID, update the form with it
      if (data.id && !form.getValues().id) {
        form.setValue('id', data.id);
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to save quote",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Handle form submission
  const onSubmit = (data: z.infer<typeof quoteSchema>) => {
    try {
      // Create full quote data
      const fullQuoteData = {
        ...data,
        subtotal,
        tax,
        total: subtotal + tax - (data.discount || 0), 
        laborTotal,
        materialsTotal,
        status: quoteStatus,
        logoUrl: "",
        items: quoteItems,
        projectId: parseInt(projectId.toString()),
        createdById: 1, // This would come from the authenticated user
      };

      // Save to backend
      saveMutation.mutate(fullQuoteData);
    } catch (error) {
      console.error("Error saving quote:", error);
      toast({
        title: "Error",
        description: "There was an error saving your quote. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Generate PDF
  const generatePdf = () => {
    setIsGeneratingPdf(true);
    
    // Simulate PDF generation
    setTimeout(() => {
      setIsGeneratingPdf(false);
      
      toast({
        title: "PDF Generated",
        description: "Your quote PDF has been generated successfully."
      });
    }, 2000);
  };

  // Send quote via email
  const sendEmailMutation = useMutation({
    mutationFn: async () => {
      // Get the quote ID from the form
      const quoteId = form.getValues().id;
      if (!quoteId) {
        throw new Error("Please save the quote before sending it");
      }
      
      const response = await fetch(`/api/quotes/${quoteId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailDetails),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send email');
      }
      
      return response.json();
    },
    onSuccess: () => {
      setIsSendingEmail(false);
      setShowSendDialog(false);
      setQuoteStatus("sent");
      
      // Update the quote status in the form
      const quoteId = form.getValues().id;
      if (quoteId) {
        fetch(`/api/quotes/${quoteId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ status: 'sent' }),
        }).catch(error => {
          console.error("Error updating quote status:", error);
        });
      }
      
      toast({
        title: "Quote Sent",
        description: `Your quote has been sent to ${emailDetails.to}`
      });
    },
    onError: (error: Error) => {
      setIsSendingEmail(false);
      
      toast({
        title: "Failed to send email",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  const sendQuote = () => {
    setIsSendingEmail(true);
    sendEmailMutation.mutate();
  };

  // Handle update of quote status
  const updateQuoteStatus = (status: "draft" | "sent" | "viewed" | "approved" | "declined") => {
    setQuoteStatus(status);
    
    toast({
      title: "Status Updated",
      description: `Quote status changed to ${QUOTE_STATUSES[status].label}`
    });
  };

  // Handle additional options change
  const handleAdditionalOptionChange = (option: keyof typeof selectedAdditionalFeatures, value: boolean) => {
    setSelectedAdditionalFeatures(prev => ({
      ...prev,
      [option]: value
    }));
    
    form.setValue(`additionalOptions.${option}` as any, value);
  };

  // Prepare email details when dialog is opened
  useEffect(() => {
    if (showSendDialog) {
      const clientEmail = form.getValues().clientEmail;
      const projectName = form.getValues().projectName;
      
      setEmailDetails({
        to: clientEmail,
        cc: "",
        subject: `Quote for ${projectName}`,
        message: `Dear ${form.getValues().clientName},\n\nPlease find attached our quote for the ${projectName} project.\n\nThe total amount for this project is $${total.toFixed(2)}.\n\nThis quote is valid until ${format(form.getValues().expiryDate, "MMMM d, yyyy")}.\n\nIf you have any questions, please don't hesitate to contact us.\n\nThank you for your business.\n\nBest regards,\n${form.getValues().company.name}`
      });
    }
  }, [showSendDialog, form, total]);

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <AICard>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-4">
              <div>
                <h2 className="text-lg font-medium">Quote Generation</h2>
                <p className="text-sm text-muted-foreground">
                  Create and manage professional quotes for your clients
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="preview-mode"
                    checked={previewMode}
                    onCheckedChange={setPreviewMode}
                  />
                  <Label htmlFor="preview-mode">Preview Mode</Label>
                </div>
                
                <Button type="submit" variant="outline">
                  <Save className="mr-2 h-4 w-4" />
                  Save Quote
                </Button>
                
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={generatePdf}
                  disabled={isGeneratingPdf}
                >
                  {isGeneratingPdf ? (
                    <>
                      <span className="animate-spin mr-2">
                        <Clock className="h-4 w-4" />
                      </span>
                      Generating...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Download PDF
                    </>
                  )}
                </Button>
                
                <Dialog open={showSendDialog} onOpenChange={setShowSendDialog}>
                  <DialogTrigger asChild>
                    <Button type="button">
                      <Send className="mr-2 h-4 w-4" />
                      Send Quote
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Send Quote</DialogTitle>
                      <DialogDescription>
                        Send this quote to your client via email.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Label htmlFor="to">To</Label>
                        <Input
                          id="to"
                          value={emailDetails.to}
                          onChange={(e) => setEmailDetails({ ...emailDetails, to: e.target.value })}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="cc">CC</Label>
                        <Input
                          id="cc"
                          value={emailDetails.cc}
                          onChange={(e) => setEmailDetails({ ...emailDetails, cc: e.target.value })}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="subject">Subject</Label>
                        <Input
                          id="subject"
                          value={emailDetails.subject}
                          onChange={(e) => setEmailDetails({ ...emailDetails, subject: e.target.value })}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="message">Message</Label>
                        <Textarea
                          id="message"
                          value={emailDetails.message}
                          onChange={(e) => setEmailDetails({ ...emailDetails, message: e.target.value })}
                          rows={6}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={() => setShowSendDialog(false)}>
                        Cancel
                      </Button>
                      <Button 
                        type="button" 
                        onClick={sendQuote}
                        disabled={isSendingEmail}
                      >
                        {isSendingEmail ? (
                          <>
                            <span className="animate-spin mr-2">
                              <Clock className="h-4 w-4" />
                            </span>
                            Sending...
                          </>
                        ) : (
                          <>
                            <Mail className="mr-2 h-4 w-4" />
                            Send Email
                          </>
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </AICard>
          
          {/* Quote Details and Preview Panels */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Form Panel - visible when not in preview mode */}
            {!previewMode && (
              <div className="lg:col-span-3">
                <AICard>
                  <div className="p-4 space-y-4">
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                      <TabsList className="mb-4">
                        <TabsTrigger value="details">Quote Details</TabsTrigger>
                        <TabsTrigger value="client">Client Information</TabsTrigger>
                        <TabsTrigger value="items">Line Items</TabsTrigger>
                        <TabsTrigger value="company">Company Info</TabsTrigger>
                        <TabsTrigger value="options">Additional Options</TabsTrigger>
                      </TabsList>
                      
                      {/* Quote Details Tab */}
                      <TabsContent value="details" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="projectName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Project Name</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="quoteNumber"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Quote Number</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="issueDate"
                            render={({ field }) => (
                              <FormItem className="flex flex-col">
                                <FormLabel>Issue Date</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant={"outline"}
                                        className={cn(
                                          "w-full pl-3 text-left font-normal",
                                          !field.value && "text-muted-foreground"
                                        )}
                                      >
                                        {field.value ? (
                                          format(field.value, "PPP")
                                        ) : (
                                          <span>Pick a date</span>
                                        )}
                                        <CalendarDays className="ml-auto h-4 w-4 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={field.onChange}
                                      disabled={(date) =>
                                        date < new Date("1900-01-01")
                                      }
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="expiryDate"
                            render={({ field }) => (
                              <FormItem className="flex flex-col">
                                <FormLabel>Expiry Date</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant={"outline"}
                                        className={cn(
                                          "w-full pl-3 text-left font-normal",
                                          !field.value && "text-muted-foreground"
                                        )}
                                      >
                                        {field.value ? (
                                          format(field.value, "PPP")
                                        ) : (
                                          <span>Pick a date</span>
                                        )}
                                        <CalendarDays className="ml-auto h-4 w-4 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={field.onChange}
                                      disabled={(date) =>
                                        date < new Date()
                                      }
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="taxRate"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Tax Rate (%)</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="number" 
                                    min={0} 
                                    step={0.1} 
                                    {...field}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="markupPercentage"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Markup Percentage (%)</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="number" 
                                    min={0} 
                                    step={1} 
                                    {...field}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="discount"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Discount Amount ($)</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="number" 
                                    min={0} 
                                    step={1} 
                                    {...field}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <FormField
                          control={form.control}
                          name="paymentTerms"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Payment Terms</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select payment terms" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {PAYMENT_TERMS.map((term) => (
                                    <SelectItem key={term} value={term}>
                                      {term}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Notes</FormLabel>
                              <FormControl>
                                <Textarea 
                                  rows={4} 
                                  placeholder="Add any additional notes or terms" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="bg-muted/30 p-4 rounded-lg">
                          <h3 className="text-sm font-medium mb-3">Quote Template</h3>
                          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                            {QUOTE_TEMPLATES.map(template => (
                              <Card 
                                key={template.id}
                                className={cn(
                                  "cursor-pointer transition-colors",
                                  selectedTemplate === template.id 
                                    ? "border-primary bg-primary/5" 
                                    : "hover:border-muted-foreground/50"
                                )}
                                onClick={() => setSelectedTemplate(template.id)}
                              >
                                <CardHeader className="p-3">
                                  <div className="flex justify-between items-start">
                                    <CardTitle className="text-sm">{template.name}</CardTitle>
                                    {selectedTemplate === template.id && (
                                      <Check className="h-4 w-4 text-primary" />
                                    )}
                                  </div>
                                  <CardDescription className="text-xs">
                                    {template.description}
                                  </CardDescription>
                                </CardHeader>
                              </Card>
                            ))}
                          </div>
                        </div>
                        
                        <div className="p-4 bg-muted/30 rounded-lg">
                          <div className="flex items-center justify-between mb-3">
                            <h3 className="text-sm font-medium">Quote Status</h3>
                            <Badge 
                              className={cn(
                                "text-xs font-medium",
                                QUOTE_STATUSES[quoteStatus].color
                              )}
                            >
                              {QUOTE_STATUSES[quoteStatus].label}
                            </Badge>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {Object.entries(QUOTE_STATUSES).map(([status, { label, color }]) => (
                              <Button
                                key={status}
                                variant="outline"
                                size="sm"
                                className={cn(
                                  "text-xs",
                                  quoteStatus === status && "border-primary"
                                )}
                                onClick={() => updateQuoteStatus(status as any)}
                              >
                                Set to {label}
                              </Button>
                            ))}
                          </div>
                        </div>
                      </TabsContent>
                      
                      {/* Client Information Tab */}
                      <TabsContent value="client" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="clientName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Client Name</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="clientEmail"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Client Email</FormLabel>
                                <FormControl>
                                  <Input {...field} type="email" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="clientPhone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Client Phone</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="clientCompany"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Client Company</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <FormField
                          control={form.control}
                          name="clientAddress"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Client Address</FormLabel>
                              <FormControl>
                                <Textarea {...field} rows={3} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TabsContent>
                      
                      {/* Line Items Tab */}
                      <TabsContent value="items" className="space-y-4">
                        <div className="border rounded-md">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Description</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead className="text-right">Quantity</TableHead>
                                <TableHead className="text-right">Unit Price</TableHead>
                                <TableHead className="text-right">Total</TableHead>
                                <TableHead className="w-[100px]">Type</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {quoteItems.length === 0 ? (
                                <TableRow>
                                  <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                                    No items added to this quote yet.
                                  </TableCell>
                                </TableRow>
                              ) : (
                                quoteItems.map((item, index) => (
                                  <TableRow key={item.id}>
                                    <TableCell className="font-medium">{item.description}</TableCell>
                                    <TableCell>{item.category}</TableCell>
                                    <TableCell className="text-right">{item.quantity}</TableCell>
                                    <TableCell className="text-right">${item.unitPrice.toFixed(2)}</TableCell>
                                    <TableCell className="text-right">${item.total.toFixed(2)}</TableCell>
                                    <TableCell>
                                      <Badge 
                                        variant="outline" 
                                        className={cn(
                                          item.type === "material" && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                                          item.type === "labor" && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                                          item.type === "other" && "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
                                        )}
                                      >
                                        {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                                      </Badge>
                                    </TableCell>
                                  </TableRow>
                                ))
                              )}
                            </TableBody>
                          </Table>
                        </div>
                        
                        <div className="flex justify-between p-4 bg-muted/30 rounded-lg">
                          <div>
                            <p className="text-sm font-medium">Totals Summary</p>
                            <p className="text-xs text-muted-foreground">Breakdown of all costs</p>
                          </div>
                          <div className="space-y-1 text-right">
                            <div className="text-sm">
                              <span className="text-muted-foreground">Materials:</span> 
                              <span className="ml-2 font-medium">${materialsTotal.toFixed(2)}</span>
                            </div>
                            <div className="text-sm">
                              <span className="text-muted-foreground">Labor:</span> 
                              <span className="ml-2 font-medium">${laborTotal.toFixed(2)}</span>
                            </div>
                            <div className="text-sm">
                              <span className="text-muted-foreground">Other:</span> 
                              <span className="ml-2 font-medium">${otherTotal.toFixed(2)}</span>
                            </div>
                            <div className="text-sm pt-1 border-t">
                              <span className="text-muted-foreground">Subtotal:</span>
                              <span className="ml-2 font-medium">${subtotal.toFixed(2)}</span>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                      
                      {/* Company Info Tab */}
                      <TabsContent value="company" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="company.name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Company Name</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="company.phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Company Phone</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="company.email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Company Email</FormLabel>
                                <FormControl>
                                  <Input {...field} type="email" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="company.website"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Company Website</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <FormField
                          control={form.control}
                          name="company.address"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Company Address</FormLabel>
                              <FormControl>
                                <Textarea {...field} rows={3} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="company.license"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>License Number</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="p-4 bg-muted/30 rounded-lg">
                          <h3 className="text-sm font-medium mb-3">Logo</h3>
                          <div className="flex items-center gap-4">
                            <div className="h-20 w-20 rounded-md border flex items-center justify-center bg-muted/50">
                              <Image className="h-8 w-8 text-muted-foreground" />
                            </div>
                            <div className="space-y-2">
                              <Button type="button" variant="outline" size="sm">
                                Upload Logo
                              </Button>
                              <p className="text-xs text-muted-foreground">
                                Recommended size: 200x200px. Max file size: 2MB.
                              </p>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                      
                      {/* Additional Options Tab */}
                      <TabsContent value="options" className="space-y-4">
                        <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
                          <h3 className="text-sm font-medium">Optional Features</h3>
                          <p className="text-xs text-muted-foreground mb-4">
                            These items will be added to your quote as separate line items.
                          </p>
                          
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <FormField
                                control={form.control}
                                name="additionalOptions.permitFees"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={selectedAdditionalFeatures.permitFees}
                                        onCheckedChange={(checked) => 
                                          handleAdditionalOptionChange('permitFees', !!checked)
                                        }
                                      />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                      <FormLabel>Permit Fees</FormLabel>
                                      <FormDescription>
                                        Include permit acquisition fees ($350)
                                      </FormDescription>
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <FormField
                                control={form.control}
                                name="additionalOptions.inspectionFees"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={selectedAdditionalFeatures.inspectionFees}
                                        onCheckedChange={(checked) => 
                                          handleAdditionalOptionChange('inspectionFees', !!checked)
                                        }
                                      />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                      <FormLabel>Inspection Fees</FormLabel>
                                      <FormDescription>
                                        Include inspection scheduling and fees ($250)
                                      </FormDescription>
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <FormField
                                control={form.control}
                                name="additionalOptions.cleanup"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={selectedAdditionalFeatures.cleanup}
                                        onCheckedChange={(checked) => 
                                          handleAdditionalOptionChange('cleanup', !!checked)
                                        }
                                      />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                      <FormLabel>Cleanup and Disposal</FormLabel>
                                      <FormDescription>
                                        Include cleanup and waste disposal services ($200)
                                      </FormDescription>
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <FormField
                                control={form.control}
                                name="additionalOptions.warranty"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={selectedAdditionalFeatures.warranty}
                                        onCheckedChange={(checked) => 
                                          handleAdditionalOptionChange('warranty', !!checked)
                                        }
                                      />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                      <FormLabel>Extended Warranty</FormLabel>
                                      <FormDescription>
                                        Include 2-year extended warranty on workmanship (Included)
                                      </FormDescription>
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </div>
                </AICard>
              </div>
            )}
            
            {/* Preview Panel - expands to full width in preview mode */}
            <div className={cn(
              "bg-white dark:bg-black print:bg-white print:dark:bg-white print:text-black rounded-lg shadow-sm border",
              previewMode ? "lg:col-span-3" : "lg:col-span-3"
            )}>
              <div 
                ref={quotePreviewRef} 
                className="p-8 min-h-[800px] print:min-h-0 print:p-0"
              >
                {/* Quote Preview Header */}
                <div className="flex justify-between items-start mb-8 print:mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-50 print:text-black">
                      QUOTE
                    </h1>
                    <p className="text-xl text-muted-foreground mt-1">{form.watch("quoteNumber")}</p>
                  </div>
                  <div className="text-right">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-gray-50 print:text-black">
                      {form.watch("company.name")}
                    </h2>
                    <p className="text-muted-foreground">{form.watch("company.address")}</p>
                    <p className="text-muted-foreground">{form.watch("company.phone")}</p>
                    <p className="text-muted-foreground">{form.watch("company.email")}</p>
                    <p className="text-muted-foreground">{form.watch("company.website")}</p>
                    <p className="text-muted-foreground">{form.watch("company.license")}</p>
                  </div>
                </div>
                
                {/* Quote Details */}
                <div className="grid grid-cols-2 gap-8 mb-8 print:mb-4">
                  <div>
                    <h3 className="text-sm font-semibold uppercase text-muted-foreground mb-2">
                      Bill To:
                    </h3>
                    <p className="text-lg font-medium">{form.watch("clientName")}</p>
                    {form.watch("clientCompany") && (
                      <p>{form.watch("clientCompany")}</p>
                    )}
                    <p>{form.watch("clientAddress")}</p>
                    <p>{form.watch("clientPhone")}</p>
                    <p>{form.watch("clientEmail")}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-semibold uppercase text-muted-foreground mb-2">
                      Quote Details:
                    </h3>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <p className="text-muted-foreground">Quote Number:</p>
                        <p>{form.watch("quoteNumber")}</p>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-muted-foreground">Project Name:</p>
                        <p>{form.watch("projectName")}</p>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-muted-foreground">Issue Date:</p>
                        <p>{form.watch("issueDate") ? format(form.watch("issueDate"), "MMMM d, yyyy") : ""}</p>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-muted-foreground">Valid Until:</p>
                        <p>{form.watch("expiryDate") ? format(form.watch("expiryDate"), "MMMM d, yyyy") : ""}</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Line Items */}
                <div className="mb-8 print:mb-4">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted print:bg-gray-100">
                        <TableHead>Description</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead className="text-right">Unit Price</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {quoteItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                            No items added to this quote yet.
                          </TableCell>
                        </TableRow>
                      ) : (
                        <>
                          {/* Materials Section */}
                          {quoteItems.filter(item => item.type === "material").length > 0 && (
                            <>
                              <TableRow className="bg-muted/50 print:bg-gray-50">
                                <TableCell colSpan={4} className="font-medium">
                                  Materials
                                </TableCell>
                              </TableRow>
                              {quoteItems
                                .filter(item => item.type === "material")
                                .map((item) => (
                                  <TableRow key={item.id}>
                                    <TableCell>{item.description}</TableCell>
                                    <TableCell>{item.quantity}</TableCell>
                                    <TableCell className="text-right">${item.unitPrice.toFixed(2)}</TableCell>
                                    <TableCell className="text-right">${item.total.toFixed(2)}</TableCell>
                                  </TableRow>
                                ))}
                            </>
                          )}

                          {/* Labor Section */}
                          {quoteItems.filter(item => item.type === "labor").length > 0 && (
                            <>
                              <TableRow className="bg-muted/50 print:bg-gray-50">
                                <TableCell colSpan={4} className="font-medium">
                                  Labor
                                </TableCell>
                              </TableRow>
                              {quoteItems
                                .filter(item => item.type === "labor")
                                .map((item) => (
                                  <TableRow key={item.id}>
                                    <TableCell>{item.description}</TableCell>
                                    <TableCell>{item.quantity} hrs</TableCell>
                                    <TableCell className="text-right">${item.unitPrice.toFixed(2)}/hr</TableCell>
                                    <TableCell className="text-right">${item.total.toFixed(2)}</TableCell>
                                  </TableRow>
                                ))}
                            </>
                          )}

                          {/* Other Items Section */}
                          {quoteItems.filter(item => item.type === "other").length > 0 && (
                            <>
                              <TableRow className="bg-muted/50 print:bg-gray-50">
                                <TableCell colSpan={4} className="font-medium">
                                  Additional Items
                                </TableCell>
                              </TableRow>
                              {quoteItems
                                .filter(item => item.type === "other")
                                .map((item) => (
                                  <TableRow key={item.id}>
                                    <TableCell>{item.description}</TableCell>
                                    <TableCell>{item.quantity}</TableCell>
                                    <TableCell className="text-right">${item.unitPrice.toFixed(2)}</TableCell>
                                    <TableCell className="text-right">${item.total.toFixed(2)}</TableCell>
                                  </TableRow>
                                ))}
                            </>
                          )}
                        </>
                      )}
                    </TableBody>
                  </Table>
                </div>
                
                {/* Totals */}
                <div className="flex justify-end mb-8 print:mb-4">
                  <div className="w-64 space-y-2">
                    <div className="flex justify-between">
                      <p className="text-muted-foreground">Subtotal:</p>
                      <p>${subtotal.toFixed(2)}</p>
                    </div>
                    
                    {discount > 0 && (
                      <div className="flex justify-between">
                        <p className="text-muted-foreground">Discount:</p>
                        <p>-${discount.toFixed(2)}</p>
                      </div>
                    )}
                    
                    <div className="flex justify-between">
                      <p className="text-muted-foreground">Tax ({taxRate}%):</p>
                      <p>${tax.toFixed(2)}</p>
                    </div>
                    
                    <div className="flex justify-between pt-2 border-t font-medium">
                      <p>Total:</p>
                      <p className="text-xl">${total.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
                
                {/* Payment Terms & Notes */}
                <div className="space-y-4 mb-8 print:mb-4">
                  <div>
                    <h3 className="font-medium mb-1">Payment Terms</h3>
                    <p className="text-muted-foreground">{form.watch("paymentTerms")}</p>
                  </div>
                  
                  {form.watch("notes") && (
                    <div>
                      <h3 className="font-medium mb-1">Notes</h3>
                      <p className="text-muted-foreground whitespace-pre-line">{form.watch("notes")}</p>
                    </div>
                  )}
                </div>
                
                {/* Thank you message */}
                <div className="text-center py-4 bg-muted/20 rounded-lg print:border print:border-gray-200">
                  <p className="font-medium">Thank you for your business!</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Quote Management Tools */}
          <AICard>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      Quote Templates
                    </CardTitle>
                    <CardDescription>
                      Choose from professionally designed templates
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="text-sm">
                    <div className="space-y-2">
                      {QUOTE_TEMPLATES.map(template => (
                        <div 
                          key={template.id}
                          className={cn(
                            "p-2 rounded-md flex items-center justify-between cursor-pointer hover:bg-muted/50",
                            selectedTemplate === template.id && "bg-muted"
                          )}
                          onClick={() => setSelectedTemplate(template.id)}
                        >
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-md bg-muted flex items-center justify-center mr-2">
                              <FileText className="h-4 w-4" />
                            </div>
                            <span>{template.name}</span>
                          </div>
                          {selectedTemplate === template.id && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" size="sm" className="w-full">
                      Manage Templates
                    </Button>
                  </CardFooter>
                </Card>
                
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center">
                      <Send className="h-4 w-4 mr-2" />
                      Delivery Options
                    </CardTitle>
                    <CardDescription>
                      Send your quotes to clients
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2 text-sm">
                    <div className="p-2 rounded-md hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-md bg-muted flex items-center justify-center mr-2">
                          <Mail className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium">Email Delivery</p>
                          <p className="text-xs text-muted-foreground">Send directly to client's inbox</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-2 rounded-md hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-md bg-muted flex items-center justify-center mr-2">
                          <MessageSquare className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium">Client Portal</p>
                          <p className="text-xs text-muted-foreground">Share via secure client portal</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-2 rounded-md hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-md bg-muted flex items-center justify-center mr-2">
                          <Download className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium">Download PDF</p>
                          <p className="text-xs text-muted-foreground">Save for printing or sharing</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button size="sm" className="w-full" onClick={() => setShowSendDialog(true)}>
                      <Send className="h-4 w-4 mr-2" />
                      Send Quote
                    </Button>
                  </CardFooter>
                </Card>
                
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center">
                      <FileSearch className="h-4 w-4 mr-2" />
                      Quote Status
                    </CardTitle>
                    <CardDescription>
                      Track and manage quote status
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex gap-3 items-center">
                        <Badge 
                          className={cn(
                            "w-20 justify-center",
                            QUOTE_STATUSES[quoteStatus].color
                          )}
                        >
                          {QUOTE_STATUSES[quoteStatus].label}
                        </Badge>
                        
                        <p className="text-sm text-muted-foreground">
                          {quoteStatus === 'draft' && 'Quote is being prepared'}
                          {quoteStatus === 'sent' && 'Quote has been sent to client'}
                          {quoteStatus === 'viewed' && 'Client has viewed the quote'}
                          {quoteStatus === 'approved' && 'Client has approved the quote'}
                          {quoteStatus === 'declined' && 'Client has declined the quote'}
                        </p>
                      </div>
                      
                      <div className="pt-2 text-sm">
                        <p className="font-medium mb-2">Update Status</p>
                        <div className="flex flex-wrap gap-2">
                          {Object.entries(QUOTE_STATUSES).map(([status, { label }]) => (
                            <Button
                              key={status}
                              variant="outline"
                              size="sm"
                              className={cn(
                                "text-xs",
                                quoteStatus === status && "border-primary"
                              )}
                              onClick={() => updateQuoteStatus(status as any)}
                            >
                              {label}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" size="sm" className="w-full">
                      <Clock className="h-4 w-4 mr-2" />
                      View History
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </AICard>
        </form>
      </Form>
    </div>
  );
}