import { useState } from "react";
import { 
  MessageSquare, 
  Clock, 
  RefreshCw, 
  Send, 
  CheckCircle, 
  XCircle, 
  User, 
  ChevronDown, 
  ChevronRight,
  Edit,
  Plus,
  MoreHorizontal
} from "lucide-react";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AICard } from "@/components/ai-card";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface QuoteRevisionPanelProps {
  quoteId: string;
  onRevisionComplete?: () => void;
}

interface FeedbackItem {
  id: string;
  message: string;
  timestamp: string;
  section?: string;
  itemIndex?: number;
  isClient: boolean;
  status: 'new' | 'in-progress' | 'resolved' | 'rejected';
  response?: string;
  responseTimestamp?: string;
}

interface RevisionAction {
  id: string;
  feedbackId: string;
  description: string;
  timestamp: string;
  status: 'pending' | 'completed';
  assignee: string;
  updatedAt?: string;
}

export function QuoteRevisionPanel({ quoteId, onRevisionComplete }: QuoteRevisionPanelProps) {
  const { toast } = useToast();
  const [feedbackItems, setFeedbackItems] = useState<FeedbackItem[]>([
    {
      id: "f1",
      message: "Could you provide more details about the LED light fixtures?",
      timestamp: "2023-05-03T14:30:00Z",
      section: "items",
      itemIndex: 2,
      isClient: true,
      status: 'resolved',
      response: "These are 6-inch recessed LED fixtures, 3000K color temperature, dimmable, with a 5-year warranty.",
      responseTimestamp: "2023-05-03T15:45:00Z"
    },
    {
      id: "f2",
      message: "The hourly rate for electricians seems high. Can we discuss this?",
      timestamp: "2023-05-04T09:15:00Z",
      section: "items",
      itemIndex: 4,
      isClient: true,
      status: 'in-progress'
    },
    {
      id: "f3",
      message: "Can we remove the permit fees from the quote? I'll handle that separately.",
      timestamp: "2023-05-04T10:30:00Z",
      section: "items",
      itemIndex: 6,
      isClient: true,
      status: 'new'
    }
  ]);
  
  const [revisionActions, setRevisionActions] = useState<RevisionAction[]>([
    {
      id: "r1",
      feedbackId: "f1",
      description: "Update LED fixture description in line item",
      timestamp: "2023-05-03T16:00:00Z",
      status: 'completed',
      assignee: "John Smith",
      updatedAt: "2023-05-03T16:45:00Z"
    },
    {
      id: "r2",
      feedbackId: "f2",
      description: "Review and adjust electrician hourly rate",
      timestamp: "2023-05-04T09:30:00Z",
      status: 'pending',
      assignee: "Jane Doe"
    }
  ]);
  
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackItem | null>(null);
  const [responseText, setResponseText] = useState("");
  const [showResponseDialog, setShowResponseDialog] = useState(false);
  const [showActionDialog, setShowActionDialog] = useState(false);
  const [newActionText, setNewActionText] = useState("");
  const [expandedFeedback, setExpandedFeedback] = useState<Record<string, boolean>>({
    f1: true,
    f2: true,
    f3: true
  });
  
  // Format functions
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy 'at' h:mm a");
    } catch (e) {
      return dateString;
    }
  };
  
  // Status badge for feedback
  const getFeedbackStatusBadge = (status: FeedbackItem['status']) => {
    switch(status) {
      case 'new':
        return <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">New</Badge>;
      case 'in-progress':
        return <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">In Progress</Badge>;
      case 'resolved':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Resolved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">Rejected</Badge>;
      default:
        return null;
    }
  };
  
  // Status badge for actions
  const getActionStatusBadge = (status: RevisionAction['status']) => {
    switch(status) {
      case 'pending':
        return <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">Pending</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Completed</Badge>;
      default:
        return null;
    }
  };
  
  // Toggle feedback expansion
  const toggleFeedbackExpansion = (id: string) => {
    setExpandedFeedback(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };
  
  // Handle responding to feedback
  const handleResponseSubmit = () => {
    if (!selectedFeedback || !responseText.trim()) return;
    
    const updatedFeedback = {
      ...selectedFeedback,
      status: 'resolved' as const,
      response: responseText,
      responseTimestamp: new Date().toISOString()
    };
    
    setFeedbackItems(prev => 
      prev.map(item => 
        item.id === selectedFeedback.id ? updatedFeedback : item
      )
    );
    
    setShowResponseDialog(false);
    setResponseText("");
    
    toast({
      title: "Response Sent",
      description: "Your response has been added to the feedback."
    });
  };
  
  // Handle adding a new revision action
  const handleAddAction = () => {
    if (!selectedFeedback || !newActionText.trim()) return;
    
    const newAction: RevisionAction = {
      id: `r${revisionActions.length + 1}`,
      feedbackId: selectedFeedback.id,
      description: newActionText,
      timestamp: new Date().toISOString(),
      status: 'pending',
      assignee: "Current User" // In a real app, this would be the current user
    };
    
    setRevisionActions(prev => [...prev, newAction]);
    
    // Update feedback status to in-progress if it's new
    if (selectedFeedback.status === 'new') {
      setFeedbackItems(prev => 
        prev.map(item => 
          item.id === selectedFeedback.id 
            ? { ...item, status: 'in-progress' } 
            : item
        )
      );
    }
    
    setShowActionDialog(false);
    setNewActionText("");
    
    toast({
      title: "Action Created",
      description: "A new revision action has been created."
    });
  };
  
  // Mark an action as complete
  const completeAction = (actionId: string) => {
    setRevisionActions(prev => 
      prev.map(action => 
        action.id === actionId 
          ? { 
              ...action, 
              status: 'completed',
              updatedAt: new Date().toISOString()
            } 
          : action
      )
    );
    
    toast({
      title: "Action Completed",
      description: "The revision action has been marked as complete."
    });
  };
  
  // Get feedback status counts
  const feedbackCounts = {
    total: feedbackItems.length,
    new: feedbackItems.filter(item => item.status === 'new').length,
    inProgress: feedbackItems.filter(item => item.status === 'in-progress').length,
    resolved: feedbackItems.filter(item => item.status === 'resolved').length,
    rejected: feedbackItems.filter(item => item.status === 'rejected').length
  };
  
  // Get actions for a specific feedback item
  const getActionsForFeedback = (feedbackId: string) => {
    return revisionActions.filter(action => action.feedbackId === feedbackId);
  };
  
  return (
    <div className="space-y-6">
      <AICard>
        <div className="p-6 border-b">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h2 className="text-xl font-bold">Quote Revision Workflow</h2>
              <p className="text-sm text-muted-foreground">
                Track and manage client feedback and quote revisions
              </p>
            </div>
            
            <div>
              <Button 
                onClick={() => {
                  // In a real app, this would finalize the revised quote
                  if (onRevisionComplete) {
                    onRevisionComplete();
                  }
                  
                  toast({
                    title: "Revision Complete",
                    description: "The revised quote has been created and is ready for review."
                  });
                }}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Generate Revised Quote
              </Button>
            </div>
          </div>
        </div>
        
        {/* Stats cards */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-px bg-border">
          <div className="bg-background p-4">
            <div className="flex flex-col items-center">
              <p className="text-3xl font-bold">{feedbackCounts.total}</p>
              <p className="text-sm text-muted-foreground">Total Feedback</p>
            </div>
          </div>
          <div className="bg-background p-4">
            <div className="flex flex-col items-center">
              <p className="text-3xl font-bold text-blue-600">{feedbackCounts.new}</p>
              <p className="text-sm text-muted-foreground">New</p>
            </div>
          </div>
          <div className="bg-background p-4">
            <div className="flex flex-col items-center">
              <p className="text-3xl font-bold text-amber-600">{feedbackCounts.inProgress}</p>
              <p className="text-sm text-muted-foreground">In Progress</p>
            </div>
          </div>
          <div className="bg-background p-4">
            <div className="flex flex-col items-center">
              <p className="text-3xl font-bold text-green-600">{feedbackCounts.resolved}</p>
              <p className="text-sm text-muted-foreground">Resolved</p>
            </div>
          </div>
        </div>
      </AICard>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Feedback list */}
        <div className="lg:col-span-2">
          <AICard>
            <div className="p-6 border-b">
              <h3 className="text-lg font-bold">Client Feedback</h3>
              <p className="text-sm text-muted-foreground">
                Review and respond to client feedback on the quote
              </p>
            </div>
            
            <div className="p-4">
              <div className="space-y-4">
                {feedbackItems.length === 0 ? (
                  <div className="text-center py-12">
                    <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground opacity-50 mb-4" />
                    <h3 className="text-lg font-medium">No Feedback Yet</h3>
                    <p className="text-muted-foreground">
                      When the client provides feedback, it will appear here.
                    </p>
                  </div>
                ) : (
                  feedbackItems.map(feedback => (
                    <div 
                      key={feedback.id} 
                      className="border rounded-lg overflow-hidden"
                    >
                      {/* Feedback header */}
                      <div 
                        className={cn(
                          "p-4 flex justify-between items-start bg-muted/10 cursor-pointer",
                          feedback.status === 'new' && "border-l-4 border-blue-500"
                        )}
                        onClick={() => toggleFeedbackExpansion(feedback.id)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0 mt-1">
                            {expandedFeedback[feedback.id] ? (
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{feedback.message}</div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {formatDate(feedback.timestamp)} • 
                              {feedback.section && (
                                <span> Section: {feedback.section}</span>
                              )}
                              {feedback.itemIndex !== undefined && (
                                <span> • Item #{feedback.itemIndex + 1}</span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div>
                          {getFeedbackStatusBadge(feedback.status)}
                        </div>
                      </div>
                      
                      {/* Expanded feedback content */}
                      {expandedFeedback[feedback.id] && (
                        <div className="p-4 border-t">
                          {/* Response section */}
                          {feedback.response ? (
                            <div className="mb-4 bg-muted/10 p-3 rounded-md">
                              <div className="flex justify-between mb-2">
                                <span className="text-sm font-medium">Response:</span>
                                <span className="text-xs text-muted-foreground">
                                  {feedback.responseTimestamp && formatDate(feedback.responseTimestamp)}
                                </span>
                              </div>
                              <p className="text-sm">{feedback.response}</p>
                            </div>
                          ) : (
                            <div className="mb-4">
                              <Button 
                                size="sm" 
                                onClick={() => {
                                  setSelectedFeedback(feedback);
                                  setShowResponseDialog(true);
                                }}
                              >
                                <MessageSquare className="h-4 w-4 mr-2" />
                                Respond to Feedback
                              </Button>
                            </div>
                          )}
                          
                          {/* Actions section */}
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <h4 className="text-sm font-medium">Revision Actions</h4>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => {
                                  setSelectedFeedback(feedback);
                                  setShowActionDialog(true);
                                }}
                              >
                                <Plus className="h-3 w-3 mr-1" />
                                Add Action
                              </Button>
                            </div>
                            
                            {getActionsForFeedback(feedback.id).length === 0 ? (
                              <p className="text-sm text-muted-foreground">
                                No actions created yet. Add an action to track the revision process.
                              </p>
                            ) : (
                              <div className="space-y-2">
                                {getActionsForFeedback(feedback.id).map(action => (
                                  <div 
                                    key={action.id} 
                                    className="flex justify-between items-center bg-muted/5 p-2 rounded-md text-sm"
                                  >
                                    <div>
                                      <div className="flex items-center">
                                        <span>{action.description}</span>
                                      </div>
                                      <div className="text-xs text-muted-foreground">
                                        {formatDate(action.timestamp)} • Assigned to: {action.assignee}
                                      </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-2">
                                      {getActionStatusBadge(action.status)}
                                      
                                      {action.status === 'pending' && (
                                        <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="icon" className="h-8 w-8">
                                              <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => completeAction(action.id)}>
                                              <CheckCircle className="h-4 w-4 mr-2" />
                                              Mark as Complete
                                            </DropdownMenuItem>
                                            <DropdownMenuItem>
                                              <Edit className="h-4 w-4 mr-2" />
                                              Edit Action
                                            </DropdownMenuItem>
                                          </DropdownMenuContent>
                                        </DropdownMenu>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </AICard>
        </div>
        
        {/* Revision summary */}
        <div>
          <AICard>
            <div className="p-6 border-b">
              <h3 className="text-lg font-bold">Revision Summary</h3>
              <p className="text-sm text-muted-foreground">
                Track progress of all revision actions
              </p>
            </div>
            
            <div className="p-4">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium">Actions by Status</h4>
                  <span className="text-sm text-muted-foreground">
                    {revisionActions.filter(a => a.status === 'completed').length} / {revisionActions.length} Complete
                  </span>
                </div>
                
                <div className="h-2 rounded-full bg-muted overflow-hidden">
                  <div 
                    className="h-full bg-green-500 rounded-full"
                    style={{
                      width: `${(revisionActions.filter(a => a.status === 'completed').length / Math.max(revisionActions.length, 1)) * 100}%`
                    }}
                  ></div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Pending Actions</h4>
                  {revisionActions.filter(a => a.status === 'pending').length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      No pending actions. All tasks are complete.
                    </p>
                  ) : (
                    <div className="space-y-2">
                      {revisionActions
                        .filter(a => a.status === 'pending')
                        .map(action => (
                          <div key={action.id} className="p-2 bg-muted/10 rounded-md text-sm">
                            <div className="font-medium">{action.description}</div>
                            <div className="flex justify-between text-xs text-muted-foreground mt-1">
                              <span>Assigned to: {action.assignee}</span>
                              <span>{formatDate(action.timestamp)}</span>
                            </div>
                          </div>
                        ))
                      }
                    </div>
                  )}
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Recently Completed</h4>
                  {revisionActions.filter(a => a.status === 'completed').length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      No completed actions yet.
                    </p>
                  ) : (
                    <div className="space-y-2">
                      {revisionActions
                        .filter(a => a.status === 'completed')
                        .slice(0, 3)
                        .map(action => (
                          <div key={action.id} className="p-2 bg-muted/10 rounded-md text-sm">
                            <div className="font-medium flex items-center">
                              <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                              {action.description}
                            </div>
                            <div className="flex justify-between text-xs text-muted-foreground mt-1">
                              <span>Completed by: {action.assignee}</span>
                              <span>{action.updatedAt && formatDate(action.updatedAt)}</span>
                            </div>
                          </div>
                        ))
                      }
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AICard>
        </div>
      </div>
      
      {/* Response Dialog */}
      <Dialog open={showResponseDialog} onOpenChange={setShowResponseDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Respond to Feedback</DialogTitle>
            <DialogDescription>
              Provide a response to the client's feedback
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            {selectedFeedback && (
              <div className="mb-4 bg-muted/10 p-3 rounded-md">
                <div className="text-sm text-muted-foreground mb-1">Client Feedback:</div>
                <p className="font-medium">{selectedFeedback.message}</p>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="response">Your Response</Label>
              <Textarea
                id="response"
                rows={5}
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                placeholder="Type your response to the client's feedback..."
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowResponseDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleResponseSubmit} disabled={!responseText.trim()}>
              <Send className="h-4 w-4 mr-2" />
              Send Response
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* New Action Dialog */}
      <Dialog open={showActionDialog} onOpenChange={setShowActionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Revision Action</DialogTitle>
            <DialogDescription>
              Add a new action item to track the revision process
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            {selectedFeedback && (
              <div className="mb-4 bg-muted/10 p-3 rounded-md">
                <div className="text-sm text-muted-foreground mb-1">Related Feedback:</div>
                <p className="font-medium">{selectedFeedback.message}</p>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="action">Action Description</Label>
              <Textarea
                id="action"
                rows={3}
                value={newActionText}
                onChange={(e) => setNewActionText(e.target.value)}
                placeholder="Describe the action needed to address this feedback..."
              />
            </div>
            
            <div className="space-y-2 mt-4">
              <Label htmlFor="assignee">Assign To</Label>
              <Input id="assignee" value="Current User" disabled />
              <p className="text-xs text-muted-foreground">
                In a production app, you would be able to select team members.
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowActionDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleAddAction} disabled={!newActionText.trim()}>
              <Plus className="h-4 w-4 mr-2" />
              Create Action
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}