import { useState } from "react";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";
import { useSupabaseAuth } from "@/hooks/use-supabase-auth";

export function SignOutButton({ variant = "default" }: { variant?: "default" | "outline" | "destructive" | "ghost" | "link" | "secondary" }) {
  const [isLoading, setIsLoading] = useState(false);
  const { signOut } = useSupabaseAuth();

  const handleSignOut = async () => {
    setIsLoading(true);
    try {
      await signOut();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      variant={variant} 
      size="sm" 
      onClick={handleSignOut} 
      disabled={isLoading}
    >
      {isLoading ? (
        <span className="flex items-center">
          <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
          Signing out...
        </span>
      ) : (
        <span className="flex items-center">
          <LogOut className="mr-2 h-4 w-4" />
          Sign out
        </span>
      )}
    </Button>
  );
}