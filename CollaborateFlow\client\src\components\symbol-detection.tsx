import { useState, useEffect } from "react";
import { Search, Zap, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { SymbolType, SYMBOL_TYPES } from "./symbol-editor";

interface DetectedSymbol {
  id: string;
  type: keyof typeof SYMBOL_TYPES;
  count: number;
  confidence: number;
  x: number;
  y: number;
}

interface SymbolDetectionProps {
  imageUrl: string;
  onComplete?: (symbols: DetectedSymbol[]) => void;
}

export function SymbolDetection({ imageUrl, onComplete }: SymbolDetectionProps) {
  const [detectionState, setDetectionState] = useState<"idle" | "processing" | "complete" | "error">("idle");
  const [progress, setProgress] = useState(0);
  const [detectedSymbols, setDetectedSymbols] = useState<DetectedSymbol[]>([]);
  const [activeTab, setActiveTab] = useState("overview");
  const [currentStep, setCurrentStep] = useState(0);
  const { toast } = useToast();
  
  // Detection process steps
  const detectionSteps = [
    { name: "Image Preprocessing", description: "Enhancing image quality for detection" },
    { name: "Symbol Detection", description: "Identifying electrical symbols" },
    { name: "Classification", description: "Classifying symbols by type" },
    { name: "Validation", description: "Verifying detection accuracy" }
  ];
  
  // Simulate the detection process
  const startDetection = () => {
    setDetectionState("processing");
    setProgress(0);
    setCurrentStep(0);
    
    // Detection process with backend API
    const processSteps = detectionSteps.length;
    let currentStep = 0;
    
    // Create a progress simulation interval
    const progressInterval = setInterval(() => {
      const stepProgress = Math.random() * 25 + 5; // Random progress between 5-30%
      const newProgress = Math.min(progress + stepProgress, (currentStep + 1) * (100 / processSteps));
      
      setProgress(newProgress);
      
      if (newProgress >= (currentStep + 1) * (100 / processSteps)) {
        currentStep++;
        setCurrentStep(currentStep);
        
        if (currentStep >= processSteps) {
          clearInterval(progressInterval);
          // Don't set complete here, we'll wait for the API response
          
          // Call the API to detect symbols
          detectSymbolsFromAPI();
        }
      }
    }, 700);
    
    return () => clearInterval(progressInterval);
  };
  
  // Call the backend API to detect symbols
  const detectSymbolsFromAPI = async () => {
    try {
      const response = await fetch('/api/ai/detect-symbols', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to detect symbols');
      }
      
      const data = await response.json();
      
      if (data.success && data.symbols) {
        setDetectedSymbols(data.symbols);
        setDetectionState("complete");
        
        toast({
          title: "Symbol Detection Complete",
          description: `${data.symbols.length} electrical symbols detected on the floor plan.`,
        });
        
        if (onComplete) {
          onComplete(data.symbols);
        }
      } else {
        throw new Error(data.message || 'Failed to detect symbols');
      }
    } catch (error) {
      console.error('Symbol detection error:', error);
      setDetectionState("error");
      
      toast({
        title: "Detection Error",
        description: `Failed to detect symbols: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
      
      // Fallback to locally generated symbols if API fails
      generateLocalSymbols();
    }
  };
  
  // Generate local symbols as fallback if API fails
  const generateLocalSymbols = () => {
    // Mock data for demonstration
    const mockSymbols: DetectedSymbol[] = [
      { 
        id: "s1", 
        type: "outlet", 
        count: 12, 
        confidence: 0.95,
        x: 150,
        y: 200
      },
      { 
        id: "s2", 
        type: "switch", 
        count: 8, 
        confidence: 0.88,
        x: 250,
        y: 180
      },
      { 
        id: "s3", 
        type: "light", 
        count: 15, 
        confidence: 0.92,
        x: 320,
        y: 240
      },
      { 
        id: "s4", 
        type: "panel", 
        count: 2, 
        confidence: 0.97,
        x: 100,
        y: 350
      },
      { 
        id: "s5", 
        type: "data", 
        count: 6, 
        confidence: 0.84,
        x: 400,
        y: 300
      }
    ];
    
    setDetectedSymbols(mockSymbols);
    setDetectionState("complete");
    
    if (onComplete) {
      onComplete(mockSymbols);
    }
  };
  
  // Handle detection completion
  const handleComplete = () => {
    toast({
      title: "Detection Complete",
      description: `${detectedSymbols.reduce((acc, symbol) => acc + symbol.count, 0)} symbols detected across ${detectedSymbols.length} categories`,
    });
    
    if (onComplete) {
      onComplete(detectedSymbols);
    }
  };
  
  // Get confidence level text and color
  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 0.9) {
      return { text: "High", color: "text-green-500" };
    } else if (confidence >= 0.7) {
      return { text: "Medium", color: "text-yellow-500" };
    } else {
      return { text: "Low", color: "text-red-500" };
    }
  };
  
  // Calculate total symbols detected
  const totalSymbols = detectedSymbols.reduce((acc, symbol) => acc + symbol.count, 0);
  
  return (
    <AICard>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium">Electrical Symbol Detection</h2>
            <p className="text-sm text-muted-foreground">
              Automatically detect electrical symbols on floor plans
            </p>
          </div>
          
          {detectionState === "complete" ? (
            <Button onClick={() => setDetectionState("idle")}>
              Restart Detection
            </Button>
          ) : (
            <Button 
              onClick={startDetection} 
              disabled={detectionState === "processing"}
            >
              {detectionState === "processing" ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Start Detection
                </>
              )}
            </Button>
          )}
        </div>
        
        {/* Detection process visualization */}
        {detectionState === "processing" && (
          <div className="space-y-4">
            <Progress value={progress} className="h-2" />
            
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
              {detectionSteps.map((step, index) => (
                <div 
                  key={index} 
                  className={`p-4 rounded-lg border ${
                    currentStep > index 
                      ? 'bg-primary/10 border-primary' 
                      : currentStep === index 
                        ? 'bg-primary/5 border-primary border-dashed' 
                        : 'border-border'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{step.name}</p>
                    {currentStep > index ? (
                      <CheckCircle className="h-4 w-4 text-primary" />
                    ) : (
                      currentStep === index && <Loader2 className="h-4 w-4 animate-spin" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{step.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Detection results */}
        {detectionState === "complete" && (
          <div className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="visualization">Visualization</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="rounded-lg p-4 border border-border">
                    <p className="text-sm font-medium text-muted-foreground">Total Symbols</p>
                    <p className="text-3xl font-bold mt-2">{totalSymbols}</p>
                  </div>
                  
                  <div className="rounded-lg p-4 border border-border">
                    <p className="text-sm font-medium text-muted-foreground">Categories</p>
                    <p className="text-3xl font-bold mt-2">{detectedSymbols.length}</p>
                  </div>
                  
                  <div className="rounded-lg p-4 border border-border">
                    <p className="text-sm font-medium text-muted-foreground">Avg. Confidence</p>
                    <p className="text-3xl font-bold mt-2">
                      {(detectedSymbols.reduce((acc, s) => acc + s.confidence, 0) / detectedSymbols.length * 100).toFixed(1)}%
                    </p>
                  </div>
                </div>
                
                <div className="rounded-lg border border-border overflow-hidden">
                  <div className="p-4 bg-muted/30">
                    <h3 className="text-sm font-medium">Symbol Summary</h3>
                  </div>
                  
                  <div className="p-4 space-y-4">
                    {detectedSymbols.map(symbol => (
                      <div key={symbol.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div 
                            className="w-8 h-8 flex items-center justify-center rounded-md mr-3" 
                            style={{ backgroundColor: `${SYMBOL_TYPES[symbol.type].color}20`, color: SYMBOL_TYPES[symbol.type].color }}
                          >
                            {SYMBOL_TYPES[symbol.type].icon}
                          </div>
                          <div>
                            <p className="font-medium text-sm">{SYMBOL_TYPES[symbol.type].name}</p>
                            <p className="text-xs text-muted-foreground">
                              Confidence: <span className={getConfidenceLevel(symbol.confidence).color}>
                                {(symbol.confidence * 100).toFixed(1)}%
                              </span>
                            </p>
                          </div>
                        </div>
                        
                        <Badge variant="outline" className="ml-auto">
                          {symbol.count}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button onClick={handleComplete}>
                    Continue with Detection
                  </Button>
                </div>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-4">
                <div className="rounded-lg border border-border overflow-hidden">
                  <div className="bg-muted/30 p-4">
                    <h3 className="text-sm font-medium">Detection Details</h3>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="border-b border-border">
                        <tr>
                          <th className="text-left p-3 text-xs font-medium text-muted-foreground">Symbol Type</th>
                          <th className="text-left p-3 text-xs font-medium text-muted-foreground">Count</th>
                          <th className="text-left p-3 text-xs font-medium text-muted-foreground">Confidence</th>
                          <th className="text-left p-3 text-xs font-medium text-muted-foreground">Position</th>
                          <th className="text-left p-3 text-xs font-medium text-muted-foreground">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {detectedSymbols.map((symbol) => (
                          <tr key={symbol.id} className="border-b border-border">
                            <td className="p-3">
                              <div className="flex items-center">
                                <div 
                                  className="w-6 h-6 flex items-center justify-center rounded-md mr-3" 
                                  style={{ backgroundColor: `${SYMBOL_TYPES[symbol.type].color}20`, color: SYMBOL_TYPES[symbol.type].color }}
                                >
                                  {SYMBOL_TYPES[symbol.type].icon}
                                </div>
                                <span className="font-medium text-sm">{SYMBOL_TYPES[symbol.type].name}</span>
                              </div>
                            </td>
                            <td className="p-3 text-sm">{symbol.count}</td>
                            <td className="p-3 text-sm">
                              <div className="flex items-center">
                                <div className="w-full max-w-[100px] h-2 rounded-full bg-muted overflow-hidden mr-2">
                                  <div 
                                    className="h-full rounded-full" 
                                    style={{ 
                                      width: `${symbol.confidence * 100}%`,
                                      backgroundColor: symbol.confidence > 0.9 
                                        ? '#10b981' 
                                        : symbol.confidence > 0.7 
                                          ? '#eab308' 
                                          : '#ef4444'
                                    }}
                                  ></div>
                                </div>
                                <span className={getConfidenceLevel(symbol.confidence).color}>
                                  {(symbol.confidence * 100).toFixed(1)}%
                                </span>
                              </div>
                            </td>
                            <td className="p-3 text-sm">
                              x: {symbol.x}, y: {symbol.y}
                            </td>
                            <td className="p-3 text-sm">
                              <Button variant="ghost" size="sm">
                                <Search className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  
                  <div className="p-4 flex justify-end">
                    <Button onClick={handleComplete}>
                      Continue with Detection
                    </Button>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="visualization" className="space-y-4">
                <div className="flex justify-center items-center h-[400px] border border-border rounded-lg p-6">
                  <div className="text-center space-y-4">
                    <Search className="h-12 w-12 text-muted-foreground mx-auto" />
                    <h3 className="text-lg font-medium">Symbol Visualization</h3>
                    <p className="text-sm text-muted-foreground max-w-md mx-auto">
                      The visualization shows the detected symbols overlaid on the floor plan.
                      You can further edit and refine these symbols in the Symbol Editor.
                    </p>
                    <Button onClick={handleComplete}>
                      Continue to Symbol Editor
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
        
        {/* Initial state */}
        {detectionState === "idle" && (
          <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
            <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-4">
              <Zap className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-lg font-medium mb-2">Start Symbol Detection</h3>
            <p className="text-sm text-muted-foreground mb-4 max-w-md mx-auto">
              Our AI will analyze the floor plan and automatically detect electrical symbols such as outlets, switches, lights, and more.
            </p>
            <Button onClick={startDetection}>
              <Zap className="mr-2 h-4 w-4" />
              Start Detection
            </Button>
          </div>
        )}
        
        {/* Error state */}
        {detectionState === "error" && (
          <div className="border border-destructive/20 bg-destructive/10 rounded-lg p-6 text-center">
            <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-destructive/20 mb-4">
              <AlertCircle className="h-6 w-6 text-destructive" />
            </div>
            <h3 className="text-lg font-medium mb-2">Detection Failed</h3>
            <p className="text-sm text-muted-foreground mb-4 max-w-md mx-auto">
              An error occurred while detecting symbols. Please try again or upload a clearer image of the floor plan.
            </p>
            <Button onClick={startDetection}>
              <Zap className="mr-2 h-4 w-4" />
              Retry Detection
            </Button>
          </div>
        )}
      </div>
    </AICard>
  );
}