import { useState, useRef, useEffect } from "react";
import { 
  Move, 
  Pointer, 
  Trash, 
  RotateCw, 
  ZoomIn, 
  ZoomOut, 
  Save, 
  Copy, 
  Undo, 
  Redo, 
  Plus, 
  Minus, 
  RotateCcw,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  ChevronDown,
  Search,
  MoreVertical
} from "lucide-react";
import { AICard } from "@/components/ai-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Toggle } from "@/components/ui/toggle";
import { useToast } from "@/hooks/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Define symbol types
export type SymbolType = "outlet" | "switch" | "light" | "panel" | "data";

// Symbol types with visual attributes
export const SYMBOL_TYPES: Record<
  SymbolType, 
  { name: string; description: string; icon: string; color: string }
> = {
  outlet: { 
    name: "Outlet", 
    description: "Standard electrical outlet", 
    icon: "⚡", 
    color: "#f97316" 
  },
  switch: { 
    name: "Switch", 
    description: "Light switch", 
    icon: "⚙️", 
    color: "#06b6d4" 
  },
  light: { 
    name: "Light Fixture", 
    description: "Ceiling or wall mounted light", 
    icon: "💡", 
    color: "#eab308" 
  },
  panel: { 
    name: "Panel", 
    description: "Electrical panel", 
    icon: "🔌", 
    color: "#8b5cf6" 
  },
  data: { 
    name: "Data Port", 
    description: "Network or phone connection", 
    icon: "📡", 
    color: "#10b981" 
  }
};

// Symbol library categories
const SYMBOL_CATEGORIES = [
  { id: "power", name: "Power", types: ["outlet", "panel"] as SymbolType[] },
  { id: "lighting", name: "Lighting", types: ["switch", "light"] as SymbolType[] },
  { id: "data", name: "Data & Communications", types: ["data"] as SymbolType[] }
];

interface Position {
  x: number;
  y: number;
}

interface EditorSymbol {
  id: string;
  type: SymbolType;
  position: Position;
  rotation: number;
  scale: number;
  selected: boolean;
}

interface SymbolEditorProps {
  imageUrl: string;
  detectedSymbols?: any[];
  onSymbolsUpdated?: (symbols: EditorSymbol[]) => void;
}

type EditingTool = "select" | "move" | "draw" | "erase" | "add";

export function SymbolEditor({ imageUrl, detectedSymbols = [], onSymbolsUpdated }: SymbolEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);
  const { toast } = useToast();
  
  // Editor state
  const [symbols, setSymbols] = useState<EditorSymbol[]>([]);
  const [activeTool, setActiveTool] = useState<EditingTool>("select");
  const [selectedSymbol, setSelectedSymbol] = useState<string | null>(null);
  const [selectedSymbolType, setSelectedSymbolType] = useState<SymbolType>("outlet");
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
  const [isMovingSymbol, setIsMovingSymbol] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [history, setHistory] = useState<EditorSymbol[][]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [showSymbolLibrary, setShowSymbolLibrary] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState(SYMBOL_CATEGORIES[0].id);
  
  // Initialize editor with detected symbols if provided
  useEffect(() => {
    if (detectedSymbols && detectedSymbols.length > 0) {
      const initialSymbols = detectedSymbols.map((symbol: any) => ({
        id: symbol.id || Math.random().toString(36).substring(2, 9),
        type: symbol.type as SymbolType || "outlet",
        position: symbol.position || { x: 0, y: 0 },
        rotation: symbol.rotation || 0,
        scale: symbol.scale || 1,
        selected: false
      }));
      
      setSymbols(initialSymbols);
      // Initialize history with initial state
      setHistory([initialSymbols]);
      setHistoryIndex(0);
    }
  }, [detectedSymbols]);
  
  // Load the background image
  useEffect(() => {
    if (!imageUrl) return;
    
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      imageRef.current = img;
      drawCanvas();
    };
  }, [imageUrl]);
  
  // Redraw canvas when state changes
  useEffect(() => {
    drawCanvas();
  }, [symbols, activeTool, selectedSymbol, scale, position]);
  
  // Add a new history state
  const addHistoryState = (newSymbols: EditorSymbol[]) => {
    // Discard any future history states if we're not at the latest state
    const newHistory = history.slice(0, historyIndex + 1);
    // Add the new state to history
    newHistory.push([...newSymbols]);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };
  
  // Undo the last action
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setSymbols([...history[historyIndex - 1]]);
    }
  };
  
  // Redo a previously undone action
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setSymbols([...history[historyIndex + 1]]);
    }
  };
  
  // Draw the canvas
  const drawCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    
    if (!canvas || !ctx || !imageRef.current) return;
    
    // Set canvas dimensions to match container
    if (containerRef.current) {
      canvas.width = containerRef.current.clientWidth;
      canvas.height = containerRef.current.clientHeight;
    }
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background image with scale and position
    const img = imageRef.current;
    ctx.save();
    ctx.translate(position.x, position.y);
    ctx.scale(scale, scale);
    ctx.drawImage(img, 0, 0);
    ctx.restore();
    
    // Draw symbols
    symbols.forEach(symbol => {
      ctx.save();
      
      // Apply position, rotation, and scale
      ctx.translate(
        position.x + symbol.position.x * scale, 
        position.y + symbol.position.y * scale
      );
      ctx.rotate((symbol.rotation * Math.PI) / 180);
      ctx.scale(symbol.scale * scale, symbol.scale * scale);
      
      // Draw highlight if selected
      if (symbol.selected || symbol.id === selectedSymbol) {
        ctx.beginPath();
        ctx.arc(0, 0, 25, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(0, 120, 255, 0.2)';
        ctx.fill();
        ctx.strokeStyle = 'rgba(0, 120, 255, 0.8)';
        ctx.lineWidth = 2 / (symbol.scale * scale);
        ctx.stroke();
      }
      
      // Draw the symbol
      ctx.fillStyle = SYMBOL_TYPES[symbol.type].color;
      ctx.font = `${48 / (symbol.scale * scale)}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(SYMBOL_TYPES[symbol.type].icon, 0, 0);
      
      ctx.restore();
    });
    
    // Add hint for add mode
    if (activeTool === "add") {
      ctx.save();
      ctx.font = '16px Arial';
      ctx.fillStyle = '#667085';
      ctx.textAlign = 'center';
      ctx.fillText('Click on the canvas to add symbols', canvas.width / 2, 30);
      ctx.restore();
    }
  };
  
  // Handle mouse down event
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    switch (activeTool) {
      case "select":
        // Check if we clicked on a symbol
        const clickedSymbol = findSymbolAtPosition(mouseX, mouseY);
        
        // If we clicked on a symbol, select it
        if (clickedSymbol) {
          const newSymbols = symbols.map(s => ({
            ...s,
            selected: s.id === clickedSymbol.id
          }));
          
          setSymbols(newSymbols);
          setSelectedSymbol(clickedSymbol.id);
        } else {
          // If we didn't click on a symbol, deselect all
          const newSymbols = symbols.map(s => ({
            ...s,
            selected: false
          }));
          
          setSymbols(newSymbols);
          setSelectedSymbol(null);
        }
        break;
      
      case "move":
        setIsDragging(true);
        setDragStartPos({ x: mouseX, y: mouseY });
        break;
      
      case "add":
        const canvasX = (mouseX - position.x) / scale;
        const canvasY = (mouseY - position.y) / scale;
        
        const newSymbol: EditorSymbol = {
          id: Math.random().toString(36).substring(2, 9),
          type: selectedSymbolType,
          position: { x: canvasX, y: canvasY },
          rotation: 0,
          scale: 1,
          selected: true
        };
        
        const newSymbols = symbols.map(s => ({ ...s, selected: false })).concat(newSymbol);
        
        setSymbols(newSymbols);
        setSelectedSymbol(newSymbol.id);
        addHistoryState(newSymbols);
        break;
      
      case "erase":
        const symbolToErase = findSymbolAtPosition(mouseX, mouseY);
        
        if (symbolToErase) {
          const newSymbols = symbols.filter(s => s.id !== symbolToErase.id);
          setSymbols(newSymbols);
          setSelectedSymbol(null);
          addHistoryState(newSymbols);
        }
        break;
    }
  };
  
  // Handle mouse move event
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    // Moving the canvas
    if (isDragging && activeTool === "move") {
      const deltaX = mouseX - dragStartPos.x;
      const deltaY = mouseY - dragStartPos.y;
      
      setPosition(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      
      setDragStartPos({ x: mouseX, y: mouseY });
    }
    
    // Moving a selected symbol
    if (selectedSymbol && e.buttons === 1 && activeTool === "select" && !isMovingSymbol) {
      setIsMovingSymbol(true);
      setDragStartPos({ x: mouseX, y: mouseY });
    }
    
    if (isMovingSymbol && e.buttons === 1) {
      const deltaX = mouseX - dragStartPos.x;
      const deltaY = mouseY - dragStartPos.y;
      
      setSymbols(prev => 
        prev.map(symbol => 
          symbol.id === selectedSymbol
            ? {
                ...symbol,
                position: {
                  x: symbol.position.x + deltaX / scale,
                  y: symbol.position.y + deltaY / scale
                }
              }
            : symbol
        )
      );
      
      setDragStartPos({ x: mouseX, y: mouseY });
    }
  };
  
  // Handle mouse up event
  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false);
    }
    
    if (isMovingSymbol) {
      setIsMovingSymbol(false);
      addHistoryState(symbols);
    }
  };
  
  // Handle key down event
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!selectedSymbol) return;
    
    const step = e.shiftKey ? 10 : 1;
    let updated = false;
    let newSymbols = [...symbols];
    
    // Clone the current symbol
    if (e.key === 'd' && e.ctrlKey) {
      e.preventDefault();
      const symbolToCopy = symbols.find(s => s.id === selectedSymbol);
      
      if (symbolToCopy) {
        const newSymbol: EditorSymbol = {
          ...symbolToCopy,
          id: Math.random().toString(36).substring(2, 9),
          position: { 
            x: symbolToCopy.position.x + 20, 
            y: symbolToCopy.position.y + 20 
          },
          selected: true
        };
        
        newSymbols = symbols.map(s => ({ ...s, selected: false })).concat(newSymbol);
        setSymbols(newSymbols);
        setSelectedSymbol(newSymbol.id);
        updated = true;
      }
    }
    
    // Move with arrow keys
    if (e.key.startsWith('Arrow')) {
      e.preventDefault();
      
      newSymbols = symbols.map(symbol => {
        if (symbol.id !== selectedSymbol) return symbol;
        
        let newX = symbol.position.x;
        let newY = symbol.position.y;
        
        switch (e.key) {
          case 'ArrowUp': 
            newY -= step; 
            break;
          case 'ArrowDown': 
            newY += step; 
            break;
          case 'ArrowLeft': 
            newX -= step; 
            break;
          case 'ArrowRight': 
            newX += step; 
            break;
        }
        
        return {
          ...symbol,
          position: { x: newX, y: newY }
        };
      });
      
      setSymbols(newSymbols);
      updated = true;
    }
    
    // Rotate with [ and ]
    if (e.key === '[' || e.key === ']') {
      e.preventDefault();
      const rotationChange = e.key === '[' ? -15 : 15;
      
      newSymbols = symbols.map(symbol => {
        if (symbol.id !== selectedSymbol) return symbol;
        
        return {
          ...symbol,
          rotation: (symbol.rotation + rotationChange) % 360
        };
      });
      
      setSymbols(newSymbols);
      updated = true;
    }
    
    // Scale with - and =
    if (e.key === '-' || e.key === '=') {
      e.preventDefault();
      const scaleChange = e.key === '-' ? -0.1 : 0.1;
      
      newSymbols = symbols.map(symbol => {
        if (symbol.id !== selectedSymbol) return symbol;
        
        return {
          ...symbol,
          scale: Math.max(0.1, Math.min(5, symbol.scale + scaleChange))
        };
      });
      
      setSymbols(newSymbols);
      updated = true;
    }
    
    // Delete with Delete or Backspace
    if (e.key === 'Delete' || e.key === 'Backspace') {
      e.preventDefault();
      newSymbols = symbols.filter(s => s.id !== selectedSymbol);
      setSymbols(newSymbols);
      setSelectedSymbol(null);
      updated = true;
    }
    
    // Undo with Ctrl+Z
    if (e.key === 'z' && e.ctrlKey) {
      e.preventDefault();
      handleUndo();
      return;
    }
    
    // Redo with Ctrl+Y or Ctrl+Shift+Z
    if ((e.key === 'y' && e.ctrlKey) || (e.key === 'z' && e.ctrlKey && e.shiftKey)) {
      e.preventDefault();
      handleRedo();
      return;
    }
    
    if (updated) {
      addHistoryState(newSymbols);
    }
  };
  
  // Find symbol at position
  const findSymbolAtPosition = (x: number, y: number) => {
    // Check in reverse order (top to bottom in z-index)
    for (let i = symbols.length - 1; i >= 0; i--) {
      const symbol = symbols[i];
      
      // Convert canvas coords to symbol's local coords
      const symbolX = position.x + symbol.position.x * scale;
      const symbolY = position.y + symbol.position.y * scale;
      
      // Simple circular hit test (can be improved for more accurate detection)
      const distance = Math.sqrt(Math.pow(x - symbolX, 2) + Math.pow(y - symbolY, 2));
      const hitRadius = 20 * symbol.scale * scale; // Adjust hit radius based on symbol size
      
      if (distance <= hitRadius) {
        return symbol;
      }
    }
    return null;
  };
  
  // Handle wheel event for zooming
  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    
    // Zoom amount
    const zoomIntensity = 0.1;
    const delta = e.deltaY > 0 ? -zoomIntensity : zoomIntensity;
    
    // Calculate new scale
    const newScale = Math.max(0.1, Math.min(5, scale + delta));
    
    // Calculate zoom point
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    // Calculate new position to zoom towards cursor
    const newPosition = {
      x: mouseX - (mouseX - position.x) * (newScale / scale),
      y: mouseY - (mouseY - position.y) * (newScale / scale)
    };
    
    // Update state
    setScale(newScale);
    setPosition(newPosition);
  };
  
  // Handle zoom in button
  const handleZoomIn = () => {
    setScale(prev => Math.min(5, prev + 0.1));
  };
  
  // Handle zoom out button
  const handleZoomOut = () => {
    setScale(prev => Math.max(0.1, prev - 0.1));
  };
  
  // Handle reset view
  const handleResetView = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };
  
  // Handle save symbols
  const handleSave = () => {
    if (onSymbolsUpdated) {
      onSymbolsUpdated(symbols);
    }
    
    toast({
      title: "Symbols saved",
      description: `${symbols.length} symbols have been saved.`
    });
  };
  
  // Handle symbol property changes
  const updateSymbolProperty = (
    property: keyof EditorSymbol, 
    value: any
  ) => {
    if (!selectedSymbol) return;
    
    const newSymbols = symbols.map(symbol => 
      symbol.id === selectedSymbol
        ? { ...symbol, [property]: value }
        : symbol
    );
    
    setSymbols(newSymbols);
    addHistoryState(newSymbols);
  };
  
  // Change symbol type
  const changeSymbolType = (type: SymbolType) => {
    if (!selectedSymbol) return;
    updateSymbolProperty('type', type);
  };
  
  // Handle rotation change
  const handleRotationChange = (rotation: number) => {
    if (!selectedSymbol) return;
    updateSymbolProperty('rotation', rotation);
  };
  
  // Handle scale change
  const handleScaleChange = (newScale: number) => {
    if (!selectedSymbol) return;
    updateSymbolProperty('scale', newScale);
  };
  
  // Effect to add keyboard event listeners
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [symbols, selectedSymbol, historyIndex, history]);
  
  // Filter symbols by search query
  const filteredSymbols = Object.entries(SYMBOL_TYPES).filter(([_, data]) => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      data.name.toLowerCase().includes(query) || 
      data.description.toLowerCase().includes(query)
    );
  });
  
  // Get selected symbol data
  const selectedSymbolData = selectedSymbol 
    ? symbols.find(s => s.id === selectedSymbol) 
    : null;
  
  // Get symbols for the current category
  const currentCategoryTypes = SYMBOL_CATEGORIES.find(cat => cat.id === selectedCategory)?.types || [];
  
  return (
    <AICard className="flex flex-col h-full">
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium">Symbol Editor</h2>
          
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    onClick={handleUndo}
                    disabled={historyIndex <= 0}
                  >
                    <Undo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    onClick={handleRedo}
                    disabled={historyIndex >= history.length - 1}
                  >
                    <Redo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save Symbols
            </Button>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Toggle 
                  pressed={activeTool === "select"} 
                  onPressedChange={() => setActiveTool("select")}
                  variant="outline"
                >
                  <Pointer className="h-4 w-4 mr-2" />
                  Select
                </Toggle>
              </TooltipTrigger>
              <TooltipContent>Select and move symbols</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Toggle 
                  pressed={activeTool === "move"} 
                  onPressedChange={() => setActiveTool("move")}
                  variant="outline"
                >
                  <Move className="h-4 w-4 mr-2" />
                  Pan
                </Toggle>
              </TooltipTrigger>
              <TooltipContent>Pan the floor plan</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Toggle 
                  pressed={activeTool === "add"} 
                  onPressedChange={() => setActiveTool("add")}
                  variant="outline"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add
                </Toggle>
              </TooltipTrigger>
              <TooltipContent>Add new symbols</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Toggle 
                  pressed={activeTool === "erase"} 
                  onPressedChange={() => setActiveTool("erase")}
                  variant="outline"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Erase
                </Toggle>
              </TooltipTrigger>
              <TooltipContent>Remove symbols</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="flex-1" />
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={handleZoomIn}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Zoom in</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={handleZoomOut}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Zoom out</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={handleResetView}
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Reset view</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Symbol Library / Properties Panel */}
        <div className="w-80 border-r border-border overflow-auto flex flex-col">
          <Tabs defaultValue="library">
            <TabsList className="w-full">
              <TabsTrigger value="library" className="flex-1">Library</TabsTrigger>
              <TabsTrigger value="properties" className="flex-1">Properties</TabsTrigger>
            </TabsList>
            
            <TabsContent value="library" className="p-4 space-y-4">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Search symbols..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              {searchQuery ? (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Search Results</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {filteredSymbols.map(([type, data]) => (
                      <Button
                        key={type}
                        variant="outline"
                        size="sm"
                        className={`flex flex-col h-auto py-3 justify-start items-center ${selectedSymbolType === type ? 'border-primary' : ''}`}
                        onClick={() => {
                          setSelectedSymbolType(type as SymbolType);
                          if (activeTool !== "add") setActiveTool("add");
                        }}
                      >
                        <div className="text-2xl mb-1">{data.icon}</div>
                        <div className="text-xs">{data.name}</div>
                      </Button>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="px-2"
                      onClick={() => setShowSymbolLibrary(!showSymbolLibrary)}
                    >
                      <ChevronDown className={`h-4 w-4 transition-transform ${showSymbolLibrary ? '' : '-rotate-90'}`} />
                    </Button>
                    <h3 className="text-sm font-medium">Categories</h3>
                  </div>
                  
                  {showSymbolLibrary && (
                    <div className="space-y-4">
                      <div className="flex space-x-2 overflow-x-auto pb-1">
                        {SYMBOL_CATEGORIES.map(category => (
                          <Button
                            key={category.id}
                            variant={selectedCategory === category.id ? "secondary" : "outline"}
                            size="sm"
                            onClick={() => setSelectedCategory(category.id)}
                          >
                            {category.name}
                          </Button>
                        ))}
                      </div>
                      
                      <div className="space-y-2">
                        <h4 className="text-xs font-medium text-muted-foreground">
                          {SYMBOL_CATEGORIES.find(cat => cat.id === selectedCategory)?.name || "All Symbols"}
                        </h4>
                        <div className="grid grid-cols-2 gap-2">
                          {currentCategoryTypes.map(type => (
                            <Button
                              key={type}
                              variant="outline"
                              size="sm"
                              className={`flex flex-col h-auto py-3 justify-start items-center ${selectedSymbolType === type ? 'border-primary' : ''}`}
                              onClick={() => {
                                setSelectedSymbolType(type);
                                if (activeTool !== "add") setActiveTool("add");
                              }}
                            >
                              <div className="text-2xl mb-1">{SYMBOL_TYPES[type].icon}</div>
                              <div className="text-xs">{SYMBOL_TYPES[type].name}</div>
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
              
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="all">
                  <AccordionTrigger className="text-sm">All Symbols</AccordionTrigger>
                  <AccordionContent>
                    <div className="grid grid-cols-2 gap-2">
                      {Object.entries(SYMBOL_TYPES).map(([type, data]) => (
                        <Button
                          key={type}
                          variant="outline"
                          size="sm"
                          className={`flex flex-col h-auto py-3 justify-start items-center ${selectedSymbolType === type ? 'border-primary' : ''}`}
                          onClick={() => {
                            setSelectedSymbolType(type as SymbolType);
                            if (activeTool !== "add") setActiveTool("add");
                          }}
                        >
                          <div className="text-2xl mb-1">{data.icon}</div>
                          <div className="text-xs">{data.name}</div>
                        </Button>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
              
              <div className="pt-4 border-t border-border">
                <h3 className="text-sm font-medium mb-2">Selected Symbol</h3>
                {selectedSymbolType && (
                  <div className="border border-border rounded-md p-3">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{SYMBOL_TYPES[selectedSymbolType].icon}</div>
                      <div>
                        <p className="font-medium">{SYMBOL_TYPES[selectedSymbolType].name}</p>
                        <p className="text-xs text-muted-foreground">{SYMBOL_TYPES[selectedSymbolType].description}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="properties" className="p-4 space-y-4">
              {selectedSymbolData ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">Symbol Properties</h3>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          onClick={() => {
                            const symbolToCopy = symbols.find(s => s.id === selectedSymbol);
                            
                            if (symbolToCopy) {
                              const newSymbol: EditorSymbol = {
                                ...symbolToCopy,
                                id: Math.random().toString(36).substring(2, 9),
                                position: { 
                                  x: symbolToCopy.position.x + 20, 
                                  y: symbolToCopy.position.y + 20 
                                },
                                selected: true
                              };
                              
                              const newSymbols = symbols.map(s => ({ ...s, selected: false })).concat(newSymbol);
                              setSymbols(newSymbols);
                              setSelectedSymbol(newSymbol.id);
                              addHistoryState(newSymbols);
                            }
                          }}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate (Ctrl+D)
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => {
                            const newSymbols = symbols.filter(s => s.id !== selectedSymbol);
                            setSymbols(newSymbols);
                            setSelectedSymbol(null);
                            addHistoryState(newSymbols);
                          }}
                        >
                          <Trash className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="text-xs text-muted-foreground block mb-1">Symbol Type</label>
                      <div className="grid grid-cols-3 gap-2">
                        {Object.entries(SYMBOL_TYPES).map(([type, data]) => (
                          <Button
                            key={type}
                            variant="outline"
                            size="sm"
                            className={`h-auto py-2 ${selectedSymbolData.type === type ? 'border-primary' : ''}`}
                            onClick={() => changeSymbolType(type as SymbolType)}
                          >
                            <div className="mr-1">{data.icon}</div>
                            <div className="text-xs">{data.name}</div>
                          </Button>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs text-muted-foreground block mb-1">Position</label>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <label className="text-xs block mb-1">X</label>
                          <Input 
                            type="number"
                            value={Math.round(selectedSymbolData.position.x)}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              if (!isNaN(value)) {
                                updateSymbolProperty('position', {
                                  ...selectedSymbolData.position,
                                  x: value
                                });
                              }
                            }}
                            className="h-8"
                          />
                        </div>
                        <div>
                          <label className="text-xs block mb-1">Y</label>
                          <Input 
                            type="number"
                            value={Math.round(selectedSymbolData.position.y)}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              if (!isNaN(value)) {
                                updateSymbolProperty('position', {
                                  ...selectedSymbolData.position,
                                  y: value
                                });
                              }
                            }}
                            className="h-8"
                          />
                        </div>
                      </div>
                      <div className="flex justify-center mt-2">
                        <div className="grid grid-cols-3 gap-1">
                          <Button 
                            variant="outline" 
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => {
                              updateSymbolProperty('position', {
                                ...selectedSymbolData.position,
                                x: selectedSymbolData.position.x - 5
                              });
                            }}
                          >
                            <AlignLeft className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => {
                              updateSymbolProperty('position', {
                                ...selectedSymbolData.position,
                                y: selectedSymbolData.position.y - 5
                              });
                            }}
                          >
                            <AlignCenter className="h-3 w-3 rotate-90" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => {
                              updateSymbolProperty('position', {
                                ...selectedSymbolData.position,
                                x: selectedSymbolData.position.x + 5
                              });
                            }}
                          >
                            <AlignRight className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => {
                              updateSymbolProperty('position', {
                                ...selectedSymbolData.position,
                                x: selectedSymbolData.position.x - 5,
                                y: selectedSymbolData.position.y + 5
                              });
                            }}
                          >
                            <AlignLeft className="h-3 w-3 rotate-45" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => {
                              updateSymbolProperty('position', {
                                ...selectedSymbolData.position,
                                y: selectedSymbolData.position.y + 5
                              });
                            }}
                          >
                            <AlignJustify className="h-3 w-3 rotate-90" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => {
                              updateSymbolProperty('position', {
                                ...selectedSymbolData.position,
                                x: selectedSymbolData.position.x + 5,
                                y: selectedSymbolData.position.y + 5
                              });
                            }}
                          >
                            <AlignRight className="h-3 w-3 rotate-45" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs text-muted-foreground block mb-1">
                        Rotation: {selectedSymbolData.rotation}°
                      </label>
                      <div className="flex items-center space-x-2">
                        <Button 
                          variant="outline" 
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleRotationChange((selectedSymbolData.rotation - 15 + 360) % 360)}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Slider
                          value={[selectedSymbolData.rotation]}
                          min={0}
                          max={359}
                          step={1}
                          className="flex-1"
                          onValueChange={(values) => handleRotationChange(values[0])}
                        />
                        <Button 
                          variant="outline" 
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleRotationChange((selectedSymbolData.rotation + 15) % 360)}
                        >
                          <RotateCw className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs text-muted-foreground block mb-1">
                        Scale: {selectedSymbolData.scale.toFixed(1)}
                      </label>
                      <div className="flex items-center space-x-2">
                        <Button 
                          variant="outline" 
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleScaleChange(Math.max(0.1, selectedSymbolData.scale - 0.1))}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <Slider
                          value={[selectedSymbolData.scale]}
                          min={0.1}
                          max={3}
                          step={0.1}
                          className="flex-1"
                          onValueChange={(values) => handleScaleChange(values[0])}
                        />
                        <Button 
                          variant="outline" 
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleScaleChange(Math.min(3, selectedSymbolData.scale + 0.1))}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[200px] text-center">
                  <Pointer className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium mb-1">No Symbol Selected</h3>
                  <p className="text-sm text-muted-foreground">
                    Select a symbol on the canvas to view and edit its properties
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
        
        {/* Canvas */}
        <div 
          className="flex-1 relative overflow-hidden"
          ref={containerRef}
        >
          <canvas
            ref={canvasRef}
            className="w-full h-full"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onWheel={handleWheel}
            style={{ cursor: activeTool === "add" ? "cell" : activeTool === "move" ? "grab" : "default" }}
          />
          
          {/* Status bar */}
          <div className="absolute bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm px-3 py-1 text-xs flex justify-between items-center border-t border-border">
            <div>
              Tool: {activeTool.charAt(0).toUpperCase() + activeTool.slice(1)}
            </div>
            <div>
              Zoom: {Math.round(scale * 100)}%
            </div>
            <div>
              Symbols: {symbols.length}
            </div>
          </div>
        </div>
      </div>
    </AICard>
  );
}