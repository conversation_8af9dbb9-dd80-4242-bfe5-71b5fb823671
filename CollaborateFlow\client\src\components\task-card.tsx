import { useState } from "react";
import { Task } from "@shared/schema";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AvatarGroup } from "@/components/avatar-group";
import { Calendar, MoreVertical } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TaskCardProps {
  task: Task;
  onDragStart: (e: React.DragEvent) => void;
}

export function TaskCard({ task, onDragStart }: TaskCardProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  const getCategoryColor = (category: string) => {
    switch (category) {
      case "development":
        return "bg-blue-50 text-primary dark:bg-blue-900 dark:bg-opacity-30";
      case "design":
        return "bg-green-50 text-secondary dark:bg-green-900 dark:bg-opacity-30";
      case "testing":
        return "bg-yellow-50 text-amber-700 dark:bg-yellow-900 dark:bg-opacity-30";
      case "infrastructure":
        return "bg-purple-50 text-purple-700 dark:bg-purple-900 dark:bg-opacity-30";
      default:
        return "bg-gray-50 text-gray-700 dark:bg-gray-700 dark:bg-opacity-30";
    }
  };
  
  const getPriorityStyles = (priority: string) => {
    if (priority === "high" || priority === "urgent") {
      return "border-l-4 border-l-accent";
    }
    return "";
  };

  return (
    <div 
      className={`task-card bg-card rounded-lg p-4 mb-3 shadow-sm cursor-pointer border border-border ${getPriorityStyles(task.priority)}`}
      draggable
      onDragStart={onDragStart}
    >
      <div className="flex justify-between items-start mb-3">
        <Badge variant="outline" className={`${getCategoryColor(task.category)}`}>
          {task.category}
        </Badge>
        
        <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
            <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-40">
            <DropdownMenuItem>Edit</DropdownMenuItem>
            <DropdownMenuItem>Mark Complete</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-destructive">Delete</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      <h4 className="font-medium mb-2">{task.title}</h4>
      
      {task.description && (
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{task.description}</p>
      )}
      
      <div className="flex justify-between items-center">
        <AvatarGroup members={task.assignees || []} limit={3} size="sm" />
        
        {task.dueDate && (
          <div className="text-xs text-muted-foreground flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            {new Date(task.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
          </div>
        )}
      </div>
      
      {task.priority === "high" || task.priority === "urgent" ? (
        <div className="mt-2 flex items-center">
          <Badge variant="outline" className="bg-accent/10 text-accent text-xs">
            {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority
          </Badge>
        </div>
      ) : null}
    </div>
  );
}
