import { useTheme } from "next-themes";
import { Moon, Sun } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";

export function ThemeToggle() {
  const { resolvedTheme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch by only rendering after component is mounted
  useEffect(() => {
    setMounted(true);
  }, []);

  // Force apply dark mode class if resolvedTheme is dark
  useEffect(() => {
    if (mounted && resolvedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else if (mounted) {
      document.documentElement.classList.remove('dark');
    }
  }, [mounted, resolvedTheme]);

  if (!mounted) return null;

  const toggleTheme = () => {
    const newTheme = resolvedTheme === "dark" ? "light" : "dark";
    setTheme(newTheme);
    
    // Manually update the class for immediate visual feedback
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      aria-label="Toggle theme"
      className="rounded-full w-9 h-9 bg-transparent hover:bg-muted/50"
    >
      {resolvedTheme === "dark" ? (
        <Sun className="h-[18px] w-[18px] text-muted-foreground" />
      ) : (
        <Moon className="h-[18px] w-[18px] text-muted-foreground" />
      )}
      <span className="sr-only">
        {resolvedTheme === "dark" ? "Switch to light theme" : "Switch to dark theme"}
      </span>
    </Button>
  );
}