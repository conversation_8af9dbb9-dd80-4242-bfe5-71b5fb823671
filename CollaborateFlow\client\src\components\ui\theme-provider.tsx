"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"

// Simple props interface to avoid type issues
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: string;
  storageKey?: string;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
}

export function ThemeProvider({ 
  children, 
  defaultTheme = "dark", 
  storageKey = "coelec-theme",
  enableSystem = false, // Disable system theme by default
  ...props
}: ThemeProviderProps) {
  // Cast to any to avoid TypeScript errors with "attribute"
  const themeProviderProps: any = {
    defaultTheme,
    storageKey,
    enableSystem,
    attribute: "class", // Use class instead of data-theme for Tailwind
    ...props
  };
  
  return (
    <NextThemesProvider {...themeProviderProps}>
      {children}
    </NextThemesProvider>
  )
}