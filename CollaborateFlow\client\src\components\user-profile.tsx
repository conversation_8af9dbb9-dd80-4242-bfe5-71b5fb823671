import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Lock, 
  Globe,
  Key,
  LogOut,
  Upload,
  Trash,
  Github,
  Twitter,
  Linkedin,
  Clock,
  AlertCircle
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";

export function UserProfile() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("personal");
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    fullName: user?.fullName || "",
    email: user?.email || "",
    phone: "",
    location: "",
    bio: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  // Mock data for activity history
  const activityHistory = [
    { id: 1, action: "Project Created", details: "Created a new project 'Hermes'", timestamp: "2025-05-12T15:30:00Z" },
    { id: 2, action: "Quote Sent", details: "Sent quote #QT-001 to client", timestamp: "2025-05-11T11:45:00Z" },
    { id: 3, action: "Task Completed", details: "Marked task 'Create floor plan' as completed", timestamp: "2025-05-10T09:20:00Z" },
    { id: 4, action: "Login", details: "Logged in from new device", timestamp: "2025-05-10T08:10:00Z" },
    { id: 5, action: "Profile Updated", details: "Updated profile information", timestamp: "2025-05-09T14:25:00Z" },
    { id: 6, action: "Quote Approved", details: "Client approved quote #QT-001", timestamp: "2025-05-08T16:50:00Z" },
    { id: 7, action: "Team Joined", details: "Joined team 'Engineering'", timestamp: "2025-05-07T10:15:00Z" },
    { id: 8, action: "Account Created", details: "Account created successfully", timestamp: "2025-05-06T09:30:00Z" },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setAvatarPreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSavePersonalInfo = () => {
    toast({
      title: "Profile updated",
      description: "Your personal information has been updated successfully."
    });
  };

  const handleChangePassword = () => {
    if (formData.newPassword !== formData.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation password must match.",
        variant: "destructive"
      });
      return;
    }
    toast({
      title: "Password updated",
      description: "Your password has been changed successfully."
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-4 gap-4 h-auto p-1">
          <TabsTrigger value="personal" className="py-2">
            <User className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Personal Info</span>
            <span className="sm:hidden">Info</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="py-2">
            <Lock className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Password</span>
            <span className="sm:hidden">Password</span>
          </TabsTrigger>
          <TabsTrigger value="linked" className="py-2">
            <Globe className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Linked Accounts</span>
            <span className="sm:hidden">Accounts</span>
          </TabsTrigger>
          <TabsTrigger value="activity" className="py-2">
            <Clock className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Activity History</span>
            <span className="sm:hidden">Activity</span>
          </TabsTrigger>
        </TabsList>

        {/* Personal Information */}
        <TabsContent value="personal" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Update your personal information and how others see you on the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Avatar Section */}
              <div className="flex flex-col md:flex-row gap-6 items-start">
                <div className="flex flex-col items-center space-y-3">
                  <Avatar className="h-24 w-24">
                    {avatarPreview ? (
                      <AvatarImage src={avatarPreview} alt="Profile" />
                    ) : (
                      user?.fullName ? (
                        <>
                          <AvatarImage src={user?.avatarUrl || ''} alt={user?.fullName} />
                          <AvatarFallback>{getInitials(user.fullName)}</AvatarFallback>
                        </>
                      ) : (
                        <AvatarFallback>
                          <User className="h-12 w-12 text-muted-foreground" />
                        </AvatarFallback>
                      )
                    )}
                  </Avatar>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="relative">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarChange}
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                      <Upload className="h-4 w-4 mr-1" />
                      Upload
                    </Button>
                    {avatarPreview && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setAvatarPreview(null)}
                      >
                        <Trash className="h-4 w-4 mr-1" />
                        Remove
                      </Button>
                    )}
                  </div>
                </div>
                
                <div className="flex-1 space-y-4">
                  <div>
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      placeholder="Enter your full name"
                    />
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="Enter your email"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="Enter your phone number"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      placeholder="City, Country"
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  placeholder="Write a short bio about yourself"
                  rows={4}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Brief description for your profile. This will be visible to team members.
                </p>
              </div>
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <Button onClick={handleSavePersonalInfo}>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Password & Security */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password and manage account security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input
                  id="currentPassword"
                  name="currentPassword"
                  type="password"
                  value={formData.currentPassword}
                  onChange={handleInputChange}
                  placeholder="Enter your current password"
                />
              </div>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    placeholder="Enter new password"
                  />
                </div>
                <div>
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="Confirm new password"
                  />
                </div>
              </div>
              
              <div className="bg-muted/40 rounded-md p-4 space-y-3">
                <h4 className="text-sm font-medium">Password Requirements</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li className="flex items-center">
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                    Minimum 8 characters
                  </li>
                  <li className="flex items-center">
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                    At least one uppercase letter
                  </li>
                  <li className="flex items-center">
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                    At least one number
                  </li>
                  <li className="flex items-center">
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                    At least one special character
                  </li>
                </ul>
              </div>
            </CardContent>
            <CardFooter className="border-t px-6 py-4 flex justify-between">
              <Button variant="outline">Cancel</Button>
              <Button onClick={handleChangePassword}>Update Password</Button>
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Two-Factor Authentication</CardTitle>
              <CardDescription>
                Add an extra layer of security to your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <div className="space-y-1">
                  <h4 className="font-medium">Authenticator App</h4>
                  <p className="text-sm text-muted-foreground">
                    Use an authenticator app to generate verification codes
                  </p>
                </div>
                <Button variant="outline">Set Up</Button>
              </div>
              
              <Separator className="my-4" />
              
              <div className="flex justify-between items-center">
                <div className="space-y-1">
                  <h4 className="font-medium">SMS Authentication</h4>
                  <p className="text-sm text-muted-foreground">
                    Receive a code via SMS for verification
                  </p>
                </div>
                <Button variant="outline">Set Up</Button>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Active Sessions</CardTitle>
              <CardDescription>
                Manage your active sessions across devices
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-start gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Computer className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Current Session</h4>
                    <p className="text-xs text-muted-foreground">
                      Windows • Chrome • San Francisco, USA
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Started 10 minutes ago
                    </p>
                  </div>
                </div>
                <div className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-full">
                  Active
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-start gap-3">
                  <div className="bg-muted p-2 rounded-full">
                    <Smartphone className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <h4 className="font-medium">Mobile Session</h4>
                    <p className="text-xs text-muted-foreground">
                      iOS • Safari • Los Angeles, USA
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Last active 3 hours ago
                    </p>
                  </div>
                </div>
                <Button size="sm" variant="outline">Revoke</Button>
              </div>
            </CardContent>
            <CardFooter className="border-t px-6 py-4">
              <Button variant="outline" className="text-destructive">Sign Out All Devices</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Linked Accounts */}
        <TabsContent value="linked" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Linked Accounts</CardTitle>
              <CardDescription>
                Connect your account to other services for easier sign-in and additional features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="bg-black p-2 rounded-full">
                    <Github className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium">GitHub</h4>
                    <p className="text-sm text-muted-foreground">
                      Connect to access repositories and code integrations
                    </p>
                  </div>
                </div>
                <Button variant="outline">Connect</Button>
              </div>
              
              <Separator />
              
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="bg-[#1DA1F2] p-2 rounded-full">
                    <Twitter className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium">Twitter</h4>
                    <p className="text-sm text-muted-foreground">
                      Share project updates and announcements directly to Twitter
                    </p>
                  </div>
                </div>
                <Button variant="outline">Connect</Button>
              </div>
              
              <Separator />
              
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="bg-[#0A66C2] p-2 rounded-full">
                    <Linkedin className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium">LinkedIn</h4>
                    <p className="text-sm text-muted-foreground">
                      Share professional updates with your network
                    </p>
                  </div>
                </div>
                <Button variant="outline">Connect</Button>
              </div>
              
              <Separator />
              
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="bg-[#4285F4] p-2 rounded-full">
                    <svg className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l3.66-2.84z" />
                      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium">Google</h4>
                    <p className="text-sm text-muted-foreground">
                      Connect to use Google services like Calendar and Drive
                    </p>
                  </div>
                </div>
                <Button variant="outline">Connect</Button>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>OAuth Providers</CardTitle>
              <CardDescription>
                Configure sign-in methods for your account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between items-center py-2">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Email/Password</h4>
                    <p className="text-sm text-muted-foreground">
                      {user?.email}
                    </p>
                  </div>
                </div>
                <div className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-full">
                  Primary
                </div>
              </div>
              
              <Separator />
              
              <div className="flex justify-between items-center py-2">
                <div className="flex items-center gap-3">
                  <Github className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">GitHub</h4>
                    <p className="text-sm text-muted-foreground">
                      Not connected
                    </p>
                  </div>
                </div>
                <Button variant="outline" size="sm">Set Up</Button>
              </div>
              
              <Separator />
              
              <div className="flex justify-between items-center py-2">
                <div className="flex items-center gap-3">
                  <svg className="h-5 w-5 text-muted-foreground" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l3.66-2.84z" />
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                  </svg>
                  <div>
                    <h4 className="font-medium">Google</h4>
                    <p className="text-sm text-muted-foreground">
                      Not connected
                    </p>
                  </div>
                </div>
                <Button variant="outline" size="sm">Set Up</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity History */}
        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Activity History</CardTitle>
              <CardDescription>
                Track recent activities and events on your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {activityHistory.map((activity, index) => (
                  <div key={activity.id} className="relative">
                    {/* Timeline connector */}
                    {index < activityHistory.length - 1 && (
                      <div className="absolute left-2.5 top-8 bottom-0 w-0.5 bg-muted-foreground/20"></div>
                    )}
                    
                    <div className="flex gap-4">
                      <div className="flex-shrink-0 mt-1">
                        <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center">
                          <Clock className="h-3 w-3 text-primary-foreground" />
                        </div>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                          <div>
                            <h4 className="font-medium">{activity.action}</h4>
                            <p className="text-sm text-muted-foreground mt-0.5">{activity.details}</p>
                          </div>
                          <time className="text-xs text-muted-foreground mt-1 sm:mt-0">
                            {formatDate(activity.timestamp)}
                          </time>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="border-t px-6 py-4 flex justify-between">
              <div className="text-sm text-muted-foreground">
                Showing the last 8 activities
              </div>
              <Button variant="outline" size="sm">View All Activity</Button>
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Security Events</CardTitle>
              <CardDescription>
                Recent security-related activities on your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="bg-yellow-100 dark:bg-yellow-900 p-2 rounded-full">
                    <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-300" />
                  </div>
                  <div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
                      <h4 className="font-medium">New login detected</h4>
                      <time className="text-xs text-muted-foreground">May 10, 2025 at 08:10 AM</time>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      New login from Chrome on Windows in San Francisco, USA
                    </p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-full">
                    <Lock className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                  </div>
                  <div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
                      <h4 className="font-medium">Password changed</h4>
                      <time className="text-xs text-muted-foreground">May 1, 2025 at 02:15 PM</time>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Your account password was successfully updated
                    </p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex items-start gap-3">
                  <div className="bg-green-100 dark:bg-green-900 p-2 rounded-full">
                    <Mail className="h-4 w-4 text-green-600 dark:text-green-300" />
                  </div>
                  <div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
                      <h4 className="font-medium">Email verified</h4>
                      <time className="text-xs text-muted-foreground">April 25, 2025 at 10:30 AM</time>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Your email address has been successfully verified
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Adding missing components for TypeScript compatibility
const Computer = ({ className }: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <rect width="14" height="8" x="5" y="2" rx="2" />
      <rect width="20" height="8" x="2" y="14" rx="2" />
      <path d="M6 18h2" />
      <path d="M12 18h6" />
      <path d="M12 6h.01" />
      <path d="M12 10V6" />
    </svg>
  );
};

const Smartphone = ({ className }: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <rect width="14" height="20" x="5" y="2" rx="2" ry="2" />
      <path d="M12 18h.01" />
    </svg>
  );
};