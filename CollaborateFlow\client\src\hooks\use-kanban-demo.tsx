import { useState, useEffect } from "react";
import { IColumn, ITask } from "@/components/kanban/kanban-board";

export function useKanbanDemo(projectId: number) {
  const [columns, setColumns] = useState<IColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // This is a demo implementation that creates sample data for the Kanban board
    const demoColumns: IColumn[] = [
      {
        id: 1,
        name: "New Project",
        order: 0,
        projectId,
        tasks: [
          {
            id: 1,
            title: "Initial site assessment",
            description: "Visit the site and evaluate existing electrical infrastructure",
            status: "active",
            priority: "high",
            columnId: 1,
            projectId,
            order: 0
          },
          {
            id: 2,
            title: "Upload floor plans",
            description: "Obtain and upload floor plans for the building",
            status: "active",
            priority: "medium",
            columnId: 1,
            projectId,
            order: 1
          }
        ]
      },
      {
        id: 2,
        name: "Symbol Detection",
        order: 1,
        projectId,
        tasks: [
          {
            id: 3,
            title: "Run AI detection model",
            description: "Process floor plans using AI to detect electrical symbols",
            status: "active",
            priority: "medium",
            columnId: 2,
            projectId,
            order: 0
          }
        ]
      },
      {
        id: 3,
        name: "Symbol Review",
        order: 2,
        projectId,
        tasks: [
          {
            id: 4,
            title: "Verify light fixture symbols",
            description: "Check that all light fixtures are correctly identified",
            status: "active",
            priority: "medium",
            columnId: 3,
            projectId,
            order: 0
          },
          {
            id: 5,
            title: "Validate outlet placement",
            description: "Confirm electrical outlets are accurately placed",
            status: "active",
            priority: "high",
            columnId: 3,
            projectId,
            order: 1
          }
        ]
      },
      {
        id: 4,
        name: "Material Estimation",
        order: 3,
        projectId,
        tasks: [
          {
            id: 6,
            title: "Calculate wire requirements",
            description: "Determine total length and gauge of wiring needed",
            status: "active",
            priority: "medium",
            columnId: 4,
            projectId,
            order: 0
          },
          {
            id: 7,
            title: "Select fixture models",
            description: "Choose specific models for each light fixture type",
            status: "active",
            priority: "low",
            columnId: 4,
            projectId,
            order: 1
          }
        ]
      },
      {
        id: 5,
        name: "Labor Estimation",
        order: 4,
        projectId,
        tasks: [
          {
            id: 8,
            title: "Estimate installation hours",
            description: "Calculate labor hours for fixture installation",
            status: "active",
            priority: "medium",
            columnId: 5,
            projectId,
            order: 0
          }
        ]
      },
      {
        id: 6,
        name: "Quote Generation",
        order: 5,
        projectId,
        tasks: []
      },
      {
        id: 7,
        name: "Client Approval",
        order: 6,
        projectId,
        tasks: []
      }
    ];

    // Simulate loading time
    setTimeout(() => {
      setColumns(demoColumns);
      setIsLoading(false);
    }, 1000);
  }, [projectId]);

  // Simulate task updates
  const updateTask = (taskId: number, updates: Partial<ITask>) => {
    setColumns(prevColumns => {
      const newColumns = [...prevColumns];
      
      // Find the task in all columns
      for (const column of newColumns) {
        const taskIndex = column.tasks.findIndex(t => t.id === taskId);
        
        if (taskIndex !== -1) {
          // If task is being moved to a different column
          if (updates.columnId && updates.columnId !== column.id) {
            // Remove from current column
            const [task] = column.tasks.splice(taskIndex, 1);
            
            // Find the destination column
            const destColumn = newColumns.find(c => c.id === updates.columnId);
            if (destColumn) {
              // Add to the destination column
              destColumn.tasks.splice(updates.order || 0, 0, {
                ...task,
                ...updates
              });
              
              // Update all task orders in the destination column
              destColumn.tasks.forEach((t, i) => {
                t.order = i;
              });
            }
          } else {
            // Just update the task in place
            column.tasks[taskIndex] = {
              ...column.tasks[taskIndex],
              ...updates
            };
            
            // If order was changed, reorder tasks
            if (updates.order !== undefined) {
              column.tasks.sort((a, b) => a.order - b.order);
            }
          }
          
          break;
        }
      }
      
      return newColumns;
    });
    
    return true;
  };

  return {
    columns,
    isLoading,
    updateTask
  };
}