import { useState, useEffect } from 'react';
import { isMobile as detectIsMobile } from 'react-device-detect';

export function useMobileDetection() {
  const [isMobile, setIsMobile] = useState(detectIsMobile);
  
  useEffect(() => {
    // Function to update the state based on window size
    const checkForMobile = () => {
      setIsMobile(detectIsMobile || window.innerWidth < 768);
    };
    
    // Run once on mount
    checkForMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkForMobile);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', checkForMobile);
    };
  }, []);
  
  return { isMobile };
}