import { createContext, ReactNode, useContext, useEffect, useState } from "react";
import { useLocation } from "wouter";
import { AuthError, Session, User } from "@supabase/supabase-js";
import { supabase } from "@/lib/supabase-auth";
import { syncSupabaseWithExpressSession } from "@/lib/auth-sync";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";

// Types for our authentication context
type User = {
  id: string;
  email?: string;
  profile_image_url?: string;
  full_name?: string;
};

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, fullName: string) => Promise<any>;
  signOut: () => Promise<void>;
};

// Create the auth context
export const SupabaseAuthContext = createContext<AuthContextType | null>(null);

// Auth provider component
export function SupabaseAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Set up auth state listener
  useEffect(() => {
    // Check for existing session
    const checkSession = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        if (data.session) {
          const userData = {
            id: data.session.user.id,
            email: data.session.user.email,
            profile_image_url: data.session.user.user_metadata.avatar_url,
            full_name: data.session.user.user_metadata.full_name
          };
          setUser(userData);
          queryClient.setQueryData(["/api/user"], userData);
        }
      } catch (error) {
        console.error("Error checking session:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkSession();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session) {
          const userData = {
            id: session.user.id,
            email: session.user.email,
            profile_image_url: session.user.user_metadata.avatar_url,
            full_name: session.user.user_metadata.full_name
          };
          setUser(userData);
          queryClient.setQueryData(["/api/user"], userData);
        } else {
          setUser(null);
          queryClient.setQueryData(["/api/user"], null);
        }
        setIsLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          title: "Login failed",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }

      // Sync Supabase auth with Express session
      try {
        await syncSupabaseWithExpressSession();
        console.log('Authentication synchronized with Express session');
      } catch (syncError) {
        console.error('Failed to sync with Express session:', syncError);
        // Continue anyway since Supabase auth succeeded
      }

      toast({
        title: "Welcome back!",
        description: "You've successfully logged in.",
      });

      navigate("/");
      return data;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign up function
  const signUp = async (email: string, password: string, fullName: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        toast({
          title: "Registration failed",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }

      // Sync Supabase auth with Express session
      try {
        await syncSupabaseWithExpressSession();
        console.log('Authentication synchronized with Express session after signup');
      } catch (syncError) {
        console.error('Failed to sync with Express session after signup:', syncError);
        // Continue anyway since the Supabase signup succeeded
      }

      toast({
        title: "Account created",
        description: "Please check your email for confirmation instructions.",
      });
      
      return data;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        toast({
          title: "Sign out failed",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      
      toast({
        title: "Signed out",
        description: "You've been successfully signed out.",
      });
      
      navigate("/auth");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SupabaseAuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        signIn,
        signUp,
        signOut,
      }}
    >
      {children}
    </SupabaseAuthContext.Provider>
  );
}

// Hook for using auth
export function useSupabaseAuth() {
  const context = useContext(SupabaseAuthContext);
  if (!context) {
    throw new Error("useSupabaseAuth must be used within a SupabaseAuthProvider");
  }
  return context;
}