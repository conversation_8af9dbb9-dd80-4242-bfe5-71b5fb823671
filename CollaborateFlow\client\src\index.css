@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 213.4 89.8% 50.4%;  /* Google Blue #1a73e8 */
    --primary-foreground: 210 40% 98%;
    --secondary: 142.1 76.2% 36.3%; /* Google Green #34a853 */
    --secondary-foreground: 355.7 100% 97.3%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 44.8 92.4% 49.8%; /* Google Yellow #fbbc04 */
    --accent-foreground: 355.7 100% 97.3%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --radius: 0.5rem;

    /* Chart colors */
    --chart-1: 213.4 89.8% 50.4%;
    --chart-2: 142.1 76.2% 36.3%;
    --chart-3: 44.8 92.4% 49.8%;
    --chart-4: 0 84.2% 60.2%;
    --chart-5: 262 83% 57%;

    /* Sidebar colors */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 224 71.4% 4.1%;
    --sidebar-primary: 213.4 89.8% 50.4%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 224 71.4% 4.1%;
  }

  .dark {
    --background: 220 10% 12%; /* Google AI Dark #1f1f23 */
    --foreground: 0 0% 95%;
    --card: 220 10% 16%; /* Slightly lighter than background */
    --card-foreground: 0 0% 95%;
    --popover: 220 10% 16%;
    --popover-foreground: 0 0% 95%;
    --primary: 213.4 89.8% 50.4%;
    --primary-foreground: 0 0% 98%;
    --secondary: 142.1 76.2% 36.3%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 44.8 92.4% 49.8%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    /* Chart colors */
    --chart-1: 213.4 89.8% 50.4%;
    --chart-2: 142.1 76.2% 36.3%;
    --chart-3: 44.8 92.4% 49.8%;
    --chart-4: 0 84.2% 60.2%;
    --chart-5: 262 83% 57%;

    /* Sidebar colors - Google AI Studio inspired */
    --sidebar-background: 220 10% 10%; /* Darker than main background */
    --sidebar-foreground: 0 0% 90%;
    --sidebar-primary: 213.4 89.8% 50.4%;
    --sidebar-primary-foreground: 0 0% 95%;
    --sidebar-accent: 220 10% 15%;
    --sidebar-accent-foreground: 0 0% 95%; 
    --sidebar-border: 220 10% 20%;
    --sidebar-ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium;
  }

  .kanban-column {
    @apply min-w-[280px];
  }

  .task-card {
    @apply transition-all duration-200 ease-in-out;
  }

  .task-card:hover {
    @apply shadow-md -translate-y-0.5;
  }

  .sidebar-item {
    @apply flex items-center px-6 py-3 text-sm font-medium rounded-md mx-2 my-0.5 transition-colors;
  }

  .sidebar-item:hover {
    @apply bg-gray-100 bg-opacity-80 dark:bg-gray-700 dark:bg-opacity-30;
  }

  .sidebar-item.active {
    @apply bg-blue-50 text-primary dark:bg-blue-900 dark:bg-opacity-50 dark:text-blue-400;
  }

  /* Import Google Sans font */
  @import url('https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&display=swap');
}
