import { createClient } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';

// Create Supabase client
// Try to use runtime config first, fall back to import.meta.env
// This helps with containerized environments where env vars might not be available at build time
const getRuntimeConfig = () => {
  if (typeof window !== 'undefined' && window.RUNTIME_CONFIG) {
    return {
      url: window.RUNTIME_CONFIG.SUPABASE_URL,
      key: window.RUNTIME_CONFIG.SUPABASE_KEY,
    };
  }
  return {
    url: import.meta.env.VITE_SUPABASE_URL as string,
    key: import.meta.env.VITE_SUPABASE_KEY as string,
  };
};

const { url: supabaseUrl, key: supabaseKey } = getRuntimeConfig();

// Throw meaningful errors if configs are missing
if (!supabaseUrl) {
  console.error('Supabase URL is missing. Check environment variables or runtime config.');
}

if (!supabaseKey) {
  console.error('Supabase Key is missing. Check environment variables or runtime config.');
}

export const supabase = createClient(supabaseUrl, supabaseKey);

// Define user type
export type User = {
  id: string;
  email?: string;
  avatar_url?: string;
  full_name?: string;
};

// Auth hook for React components
export function useSupabaseAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session) {
        setUser({
          id: session.user.id,
          email: session.user.email,
          avatar_url: session.user.user_metadata.avatar_url,
          full_name: session.user.user_metadata.full_name,
        });
      }
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (session) {
          setUser({
            id: session.user.id,
            email: session.user.email,
            avatar_url: session.user.user_metadata.avatar_url,
            full_name: session.user.user_metadata.full_name,
          });
        } else {
          setUser(null);
        }
        setLoading(false);
      }
    );

    // Cleanup
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Sign in with email/password
  const signIn = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err: any) {
      setError(err.message || 'Error signing in');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email/password
  const signUp = async (email: string, password: string, fullName: string) => {
    setLoading(true);
    setError(null);
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err: any) {
      setError(err.message || 'Error signing up');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    setLoading(true);
    setError(null);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }
    } catch (err: any) {
      setError(err.message || 'Error signing out');
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    isAuthenticated: !!user,
  };
}