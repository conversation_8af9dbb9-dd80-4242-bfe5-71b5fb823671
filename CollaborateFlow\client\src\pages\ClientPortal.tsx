/**
 * CLIENT PORTAL PAGE
 * Complete client portal interface with authentication and quote management
 */

import { useState, useEffect } from "react";
import { useLocation, useParams } from "wouter";
import { 
  User, 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  Download,
  Eye,
  Settings,
  LogOut,
  Bell,
  Search,
  Filter
} from "lucide-react";
import { AICard } from "@/components/ai-card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { QuoteApproval } from "@/components/QuoteApproval";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface ClientPortalProps {
  clientId?: string;
  accessToken?: string;
}

interface Quote {
  id: string;
  number: string;
  title: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected';
  totalCost: number;
  currency: string;
  createdAt: string;
  validUntil: string;
  description?: string;
  materials?: any[];
  laborItems?: any[];
  feedback?: any[];
}

interface ClientProfile {
  id: string;
  name: string;
  email: string;
  company?: string;
  phone?: string;
  address?: string;
  avatar?: string;
}

interface ChangeRequest {
  id: string;
  quoteId: string;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  response?: string;
}

export function ClientPortal({ clientId, accessToken }: ClientPortalProps) {
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const params = useParams();
  
  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [clientProfile, setClientProfile] = useState<ClientProfile | null>(null);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [changeRequests, setChangeRequests] = useState<ChangeRequest[]>([]);
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  const [activeTab, setActiveTab] = useState("quotes");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Authentication and data loading
  useEffect(() => {
    const initializePortal = async () => {
      setIsLoading(true);
      try {
        // Check authentication
        const authResult = await authenticateClient(clientId, accessToken);
        if (!authResult.success) {
          toast({
            title: "Authentication Failed",
            description: "Please check your access link and try again.",
            variant: "destructive"
          });
          navigate("/auth");
          return;
        }

        setIsAuthenticated(true);
        setClientProfile(authResult.profile);

        // Load client data
        await Promise.all([
          loadQuotes(authResult.profile.id),
          loadChangeRequests(authResult.profile.id)
        ]);

      } catch (error) {
        console.error("Portal initialization error:", error);
        toast({
          title: "Error",
          description: "Failed to load client portal. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    initializePortal();
  }, [clientId, accessToken]);

  // Authentication function
  const authenticateClient = async (clientId?: string, token?: string) => {
    try {
      // For now, use mock authentication
      // In production, this would validate the client ID and token
      const mockProfile: ClientProfile = {
        id: clientId || 'client_001',
        name: 'John Smith',
        email: '<EMAIL>',
        company: 'Smith Construction',
        phone: '******-0123',
        address: '123 Main St, Anytown, ST 12345',
        avatar: undefined
      };

      return {
        success: true,
        profile: mockProfile
      };
    } catch (error) {
      return { success: false, profile: null };
    }
  };

  // Load quotes for client
  const loadQuotes = async (clientId: string) => {
    try {
      // Mock quotes data - in production, fetch from API
      const mockQuotes: Quote[] = [
        {
          id: 'quote_001',
          number: 'Q-2024-001',
          title: 'Office Building Electrical Installation',
          status: 'pending',
          totalCost: 25000.00,
          currency: 'USD',
          createdAt: '2024-01-15T10:00:00Z',
          validUntil: '2024-02-15T23:59:59Z',
          description: 'Complete electrical installation for new office building including outlets, lighting, and panel upgrades.',
          materials: [],
          laborItems: [],
          feedback: []
        },
        {
          id: 'quote_002',
          number: 'Q-2024-002',
          title: 'Warehouse Lighting Upgrade',
          status: 'approved',
          totalCost: 15000.00,
          currency: 'USD',
          createdAt: '2024-01-10T14:30:00Z',
          validUntil: '2024-02-10T23:59:59Z',
          description: 'LED lighting upgrade for warehouse facility with motion sensors and smart controls.',
          materials: [],
          laborItems: [],
          feedback: []
        },
        {
          id: 'quote_003',
          number: 'Q-2024-003',
          title: 'Emergency Generator Installation',
          status: 'draft',
          totalCost: 45000.00,
          currency: 'USD',
          createdAt: '2024-01-20T09:15:00Z',
          validUntil: '2024-02-20T23:59:59Z',
          description: 'Installation of backup generator system with automatic transfer switch.',
          materials: [],
          laborItems: [],
          feedback: []
        }
      ];

      setQuotes(mockQuotes);
    } catch (error) {
      console.error("Error loading quotes:", error);
      toast({
        title: "Error",
        description: "Failed to load quotes.",
        variant: "destructive"
      });
    }
  };

  // Load change requests for client
  const loadChangeRequests = async (clientId: string) => {
    try {
      // Mock change requests data
      const mockChangeRequests: ChangeRequest[] = [
        {
          id: 'cr_001',
          quoteId: 'quote_001',
          title: 'Add Additional Outlets',
          description: 'Please add 5 more outlets in the conference room area.',
          status: 'pending',
          createdAt: '2024-01-16T11:30:00Z'
        },
        {
          id: 'cr_002',
          quoteId: 'quote_002',
          title: 'Change Light Fixture Type',
          description: 'Can we use a different style of LED fixtures? Something more modern.',
          status: 'approved',
          createdAt: '2024-01-12T16:45:00Z',
          response: 'We can accommodate this change. Updated quote will be sent shortly.'
        }
      ];

      setChangeRequests(mockChangeRequests);
    } catch (error) {
      console.error("Error loading change requests:", error);
    }
  };

  // Handle quote approval
  const handleQuoteApproval = async (quoteId: string, approved: boolean, signature?: string) => {
    try {
      setIsLoading(true);

      // Update quote status
      setQuotes(prev => prev.map(quote => 
        quote.id === quoteId 
          ? { ...quote, status: approved ? 'approved' : 'rejected' }
          : quote
      ));

      toast({
        title: approved ? "Quote Approved" : "Quote Rejected",
        description: approved 
          ? "Thank you for approving this quote. We'll be in touch soon to get started."
          : "The quote has been rejected. We'll review your feedback and get back to you.",
      });

      // In production, send to API
      // await fetch(`/api/client/quotes/${quoteId}/approve`, { ... });

    } catch (error) {
      console.error("Error processing quote approval:", error);
      toast({
        title: "Error",
        description: "Failed to process quote approval. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle change request submission
  const handleChangeRequest = async (quoteId: string, title: string, description: string) => {
    try {
      const newChangeRequest: ChangeRequest = {
        id: `cr_${Date.now()}`,
        quoteId,
        title,
        description,
        status: 'pending',
        createdAt: new Date().toISOString()
      };

      setChangeRequests(prev => [newChangeRequest, ...prev]);

      toast({
        title: "Change Request Submitted",
        description: "Your change request has been submitted and will be reviewed shortly."
      });

      // In production, send to API
      // await fetch(`/api/client/change-requests`, { ... });

    } catch (error) {
      console.error("Error submitting change request:", error);
      toast({
        title: "Error",
        description: "Failed to submit change request. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Filter quotes based on search and status
  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.number.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || quote.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Get status badge color
  const getStatusBadge = (status: string) => {
    const colors = {
      draft: "bg-gray-100 text-gray-800",
      pending: "bg-yellow-100 text-yellow-800",
      approved: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800"
    };
    return colors[status as keyof typeof colors] || colors.draft;
  };

  // Handle logout
  const handleLogout = () => {
    setIsAuthenticated(false);
    setClientProfile(null);
    navigate("/auth");
    toast({
      title: "Logged Out",
      description: "You have been successfully logged out."
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading client portal...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !clientProfile) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <AICard className="w-full max-w-md">
          <div className="p-6 text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">
              Unable to authenticate. Please check your access link.
            </p>
            <Button onClick={() => navigate("/auth")}>
              Go to Login
            </Button>
          </div>
        </AICard>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Client Portal
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <Bell className="h-4 w-4" />
              </Button>
              
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {clientProfile.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {clientProfile.company}
                  </p>
                </div>
              </div>
              
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="quotes" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Quotes
            </TabsTrigger>
            <TabsTrigger value="requests" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Change Requests
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Documents
            </TabsTrigger>
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Profile
            </TabsTrigger>
          </TabsList>

          {/* Quotes Tab */}
          <TabsContent value="quotes" className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Your Quotes</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Review and manage your electrical project quotes
                </p>
              </div>
              
              <div className="flex gap-2 w-full sm:w-auto">
                <div className="relative flex-1 sm:w-64">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search quotes..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>

            <div className="grid gap-6">
              {filteredQuotes.map((quote) => (
                <AICard key={quote.id} className="hover:shadow-md transition-shadow">
                  <div className="p-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {quote.title}
                          </h3>
                          <Badge className={getStatusBadge(quote.status)}>
                            {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          Quote #{quote.number}
                        </p>
                        
                        <p className="text-gray-700 dark:text-gray-300 mb-4">
                          {quote.description}
                        </p>
                        
                        <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            Created {new Date(quote.createdAt).toLocaleDateString()}
                          </span>
                          <span>
                            Valid until {new Date(quote.validUntil).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          ${quote.totalCost.toLocaleString()}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {quote.currency}
                        </p>
                        
                        <div className="flex gap-2 mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedQuote(quote)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                          
                          {quote.status === 'pending' && (
                            <Button
                              size="sm"
                              onClick={() => setSelectedQuote(quote)}
                            >
                              Review & Approve
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </AICard>
              ))}
              
              {filteredQuotes.length === 0 && (
                <AICard>
                  <div className="p-12 text-center">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      No quotes found
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {searchQuery || statusFilter !== "all" 
                        ? "Try adjusting your search or filter criteria."
                        : "You don't have any quotes yet. Your contractor will send quotes here when available."
                      }
                    </p>
                  </div>
                </AICard>
              )}
            </div>
          </TabsContent>

          {/* Change Requests Tab */}
          <TabsContent value="requests" className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Change Requests</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Submit and track requests for changes to your quotes
              </p>
            </div>

            <div className="grid gap-4">
              {changeRequests.map((request) => (
                <AICard key={request.id}>
                  <div className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {request.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          Quote #{quotes.find(q => q.id === request.quoteId)?.number}
                        </p>
                        <p className="text-gray-700 dark:text-gray-300 mt-2">
                          {request.description}
                        </p>
                        {request.response && (
                          <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                            <p className="text-sm text-blue-800 dark:text-blue-200">
                              <strong>Response:</strong> {request.response}
                            </p>
                          </div>
                        )}
                      </div>
                      <Badge className={getStatusBadge(request.status)}>
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </AICard>
              ))}
            </div>
          </TabsContent>

          {/* Documents Tab */}
          <TabsContent value="documents" className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Documents</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Download contracts, invoices, and project documents
              </p>
            </div>

            <AICard>
              <div className="p-12 text-center">
                <Download className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No documents available
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Documents will appear here once your projects begin.
                </p>
              </div>
            </AICard>
          </TabsContent>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Profile</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Manage your account information and preferences
              </p>
            </div>

            <AICard>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Full Name
                    </label>
                    <p className="mt-1 text-gray-900 dark:text-white">{clientProfile.name}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Email
                    </label>
                    <p className="mt-1 text-gray-900 dark:text-white">{clientProfile.email}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Company
                    </label>
                    <p className="mt-1 text-gray-900 dark:text-white">{clientProfile.company}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Phone
                    </label>
                    <p className="mt-1 text-gray-900 dark:text-white">{clientProfile.phone}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Address
                    </label>
                    <p className="mt-1 text-gray-900 dark:text-white">{clientProfile.address}</p>
                  </div>
                </div>
              </div>
            </AICard>
          </TabsContent>
        </Tabs>
      </main>

      {/* Quote Details Modal */}
      {selectedQuote && (
        <QuoteApproval
          quote={selectedQuote}
          onApprove={(signature) => handleQuoteApproval(selectedQuote.id, true, signature)}
          onReject={() => handleQuoteApproval(selectedQuote.id, false)}
          onChangeRequest={(title, description) => handleChangeRequest(selectedQuote.id, title, description)}
          onClose={() => setSelectedQuote(null)}
        />
      )}
    </div>
  );
}

export default ClientPortal;
