import { useState } from "react";
import { Layout } from "@/components/layout";
import { AICard } from "@/components/ai-card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  <PERSON><PERSON>hart, 
  PieChart, 
  Activity, 
  Users, 
  DollarSign,
  Calendar,
  Download,
  RefreshCw,
  Filter,
  FileBarChart
} from "lucide-react";
import { ProjectMetricsPanel } from "@/components/analytics/project-metrics-panel";
import { FinancialReportsPanel } from "@/components/analytics/financial-reports-panel";
import { EstimationAccuracyPanel } from "@/components/analytics/estimation-accuracy-panel";
import { UserActivityPanel } from "@/components/analytics/user-activity-panel";
import { ClientEngagementPanel } from "@/components/analytics/client-engagement-panel";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem
} from "@/components/ui/dropdown-menu";

export default function AnalyticsDashboardPage() {
  const [activeTab, setActiveTab] = useState("project-metrics");
  const [timeRange, setTimeRange] = useState("30days");
  const [isLoading, setIsLoading] = useState(false);
  
  const refreshData = () => {
    setIsLoading(true);
    
    // Simulate data fetching delay
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };
  
  const getTimeRangeLabel = () => {
    switch (timeRange) {
      case "7days": return "Last 7 Days";
      case "30days": return "Last 30 Days";
      case "90days": return "Last 90 Days";
      case "year": return "Last 12 Months";
      case "ytd": return "Year to Date";
      case "all": return "All Time";
      default: return "Last 30 Days";
    }
  };
  
  const exportData = (format: string) => {
    // In a real application, this would trigger a data export
    console.log(`Exporting ${activeTab} data in ${format} format`);
  };
  
  return (
    <Layout>
      <div className="container max-w-screen-xl mx-auto py-6 px-4 md:px-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-normal mb-1">Analytics Dashboard</h1>
            <p className="text-muted-foreground">
              Analyze performance metrics, financial data, and user activity
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {/* Time Range Filter */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{getTimeRangeLabel()}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Time Range</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup value={timeRange} onValueChange={setTimeRange}>
                  <DropdownMenuRadioItem value="7days">Last 7 Days</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="30days">Last 30 Days</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="90days">Last 90 Days</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="year">Last 12 Months</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="ytd">Year to Date</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="all">All Time</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* More Filters */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Filter className="h-4 w-4" />
                  <span>Filters</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Filter By</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Project Type</DropdownMenuItem>
                <DropdownMenuItem>Client</DropdownMenuItem>
                <DropdownMenuItem>Team Member</DropdownMenuItem>
                <DropdownMenuItem>Status</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Button variant="ghost" size="sm" className="w-full justify-start p-0 h-auto font-normal">
                    Advanced Filters
                  </Button>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* Export Options */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Export Format</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => exportData("csv")}>
                  <FileBarChart className="h-4 w-4 mr-2" />
                  CSV File
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportData("excel")}>
                  <FileBarChart className="h-4 w-4 mr-2" />
                  Excel File
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportData("pdf")}>
                  <FileBarChart className="h-4 w-4 mr-2" />
                  PDF Report
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* Refresh Button */}
            <Button
              variant="outline"
              size="icon"
              onClick={refreshData}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            </Button>
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="w-full bg-muted/50 p-1 h-auto flex flex-wrap">
            <TabsTrigger value="project-metrics" className="data-[state=active]:bg-background py-2 flex-1">
              <BarChart className="h-4 w-4 mr-2" /> 
              <span className="hidden sm:inline">Project Metrics</span>
              <span className="sm:hidden">Projects</span>
            </TabsTrigger>
            <TabsTrigger value="financial-reports" className="data-[state=active]:bg-background py-2 flex-1">
              <DollarSign className="h-4 w-4 mr-2" /> 
              <span className="hidden sm:inline">Financial Reports</span>
              <span className="sm:hidden">Financial</span>
            </TabsTrigger>
            <TabsTrigger value="estimation-accuracy" className="data-[state=active]:bg-background py-2 flex-1">
              <Activity className="h-4 w-4 mr-2" /> 
              <span className="hidden sm:inline">Estimation Accuracy</span>
              <span className="sm:hidden">Accuracy</span>
            </TabsTrigger>
            <TabsTrigger value="user-activity" className="data-[state=active]:bg-background py-2 flex-1">
              <Users className="h-4 w-4 mr-2" /> 
              <span className="hidden sm:inline">User Activity</span>
              <span className="sm:hidden">Users</span>
            </TabsTrigger>
            <TabsTrigger value="client-engagement" className="data-[state=active]:bg-background py-2 flex-1">
              <PieChart className="h-4 w-4 mr-2" /> 
              <span className="hidden sm:inline">Client Engagement</span>
              <span className="sm:hidden">Client</span>
            </TabsTrigger>
          </TabsList>
          
          <AICard className="p-0">
            <TabsContent value="project-metrics" className="m-0">
              <ProjectMetricsPanel timeRange={timeRange} isLoading={isLoading} />
            </TabsContent>
            
            <TabsContent value="financial-reports" className="m-0">
              <FinancialReportsPanel timeRange={timeRange} isLoading={isLoading} />
            </TabsContent>
            
            <TabsContent value="estimation-accuracy" className="m-0">
              <EstimationAccuracyPanel timeRange={timeRange} isLoading={isLoading} />
            </TabsContent>
            
            <TabsContent value="user-activity" className="m-0">
              <UserActivityPanel timeRange={timeRange} isLoading={isLoading} />
            </TabsContent>
            
            <TabsContent value="client-engagement" className="m-0">
              <ClientEngagementPanel timeRange={timeRange} isLoading={isLoading} />
            </TabsContent>
          </AICard>
        </Tabs>
      </div>
    </Layout>
  );
}