import { useState, useEffect } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { Eye, Mail, Clock, CheckCircle, XCircle, ArrowLeft } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ClientPortal } from "@/components/client-portal";
import { useToast } from "@/hooks/use-toast";

interface ClientApprovalPageProps {
  quoteId?: string;
  token?: string;
}

export default function ClientApprovalPage({ quoteId, token }: ClientApprovalPageProps) {
  // Get the params either from props or from URL in case component is accessed directly
  const params = useParams();
  const quoteIdValue = quoteId || params.quoteId;
  const tokenValue = token || params.token;
  const [, navigate] = useLocation();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(true);
  const [quoteDetails, setQuoteDetails] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch quote using the API
  useEffect(() => {
    setIsLoading(true);
    
    const fetchQuote = async () => {
      try {
        if (!quoteIdValue || !tokenValue) {
          setError("Invalid quote link. Please check your email for the correct link.");
          setIsLoading(false);
          return;
        }
        
        const response = await fetch(`/api/quotes/${quoteIdValue}/view/${tokenValue}`);
        
        if (!response.ok) {
          throw new Error("Failed to load quote. The link may be expired or invalid.");
        }
        
        const data = await response.json();
        setQuoteDetails(data);
      } catch (error) {
        console.error("Error fetching quote:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchQuote();
  }, [quoteIdValue, tokenValue]);
  
  // Handle approval
  const handleApproval = () => {
    toast({
      title: "Quote Approved",
      description: "Thank you for approving this quote. We'll be in touch soon to get started."
    });
    
    // In a real app, we would update the quote status via API
    setQuoteDetails((prev: any) => ({
      ...prev,
      status: "approved"
    }));
  };
  
  // Handle rejection
  const handleRejection = () => {
    toast({
      title: "Quote Rejected",
      description: "The quote has been rejected. We'll review your feedback and get back to you."
    });
    
    // In a real app, we would update the quote status via API
    setQuoteDetails((prev: any) => ({
      ...prev,
      status: "rejected"
    }));
  };
  
  // Handle feedback
  const handleFeedback = (feedback: any) => {
    // In a real app, we would send this feedback to the server
    console.log("Feedback received:", feedback);
    
    // Update status to viewed if it was just sent
    if (quoteDetails?.status === "sent") {
      setQuoteDetails((prev: any) => ({
        ...prev,
        status: "viewed"
      }));
    }
  };
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg font-medium">Loading quote...</p>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <AICard>
          <div className="p-8 text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">Error Loading Quote</h2>
            <p className="mb-6 text-muted-foreground">{error}</p>
            <Button onClick={() => navigate("/")}>
              Return to Homepage
            </Button>
          </div>
        </AICard>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-muted/20 py-8">
      <div className="container">
        <div className="mb-6">
          <Button variant="ghost" onClick={() => navigate("/")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Return to Homepage
          </Button>
        </div>
        
        <AICard>
          <div className="p-4 sm:p-6 border-b">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-xl font-bold">Quote Review Portal</h1>
                <p className="text-sm text-muted-foreground">
                  Review, provide feedback, and approve or reject this quote
                </p>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                  <Eye className="h-3 w-3 mr-1" />
                  Secure Client Portal
                </Badge>
                
                <Badge variant="outline">
                  Quote #{quoteDetails?.number}
                </Badge>
              </div>
            </div>
          </div>
          
          <ClientPortal 
            quoteId={quoteIdValue || ""}
            quoteToken={tokenValue}
            onApprove={handleApproval}
            onReject={handleRejection}
            onFeedback={handleFeedback}
          />
        </AICard>
        
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>
            If you have any questions about this quote, please contact us directly at <span className="font-medium"><EMAIL></span> or call <span className="font-medium">(555) 123-4567</span>.
          </p>
        </div>
      </div>
    </div>
  );
}