import { useState } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Upload, ChevronLeft } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { FloorPlanViewer } from "@/components/floor-plan-viewer";
import { FloorPlanVersions, type FloorPlanVersion } from "@/components/floor-plan-versions";
import { FloorPlanAnnotation, type AnnotationObject, type AnnotationLayer } from "@/components/floor-plan-annotation";
import { FloorPlanUpload } from "@/components/floor-plan-upload";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

// Mock data for development
const mockVersions = [
  {
    id: "1",
    name: "Initial Floor Plan",
    description: "First version of the floor plan",
    fileUrl: "https://images.unsplash.com/photo-1617650728444-1807011cfe45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    thumbnailUrl: "https://images.unsplash.com/photo-1617650728444-1807011cfe45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    createdBy: "John Doe",
    createdAt: new Date(2023, 5, 15),
    isLatest: false,
    changes: ["Initial floor plan upload"]
  },
  {
    id: "2",
    name: "Revised Floor Plan",
    description: "Updated dimensions for kitchen area",
    fileUrl: "https://images.unsplash.com/photo-1617650728444-1807011cfe45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    thumbnailUrl: "https://images.unsplash.com/photo-1617650728444-1807011cfe45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    createdBy: "Jane Smith",
    createdAt: new Date(2023, 6, 2),
    isLatest: true,
    changes: ["Updated kitchen dimensions", "Added outdoor patio"]
  }
];

const mockAnnotations = [
  {
    id: "anno1",
    type: "rectangle" as const,
    layerId: "default",
    x: 100,
    y: 100,
    width: 200,
    height: 150,
    color: "#1a73e8",
    strokeWidth: 2,
    filled: true,
    selected: false,
    createdBy: "John Doe",
    createdAt: new Date()
  },
  {
    id: "anno2",
    type: "text" as const,
    layerId: "default",
    x: 200,
    y: 180,
    text: "Kitchen",
    color: "#34a853",
    strokeWidth: 2,
    filled: false,
    selected: false,
    createdBy: "Jane Smith",
    createdAt: new Date()
  }
];

const mockLayers = [
  {
    id: "default",
    name: "Default Layer",
    visible: true,
    locked: false,
    zIndex: 0
  },
  {
    id: "electrical",
    name: "Electrical",
    visible: true,
    locked: false,
    zIndex: 1
  }
];

const mockCollaborators = [
  { id: "user1", name: "John Doe", color: "#1a73e8" },
  { id: "user2", name: "Jane Smith", color: "#34a853" },
  { id: "user3", name: "Mike Johnson", color: "#ea4335" }
];

export default function FloorPlanPage() {
  const [, setLocation] = useLocation();
  const { projectId } = useParams();
  const { toast } = useToast();
  
  // Local state for UI
  const [activeTab, setActiveTab] = useState("viewer");
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<FloorPlanVersion | null>(
    (mockVersions.find(v => v.isLatest) as FloorPlanVersion) || null
  );
  
  // Fetch project details
  const { data: project, isLoading: isLoadingProject } = useQuery({
    queryKey: ['/api/projects', parseInt(projectId || "0")],
    queryFn: async () => {
      const response = await fetch(`/api/projects/${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch project');
      return await response.json();
    },
    enabled: !!projectId
  });
  
  // Fetch floor plan versions (mock data for now)
  const { data: versions, isLoading: isLoadingVersions } = useQuery<FloorPlanVersion[]>({
    queryKey: ['/api/projects', parseInt(projectId || "0"), 'floor-plans'],
    queryFn: () => Promise.resolve(mockVersions as FloorPlanVersion[]),
    enabled: !!projectId
  });
  
  // Save annotations mutation
  const saveAnnotationsMutation = useMutation({
    mutationFn: async ({ annotations, layers }: { 
      annotations: AnnotationObject[], 
      layers: AnnotationLayer[] 
    }) => {
      // This would be an API call in a real application
      console.log('Saving annotations:', annotations, layers);
      return { success: true };
    },
    onSuccess: () => {
      toast({
        title: 'Annotations saved',
        description: 'Your annotations have been saved successfully.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to save annotations',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive'
      });
    }
  });
  
  // Handle floor plan version selection
  const handleSelectVersion = (version: FloorPlanVersion) => {
    setSelectedVersion(version);
  };
  
  // Handle new version upload
  const handleUploadNewVersion = () => {
    setUploadDialogOpen(true);
  };
  
  // Handle comparison between versions
  const handleCompareVersions = (version1: FloorPlanVersion, version2: FloorPlanVersion) => {
    console.log('Comparing versions:', version1.id, 'and', version2.id);
  };
  
  // Handle file upload completion
  const handleFileUploaded = (file: File, imageUrl: string) => {
    console.log('File uploaded:', file.name, imageUrl);
    setUploadDialogOpen(false);
    
    // In a real app, we would create a new version and refresh the list
    toast({
      title: 'New version created',
      description: `Floor plan "${file.name}" has been added to versions.`
    });
  };
  
  // Handle saving annotations
  const handleSaveAnnotations = (annotations: AnnotationObject[], layers: AnnotationLayer[]) => {
    saveAnnotationsMutation.mutate({ annotations, layers });
  };
  
  // If loading, show loading state
  if (isLoadingProject) {
    return (
      <div className="h-full flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // If project not found, show error
  if (!project) {
    return (
      <AICard>
        <div className="p-6 text-center">
          <h2 className="text-lg font-medium mb-2">Project Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The project you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => setLocation("/")}>
            Back to Projects
          </Button>
        </div>
      </AICard>
    );
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setLocation(`/project/${projectId}`)}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">{project.name} - Floor Plans</h1>
            <p className="text-muted-foreground">{project.description}</p>
          </div>
        </div>
        
        <Button onClick={handleUploadNewVersion}>
          <Upload className="h-4 w-4 mr-2" />
          Upload New Version
        </Button>
      </div>
      
      {/* Main content */}
      <Tabs 
        value={activeTab} 
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="viewer">Viewer</TabsTrigger>
            <TabsTrigger value="versions">Versions</TabsTrigger>
            <TabsTrigger value="annotate">Annotate</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="viewer" className="mt-0">
          {selectedVersion ? (
            <div className="space-y-4">
              <AICard>
                <div className="space-y-3">
                  <h2 className="text-lg font-medium">{selectedVersion.name}</h2>
                  {selectedVersion.description && (
                    <p className="text-sm text-muted-foreground">{selectedVersion.description}</p>
                  )}
                  
                  <div className="h-[600px] border border-border rounded-lg overflow-hidden">
                    <FloorPlanViewer imageUrl={selectedVersion.fileUrl} />
                  </div>
                </div>
              </AICard>
            </div>
          ) : (
            <AICard>
              <div className="p-12 text-center">
                <h3 className="text-lg font-medium mb-2">No Floor Plan Selected</h3>
                <p className="text-muted-foreground mb-6">
                  Select a floor plan from the Versions tab or upload a new one to get started.
                </p>
                <Button onClick={() => setActiveTab("versions")}>
                  View Versions
                </Button>
              </div>
            </AICard>
          )}
        </TabsContent>
        
        <TabsContent value="versions" className="mt-0">
          {isLoadingVersions ? (
            <div className="h-48 flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <FloorPlanVersions
              projectId={parseInt(projectId || "0")}
              versions={versions || []}
              onSelectVersion={handleSelectVersion}
              onUploadNewVersion={handleUploadNewVersion}
              onCompareVersions={handleCompareVersions}
            />
          )}
        </TabsContent>
        
        <TabsContent value="annotate" className="mt-0">
          {selectedVersion ? (
            <FloorPlanAnnotation
              imageUrl={selectedVersion.fileUrl}
              initialAnnotations={mockAnnotations}
              initialLayers={mockLayers}
              collaborators={mockCollaborators}
              onSave={handleSaveAnnotations}
            />
          ) : (
            <AICard>
              <div className="p-12 text-center">
                <h3 className="text-lg font-medium mb-2">No Floor Plan Selected</h3>
                <p className="text-muted-foreground mb-6">
                  Select a floor plan from the Versions tab to annotate it.
                </p>
                <Button onClick={() => setActiveTab("versions")}>
                  View Versions
                </Button>
              </div>
            </AICard>
          )}
        </TabsContent>
      </Tabs>
      
      {/* Upload dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Upload New Floor Plan Version</DialogTitle>
          </DialogHeader>
          
          <FloorPlanUpload onFileUploaded={handleFileUploaded} />
        </DialogContent>
      </Dialog>
    </div>
  );
}