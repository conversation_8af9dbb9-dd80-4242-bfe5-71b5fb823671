import { useState } from "react";
import { useLocation } from "wouter";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation } from "@tanstack/react-query";
import { 
  Building2, 
  ArrowRight, 
  Upload, 
  Check 
} from "lucide-react";

import { AICard } from "@/components/ai-card";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest, queryClient } from "@/lib/queryClient";

// Organization setup steps
type SetupStep = "company-info" | "contact-info" | "complete";

// Form schemas
const companyInfoSchema = z.object({
  name: z.string().min(2, "Company name must be at least 2 characters"),
  website: z.string().url("Must be a valid URL").or(z.string().length(0)),
  taxId: z.string().optional(),
  licenseNumber: z.string().optional(),
});

const contactInfoSchema = z.object({
  email: z.string().email("Must be a valid email address"),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  country: z.string().default("United States"),
});

type CompanyInfoValues = z.infer<typeof companyInfoSchema>;
type ContactInfoValues = z.infer<typeof contactInfoSchema>;

export default function OrganizationSetup() {
  const { toast } = useToast();
  const { user, isLoading } = useAuth();
  const [_, navigate] = useLocation();
  const [currentStep, setCurrentStep] = useState<SetupStep>("company-info");
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  
  // Redirect if user is already set up or not logged in
  if (!isLoading && (!user || user.hasCompletedSetup)) {
    navigate("/");
  }
  
  // Form state
  const [companyData, setCompanyData] = useState<CompanyInfoValues>({
    name: "",
    website: "",
    taxId: "",
    licenseNumber: "",
  });
  
  // Company info form
  const companyInfoForm = useForm<CompanyInfoValues>({
    resolver: zodResolver(companyInfoSchema),
    defaultValues: companyData,
  });
  
  // Contact info form
  const contactInfoForm = useForm<ContactInfoValues>({
    resolver: zodResolver(contactInfoSchema),
    defaultValues: {
      email: user?.email || "",
      phone: "",
      address: "",
      city: "",
      state: "",
      zip: "",
      country: "United States",
    },
  });
  
  // Handle logo upload
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Create organization API call
  const createOrganizationMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!user) throw new Error("User not authenticated");
      
      // First upload logo if it exists
      let logoUrl = null;
      if (logoFile) {
        const formData = new FormData();
        formData.append('file', logoFile);
        
        const uploadRes = await fetch("/api/upload", {
          method: "POST",
          body: formData,
          credentials: "include",
        });
        
        const uploadData = await uploadRes.json();
        logoUrl = uploadData.url;
      }
      
      // Then create organization
      const organizationData = {
        ...data,
        logo: logoUrl,
        createdById: user.id,
      };
      
      const res = await apiRequest("POST", "/api/organizations", organizationData);
      return await res.json();
    },
    onSuccess: async (organization) => {
      // Update user with organization ID and completed setup flag
      if (user) {
        const updateRes = await apiRequest("PATCH", `/api/users/${user.id}`, {
          organizationId: organization.id,
          hasCompletedSetup: true,
        });
        
        if (updateRes.ok) {
          queryClient.invalidateQueries({ queryKey: ["/api/user"] });
          
          toast({
            title: "Setup complete!",
            description: "Your organization has been created successfully",
          });
          
          // Move to complete step
          setCurrentStep("complete");
        }
      }
    },
    onError: (error) => {
      toast({
        title: "Failed to create organization",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Handle company info form submission
  const onCompanyInfoSubmit = (values: CompanyInfoValues) => {
    setCompanyData(values);
    setCurrentStep("contact-info");
  };
  
  // Handle contact info form submission
  const onContactInfoSubmit = async (values: ContactInfoValues) => {
    // Combine the data and submit
    const organizationData = {
      ...companyData,
      ...values,
    };
    
    createOrganizationMutation.mutate(organizationData);
  };
  
  // Rendering current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case "company-info":
        return (
          <Form {...companyInfoForm}>
            <form onSubmit={companyInfoForm.handleSubmit(onCompanyInfoSubmit)} className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-center mb-4">
                  {logoPreview ? (
                    <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-primary/20">
                      <img 
                        src={logoPreview} 
                        alt="Company Logo" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center">
                      <Building2 className="w-12 h-12 text-muted-foreground" />
                    </div>
                  )}
                </div>
                
                <div className="text-center mb-6">
                  <div className="relative inline-block">
                    <Button variant="outline" type="button" className="rounded-full">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Logo
                    </Button>
                    <input
                      type="file"
                      accept="image/*"
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      onChange={handleLogoChange}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Recommended: Square JPG or PNG, at least 400x400px
                  </p>
                </div>
              </div>
              
              <FormField
                control={companyInfoForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={companyInfoForm.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input placeholder="https://www.example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={companyInfoForm.control}
                  name="taxId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tax ID / EIN</FormLabel>
                      <FormControl>
                        <Input placeholder="Optional" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={companyInfoForm.control}
                  name="licenseNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>License Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Optional" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="flex justify-end pt-4">
                <Button type="submit" className="rounded-full">
                  Continue <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </form>
          </Form>
        );
        
      case "contact-info":
        return (
          <Form {...contactInfoForm}>
            <form onSubmit={contactInfoForm.handleSubmit(onContactInfoSubmit)} className="space-y-6">
              <FormField
                control={contactInfoForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={contactInfoForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="Optional" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={contactInfoForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Street Address</FormLabel>
                    <FormControl>
                      <Input placeholder="Optional" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={contactInfoForm.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input placeholder="Optional" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={contactInfoForm.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State/Province</FormLabel>
                      <FormControl>
                        <Input placeholder="Optional" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={contactInfoForm.control}
                  name="zip"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ZIP/Postal Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Optional" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={contactInfoForm.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex justify-between pt-4">
                <Button 
                  type="button" 
                  variant="ghost"
                  onClick={() => setCurrentStep("company-info")}
                >
                  Back
                </Button>
                <Button type="submit" className="rounded-full" disabled={createOrganizationMutation.isPending}>
                  {createOrganizationMutation.isPending ? "Creating..." : "Complete Setup"}
                </Button>
              </div>
            </form>
          </Form>
        );
        
      case "complete":
        return (
          <div className="text-center py-8">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6">
              <Check className="h-8 w-8 text-primary" />
            </div>
            
            <h2 className="text-2xl font-medium mb-4">Organization Setup Complete!</h2>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              Your organization has been created successfully. You can now start creating projects and inviting team members.
            </p>
            
            <Button 
              onClick={() => navigate("/")} 
              className="rounded-full"
              size="lg"
            >
              Go to Dashboard
            </Button>
          </div>
        );
    }
  };
  
  // Progress indicator
  const progressSteps = [
    { id: "company-info", label: "Company Info" },
    { id: "contact-info", label: "Contact Details" },
  ];
  
  return (
    <div className="flex min-h-screen bg-background">
      <div className="flex flex-col w-full">
        <header className="flex justify-between px-6 py-4">
          <div className="flex items-center">
            <Building2 className="h-6 w-6 mr-2" />
            <span className="font-medium">CoElec</span>
          </div>
        </header>
        
        <main className="flex-1 flex flex-col items-center justify-center p-4">
          <AICard className="w-full max-w-xl mx-auto">
            <div className="px-6 pt-6 pb-2">
              <h1 className="text-2xl font-medium mb-2">Organization Setup</h1>
              <p className="text-muted-foreground text-sm mb-6">
                Set up your organization to get started with CoElec
              </p>
              
              {currentStep !== "complete" && (
                <div className="flex mb-6">
                  {progressSteps.map((step, index) => (
                    <div key={step.id} className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === step.id ? "bg-primary text-white" : index < progressSteps.findIndex(s => s.id === currentStep) ? "bg-primary/20 text-primary" : "bg-muted text-muted-foreground"}`}>
                        {index + 1}
                      </div>
                      <span className={`ml-2 mr-4 text-sm ${currentStep === step.id ? "text-primary font-medium" : "text-muted-foreground"}`}>
                        {step.label}
                      </span>
                      {index < progressSteps.length - 1 && (
                        <div className="w-8 h-px bg-border mx-2"></div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="px-6 pb-6">
              {renderCurrentStep()}
            </div>
          </AICard>
        </main>
        
        <footer className="p-4 text-center text-sm text-muted-foreground">
          <p>&copy; {new Date().getFullYear()} CoElec. All rights reserved.</p>
        </footer>
      </div>
    </div>
  );
}