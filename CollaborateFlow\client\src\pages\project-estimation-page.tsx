import { useState, useEffect } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Calculator, Save, FileDown, Send } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { MaterialEstimation } from "@/components/material-estimation";
import { LaborEstimation } from "@/components/labor-estimation";
import { SymbolType } from "@/components/symbol-editor";
import { useToast } from "@/hooks/use-toast";

interface DetectedSymbol {
  id: string;
  type: string;
  position?: { x: number; y: number };
  rotation?: number;
  scale?: number;
  selected?: boolean;
  count?: number;
}

interface MaterialItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unitPrice: number;
  supplier: string;
  alternativeIds: string[];
}

interface LaborTask {
  id: string;
  name: string;
  category: string;
  hours: number;
  rate: number;
  level: "apprentice" | "journeyman" | "master";
  workers: number;
}

export default function ProjectEstimationPage() {
  const [, setLocation] = useLocation();
  const { projectId } = useParams();
  const { toast } = useToast();
  
  // Local state
  const [activeTab, setActiveTab] = useState("materials");
  const [materials, setMaterials] = useState<MaterialItem[]>([]);
  const [laborTasks, setLaborTasks] = useState<LaborTask[]>([]);
  const [detectedSymbols, setDetectedSymbols] = useState<DetectedSymbol[]>([]);
  
  // Fetch project details
  const { data: project, isLoading: isLoadingProject } = useQuery({
    queryKey: ['/api/projects', parseInt(projectId || "0")],
    queryFn: async () => {
      const response = await fetch(`/api/projects/${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch project');
      return await response.json();
    },
    enabled: !!projectId
  });
  
  // Mock fetch detected symbols from the project
  // In a real app, this would be fetched from the backend
  useEffect(() => {
    if (!projectId) return;
    
    // Simulate a API fetch with timeout
    const timeout = setTimeout(() => {
      const mockSymbols: DetectedSymbol[] = [
        {
          id: 'sym-1',
          type: 'outlet',
          count: 12,
          position: { x: 100, y: 100 }
        },
        {
          id: 'sym-2',
          type: 'switch',
          count: 8,
          position: { x: 200, y: 150 }
        },
        {
          id: 'sym-3',
          type: 'light',
          count: 6,
          position: { x: 300, y: 200 }
        },
        {
          id: 'sym-4',
          type: 'panel',
          count: 1,
          position: { x: 400, y: 300 }
        },
        {
          id: 'sym-5',
          type: 'data',
          count: 4,
          position: { x: 500, y: 100 }
        }
      ];
      
      setDetectedSymbols(mockSymbols);
    }, 1000);
    
    return () => clearTimeout(timeout);
  }, [projectId]);
  
  // Handle materials updated
  const handleMaterialsUpdated = (updatedMaterials: MaterialItem[]) => {
    setMaterials(updatedMaterials);
  };
  
  // Handle labor tasks updated
  const handleLaborTasksUpdated = (updatedTasks: LaborTask[]) => {
    setLaborTasks(updatedTasks);
  };
  
  // Calculate totals
  const materialsTotal = materials.reduce(
    (sum, item) => sum + (item.quantity * item.unitPrice), 
    0
  );
  
  const laborTotal = laborTasks.reduce(
    (sum, task) => sum + (task.hours * task.rate * task.workers), 
    0
  );
  
  const total = materialsTotal + laborTotal;
  
  // Save the estimate
  const handleSaveEstimate = () => {
    // In a real app, this would be an API call to save the estimate
    console.log('Saving estimate:', {
      projectId,
      materials,
      laborTasks,
      materialsTotal,
      laborTotal,
      total
    });
    
    toast({
      title: "Estimate Saved",
      description: "Your estimation has been saved successfully."
    });
  };
  
  // Generate a quote
  const handleGenerateQuote = () => {
    // In a real app, this would navigate to a quote generation page or component
    toast({
      title: "Quote Generated",
      description: "Your quote has been generated and is ready for review."
    });
    
    // For demo purposes, let's show a success message
    setTimeout(() => {
      setLocation(`/project/${projectId}`);
    }, 1500);
  };
  
  // Download the estimate as CSV
  const handleDownloadEstimate = () => {
    // In a real app, this would generate and download a CSV file
    toast({
      title: "Download Started",
      description: "Your estimate is being downloaded as a CSV file."
    });
  };
  
  // If loading, show loading state
  if (isLoadingProject) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-pulse">Loading project...</div>
      </div>
    );
  }
  
  // If project not found, show error
  if (!project) {
    return (
      <AICard>
        <div className="p-6 text-center">
          <h2 className="text-lg font-medium mb-2">Project Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The project you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => setLocation("/")}>
            Back to Projects
          </Button>
        </div>
      </AICard>
    );
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setLocation(`/project/${projectId}`)}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">{project.name} - Electrical Estimation</h1>
            <p className="text-muted-foreground">{project.description}</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleDownloadEstimate}>
            <FileDown className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
          
          <Button variant="outline" onClick={handleSaveEstimate}>
            <Save className="mr-2 h-4 w-4" />
            Save
          </Button>
          
          <Button onClick={handleGenerateQuote}>
            <Send className="mr-2 h-4 w-4" />
            Generate Quote
          </Button>
        </div>
      </div>
      
      {/* Summary card */}
      <AICard>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Total Symbols</p>
              <p className="text-2xl font-bold">
                {detectedSymbols.reduce((sum, symbol) => sum + (symbol.count || 1), 0)}
              </p>
            </div>
            
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Materials Cost</p>
              <p className="text-2xl font-bold">${materialsTotal.toFixed(2)}</p>
            </div>
            
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Labor Cost</p>
              <p className="text-2xl font-bold">${laborTotal.toFixed(2)}</p>
            </div>
            
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Total Estimate</p>
              <p className="text-2xl font-bold text-primary">${total.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </AICard>
      
      {/* Main tabs content */}
      <Tabs 
        value={activeTab} 
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="materials">Materials</TabsTrigger>
            <TabsTrigger value="labor">Labor</TabsTrigger>
          </TabsList>
          
          <div className="hidden sm:block text-muted-foreground text-sm">
            Last updated: {new Date().toLocaleDateString('en-US', { 
              month: 'short', 
              day: 'numeric', 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </div>
        </div>
        
        <TabsContent value="materials" className="mt-0">
          <MaterialEstimation 
            detectedSymbols={detectedSymbols}
            onMaterialsUpdated={handleMaterialsUpdated}
          />
        </TabsContent>
        
        <TabsContent value="labor" className="mt-0">
          <LaborEstimation 
            detectedSymbols={detectedSymbols}
            onLaborTasksUpdated={handleLaborTasksUpdated}
          />
        </TabsContent>
      </Tabs>
      
      {/* Quick actions */}
      <AICard>
        <div className="p-6">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 sm:mb-0">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <Calculator className="h-5 w-5 text-primary" />
              </div>
              
              <div>
                <h3 className="font-medium">Ready to Generate a Quote?</h3>
                <p className="text-sm text-muted-foreground">Create a professional quote from your estimate</p>
              </div>
            </div>
            
            <Button onClick={handleGenerateQuote}>
              Generate Quote
            </Button>
          </div>
        </div>
      </AICard>
    </div>
  );
}