import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { 
  FileText, 
  Plus, 
  Search, 
  Filter, 
  ArrowUpDown, 
  MoreHorizontal, 
  Send, 
  Edit, 
  Copy, 
  Trash, 
  Download, 
  Eye, 
  CheckCircle2, 
  XCircle, 
  Clock
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { AICard } from "@/components/ai-card";
import { QuoteGeneration } from "@/components/quote-generation";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";

// Types
interface QuoteItem {
  id: string;
  description: string;
  category: string;
  quantity: number;
  unitPrice: number;
  total: number;
  type: "material" | "labor" | "other";
}

interface QuoteData {
  id: string;
  projectId: string;
  projectName: string;
  clientName: string;
  clientEmail: string;
  quoteNumber: string;
  issueDate: string;
  expiryDate: string;
  total: number;
  status: "draft" | "sent" | "viewed" | "approved" | "declined";
  createdAt: string;
  updatedAt: string;
}

// Quote statuses with colors
const QUOTE_STATUSES = {
  draft: { label: "Draft", color: "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300" },
  sent: { label: "Sent", color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300" },
  viewed: { label: "Viewed", color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300" },
  approved: { label: "Approved", color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" },
  declined: { label: "Declined", color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300" },
};

export default function QuoteManagementPage() {
  const [, navigate] = useLocation();
  const { projectId } = useParams();
  const { toast } = useToast();
  
  // Local state
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | "all">("all");
  const [sortField, setSortField] = useState<keyof QuoteData>("updatedAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedQuote, setSelectedQuote] = useState<QuoteData | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSendDialogOpen, setIsSendDialogOpen] = useState(false);
  
  // Fetch project details if projectId exists
  const { data: project, isLoading: isLoadingProject } = useQuery({
    queryKey: ['/api/projects', parseInt(projectId || "0")],
    queryFn: async () => {
      const response = await fetch(`/api/projects/${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch project');
      return await response.json();
    },
    enabled: !!projectId
  });
  
  // Mock quotes data (would normally be fetched from API)
  const [quotes, setQuotes] = useState<QuoteData[]>([
    {
      id: "q1",
      projectId: projectId || "1",
      projectName: "Commercial Building Wiring",
      clientName: "Acme Commercial Properties",
      clientEmail: "<EMAIL>",
      quoteNumber: "Q-20230501-1234",
      issueDate: "2023-05-01",
      expiryDate: "2023-06-01",
      total: 24850.75,
      status: "approved",
      createdAt: "2023-04-28",
      updatedAt: "2023-05-05"
    },
    {
      id: "q2",
      projectId: projectId || "1",
      projectName: "Office Renovation",
      clientName: "TechStart Inc",
      clientEmail: "<EMAIL>",
      quoteNumber: "Q-20230615-2345",
      issueDate: "2023-06-15",
      expiryDate: "2023-07-15",
      total: 12750.00,
      status: "sent",
      createdAt: "2023-06-10",
      updatedAt: "2023-06-15"
    },
    {
      id: "q3",
      projectId: projectId || "2",
      projectName: "Residential Rewiring",
      clientName: "John Smith",
      clientEmail: "<EMAIL>",
      quoteNumber: "Q-20230720-3456",
      issueDate: "2023-07-20",
      expiryDate: "2023-08-20",
      total: 8450.50,
      status: "viewed",
      createdAt: "2023-07-18",
      updatedAt: "2023-07-22"
    },
    {
      id: "q4",
      projectId: projectId || "3",
      projectName: "New Home Construction",
      clientName: "Emily Johnson",
      clientEmail: "<EMAIL>",
      quoteNumber: "Q-20230805-4567",
      issueDate: "2023-08-05",
      expiryDate: "2023-09-05",
      total: 32150.25,
      status: "draft",
      createdAt: "2023-08-01",
      updatedAt: "2023-08-01"
    },
    {
      id: "q5",
      projectId: projectId || "4",
      projectName: "Retail Store Lighting",
      clientName: "Fashion First",
      clientEmail: "<EMAIL>",
      quoteNumber: "Q-20230910-5678",
      issueDate: "2023-09-10",
      expiryDate: "2023-10-10",
      total: 15750.00,
      status: "declined",
      createdAt: "2023-09-05",
      updatedAt: "2023-09-12"
    }
  ]);
  
  // Filter quotes to only show those for the current project if projectId exists
  const filteredQuotes = (projectId
    ? quotes.filter(q => q.projectId === projectId)
    : quotes
  ).filter(quote => {
    // Apply search filter
    const matchesSearch = searchQuery
      ? quote.projectName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        quote.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        quote.quoteNumber.toLowerCase().includes(searchQuery.toLowerCase())
      : true;
      
    // Apply status filter
    const matchesStatus = statusFilter !== "all"
      ? quote.status === statusFilter
      : true;
      
    return matchesSearch && matchesStatus;
  });
  
  // Sort quotes
  const sortedQuotes = [...filteredQuotes].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    } else if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc'
        ? aValue - bValue
        : bValue - aValue;
    }
    
    return 0;
  });
  
  // Handle sort click
  const handleSort = (field: keyof QuoteData) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // Create a new quote
  const handleCreateQuote = () => {
    // This would normally save the quote to the backend
    const newQuote: QuoteData = {
      id: `q${quotes.length + 1}`,
      projectId: projectId || "1",
      projectName: project?.name || "New Project",
      clientName: "New Client",
      clientEmail: "<EMAIL>",
      quoteNumber: `Q-${new Date().toISOString().slice(0, 10).replace(/-/g, "")}-${Math.floor(1000 + Math.random() * 9000)}`,
      issueDate: new Date().toISOString().slice(0, 10),
      expiryDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().slice(0, 10),
      total: 0,
      status: "draft",
      createdAt: new Date().toISOString().slice(0, 10),
      updatedAt: new Date().toISOString().slice(0, 10)
    };
    
    setQuotes(prev => [newQuote, ...prev]);
    setIsCreateDialogOpen(false);
    
    toast({
      title: "Quote Created",
      description: "New quote has been created successfully."
    });
    
    // Navigate to quote editing
    // navigate(`/project/${projectId}/quotes/${newQuote.id}/edit`);
  };
  
  // Delete a quote
  const handleDeleteQuote = () => {
    if (!selectedQuote) return;
    
    setQuotes(prev => prev.filter(q => q.id !== selectedQuote.id));
    setIsDeleteDialogOpen(false);
    
    toast({
      title: "Quote Deleted",
      description: `Quote ${selectedQuote.quoteNumber} has been deleted.`
    });
  };
  
  // Send a quote
  const handleSendQuote = () => {
    if (!selectedQuote) return;
    
    setQuotes(prev => 
      prev.map(q => 
        q.id === selectedQuote.id 
          ? { ...q, status: "sent", updatedAt: new Date().toISOString().slice(0, 10) }
          : q
      )
    );
    
    setIsSendDialogOpen(false);
    
    toast({
      title: "Quote Sent",
      description: `Quote ${selectedQuote.quoteNumber} has been sent to ${selectedQuote.clientEmail}.`
    });
  };
  
  // Update quote status
  const handleUpdateStatus = (id: string, status: "draft" | "sent" | "viewed" | "approved" | "declined") => {
    setQuotes(prev => 
      prev.map(q => 
        q.id === id 
          ? { ...q, status, updatedAt: new Date().toISOString().slice(0, 10) }
          : q
      )
    );
    
    toast({
      title: "Status Updated",
      description: `Quote status has been updated to ${QUOTE_STATUSES[status].label}.`
    });
  };
  
  // Format date for display
  const formatDate = (dateStr: string) => {
    try {
      return format(new Date(dateStr), "MMM d, yyyy");
    } catch (error) {
      return dateStr;
    }
  };
  
  // Calculate statistics
  const stats = {
    total: quotes.length,
    draft: quotes.filter(q => q.status === "draft").length,
    sent: quotes.filter(q => q.status === "sent").length,
    viewed: quotes.filter(q => q.status === "viewed").length,
    approved: quotes.filter(q => q.status === "approved").length,
    declined: quotes.filter(q => q.status === "declined").length,
  };
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{projectId ? `${project?.name} - ` : ""}Quote Management</h1>
          <p className="text-muted-foreground">
            Create and manage quotes for your electrical projects
          </p>
        </div>
        
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Quote
        </Button>
      </div>
      
      {/* Stats cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col items-center justify-center text-center h-full">
              <p className="text-4xl font-bold">{stats.total}</p>
              <p className="text-sm text-muted-foreground">Total Quotes</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col items-center justify-center text-center h-full">
              <p className="text-3xl font-bold">{stats.draft}</p>
              <p className="text-sm text-muted-foreground">Drafts</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col items-center justify-center text-center h-full">
              <p className="text-3xl font-bold">{stats.sent}</p>
              <p className="text-sm text-muted-foreground">Sent</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col items-center justify-center text-center h-full">
              <p className="text-3xl font-bold">{stats.viewed}</p>
              <p className="text-sm text-muted-foreground">Viewed</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col items-center justify-center text-center h-full">
              <p className="text-3xl font-bold text-green-600">{stats.approved}</p>
              <p className="text-sm text-muted-foreground">Approved</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col items-center justify-center text-center h-full">
              <p className="text-3xl font-bold text-red-600">{stats.declined}</p>
              <p className="text-sm text-muted-foreground">Declined</p>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Filters */}
      <AICard>
        <div className="p-4 flex flex-col sm:flex-row items-center gap-4">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search quotes..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center gap-2 ml-auto">
            <Select
              value={statusFilter}
              onValueChange={(value) => setStatusFilter(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {Object.entries(QUOTE_STATUSES).map(([status, { label }]) => (
                  <SelectItem key={status} value={status}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button variant="outline" size="icon" title="Reset filters" onClick={() => {
              setSearchQuery("");
              setStatusFilter("all");
            }}>
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </AICard>
      
      {/* Quotes table */}
      <AICard>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">#</TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('quoteNumber')}
                >
                  Quote Number
                  {sortField === 'quoteNumber' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('projectName')}
                >
                  Project
                  {sortField === 'projectName' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('clientName')}
                >
                  Client
                  {sortField === 'clientName' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('issueDate')}
                >
                  Issue Date
                  {sortField === 'issueDate' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('expiryDate')}
                >
                  Expiry Date
                  {sortField === 'expiryDate' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead 
                  className="text-right cursor-pointer"
                  onClick={() => handleSort('total')}
                >
                  Total
                  {sortField === 'total' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedQuotes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                    No quotes found. Create a new quote to get started.
                  </TableCell>
                </TableRow>
              ) : (
                sortedQuotes.map((quote, index) => (
                  <TableRow key={quote.id}>
                    <TableCell className="font-medium">{index + 1}</TableCell>
                    <TableCell>{quote.quoteNumber}</TableCell>
                    <TableCell>{quote.projectName}</TableCell>
                    <TableCell>{quote.clientName}</TableCell>
                    <TableCell>{formatDate(quote.issueDate)}</TableCell>
                    <TableCell>{formatDate(quote.expiryDate)}</TableCell>
                    <TableCell className="text-right">${quote.total.toLocaleString()}</TableCell>
                    <TableCell>
                      <Badge 
                        className={cn(
                          "text-xs font-medium",
                          QUOTE_STATUSES[quote.status].color
                        )}
                      >
                        {QUOTE_STATUSES[quote.status].label}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedQuote(quote);
                              setIsViewDialogOpen(true);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => navigate(`/project/${projectId}/quote/edit/${quote.id}`)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedQuote(quote);
                              setIsSendDialogOpen(true);
                            }}
                          >
                            <Send className="h-4 w-4 mr-2" />
                            Send
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              const newQuote = {...quote};
                              newQuote.id = `q${quotes.length + 1}`;
                              newQuote.quoteNumber = `${quote.quoteNumber}-copy`;
                              newQuote.status = "draft";
                              newQuote.createdAt = new Date().toISOString().slice(0, 10);
                              newQuote.updatedAt = new Date().toISOString().slice(0, 10);
                              setQuotes(prev => [newQuote, ...prev]);
                              
                              toast({
                                title: "Quote Duplicated",
                                description: "A copy of the quote has been created."
                              });
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              toast({
                                title: "PDF Downloaded",
                                description: "Quote PDF has been downloaded."
                              });
                            }}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download PDF
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(quote.id, "approved")}
                            className="text-green-600"
                          >
                            <CheckCircle2 className="h-4 w-4 mr-2" />
                            Mark as Approved
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleUpdateStatus(quote.id, "declined")}
                            className="text-red-600"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Mark as Declined
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedQuote(quote);
                              setIsDeleteDialogOpen(true);
                            }}
                            className="text-red-600"
                          >
                            <Trash className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </AICard>
      
      {/* Recent activity */}
      <AICard>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Track the latest actions on your quotes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedQuotes.slice(0, 5).map((quote) => (
              <div key={quote.id} className="flex items-start space-x-4">
                <div className={cn(
                  "mt-1 h-8 w-8 rounded-full flex items-center justify-center",
                  quote.status === "approved" ? "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300" :
                  quote.status === "declined" ? "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300" :
                  quote.status === "sent" ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300" :
                  quote.status === "viewed" ? "bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300" :
                  "bg-slate-100 text-slate-700 dark:bg-slate-900 dark:text-slate-300"
                )}>
                  {quote.status === "approved" ? <CheckCircle2 className="h-4 w-4" /> :
                   quote.status === "declined" ? <XCircle className="h-4 w-4" /> :
                   quote.status === "sent" ? <Send className="h-4 w-4" /> :
                   quote.status === "viewed" ? <Eye className="h-4 w-4" /> :
                   <Clock className="h-4 w-4" />}
                </div>
                <div className="space-y-1">
                  <p className="font-medium">
                    Quote {quote.quoteNumber} was 
                    {quote.status === "approved" ? " approved by " :
                     quote.status === "declined" ? " declined by " :
                     quote.status === "sent" ? " sent to " :
                     quote.status === "viewed" ? " viewed by " :
                     " updated by "}
                    {quote.clientName}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(quote.updatedAt)} • Project: {quote.projectName}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </AICard>
      
      {/* Create Quote Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Quote</DialogTitle>
            <DialogDescription>
              Create a new quote based on project specifications.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <QuoteGeneration 
              projectId={projectId}
              projectName={project?.name}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateQuote}>
              Create Quote
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* View Quote Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Quote #{selectedQuote?.quoteNumber}</DialogTitle>
            <DialogDescription>
              Viewing quote details for {selectedQuote?.projectName}.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedQuote && (
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{selectedQuote.projectName}</CardTitle>
                      <CardDescription>{selectedQuote.quoteNumber}</CardDescription>
                    </div>
                    <Badge 
                      className={cn(
                        "text-xs font-medium",
                        QUOTE_STATUSES[selectedQuote.status].color
                      )}
                    >
                      {QUOTE_STATUSES[selectedQuote.status].label}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Client Information</h3>
                      <p className="font-medium">{selectedQuote.clientName}</p>
                      <p className="text-sm">{selectedQuote.clientEmail}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Quote Details</h3>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <p className="text-muted-foreground">Issue Date:</p>
                        <p>{formatDate(selectedQuote.issueDate)}</p>
                        <p className="text-muted-foreground">Expiry Date:</p>
                        <p>{formatDate(selectedQuote.expiryDate)}</p>
                        <p className="text-muted-foreground">Total Amount:</p>
                        <p className="font-medium">${selectedQuote.total.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-1 space-y-2">
                    <h3 className="text-sm font-medium">Quote Timeline</h3>
                    <div className="text-sm grid grid-cols-2">
                      <p className="text-muted-foreground">Created On:</p>
                      <p>{formatDate(selectedQuote.createdAt)}</p>
                      <p className="text-muted-foreground">Last Updated:</p>
                      <p>{formatDate(selectedQuote.updatedAt)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
            <Button variant="outline" onClick={() => {
              setIsViewDialogOpen(false);
              toast({
                title: "PDF Downloaded",
                description: "Quote PDF has been downloaded."
              });
            }}>
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
            <Button onClick={() => {
              setIsViewDialogOpen(false);
              setIsSendDialogOpen(true);
            }}>
              <Send className="mr-2 h-4 w-4" />
              Send Quote
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Send Quote Dialog */}
      <Dialog open={isSendDialogOpen} onOpenChange={setIsSendDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Send Quote</DialogTitle>
            <DialogDescription>
              Send this quote to your client via email.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="recipient">To</Label>
              <Input id="recipient" value={selectedQuote?.clientEmail || ""} readOnly />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="subject">Subject</Label>
              <Input 
                id="subject" 
                defaultValue={`Quote ${selectedQuote?.quoteNumber} for ${selectedQuote?.projectName}`} 
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="message">Message</Label>
              <Textarea 
                id="message" 
                rows={6}
                defaultValue={`Dear ${selectedQuote?.clientName},

Please find attached our quote ${selectedQuote?.quoteNumber} for the ${selectedQuote?.projectName} project.

The total amount for this project is $${selectedQuote?.total.toLocaleString()}.

This quote is valid until ${selectedQuote?.expiryDate ? formatDate(selectedQuote.expiryDate) : ''}.

If you have any questions, please don't hesitate to contact us.

Thank you for your business.`} 
              />
            </div>
            <div className="grid gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="attach-pdf" defaultChecked />
                <Label htmlFor="attach-pdf">Attach PDF</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSendDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSendQuote}>
              <Send className="mr-2 h-4 w-4" />
              Send
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Quote Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Quote</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this quote? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedQuote && (
              <div className="p-4 border rounded-md bg-muted/10">
                <p className="font-medium">{selectedQuote.quoteNumber}</p>
                <p className="text-sm text-muted-foreground">Project: {selectedQuote.projectName}</p>
                <p className="text-sm text-muted-foreground">Client: {selectedQuote.clientName}</p>
                <p className="text-sm font-medium mt-2">Total: ${selectedQuote.total.toLocaleString()}</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteQuote}>
              <Trash className="mr-2 h-4 w-4" />
              Delete Quote
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}