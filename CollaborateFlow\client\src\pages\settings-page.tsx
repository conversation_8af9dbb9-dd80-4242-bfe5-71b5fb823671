import { useState } from "react";
import { Layout } from "@/components/layout";
import { AICard } from "@/components/ai-card";
import { Button } from "@/components/ui/button";
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { 
  User, 
  Settings, 
  UsersRound, 
  Key, 
  Bell, 
  Monitor, 
  Shield, 
  CreditCard,
  CheckCircle,
  XCircle,
  ChevronRight,
  Plus,
  Trash2,
  Download
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { CompanyProfileSettings } from "@/components/company-profile-settings";
import { IntegrationSettings } from "@/components/integration-settings";
import { NotificationPreferences } from "@/components/notification-preferences";

const profileFormSchema = z.object({
  fullName: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  username: z.string().min(3, {
    message: "Username must be at least 3 characters.",
  }),
  bio: z.string().optional(),
  jobTitle: z.string().optional(),
  company: z.string().optional(),
});

const accountFormSchema = z.object({
  language: z.string(),
  timeZone: z.string(),
  dateFormat: z.string(),
  notifications: z.boolean().default(true),
  marketingEmails: z.boolean().default(false),
  darkMode: z.boolean().default(true),
});

export default function SettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>("profile");

  const profileForm = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: user?.fullName || "",
      email: user?.email || "",
      username: user?.username || "",
      bio: "",
      jobTitle: "",
      company: "",
    },
  });

  const accountForm = useForm<z.infer<typeof accountFormSchema>>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      language: "en",
      timeZone: "UTC",
      dateFormat: "MM/DD/YYYY",
      notifications: true,
      marketingEmails: false,
      darkMode: true,
    },
  });

  const onProfileSubmit = (data: z.infer<typeof profileFormSchema>) => {
    toast({
      title: "Profile updated",
      description: "Your profile information has been updated.",
    });
  };

  const onAccountSubmit = (data: z.infer<typeof accountFormSchema>) => {
    toast({
      title: "Account settings updated",
      description: "Your account settings have been updated.",
    });
  };

  return (
    <Layout>
      <div className="py-4 px-4 md:px-6 max-w-[1200px] mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-normal mb-1">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account, profile, and preferences
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          <div className="md:w-1/4">
            <AICard className="mb-4 p-0">
              <div className="space-y-1">
                <TabsList className="w-full h-auto flex flex-col items-stretch p-1">
                  <TabsTrigger 
                    value="profile" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("profile")}
                  >
                    <User className="h-4 w-4 mr-2" />
                    Profile
                  </TabsTrigger>
                  <TabsTrigger 
                    value="account" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("account")}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Account
                  </TabsTrigger>
                  <TabsTrigger 
                    value="teams" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("teams")}
                  >
                    <UsersRound className="h-4 w-4 mr-2" />
                    Teams
                  </TabsTrigger>
                  <TabsTrigger 
                    value="security" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("security")}
                  >
                    <Key className="h-4 w-4 mr-2" />
                    Security
                  </TabsTrigger>
                  <TabsTrigger 
                    value="notifications" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("notifications")}
                  >
                    <Bell className="h-4 w-4 mr-2" />
                    Notifications
                  </TabsTrigger>
                  <TabsTrigger 
                    value="display" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("display")}
                  >
                    <Monitor className="h-4 w-4 mr-2" />
                    Display
                  </TabsTrigger>
                  <TabsTrigger 
                    value="privacy" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("privacy")}
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Privacy
                  </TabsTrigger>
                  <TabsTrigger 
                    value="billing" 
                    className="justify-start text-sm px-3 py-2 h-9 data-[state=active]:bg-muted"
                    onClick={() => setActiveTab("billing")}
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    Billing
                  </TabsTrigger>
                </TabsList>
              </div>
            </AICard>
          </div>

          <div className="md:w-3/4">
            <Tabs value={activeTab} onValueChange={setActiveTab} orientation="vertical">
              <TabsContent value="profile" className="m-0">
                <AICard title="Company Profile" description="Update your company information and brand settings">
                  <div className="space-y-5">
                    <div className="flex flex-col md:flex-row gap-8">
                      <CompanyProfileSettings 
                        companyData={{
                          name: "CoElec Electrical Services",
                          email: "<EMAIL>",
                          phone: "(*************",
                          address: "123 Main Street, Suite 100",
                          city: "San Francisco",
                          state: "CA",
                          zip: "94105",
                          country: "United States",
                          taxId: "XX-XXXXXXX",
                          licenseNumber: "EC-XXXXX",
                          website: "https://www.coelec.com",
                          logo: "https://placehold.co/400x400/2563eb/ffffff?text=CE"
                        }}
                      />
                    </div>
                  </div>
                </AICard>
              </TabsContent>
              
              <TabsContent value="account" className="m-0">
                <AICard title="Account Settings" description="Update your account preferences and settings">
                  <Form {...accountForm}>
                    <form onSubmit={accountForm.handleSubmit(onAccountSubmit)} className="space-y-5">
                      <div className="grid md:grid-cols-2 gap-5">
                        <FormField
                          control={accountForm.control}
                          name="language"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Language</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select language" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="en">English</SelectItem>
                                  <SelectItem value="es">Spanish</SelectItem>
                                  <SelectItem value="fr">French</SelectItem>
                                  <SelectItem value="de">German</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={accountForm.control}
                          name="timeZone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Time Zone</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select time zone" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="UTC">UTC</SelectItem>
                                  <SelectItem value="EST">Eastern Time (EST)</SelectItem>
                                  <SelectItem value="CST">Central Time (CST)</SelectItem>
                                  <SelectItem value="MST">Mountain Time (MST)</SelectItem>
                                  <SelectItem value="PST">Pacific Time (PST)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={accountForm.control}
                        name="dateFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Date Format</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select date format" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                                <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                                <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <Separator />
                      
                      <div className="space-y-3">
                        <FormField
                          control={accountForm.control}
                          name="notifications"
                          render={({ field }) => (
                            <FormItem className="flex justify-between items-center">
                              <div>
                                <FormLabel>Notifications</FormLabel>
                                <FormDescription>
                                  Receive notifications about updates and activity
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={accountForm.control}
                          name="marketingEmails"
                          render={({ field }) => (
                            <FormItem className="flex justify-between items-center">
                              <div>
                                <FormLabel>Marketing Emails</FormLabel>
                                <FormDescription>
                                  Receive emails about new features and offers
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={accountForm.control}
                          name="darkMode"
                          render={({ field }) => (
                            <FormItem className="flex justify-between items-center">
                              <div>
                                <FormLabel>Dark Mode</FormLabel>
                                <FormDescription>
                                  Use dark theme for the application
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <Button type="submit" className="rounded-full">Save Changes</Button>
                    </form>
                  </Form>
                </AICard>
              </TabsContent>
              
              <TabsContent value="teams" className="m-0">
                <AICard title="User Management" description="Manage team members and their permissions">
                  <div className="space-y-5">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-lg font-medium">Team Members</h3>
                        <p className="text-sm text-muted-foreground">
                          Invite and manage members of your team
                        </p>
                      </div>
                      <Button>
                        <UsersRound className="mr-2 h-4 w-4" />
                        Invite Member
                      </Button>
                    </div>
                    
                    <div className="border rounded-md">
                      <div className="grid grid-cols-12 gap-4 p-4 border-b text-sm font-medium text-muted-foreground">
                        <div className="col-span-4">Name / Email</div>
                        <div className="col-span-3">Role</div>
                        <div className="col-span-3">Team</div>
                        <div className="col-span-2">Status</div>
                      </div>
                      
                      <div className="divide-y">
                        {/* Team Member 1 */}
                        <div className="grid grid-cols-12 gap-4 p-4 items-center text-sm">
                          <div className="col-span-4">
                            <div className="font-medium">John Smith</div>
                            <div className="text-muted-foreground"><EMAIL></div>
                          </div>
                          <div className="col-span-3">
                            <Select defaultValue="admin">
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="admin">Administrator</SelectItem>
                                <SelectItem value="manager">Project Manager</SelectItem>
                                <SelectItem value="member">Team Member</SelectItem>
                                <SelectItem value="viewer">Viewer</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="col-span-3">
                            <div className="text-xs inline-flex items-center px-2 py-1 rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                              All Teams
                            </div>
                          </div>
                          <div className="col-span-2">
                            <div className="text-xs inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                              Active
                            </div>
                          </div>
                        </div>
                        
                        {/* Team Member 2 */}
                        <div className="grid grid-cols-12 gap-4 p-4 items-center text-sm">
                          <div className="col-span-4">
                            <div className="font-medium">Jane Doe</div>
                            <div className="text-muted-foreground"><EMAIL></div>
                          </div>
                          <div className="col-span-3">
                            <Select defaultValue="manager">
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="admin">Administrator</SelectItem>
                                <SelectItem value="manager">Project Manager</SelectItem>
                                <SelectItem value="member">Team Member</SelectItem>
                                <SelectItem value="viewer">Viewer</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="col-span-3">
                            <div className="text-xs inline-flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                              Engineering
                            </div>
                          </div>
                          <div className="col-span-2">
                            <div className="text-xs inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                              Active
                            </div>
                          </div>
                        </div>
                        
                        {/* Team Member 3 */}
                        <div className="grid grid-cols-12 gap-4 p-4 items-center text-sm">
                          <div className="col-span-4">
                            <div className="font-medium">Robert Johnson</div>
                            <div className="text-muted-foreground"><EMAIL></div>
                          </div>
                          <div className="col-span-3">
                            <Select defaultValue="member">
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="admin">Administrator</SelectItem>
                                <SelectItem value="manager">Project Manager</SelectItem>
                                <SelectItem value="member">Team Member</SelectItem>
                                <SelectItem value="viewer">Viewer</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="col-span-3">
                            <div className="text-xs inline-flex items-center px-2 py-1 rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                              Electricians
                            </div>
                          </div>
                          <div className="col-span-2">
                            <div className="text-xs inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                              Pending
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="text-lg font-medium mb-4">Role Permissions</h3>
                      <div className="space-y-5">
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-muted/10 p-4">
                            <h4 className="font-medium">Administrator</h4>
                            <p className="text-sm text-muted-foreground">
                              Full access to all system features and settings
                            </p>
                          </div>
                          <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center space-x-2">
                              <Switch id="admin-projects" defaultChecked disabled />
                              <Label htmlFor="admin-projects">Manage Projects</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="admin-users" defaultChecked disabled />
                              <Label htmlFor="admin-users">Manage Users</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="admin-billing" defaultChecked disabled />
                              <Label htmlFor="admin-billing">Manage Billing</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="admin-settings" defaultChecked disabled />
                              <Label htmlFor="admin-settings">Manage Settings</Label>
                            </div>
                          </div>
                        </div>
                        
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-muted/10 p-4">
                            <h4 className="font-medium">Project Manager</h4>
                            <p className="text-sm text-muted-foreground">
                              Can manage projects and team members, but not system settings
                            </p>
                          </div>
                          <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center space-x-2">
                              <Switch id="pm-projects" defaultChecked />
                              <Label htmlFor="pm-projects">Manage Projects</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="pm-tasks" defaultChecked />
                              <Label htmlFor="pm-tasks">Assign Tasks</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="pm-team" defaultChecked />
                              <Label htmlFor="pm-team">View Team</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="pm-reports" defaultChecked />
                              <Label htmlFor="pm-reports">Generate Reports</Label>
                            </div>
                          </div>
                        </div>
                        
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-muted/10 p-4">
                            <h4 className="font-medium">Team Member</h4>
                            <p className="text-sm text-muted-foreground">
                              Standard access to assigned projects and tasks
                            </p>
                          </div>
                          <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center space-x-2">
                              <Switch id="member-view" defaultChecked />
                              <Label htmlFor="member-view">View Projects</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="member-tasks" defaultChecked />
                              <Label htmlFor="member-tasks">Update Tasks</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="member-comment" defaultChecked />
                              <Label htmlFor="member-comment">Add Comments</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch id="member-files" defaultChecked />
                              <Label htmlFor="member-files">Upload Files</Label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </AICard>
              </TabsContent>
              
              <TabsContent value="security" className="m-0">
                <AICard title="Integration Settings" description="Configure your integrations with external services">
                  <IntegrationSettings />
                </AICard>
              </TabsContent>
              
              <TabsContent value="notifications" className="m-0">
                <AICard title="Notification Settings" description="Configure how you receive notifications">
                  <NotificationPreferences />
                </AICard>
              </TabsContent>
              
              <TabsContent value="display" className="m-0">
                <AICard title="Display Settings" description="Customize how the application looks">
                  <div className="h-[400px] flex items-center justify-center">
                    <div className="text-center">
                      <div className="mb-4 p-4 rounded-full bg-primary/10 inline-flex">
                        <Monitor className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="font-medium mb-2">Display Options</h3>
                      <p className="text-sm text-muted-foreground mb-4 max-w-xs">
                        This section will allow you to customize the display options including theme, layout and font size
                      </p>
                      <Button className="rounded-full">Coming Soon</Button>
                    </div>
                  </div>
                </AICard>
              </TabsContent>
              
              <TabsContent value="privacy" className="m-0">
                <AICard title="Privacy Settings" description="Manage your privacy preferences">
                  <div className="h-[400px] flex items-center justify-center">
                    <div className="text-center">
                      <div className="mb-4 p-4 rounded-full bg-primary/10 inline-flex">
                        <Shield className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="font-medium mb-2">Privacy Controls</h3>
                      <p className="text-sm text-muted-foreground mb-4 max-w-xs">
                        This section will allow you to manage your privacy settings and data access controls
                      </p>
                      <Button className="rounded-full">Coming Soon</Button>
                    </div>
                  </div>
                </AICard>
              </TabsContent>
              
              <TabsContent value="billing" className="m-0">
                <AICard title="Subscription Plan" description="Manage your subscription and billing details">
                  <div className="space-y-6">
                    {/* Current Plan Section */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Current Plan</h3>
                      <div className="border rounded-md overflow-hidden">
                        <div className="bg-muted/10 p-6 flex justify-between items-center">
                          <div>
                            <h4 className="text-xl font-semibold">Professional Plan</h4>
                            <p className="text-sm text-muted-foreground mt-1">
                              Billed annually at $588.00 ($49 per month)
                            </p>
                          </div>
                          <div className="text-xl font-semibold">$49.00/mo</div>
                        </div>
                        <div className="p-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div>
                            <h5 className="font-medium text-sm mb-2">Plan Includes</h5>
                            <ul className="space-y-2 text-sm">
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Up to 20 team members</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Unlimited projects</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>AI-powered estimation</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Client portal</span>
                              </li>
                            </ul>
                          </div>
                          <div>
                            <h5 className="font-medium text-sm mb-2">Plan Details</h5>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Billing cycle</span>
                                <span>Annual</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Renewal date</span>
                                <span>May 15, 2026</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Team members</span>
                                <span>3 / 20</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Storage</span>
                                <span>15 GB / 100 GB</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h5 className="font-medium text-sm mb-2">Plan Actions</h5>
                            <div className="space-y-2">
                              <Button className="w-full justify-between" variant="outline">
                                Change Plan
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                              <Button className="w-full justify-between" variant="outline">
                                Manage Billing
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                              <Button className="w-full justify-between text-destructive" variant="outline">
                                Cancel Subscription
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Available Plans Section */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Available Plans</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {/* Starter Plan */}
                        <div className="border rounded-md overflow-hidden">
                          <div className="p-5 border-b">
                            <h4 className="text-lg font-medium">Starter</h4>
                            <div className="mt-2">
                              <span className="text-2xl font-bold">$29</span>
                              <span className="text-muted-foreground">/month</span>
                            </div>
                          </div>
                          <div className="p-5">
                            <ul className="space-y-2 text-sm mb-6">
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Up to 5 team members</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Up to 10 projects</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Basic estimation tools</span>
                              </li>
                              <li className="flex items-start">
                                <XCircle className="h-4 w-4 text-muted-foreground mr-2 mt-0.5" />
                                <span className="text-muted-foreground">Client portal</span>
                              </li>
                            </ul>
                            <Button className="w-full" variant="outline">Select Plan</Button>
                          </div>
                        </div>

                        {/* Professional Plan */}
                        <div className="border rounded-md overflow-hidden border-primary">
                          <div className="bg-primary text-primary-foreground text-center text-xs font-medium py-1">
                            CURRENT PLAN
                          </div>
                          <div className="p-5 border-b">
                            <h4 className="text-lg font-medium">Professional</h4>
                            <div className="mt-2">
                              <span className="text-2xl font-bold">$49</span>
                              <span className="text-muted-foreground">/month</span>
                            </div>
                          </div>
                          <div className="p-5">
                            <ul className="space-y-2 text-sm mb-6">
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Up to 20 team members</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Unlimited projects</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>AI-powered estimation</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Client portal</span>
                              </li>
                            </ul>
                            <Button className="w-full" disabled>Current Plan</Button>
                          </div>
                        </div>

                        {/* Enterprise Plan */}
                        <div className="border rounded-md overflow-hidden">
                          <div className="p-5 border-b">
                            <h4 className="text-lg font-medium">Enterprise</h4>
                            <div className="mt-2">
                              <span className="text-2xl font-bold">$99</span>
                              <span className="text-muted-foreground">/month</span>
                            </div>
                          </div>
                          <div className="p-5">
                            <ul className="space-y-2 text-sm mb-6">
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Unlimited team members</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Unlimited projects</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>Advanced AI features</span>
                              </li>
                              <li className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5" />
                                <span>White-labeled client portal</span>
                              </li>
                            </ul>
                            <Button className="w-full">Upgrade</Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Payment Methods Section */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Payment Methods</h3>
                      <div className="border rounded-md overflow-hidden">
                        <div className="p-4 border-b">
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">Credit Cards</h4>
                            <Button variant="outline" size="sm">
                              <Plus className="h-4 w-4 mr-2" />
                              Add Card
                            </Button>
                          </div>
                        </div>
                        <div className="divide-y">
                          <div className="p-4 flex justify-between items-center">
                            <div className="flex items-center">
                              <div className="h-8 w-12 bg-muted rounded flex items-center justify-center mr-3">
                                <span className="text-xs font-medium">VISA</span>
                              </div>
                              <div>
                                <div className="font-medium">•••• •••• •••• 4242</div>
                                <div className="text-xs text-muted-foreground">Expires 12/2025</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="bg-primary/10">Default</Badge>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="h-4 w-4 text-muted-foreground" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Billing History Section */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Billing History</h3>
                      <div className="border rounded-md">
                        <div className="grid grid-cols-12 gap-4 p-4 border-b text-sm font-medium text-muted-foreground">
                          <div className="col-span-4">Invoice</div>
                          <div className="col-span-2">Amount</div>
                          <div className="col-span-3">Date</div>
                          <div className="col-span-3">Status</div>
                        </div>
                        <div className="divide-y">
                          <div className="grid grid-cols-12 gap-4 p-4 items-center text-sm">
                            <div className="col-span-4">
                              <div className="font-medium">INV-0001</div>
                              <div className="text-muted-foreground">Professional Plan - Annual</div>
                            </div>
                            <div className="col-span-2">$588.00</div>
                            <div className="col-span-3">May 15, 2025</div>
                            <div className="col-span-3 flex items-center justify-between">
                              <div className="text-xs inline-flex items-center px-2 py-1 rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                Paid
                              </div>
                              <Button variant="ghost" size="icon">
                                <Download className="h-4 w-4 text-muted-foreground" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </AICard>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Layout>
  );
}