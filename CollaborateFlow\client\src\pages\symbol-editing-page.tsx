import { useState } from "react";
import { useParams, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, Edit, Zap, Save, Trash } from "lucide-react";
import { AICard } from "@/components/ai-card";
import { SymbolDetection } from "@/components/symbol-detection";
import { SymbolEditor, type SymbolType } from "@/components/symbol-editor";
import { useToast } from "@/hooks/use-toast";

interface DetectedSymbol {
  id: string;
  type: SymbolType;
  count: number;
  confidence: number;
  x: number;
  y: number;
}

interface EditorSymbol {
  id: string;
  type: SymbolType;
  position: { x: number; y: number };
  rotation: number;
  scale: number;
  selected: boolean;
}

export default function SymbolEditingPage() {
  const [, setLocation] = useLocation();
  const { projectId } = useParams();
  const { toast } = useToast();
  
  // Local state for UI
  const [activeTab, setActiveTab] = useState("detection");
  const [detectedSymbols, setDetectedSymbols] = useState<DetectedSymbol[]>([]);
  const [editedSymbols, setEditedSymbols] = useState<EditorSymbol[]>([]);
  const [floorPlanImage, setFloorPlanImage] = useState<string | null>(null);
  
  // Fetch project details
  const { data: project, isLoading: isLoadingProject } = useQuery({
    queryKey: ['/api/projects', parseInt(projectId || "0")],
    queryFn: async () => {
      const response = await fetch(`/api/projects/${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch project');
      return await response.json();
    },
    enabled: !!projectId
  });
  
  // For demonstration purposes, use a sample floor plan image
  // In a real application, this would be fetched from the backend
  const sampleFloorPlanImage = "https://images.unsplash.com/photo-1617650728444-1807011cfe45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80";
  
  // Handle detection complete
  const handleDetectionComplete = (symbols: DetectedSymbol[]) => {
    setDetectedSymbols(symbols);
    setFloorPlanImage(sampleFloorPlanImage);
    
    // Convert detected symbols to editor format
    const editorSymbols: EditorSymbol[] = symbols.map(symbol => ({
      id: symbol.id,
      type: symbol.type,
      position: { x: symbol.x, y: symbol.y },
      rotation: 0,
      scale: 1,
      selected: false
    }));
    
    setEditedSymbols(editorSymbols);
    
    // Auto switch to the editor tab
    setActiveTab("editor");
    
    toast({
      title: "Detection Complete",
      description: `${symbols.length} symbol types detected. You can now edit them in the Symbol Editor.`,
    });
  };
  
  // Handle symbols updated in editor
  const handleSymbolsUpdated = (symbols: EditorSymbol[]) => {
    setEditedSymbols(symbols);
    
    toast({
      title: "Symbols Updated",
      description: `${symbols.length} symbols have been updated.`,
    });
  };
  
  // Handle save all symbols
  const handleSaveAll = () => {
    // In a real app, this would be an API call to save the symbols
    console.log('Saving symbols:', editedSymbols);
    
    toast({
      title: "Symbols Saved",
      description: `${editedSymbols.length} symbols have been saved to the project.`,
    });
  };
  
  // If loading, show loading state
  if (isLoadingProject) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-pulse">Loading project...</div>
      </div>
    );
  }
  
  // If project not found, show error
  if (!project) {
    return (
      <AICard>
        <div className="p-6 text-center">
          <h2 className="text-lg font-medium mb-2">Project Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The project you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => setLocation("/")}>
            Back to Projects
          </Button>
        </div>
      </AICard>
    );
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setLocation(`/project/${projectId}`)}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">{project.name} - Electrical Symbols</h1>
            <p className="text-muted-foreground">{project.description}</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {activeTab === "editor" && (
            <Button onClick={handleSaveAll}>
              <Save className="h-4 w-4 mr-2" />
              Save All
            </Button>
          )}
        </div>
      </div>
      
      {/* Main content */}
      <Tabs 
        value={activeTab} 
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="detection">Symbol Detection</TabsTrigger>
            <TabsTrigger value="editor">Symbol Editor</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="detection" className="mt-0">
          <SymbolDetection 
            imageUrl={sampleFloorPlanImage}
            onComplete={handleDetectionComplete}
          />
        </TabsContent>
        
        <TabsContent value="editor" className="mt-0">
          {floorPlanImage ? (
            <SymbolEditor 
              imageUrl={floorPlanImage}
              detectedSymbols={editedSymbols}
              onSymbolsUpdated={handleSymbolsUpdated}
            />
          ) : (
            <AICard>
              <div className="p-12 text-center">
                <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-4">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg font-medium mb-2">No Symbols Detected Yet</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Run symbol detection first to identify electrical symbols on the floor plan. 
                  You can then edit and refine them here.
                </p>
                <Button onClick={() => setActiveTab("detection")}>
                  Go to Symbol Detection
                </Button>
              </div>
            </AICard>
          )}
        </TabsContent>
      </Tabs>
      
      {/* Batch actions section - only shown when symbols are available */}
      {editedSymbols.length > 0 && activeTab === "editor" && (
        <AICard>
          <div className="p-4">
            <h3 className="text-sm font-medium mb-3">Batch Operations</h3>
            
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit All Selected
              </Button>
              
              <Button variant="outline" size="sm">
                Select All
              </Button>
              
              <Button variant="outline" size="sm">
                Deselect All
              </Button>
              
              <Button variant="outline" size="sm" className="text-destructive">
                <Trash className="h-4 w-4 mr-2" />
                Delete Selected
              </Button>
            </div>
          </div>
        </AICard>
      )}
    </div>
  );
}