-- Supabase Import Script
-- Modified from database_export.sql to be compatible with Supabase

-- Initial setup
SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- Drop existing tables if needed (uncomment if you want to start fresh)
-- DROP TABLE IF EXISTS public.quote_feedback;
-- DROP TABLE IF EXISTS public.quotes;
-- DROP TABLE IF EXISTS public.tasks;
-- DROP TABLE IF EXISTS public.columns;
-- DROP TABLE IF EXISTS public.projects;
-- DROP TABLE IF EXISTS public.team_members;
-- DROP TABLE IF EXISTS public.teams;
-- DROP TABLE IF EXISTS public.users;
-- DROP TABLE IF EXISTS public.organizations;
-- DROP TABLE IF EXISTS public.session;

-- Create tables
CREATE TABLE IF NOT EXISTS public.organizations (
    id integer NOT NULL,
    name text NOT NULL,
    email text,
    phone text,
    address text,
    city text,
    state text,
    zip text,
    country text,
    tax_id text,
    license_number text,
    website text,
    logo text,
    created_by_id integer NOT NULL,
    created_at timestamp without time zone DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.users (
    id integer NOT NULL,
    username text NOT NULL,
    password text NOT NULL,
    full_name text,
    email text,
    avatar_url text,
    role text,
    organization_id integer,
    has_completed_setup boolean DEFAULT false
);

CREATE TABLE IF NOT EXISTS public.teams (
    id integer NOT NULL,
    name text NOT NULL,
    description text,
    created_by_id integer NOT NULL,
    organization_id integer NOT NULL,
    created_at timestamp without time zone DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.team_members (
    id integer NOT NULL,
    team_id integer NOT NULL,
    user_id integer NOT NULL,
    role text DEFAULT 'member'::text
);

CREATE TABLE IF NOT EXISTS public.projects (
    id integer NOT NULL,
    name text NOT NULL,
    description text,
    start_date text,
    end_date text,
    team_id integer NOT NULL,
    created_by_id integer NOT NULL,
    status text DEFAULT 'planning'::text,
    client_name text,
    client_email text,
    client_phone text,
    estimated_budget text,
    tags jsonb DEFAULT '[]'::jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS public.columns (
    id integer NOT NULL,
    name text NOT NULL,
    "order" integer NOT NULL,
    project_id integer NOT NULL
);

CREATE TABLE IF NOT EXISTS public.tasks (
    id integer NOT NULL,
    title text NOT NULL,
    description text,
    status text NOT NULL,
    priority text DEFAULT 'medium'::text,
    category text,
    due_date text,
    column_id integer NOT NULL,
    project_id integer NOT NULL,
    assignees jsonb DEFAULT '[]'::jsonb,
    created_by_id integer NOT NULL,
    assigned_to integer,
    "order" integer DEFAULT 0 NOT NULL
);

CREATE TABLE IF NOT EXISTS public.quotes (
    id integer NOT NULL,
    quote_number text NOT NULL,
    project_id integer NOT NULL,
    project_name text NOT NULL,
    client_name text NOT NULL,
    client_email text NOT NULL,
    client_phone text,
    client_company text,
    client_address text,
    issue_date timestamp without time zone NOT NULL,
    expiry_date timestamp without time zone NOT NULL,
    subtotal numeric NOT NULL,
    tax_rate numeric NOT NULL,
    tax numeric NOT NULL,
    total numeric NOT NULL,
    labor_total numeric NOT NULL,
    materials_total numeric NOT NULL,
    status text DEFAULT 'draft'::text,
    payment_terms text,
    notes text,
    items jsonb NOT NULL,
    token text,
    created_by_id integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    logo_url text,
    organization_id integer,
    signature_data text,
    approved_at timestamp without time zone,
    rejected_at timestamp without time zone,
    viewed_at timestamp without time zone
);

CREATE TABLE IF NOT EXISTS public.quote_feedback (
    id integer NOT NULL,
    quote_id integer NOT NULL,
    message text NOT NULL,
    section text,
    item_index integer,
    is_client boolean NOT NULL,
    created_at timestamp without time zone DEFAULT now()
);

-- Create primary keys and sequences
ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.teams
    ADD CONSTRAINT teams_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.team_members
    ADD CONSTRAINT team_members_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.columns
    ADD CONSTRAINT columns_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.quotes
    ADD CONSTRAINT quotes_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.quote_feedback
    ADD CONSTRAINT quote_feedback_pkey PRIMARY KEY (id);

-- Create sequences
CREATE SEQUENCE IF NOT EXISTS public.organizations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.teams_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.team_members_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.projects_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.columns_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.tasks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.quotes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS public.quote_feedback_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- Set sequence ownership
ALTER SEQUENCE public.organizations_id_seq OWNED BY public.organizations.id;
ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;
ALTER SEQUENCE public.teams_id_seq OWNED BY public.teams.id;
ALTER SEQUENCE public.team_members_id_seq OWNED BY public.team_members.id;
ALTER SEQUENCE public.projects_id_seq OWNED BY public.projects.id;
ALTER SEQUENCE public.columns_id_seq OWNED BY public.columns.id;
ALTER SEQUENCE public.tasks_id_seq OWNED BY public.tasks.id;
ALTER SEQUENCE public.quotes_id_seq OWNED BY public.quotes.id;
ALTER SEQUENCE public.quote_feedback_id_seq OWNED BY public.quote_feedback.id;

-- Set default values for IDs
ALTER TABLE ONLY public.organizations ALTER COLUMN id SET DEFAULT nextval('public.organizations_id_seq'::regclass);
ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);
ALTER TABLE ONLY public.teams ALTER COLUMN id SET DEFAULT nextval('public.teams_id_seq'::regclass);
ALTER TABLE ONLY public.team_members ALTER COLUMN id SET DEFAULT nextval('public.team_members_id_seq'::regclass);
ALTER TABLE ONLY public.projects ALTER COLUMN id SET DEFAULT nextval('public.projects_id_seq'::regclass);
ALTER TABLE ONLY public.columns ALTER COLUMN id SET DEFAULT nextval('public.columns_id_seq'::regclass);
ALTER TABLE ONLY public.tasks ALTER COLUMN id SET DEFAULT nextval('public.tasks_id_seq'::regclass);
ALTER TABLE ONLY public.quotes ALTER COLUMN id SET DEFAULT nextval('public.quotes_id_seq'::regclass);
ALTER TABLE ONLY public.quote_feedback ALTER COLUMN id SET DEFAULT nextval('public.quote_feedback_id_seq'::regclass);

-- Insert data for organizations
INSERT INTO public.organizations (id, name, email, phone, address, city, state, zip, country, tax_id, license_number, website, logo, created_by_id, created_at) VALUES 
(1, 'Hermes', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 'United States', NULL, NULL, NULL, '/uploads/file-1747129561415-*********.webp', 1, '2025-05-13 09:46:01.769371')
ON CONFLICT (id) DO NOTHING;

-- Insert data for users
INSERT INTO public.users (id, username, password, full_name, email, avatar_url, role, organization_id, has_completed_setup) VALUES 
(1, 'kris', 'bdda149ebe4760856aa65bc6dce70405b0bf9f2fb66a6b7f9e965fac8c745dea97dd5af7e5a7597f3aca72371d3edf8dcd6f9b7df8955bf32bea86e95bd1d55d.00a8429c83433eb62df58906911730ba', 'Kris Kyle', '<EMAIL>', NULL, 'user', 1, TRUE),
(2, NULL, '39875f65f5e79219a51cf94768ac101a3e3f5d5b2ce88038310588f663c67adc15ae56720aa20be07d2083fe4fca90a2e17b01c488cd4ae800698094055a13cc.a854e60e9c2cb7f918f55f60b851dca7', NULL, '<EMAIL>', NULL, 'user', NULL, FALSE)
ON CONFLICT (id) DO NOTHING;

-- Insert data for teams
INSERT INTO public.teams (id, name, description, created_by_id, organization_id, created_at) VALUES 
(1, 'Engineering', 'Engineering team', 1, 1, '2025-05-13 06:46:26.454868'),
(2, 'Design', 'Design team', 1, 1, '2025-05-13 10:56:27.176692'),
(3, 'Marketing', 'Marketing team', 1, 1, '2025-05-14 06:00:21.606266')
ON CONFLICT (id) DO NOTHING;

-- Insert data for team_members
INSERT INTO public.team_members (id, team_id, user_id, role) VALUES 
(1, 1, 1, 'owner'),
(2, 2, 1, 'owner'),
(3, 3, 1, 'owner')
ON CONFLICT (id) DO NOTHING;

-- Insert data for projects
INSERT INTO public.projects (id, name, description, start_date, end_date, team_id, created_by_id, status, client_name, client_email, client_phone, estimated_budget, tags, created_at) VALUES 
(1, 'Hermes', 'fedf', '2025-05-13', NULL, 1, 2, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 06:46:26.452284'),
(2, 'Heremes 2', 'testing', '2025-05-13', '2025-06-04', 1, 2, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 06:56:10.110725'),
(3, 'Test', 'wguwg', '2025-05-13', '2025-06-08', 1, 2, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 07:21:30.214806'),
(4, 'Electrical Installation Demo', 'Demo project for testing the Kanban board features', '2025-05-13', '2025-06-12', 2, 1, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 10:56:27.174317'),
(5, 'test', 'sdfdv', '2025-05-14', '2025-06-08', 3, 1, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-14 06:00:21.603995')
ON CONFLICT (id) DO NOTHING;

-- Insert data for columns
INSERT INTO public.columns (id, name, "order", project_id) VALUES 
(1, 'To Do', 0, 1),
(2, 'In Progress', 1, 1),
(3, 'Review', 2, 1),
(4, 'Done', 3, 1),
(5, 'To Do', 0, 2),
(6, 'In Progress', 1, 2),
(7, 'Done', 2, 2),
(8, 'To Do', 0, 3),
(9, 'In Progress', 1, 3),
(10, 'Done', 2, 3),
(11, 'Backlog', 0, 4),
(12, 'Planning', 1, 4),
(13, 'Materials Ready', 2, 4),
(14, 'Installation', 3, 4),
(15, 'Testing', 4, 4),
(16, 'Complete', 5, 4),
(17, 'To Do', 0, 5),
(18, 'In Progress', 1, 5),
(19, 'Done', 2, 5),
(20, 'In Progress', 3, 1),
(21, 'To Do', 3, 4),
(22, 'Backlog', 3, 2),
(23, 'To Do', 3, 3),
(24, 'Materials Ready', 6, 4),
(25, 'Installation', 7, 4),
(26, 'Testing', 8, 4)
ON CONFLICT (id) DO NOTHING;

-- Insert data for tasks
INSERT INTO public.tasks (id, title, description, status, priority, category, due_date, column_id, project_id, assignees, created_by_id, assigned_to, "order") VALUES 
(1, 'Task 1', 'Description for Task 1', 'to-do', 'high', 'development', '2025-05-20', 1, 1, '[]', 1, 1, 0),
(2, 'Task 2', 'Description for Task 2', 'in-progress', 'medium', 'design', '2025-05-22', 2, 1, '[]', 1, 1, 0),
(3, 'Task 3', 'Description for Task 3', 'review', 'low', 'testing', '2025-05-24', 3, 1, '[]', 1, 1, 0),
(4, 'Task 4', 'Description for Task 4', 'done', 'high', 'deployment', '2025-05-18', 4, 1, '[]', 1, 1, 0),
(5, 'Install Electrical Panel', 'Purchase and install the main electrical panel', 'to-do', 'high', 'installation', '2025-05-25', 11, 4, '[]', 1, 1, 0),
(6, 'Run Wiring', 'Install wiring for all outlets and fixtures', 'to-do', 'medium', 'installation', '2025-05-27', 11, 4, '[]', 1, 1, 1),
(7, 'Test Circuits', 'Test all circuits for proper functionality', 'planning', 'high', 'testing', '2025-05-29', 12, 4, '[]', 1, 1, 0),
(8, 'Final Inspection', 'Conduct final inspection of all electrical work', 'planning', 'high', 'inspection', '2025-05-31', 12, 4, '[]', 1, 1, 1),
(9, 'Install Light Fixtures', 'Mount and connect all light fixtures', 'planning', 'medium', 'installation', '2025-05-28', 12, 4, '[]', 1, 1, 2)
ON CONFLICT (id) DO NOTHING;

-- Set sequence values
SELECT setval('public.organizations_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.organizations), false);
SELECT setval('public.users_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.users), false);
SELECT setval('public.teams_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.teams), false);
SELECT setval('public.team_members_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.team_members), false);
SELECT setval('public.projects_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.projects), false);
SELECT setval('public.columns_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.columns), false);
SELECT setval('public.tasks_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.tasks), false);
SELECT setval('public.quotes_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.quotes), false);
SELECT setval('public.quote_feedback_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM public.quote_feedback), false);