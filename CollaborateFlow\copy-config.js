// This script copies the config.js file to the dist/public directory
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const sourceFile = path.join(__dirname, 'client', 'public', 'config.js');
const destDir = path.join(__dirname, 'dist', 'public');
const destFile = path.join(destDir, 'config.js');

// Create the destination directory if it doesn't exist
if (!fs.existsSync(destDir)) {
  fs.mkdirSync(destDir, { recursive: true });
}

// Copy the file
fs.copyFileSync(sourceFile, destFile);

console.log(`Copied ${sourceFile} to ${destFile}`);
