@echo off
setlocal

:: Check if all arguments are provided
if "%~3"=="" (
    echo Usage: %0 ^<email^> ^<password^> ^<full name^>
    exit /b 1
)

set EMAIL=%~1
set PASSWORD=%~2

:: Combine the rest of the arguments for the full name
set "FULL_NAME=%~3"
shift /3
:loop
if "%~3"=="" goto endloop
set "FULL_NAME=%FULL_NAME% %~3"
shift /3
:endloop

:: Run the docker command to create user
echo Registering user with email: %EMAIL%
docker-compose exec -T app node -e "const { createClient } = require('@supabase/supabase-js'); const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY); (async () => { try { console.log('Registering user...'); const { data, error } = await supabase.auth.signUp({ email: '%EMAIL%', password: '%PASSWORD%', options: { data: { full_name: '%FULL_NAME%' } } }); if (error) { console.error('Error:', error.message); } else { console.log('User registered successfully!'); console.log('User ID:', data.user?.id); const { error: profileError } = await supabase.from('users').insert([{ email: '%EMAIL%', username: '%EMAIL%'.split('@')[0], full_name: '%FULL_NAME%', password: '%PASSWORD%' }]); if (profileError) { console.log('Could not add user to users table:', profileError.message); } else { console.log('User details added to users table.'); } } } catch (err) { console.error('Unexpected error:', err); } })();"
