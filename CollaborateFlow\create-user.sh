#!/bin/bash

# Check if all arguments are provided
if [ "$#" -lt 3 ]; then
  echo "Usage: $0 <email> <password> <full name>"
  exit 1
fi

EMAIL="$1"
PASSWORD="$2"
FULL_NAME="${@:3}"

# Run the docker command to create user
docker-compose exec -T app node -e "const { createClient } = require('@supabase/supabase-js'); \
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY); \
(async () => { \
  try { \
    console.log('Registering user with email: $EMAIL'); \
    const { data, error } = await supabase.auth.signUp({ \
      email: '$EMAIL', \
      password: '$PASSWORD', \
      options: { data: { full_name: '$FULL_NAME' } } \
    }); \
    if (error) { \
      console.error('Error:', error.message); \
    } else { \
      console.log('User registered successfully!'); \
      console.log('User ID:', data.user?.id); \
      const { error: profileError } = await supabase.from('users').insert([{ \
        email: '$EMAIL', \
        username: '$EMAIL'.split('@')[0], \
        full_name: '$FULL_NAME', \
        password: '$PASSWORD' \
      }]); \
      if (profileError) { \
        console.log('Could not add user to users table:', profileError.message); \
      } else { \
        console.log('User details added to users table.'); \
      } \
    } \
  } catch (err) { \
    console.error('Unexpected error:', err); \
  } \
})();"
