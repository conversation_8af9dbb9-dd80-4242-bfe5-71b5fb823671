import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// API endpoint to test Supabase variables
app.get('/api/test-supabase-vars', (req, res) => {
  res.json({
    supabaseUrl: process.env.VITE_SUPABASE_URL ? true : false,
    supabaseKey: process.env.VITE_SUPABASE_KEY ? true : false,
    actualUrl: process.env.VITE_SUPABASE_URL?.substring(0, 10) + '...' // Show just a fragment for security
  });
});

// Serve the debug HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'debug.html'));
});

const PORT = 5002;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Debug server running on port ${PORT}`);
});
