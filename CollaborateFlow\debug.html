<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CollaborateFlow Debug</title>
  <style>
    body { font-family: system-ui, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    button { padding: 8px 16px; margin: 10px 0; background: #0070f3; color: white; border: none; border-radius: 4px; cursor: pointer; }
    #log { margin-top: 20px; }
    .error { color: #e00; }
    .success { color: #0a0; }
  </style>
</head>
<body>
  <h1>CollaborateFlow Debug Page</h1>
  
  <h2>Test Application Loading</h2>
  <button id="testApp">Test Main Application</button>
  <button id="testSupabase">Test Supabase Connection</button>
  
  <div id="log"></div>

  <script>
    // Helper to log messages
    function log(message, isError = false) {
      const logEl = document.getElementById('log');
      const entry = document.createElement('div');
      entry.className = isError ? 'error' : 'success';
      entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logEl.appendChild(entry);
    }

    // Test the main application
    document.getElementById('testApp').addEventListener('click', async () => {
      try {
        log('Fetching main application index.html...');
        const response = await fetch('/');
        const html = await response.text();
        log(`Successfully loaded index.html (${html.length} bytes)`);
        
        // Check for assets
        const scriptMatch = html.match(/src="\/assets\/([^"]+)\.js"/i);
        const cssMatch = html.match(/href="\/assets\/([^"]+)\.css"/i);
        
        if (scriptMatch) {
          log(`Found JS bundle: ${scriptMatch[1]}.js`);
          const jsResponse = await fetch(`/assets/${scriptMatch[1]}.js`);
          if (jsResponse.ok) {
            log('✓ JS bundle is accessible');
          } else {
            log(`✗ JS bundle returned ${jsResponse.status}`, true);
          }
        } else {
          log('✗ Could not find JS bundle in HTML', true);
        }
        
        if (cssMatch) {
          log(`Found CSS bundle: ${cssMatch[1]}.css`);
          const cssResponse = await fetch(`/assets/${cssMatch[1]}.css`);
          if (cssResponse.ok) {
            log('✓ CSS bundle is accessible');
          } else {
            log(`✗ CSS bundle returned ${cssResponse.status}`, true);
          }
        } else {
          log('✗ Could not find CSS bundle in HTML', true);
        }
      } catch (error) {
        log(`Error testing application: ${error.message}`, true);
      }
    });

    // Test Supabase connection
    document.getElementById('testSupabase').addEventListener('click', async () => {
      try {
        log('Testing Supabase environment variables...');
        const response = await fetch('/api/test-supabase-vars');
        if (response.ok) {
          const data = await response.json();
          log(`VITE_SUPABASE_URL: ${data.supabaseUrl ? '✓ Present' : '✗ Missing'}`);
          log(`VITE_SUPABASE_KEY: ${data.supabaseKey ? '✓ Present' : '✗ Missing'}`);
        } else {
          log(`API returned ${response.status}`, true);
        }
      } catch (error) {
        log(`Error testing Supabase: ${error.message}`, true);
      }
    });
  </script>
</body>
</html>
