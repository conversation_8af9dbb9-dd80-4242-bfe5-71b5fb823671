version: '3.8'

services:
  app:
    build: .
    ports:
      - "5001:5000"
    # Add network configurations to handle IPv4 and IPv6
    networks:
      - collaborateflow-net
    dns: 
      - *******  # Use Google DNS
      - *******  # Use Cloudflare DNS as backup
    environment:
      # Direct connections to Supabase only
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - VITE_SUPABASE_URL=${SUPABASE_URL}
      - VITE_SUPABASE_KEY=${SUPABASE_KEY}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - SENDGRID_FROM_EMAIL=${SENDGRID_FROM_EMAIL}
      - PORT=5000
      - NODE_ENV=production
      # Feature flags for real implementation
      - USE_REAL_DATABASE=true
      - USE_REAL_TEAM_SERVICE=true
      - USE_REAL_PROJECT_SERVICE=true
      - USE_REAL_COLUMN_SERVICE=true
      - USE_REAL_TASK_SERVICE=true
      - USE_REAL_FLOOR_PLAN_SERVICE=true
      - USE_REAL_QUOTE_SERVICE=true
      - USE_REAL_SYMBOL_SERVICE=true
      - USE_REAL_MATERIAL_SERVICE=true
      - USE_REAL_LABOR_SERVICE=true
    volumes:
      - ./uploads:/app/uploads

networks:
  collaborateflow-net:
    driver: bridge
    # Force IPv4 networking since Supabase appears to have IPv6 connectivity issues
    driver_opts:
      com.docker.network.enable_ipv6: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"

# No volumes needed as we're using direct Supabase connections
