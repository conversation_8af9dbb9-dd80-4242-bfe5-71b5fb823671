# AI Integration Guide for CoElec

This document provides a comprehensive guide to the AI symbol detection functionality in CoElec, including setup, configuration, and usage.

## Overview

CoElec uses a sophisticated AI system to automatically detect electrical symbols in floor plans. The system leverages multiple state-of-the-art AI models through OpenRouter and implements a Model Context Protocol (MCP) server architecture for optimal performance and cost management.

## Architecture

```
Frontend (React) → Backend API → MCP Server → OpenRouter → AI Models
                                     ↓
                              Symbol Detection Results
```

### Key Components

1. **MCP Server** (`server/mcp/symbol-detection-mcp.ts`)
   - Handles AI model selection and routing
   - Manages prompt templates and versioning
   - Implements fallback strategies and cost optimization
   - Provides caching and performance monitoring

2. **AI Routes** (`server/routes/ai.ts`)
   - REST API endpoints for symbol detection
   - Integration with MCP server
   - Feature flag support for development/production

3. **Frontend Components**
   - Symbol detection UI (`client/src/components/symbol-detection.tsx`)
   - Symbol editor (`client/src/components/symbol-editor.tsx`)
   - Floor plan viewer (`client/src/components/floor-plan-viewer.tsx`)

## Supported AI Models

The system supports multiple AI models through OpenRouter:

| Model | Use Case | Cost | Speed | Quality |
|-------|----------|------|-------|---------|
| Claude 3.5 Sonnet | Complex/High-quality | High | Medium | Excellent |
| GPT-4o | Versatile/Mixed content | High | Medium | Excellent |
| Gemini Pro Vision | Standard/Fallback | Low | Fast | Good |
| Claude 3 Haiku | Simple/Preprocessing | Very Low | Very Fast | Good |

## Setup Instructions

### 1. Prerequisites

- Node.js 18+ installed
- OpenRouter account and API key
- Supabase project configured

### 2. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Configure your OpenRouter API key:
   ```bash
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   ```

3. Enable AI features:
   ```bash
   USE_REAL_AI=true
   ```

### 3. Automated Setup

Run the AI setup script:
```bash
npm run setup:ai
```

This script will:
- Check your environment configuration
- Validate your OpenRouter API key
- Test model availability
- Configure feature flags
- Provide setup recommendations

### 4. Testing

Test the AI integration:
```bash
npm run test:ai
```

This will run performance tests with sample images and provide detailed metrics.

## Configuration Options

### Feature Flags

Control AI behavior through environment variables:

```bash
# Enable/disable real AI (vs mock data)
USE_REAL_AI=true

# Default model selection
DEFAULT_AI_MODEL=claude-3.5-sonnet

# Performance settings
AI_TIMEOUT_MS=30000
MAX_AI_RETRIES=3

# Cost management
AI_DAILY_BUDGET_USD=50.00
AI_MONTHLY_BUDGET_USD=1000.00
AI_COST_ALERT_THRESHOLD=0.8
```

### Model Selection

The MCP server automatically selects the best model based on:

- **Image complexity**: Simple, standard, or complex
- **Priority**: Speed, quality, or cost
- **Fallback requirements**: Automatic fallback on failures

You can override model selection by passing options to the API:

```javascript
const result = await fetch('/api/ai/detect-symbols', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    imageUrl: 'https://example.com/floorplan.jpg',
    options: {
      complexity: 'complex',
      priority: 'quality'
    }
  })
});
```

## API Usage

### Symbol Detection Endpoint

**POST** `/api/ai/detect-symbols`

**Request Body:**
```json
{
  "imageUrl": "https://example.com/floorplan.jpg",
  "options": {
    "complexity": "simple|standard|complex",
    "priority": "speed|quality|cost",
    "promptVersion": "v1"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Symbol detection completed",
  "symbols": [
    {
      "id": "symbol_1234567890_0",
      "type": "outlet",
      "x": 150,
      "y": 200,
      "width": 20,
      "height": 20,
      "confidence": 0.95,
      "properties": {
        "subtype": "gfci",
        "voltage": "120V",
        "amperage": "20A"
      }
    }
  ],
  "symbolCounts": {
    "outlet": 5,
    "switch": 3,
    "light": 8
  },
  "metadata": {
    "model_used": "Claude 3.5 Sonnet",
    "processing_time_ms": 2500,
    "total_tokens": 1250,
    "cost_estimate": 0.0037
  }
}
```

## Development Workflow

### 1. Development Mode

For development, you can use mock data:
```bash
USE_REAL_AI=false
npm run dev
```

### 2. Testing with Real AI

Enable real AI for testing:
```bash
USE_REAL_AI=true
npm run dev
```

### 3. Prompt Engineering

Modify prompts in `server/mcp/symbol-detection-mcp.ts`:

```typescript
export const SYMBOL_DETECTION_PROMPTS = {
  v1: {
    system: "Your system prompt here...",
    user: "Your user prompt here..."
  },
  v2: {
    // New version for testing
  }
};
```

### 4. Performance Monitoring

Monitor AI performance through:
- Console logs during development
- OpenRouter dashboard for usage/costs
- Built-in metrics in API responses

## Cost Management

### Estimated Costs

Based on typical usage:
- Simple floor plan: $0.001 - $0.003
- Standard floor plan: $0.003 - $0.008
- Complex floor plan: $0.008 - $0.020

### Cost Optimization Strategies

1. **Model Selection**: Use cheaper models for simple images
2. **Caching**: Implement response caching for repeated requests
3. **Preprocessing**: Optimize images before sending to AI
4. **Batch Processing**: Process multiple symbols in one request
5. **Fallback Strategy**: Use cheaper models as fallbacks

### Budget Monitoring

Set budget limits in your environment:
```bash
AI_DAILY_BUDGET_USD=50.00
AI_MONTHLY_BUDGET_USD=1000.00
```

## Troubleshooting

### Common Issues

1. **"OpenRouter API key not configured"**
   - Ensure `OPENROUTER_API_KEY` is set in your `.env` file
   - Run `npm run setup:ai` to configure

2. **"Model not available"**
   - Check OpenRouter dashboard for model availability
   - Verify your account has access to required models

3. **High processing times**
   - Consider using faster models for simple images
   - Check your internet connection
   - Monitor OpenRouter API status

4. **High costs**
   - Review your model selection strategy
   - Implement caching for repeated requests
   - Use cheaper models for development

### Debug Mode

Enable detailed logging:
```bash
DEBUG_MODE=true
LOG_LEVEL=debug
npm run dev
```

## Production Deployment

### Environment Setup

1. Configure production environment variables
2. Set appropriate budget limits
3. Enable monitoring and alerting
4. Implement caching strategy

### Performance Optimization

1. **Caching**: Implement Redis caching for AI responses
2. **Rate Limiting**: Protect against excessive API usage
3. **Monitoring**: Set up alerts for cost and performance
4. **Scaling**: Consider horizontal scaling for high volume

### Security Considerations

1. **API Key Security**: Store OpenRouter API key securely
2. **Rate Limiting**: Implement proper rate limiting
3. **Input Validation**: Validate all image URLs and parameters
4. **Error Handling**: Don't expose sensitive error details

## Support and Resources

- **OpenRouter Documentation**: https://openrouter.ai/docs
- **Model Pricing**: https://openrouter.ai/models
- **API Status**: https://status.openrouter.ai
- **CoElec Issues**: Create an issue in the project repository

## Contributing

To contribute to the AI integration:

1. Fork the repository
2. Create a feature branch
3. Test your changes with `npm run test:ai`
4. Submit a pull request with detailed description

### Adding New Models

To add support for new AI models:

1. Update `AI_MODELS` configuration in `symbol-detection-mcp.ts`
2. Test the new model with sample images
3. Update documentation and cost estimates
4. Submit a pull request

### Improving Prompts

To improve symbol detection accuracy:

1. Create new prompt versions in `SYMBOL_DETECTION_PROMPTS`
2. Test with diverse floor plan images
3. Compare results with existing prompts
4. Document improvements and submit changes
