# CollaborateFlow - How to Test

This guide provides practical instructions for running tests in the CollaborateFlow application. It covers both unit testing and User Acceptance Testing (UAT).

## Prerequisites

Before running tests, ensure you have the following installed:

```bash
# Install test dependencies if not already installed
npm install --save-dev vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event msw jsdom
```

## Running Unit Tests

### Using the Test Runner Script

We've created a convenient script to run tests with various options:

```bash
# Make sure the script is executable
chmod +x run-tests.sh

# Run all unit tests
./run-tests.sh

# Run specific test categories
./run-tests.sh server    # Run server-side tests only
./run-tests.sh client    # Run client-side tests only
./run-tests.sh auth      # Run authentication tests only
./run-tests.sh team      # Run team management tests only
./run-tests.sh coverage  # Run all tests with coverage reporting
./run-tests.sh watch     # Run tests in watch mode (rerun on file changes)
```

### Using NPM Scripts

Alternatively, you can use the NPM scripts defined in package.json:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Running Individual Test Files

To run a specific test file directly:

```bash
npx vitest run tests/unit/server/supabase-storage-teams.test.ts
```

## Unit Test Coverage

The unit tests cover the following critical functionality:

1. **Authentication and User Management**
   - Dual ID system (UUID/numeric ID) in `auth-middleware.test.ts`
   - Super admin detection in `auth-middleware.test.ts` and `use-super-admin.test.ts`
   - AuthProvider context in modals in `add-project-dialog.test.ts`

2. **Team Management with Role-Based Access**
   - Role-based team visibility in `supabase-storage-teams.test.ts`
   - Organization selection in `add-team-dialog.test.tsx`

3. **API Integration**
   - CORS configuration in `cors-middleware.test.ts`
   - API endpoints in `use-organizations-endpoints.test.ts`
   - Supabase REST API in `supabase-rest-api.test.ts`

4. **Configuration**
   - Feature flags in `feature-flags.test.ts`

## User Acceptance Testing (UAT)

For UAT, refer to the `UAT_DOCUMENT.md` which outlines all test scenarios.

### Setting Up the UAT Environment

```bash
# Create test database with sample data
node scripts/setup-test-data.js

# Start the application in test mode
NODE_ENV=test npm run dev
```

### Running UAT Tests

UAT tests are performed manually following the test scenarios in `UAT_DOCUMENT.md`. For each test case:

1. Follow the test steps outlined in the document
2. Verify the actual result matches the expected result
3. Update the status in the document (✅, ⚠️, or ❌)

## Automated vs. Manual Testing

- **Unit Tests**: Automated using Vitest and Testing Library
- **Integration Tests**: Partially automated
- **UAT**: Manual testing with the UAT document as a guide

## Test Data

Test data is created in two ways:

1. **Unit Tests**: Mock data defined in test files
2. **UAT**: Test database created by the `setup-test-data.js` script

## Common Testing Patterns

### Testing Components with Roles

```typescript
// Example: Testing super admin visibility
it('shows organization dropdown for super admin', () => {
  // Mock super admin
  vi.mocked(useSuperAdmin).mockReturnValue({ isSuperAdmin: true });
  
  render(<AddTeamDialog />);
  
  // Check that organization selector is visible
  expect(screen.getByTestId('organization-selector')).toBeInTheDocument();
});
```

### Testing API Endpoints

```typescript
// Example: Testing API call
it('calls the correct API endpoint', async () => {
  // Mock fetch
  global.fetch = vi.fn().mockResolvedValueOnce({
    ok: true,
    json: () => Promise.resolve([{ id: 1, name: 'Test' }])
  });
  
  // Call function that makes API request
  await fetchTeams();
  
  // Verify endpoint
  expect(global.fetch).toHaveBeenCalledWith('/api/teams', expect.any(Object));
});
```

## Extending Tests

### Adding New Unit Tests

1. Create a new test file in the appropriate directory:
   - `tests/unit/server/` for server-side tests
   - `tests/unit/client/components/` for React components
   - `tests/unit/client/hooks/` for React hooks

2. Follow the existing patterns for mocking dependencies

3. Run the tests to ensure they pass

### Adding New UAT Tests

1. Add a new test case to the appropriate section in `UAT_DOCUMENT.md`
2. Follow the existing format (ID, Test Case, Test Steps, Expected Result, Status)
3. Update the `TEST_EXECUTION_PLAN.md` if needed

## Troubleshooting

### Common Issues

1. **Mock Data Issues**: Ensure mocks are properly set up and cleared between tests
2. **Import Errors**: Check relative paths in import statements
3. **Context Errors**: Verify that components requiring context (like AuthProvider) are properly wrapped

### Fixing Failed Tests

1. Run in watch mode to see real-time feedback:
   ```bash
   npm run test:watch
   ```

2. Use the debug output to identify the issue

3. Check mocks and test setup for potential problems

## Continuous Integration

Tests are automatically run on each pull request via GitHub Actions. See `.github/workflows/test.yml` for the configuration.

## Conclusion

By following this testing guide, you can ensure that CollaborateFlow maintains its functionality through comprehensive testing of critical components and user workflows.
