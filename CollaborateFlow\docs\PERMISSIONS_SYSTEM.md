# CollaborateFlow Permission System Technical Documentation

## Overview

This document provides technical details on the permission system implemented in CollaborateFlow. The system follows a team-based permission model where a user's permissions are determined by their role within each team, rather than having a single global role.

## Architecture

### Core Concepts

1. **Modules**: Functional areas of the application (project_management, estimations, quotes, etc.)
2. **Team Roles**: Roles a user can have within a team (admin, member, viewer)
3. **Permissions**: Actions a user can perform on a module (view, create, edit, delete)
4. **Super Admin**: Special users who are members of Team ID 1 and bypass all permission checks

### Database Schema

#### Modules Table

```sql
CREATE TABLE modules (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Team Role Permissions Table

```sql
CREATE TABLE team_role_permissions (
  id SERIAL PRIMARY KEY,
  team_role TEXT NOT NULL,
  module_id INTEGER REFERENCES modules(id) ON DELETE CASCADE,
  can_view BOOLEAN DEFAULT FALSE,
  can_create BOOLEAN DEFAULT FALSE,
  can_edit BOOLEAN DEFAULT FALSE,
  can_delete BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_role, module_id)
);
```

#### Default Permissions

- **Admin**: Full access to all modules
- **Member**: Can view, create, and edit but not delete
- **Viewer**: View-only access

## User Identification System

CollaborateFlow uses a dual user identification system:

1. **supabase_id (UUID)**:
   - Used in RLS policies with `users.supabase_id = auth.uid()`
   - Comes from Supabase Auth JWT tokens
   - Used when authenticating API requests

2. **user.id (Integer)**:
   - Used for database table relationships
   - Referenced in foreign keys (e.g., `team_members.user_id` references `users.id`)
   - Used in application queries between tables

When writing queries that involve both auth checks and table relationships, use joins like:

```sql
SELECT * FROM team_members tm
JOIN users u ON tm.user_id = u.id
WHERE u.supabase_id = auth.uid()
```

## Middleware Implementation

The permission system is implemented through middleware in `/server/middleware/permissions.ts`.

### Super Admin Check

Super admin status is determined by membership in Team ID 1:

```typescript
async function isSuperAdmin(userId: number): Promise<boolean> {
  try {
    const { data } = await supabase
      .from('team_members')
      .select('id')
      .eq('user_id', userId)
      .eq('team_id', 1)
      .single();
      
    return !!data;
  } catch (error) {
    console.error('Error checking super admin status:', error);
    return false;
  }
}
```

### Permission Middleware

#### Team-Specific Permissions

```typescript
requirePermission(module: string, action: string, teamIdParam: string = 'teamId')
```

Use this middleware for routes that operate within a specific team context (with team ID in URL parameters).

#### General Module Permissions

```typescript
requireModulePermission(module: string, action: string)
```

Use this middleware for routes that don't have a team context but still need module-based permission checks.

#### Team Membership Check

```typescript
requireTeamMembership(teamIdParam: string = 'teamId')
```

Use this middleware to verify a user is a member of the specified team.

## Row-Level Security (RLS) Policies

The database uses RLS policies to restrict data access at the database level:

```sql
-- Only team admins can modify team_role_permissions
CREATE POLICY modify_team_role_permissions ON team_role_permissions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      JOIN users u ON tm.user_id = u.id
      WHERE u.supabase_id = auth.uid() AND tm.role = 'admin'
    )
  );
```

## Usage Examples

### Team-Specific Routes

```typescript
import { requirePermission } from '../middleware/permissions';

// In routes with team context (team ID in URL)
router.post('/teams/:teamId/projects', 
  requirePermission('project_management', 'create'),
  async (req, res) => {
    // Create project logic
  }
);
```

### General Routes

```typescript
import { requireModulePermission } from '../middleware/permissions';

// For routes that don't have a team ID
router.get('/dashboard', 
  requireModulePermission('project_management', 'view'),
  async (req, res) => {
    // Dashboard logic
  }
);
```

### Team Membership Check

```typescript
import { requireTeamMembership } from '../middleware/permissions';

// Simple team membership verification
router.get('/teams/:teamId', 
  requireTeamMembership(),
  async (req, res) => {
    // Team view logic
  }
);
```

## Current Status

The permission system is currently implemented with stub functions that:

1. Check for super admin status (membership in Team ID 1)
2. Allow all actions for non-super-admin users

This is a compromise approach that provides the structure for a full permission system while allowing focus on project management features.

## Future Implementation

To fully implement the permission system:

1. Update the `hasPermission` function to check against the team_role_permissions table
2. Implement UI for role and permission management
3. Add more granular RLS policies

## Special Considerations

1. **Reserved IDs**:
   - Organization ID 1 is reserved for super admin organization
   - Team ID 1 is reserved for super admin team

2. **Multi-Tenant Isolation**:
   - Users can have different roles in different teams
   - Permissions are specific to the team context

3. **Database Migrations**:
   - The migration script is located at `/server/database/migrations/add-permission-structure.sql`
   - This can be run in the Supabase SQL Editor

## Troubleshooting

### Common Issues

1. **UUID vs Integer Type Mismatch**:
   - Remember to use `users.supabase_id = auth.uid()` in RLS policies
   - Use `JOIN users u ON entity.user_id = u.id` for table relationships

2. **Super Admin Access**:
   - If a super admin can't access resources, verify they are a member of Team ID 1
   - Check the isSuperAdmin function is working correctly

3. **Database Errors**:
   - If you encounter column not found errors, verify your database schema matches what's expected
