# CollaborateFlow Permissions Implementation TODO

This document outlines the tasks required to implement proper role-based access control in the CollaborateFlow system, ensuring proper multi-tenant isolation and super admin oversight.

## Technical Overview of the Permission System

### Row Level Security in Supabase

The permission system is built on Supabase's Row Level Security (RLS) policies, which provide database-level enforcement of access controls:

1. **Policy Evaluation**: When a query is made to Supabase, RLS policies are evaluated as WHERE clauses that filter which rows the user can access.

2. **Policy Types**:
   - `FOR SELECT` - Controls which rows can be read
   - `FOR INSERT` - Controls which rows can be inserted
   - `FOR UPDATE` - Controls which rows can be modified
   - `FOR DELETE` - Controls which rows can be deleted

3. **Policy Combining**: Multiple policies on a table combine with OR logic for the same operation, meaning a row is accessible if ANY matching policy allows it.

### Multi-Level Permission Structure

The system implements a hierarchical permission model with three key layers:

#### 1. User Role-Based Permissions

```
super_admin > admin > project_manager > user
```

- **Super Admin**: Full system access across all organizations
- **Admin**: Organization-wide management capabilities
- **Project Manager**: Project-level management with specific module permissions
- **User**: Basic access limited to their teams

#### 2. Team Membership Permissions

```
team_admin > team_member > team_viewer
```

- **Team Admin**: Can manage team settings and members
- **Team Member**: Can participate in team activities
- **Team Viewer**: Read-only access to team content

#### 3. Module-Based Permissions

Module permissions grant access to specific functions within the application:

- **Estimation**: Creating and managing cost estimates
- **Quotes**: Generating client quotes
- **Floor Plans**: Working with floor plan files
- **Approvals**: Client approval workflows
- **Analytics**: Reporting and analysis features

### Permission Flow During API Requests

1. **Authentication**: Supabase JWT token identifies the user
2. **API Request**: Client sends request to API endpoint
3. **Middleware Check**: Express middleware validates permissions
4. **Database Query**: Query is sent to Supabase
5. **RLS Evaluation**: Supabase applies RLS policies
6. **Response**: Only authorized data is returned

## Database RLS Policies

### Organization Permissions

- [ ] Implement super_admin view policy for all organizations
  ```sql
  CREATE POLICY super_admin_view_orgs ON organizations
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

- [ ] Implement user view policy for own organization only
  ```sql
  CREATE POLICY user_view_own_org ON organizations
    FOR SELECT USING (
      id = (SELECT organization_id FROM users WHERE users.id = auth.uid())
    );
  ```
- [ ] Restrict organization creation to super_admins only
  ```sql
  CREATE POLICY super_admin_create_orgs ON organizations
    FOR INSERT WITH CHECK (
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

- [ ] Restrict organization updates to super_admins only
  ```sql
  CREATE POLICY super_admin_update_orgs ON organizations
    FOR UPDATE USING (
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

- [ ] Restrict organization deletion to super_admins only
  ```sql
  CREATE POLICY super_admin_delete_orgs ON organizations
    FOR DELETE USING (
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

### Team Permissions

- [ ] Implement super_admin view policy for all teams
  ```sql
  CREATE POLICY super_admin_view_teams ON teams
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

- [ ] Restrict team view to team members only (for regular users)
  ```sql
  CREATE POLICY team_member_view ON teams
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM team_members 
        WHERE team_members.team_id = teams.id AND team_members.user_id = auth.uid()
      )
    );
  ```

- [ ] Restrict team creation to organization admins and super admins
  ```sql
  CREATE POLICY org_admin_create_team ON teams
    FOR INSERT WITH CHECK (
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() 
        AND (users.role = 'super_admin' OR 
            (users.role = 'admin' AND users.organization_id = NEW.organization_id))
      )
    );
  ```

- [ ] Restrict team updates to team admins and super admins
  ```sql
  CREATE POLICY team_admin_update ON teams
    FOR UPDATE USING (
      EXISTS (
        SELECT 1 FROM team_members 
        WHERE team_members.team_id = teams.id 
        AND team_members.user_id = auth.uid() 
        AND team_members.role = 'admin'
      )
      OR
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

- [ ] Restrict team deletion to team admins, organization admins, and super admins
  ```sql
  CREATE POLICY team_admin_delete ON teams
    FOR DELETE USING (
      EXISTS (
        SELECT 1 FROM team_members 
        WHERE team_members.team_id = teams.id 
        AND team_members.user_id = auth.uid() 
        AND team_members.role = 'admin'
      )
      OR
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() 
        AND (users.role = 'super_admin' OR 
            (users.role = 'admin' AND users.organization_id = teams.organization_id))
      )
    );
  ```

### Project Permissions

- [ ] Implement super_admin view policy for all projects
  ```sql
  CREATE POLICY super_admin_view_projects ON projects
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

- [ ] Restrict project view to team members only (for regular users)
  ```sql
  CREATE POLICY team_member_view_projects ON projects
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM team_members
        WHERE team_members.team_id = projects.team_id
        AND team_members.user_id = auth.uid()
      )
    );
  ```

- [ ] Restrict project creation to team members with admin/member roles and super admins
  ```sql
  CREATE POLICY team_member_create_project ON projects
    FOR INSERT WITH CHECK (
      EXISTS (
        SELECT 1 FROM team_members
        WHERE team_members.team_id = NEW.team_id
        AND team_members.user_id = auth.uid()
        AND team_members.role IN ('admin', 'member')
      )
      OR
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```
  
- [ ] Restrict project updates and deletion to project creators, team admins, and super admins
  ```sql
  CREATE POLICY project_update_delete ON projects
    FOR UPDATE USING (
      created_by_id = auth.uid()
      OR
      EXISTS (
        SELECT 1 FROM team_members
        WHERE team_members.team_id = projects.team_id
        AND team_members.user_id = auth.uid()
        AND team_members.role = 'admin'
      )
      OR
      EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
    );
  ```

## Backend Implementation

- [ ] Create permission checking utility function
  ```typescript
  export async function checkPermission(userId: number, action: string, resourceType: string, resourceId?: number) {
    // Implementation details as discussed
  }
  ```

- [ ] Create permission middleware for routes
  ```typescript
  export function requirePermission(resourceType: string, action: string) {
    return async (req: Request, res: Response, next: NextFunction) => {
      // Implementation details as discussed
    };
  }
  ```

- [ ] Add permission checks to all API routes
  - [ ] Organization routes
  - [ ] Team routes
  - [ ] Project routes
  - [ ] User management routes

## Frontend Implementation

- [ ] Create permission utility for UI components
  ```typescript
  export function usePermission(action: string, resourceType: string, resourceId?: number) {
    const { user } = useAuth();
    const [hasPermission, setHasPermission] = useState(false);
    
    useEffect(() => {
      // Fetch permission status from backend or local logic
    }, [user, action, resourceType, resourceId]);
    
    return hasPermission;
  }
  ```

- [ ] Implement conditional rendering based on permissions
  - [ ] Hide/show organization management for super admins only
  - [ ] Restrict team creation UI to organization admins
  - [ ] Disable team editing for non-admin team members
  - [ ] Conditionally render action buttons based on permissions

## Super Admin Dashboard

- [ ] Create super admin dashboard view
  - [ ] Cross-organization overview
  - [ ] User management interface
  - [ ] System health monitoring

- [ ] Implement user role management UI
  - [ ] Ability to change user roles
  - [ ] Organization assignment
  - [ ] Safeguards to prevent removal of last super admin

## Audit System

- [ ] Create audit logging table in database
  ```sql
  CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id BIGINT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  ```

- [ ] Implement audit logging middleware
  ```typescript
  export function logAuditEvent(req: Request, action: string, resourceType: string, resourceId: number, details?: any) {
    // Implementation details
  }
  ```

- [ ] Add audit log viewer for super admins

## Testing

- [ ] Test super admin access across organizations
- [ ] Test organization admin limitations
- [ ] Test regular user restrictions
- [ ] Verify team member role permissions
- [ ] Test cross-organization isolation

## Module-Based Permissions

### Database Schema

- [ ] Create module permissions table
  ```sql
  CREATE TABLE module_permissions (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT
  );
  ```

- [ ] Insert standard module permissions
  ```sql
  INSERT INTO module_permissions (name, description) VALUES
    ('create_estimation', 'Create cost estimations'),
    ('update_estimation', 'Modify existing estimations'),
    ('delete_estimation', 'Remove estimations'),
    ('create_quote', 'Create client quotes'),
    ('approve_quote', 'Approve quotes for client submission'),
    ('process_floor_plan', 'Run AI processing on floor plans'),
    ('manage_symbols', 'Add/edit symbols on floor plans'),
    ('view_analytics', 'Access analytics dashboard'),
    ('export_reports', 'Export system reports');
  ```

- [ ] Create role-permission mapping table
  ```sql
  CREATE TABLE role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role TEXT NOT NULL CHECK (role IN ('super_admin', 'admin', 'project_manager', 'member', 'viewer')),
    permission_id BIGINT REFERENCES module_permissions(id) ON DELETE CASCADE,
    UNIQUE(role, permission_id)
  );
  ```

- [ ] Create project-specific role assignment table
  ```sql
  CREATE TABLE project_roles (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT REFERENCES projects(id) ON DELETE CASCADE,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('project_manager', 'member', 'viewer')),
    UNIQUE(project_id, user_id)
  );
  ```

- [ ] Update users table to include project_manager role
  ```sql
  ALTER TABLE users
  DROP CONSTRAINT users_role_check,
  ADD CONSTRAINT users_role_check 
  CHECK (role IN ('super_admin', 'admin', 'project_manager', 'user'));
  ```

### Module-Based RLS Policies

- [ ] Implement RLS policies for estimation module
  ```sql
  CREATE POLICY estimation_module_access ON estimations
    FOR ALL USING (
      -- Super admin access
      EXISTS (
        SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'super_admin'
      )
      OR
      -- Role-based module permission check
      EXISTS (
        SELECT 1 FROM projects p
        JOIN teams t ON p.team_id = t.id
        LEFT JOIN project_roles pr ON pr.project_id = p.id AND pr.user_id = auth.uid()
        LEFT JOIN team_members tm ON tm.team_id = t.id AND tm.user_id = auth.uid()
        LEFT JOIN role_permissions rp1 ON rp1.role = pr.role
        LEFT JOIN role_permissions rp2 ON rp2.role = tm.role
        LEFT JOIN module_permissions mp1 ON mp1.id = rp1.permission_id
        LEFT JOIN module_permissions mp2 ON mp2.id = rp2.permission_id
        WHERE p.id = estimations.project_id
        AND (
          (mp1.name = 'view_estimation' OR mp2.name = 'view_estimation')
          OR (mp1.name = CASE WHEN TG_OP = 'SELECT' THEN 'view_estimation'
                            WHEN TG_OP = 'INSERT' THEN 'create_estimation' 
                            WHEN TG_OP = 'UPDATE' THEN 'update_estimation'
                            WHEN TG_OP = 'DELETE' THEN 'delete_estimation' END
            OR mp2.name = CASE WHEN TG_OP = 'SELECT' THEN 'view_estimation'
                            WHEN TG_OP = 'INSERT' THEN 'create_estimation' 
                            WHEN TG_OP = 'UPDATE' THEN 'update_estimation'
                            WHEN TG_OP = 'DELETE' THEN 'delete_estimation' END)
        )
      )
    );
  ```

- [ ] Create similar policies for other modules (quotes, floor plans, etc.)

### Backend Implementation

- [ ] Enhance permission checking to include module capabilities
  ```typescript
  export async function checkPermission(
    userId: number, 
    action: string, 
    resourceType: string, 
    resourceId?: number,
    moduleAction?: string
  ) {
    // Check both resource permissions and module permissions
    // Implementation details as discussed
  }
  ```

- [ ] Update API routes to check module permissions
  ```typescript
  router.post('/projects/:projectId/estimations', 
    authenticate,
    async (req, res) => {
      // Check both project access and module permission
      const hasPermission = await checkPermission(
        req.user?.id, 
        'create', 
        'project', 
        parseInt(req.params.projectId), 
        'create_estimation'
      );
      
      if (!hasPermission) {
        return res.status(403).json({ message: 'Forbidden' });
      }
      
      // Proceed with estimation creation
    }
  );
  ```

### Frontend Implementation

- [ ] Create UI permission hooks for module capabilities
  ```typescript
  export function useModulePermission(moduleAction: string, projectId?: number) {
    // Check if user has permission for specific module action
    // Return boolean for conditional rendering
  }
  ```

- [ ] Update UI components to check module permissions
  ```jsx
  {useModulePermission('create_estimation', projectId) && (
    <Button onClick={handleCreateEstimation}>New Estimation</Button>
  )}
  ```

- [ ] Create permission management UI for admins
  - [ ] Role assignment interface
  - [ ] Module permission configuration
  - [ ] Project-specific role management

## Custom Role Management

### Database Schema Extensions

- [ ] Create custom roles table
  ```sql
  CREATE TABLE custom_roles (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    base_role TEXT NOT NULL CHECK (base_role IN ('admin', 'project_manager', 'user')),
    organization_id BIGINT REFERENCES organizations(id),
    is_system_wide BOOLEAN DEFAULT FALSE,
    created_by_id BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  ```

- [ ] Update users table to support custom roles
  ```sql
  ALTER TABLE users 
  ADD COLUMN custom_role_id BIGINT REFERENCES custom_roles(id) ON DELETE SET NULL;
  ```

- [ ] Create custom role permissions mapping
  ```sql
  CREATE TABLE custom_role_permissions (
    id BIGSERIAL PRIMARY KEY,
    custom_role_id BIGINT NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
    permission_id BIGINT NOT NULL REFERENCES module_permissions(id) ON DELETE CASCADE,
    UNIQUE(custom_role_id, permission_id)
  );
  ```

### Custom Role Backend Implementation

- [ ] Create role management service
  ```typescript
  export class RoleManagementService {
    // Create new custom role (super_admin only)
    async createCustomRole(roleData, userId) {
      // Verify user is super_admin
      const { data: userData } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();
        
      if (userData?.role !== 'super_admin') {
        throw new Error('Only super admins can create custom roles');
      }
      
      // Create role and return
      // ...
    }
    
    // Other methods for managing roles and permissions
  }
  ```

- [ ] Create API endpoints for role management
  ```typescript
  // Custom role management routes (super_admin only)
  router.post('/roles', requireRole(['super_admin']), async (req, res) => {
    try {
      const roleService = new RoleManagementService();
      const newRole = await roleService.createCustomRole(req.body, req.user.id);
      res.status(201).json(newRole);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  ```

### Module Permission Management

- [ ] Create functions for dynamically adding new module permissions
  ```typescript
  export async function addModulePermission(name, description, userId) {
    // Verify user is super_admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();
      
    if (userData?.role !== 'super_admin') {
      throw new Error('Only super admins can add module permissions');
    }
    
    // Add new permission
    const { data, error } = await supabase
      .from('module_permissions')
      .insert({ name, description })
      .select()
      .single();
      
    if (error) throw error;
    
    // Automatically grant to super_admin role
    await supabase
      .from('role_permissions')
      .insert({ role: 'super_admin', permission_id: data.id });
      
    return data;
  }
  ```

- [ ] Create API endpoints for module permission management
  ```typescript
  router.post('/permissions', requireRole(['super_admin']), async (req, res) => {
    try {
      const { name, description } = req.body;
      const newPermission = await addModulePermission(name, description, req.user.id);
      res.status(201).json(newPermission);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  ```

### Frontend Implementation for Role & Permission Management

- [ ] Create role management UI for super admins
  - [ ] Role creation form with name, description, and base role selection
  - [ ] Permission assignment interface
  - [ ] Role assignment to users

- [ ] Implement module permission management UI
  - [ ] Add new permission form
  - [ ] Assign permissions to roles
  - [ ] Permission search and filtering

- [ ] Update permission checking to handle custom roles
  ```typescript
  export async function checkPermission(userId, action, resourceType, resourceId, moduleAction) {
    // Get user with custom role
    const { data: userData } = await supabase
      .from('users')
      .select('role, custom_role_id')
      .eq('id', userId)
      .single();
      
    // Super admin check
    if (userData?.role === 'super_admin') return true;
    
    // Custom role check
    if (userData?.custom_role_id && moduleAction) {
      const { data: hasPermission } = await supabase
        .from('custom_role_permissions')
        .select('id')
        .eq('custom_role_id', userData.custom_role_id)
        .eq('permission_id', await getPermissionId(moduleAction))
        .single();
        
      if (hasPermission) return true;
    }
    
    // Continue with other permission checks
    // ...
  }
  ```

## Documentation

- [ ] Update README with role-based permission details
- [ ] Create role permission matrix documentation
- [ ] Document super admin capabilities and safeguards
- [ ] Create module permission matrix showing which roles can access which modules
- [ ] Document custom role creation process
- [ ] Create guide for extending the permission system with new modules
