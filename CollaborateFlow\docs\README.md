# 📚 Technical Documentation

This folder contains all technical documentation for developers and the technical team.

## 🧪 Testing Documentation

- **[UNIT_TESTING_PLAN.md](./UNIT_TESTING_PLAN.md)** - Comprehensive unit testing strategy
- **[TEST_EXECUTION_PLAN.md](./TEST_EXECUTION_PLAN.md)** - Technical testing execution guide
- **[HOW_TO_TEST.md](./HOW_TO_TEST.md)** - Developer testing instructions

## 🤖 AI & Integration

- **[AI_INTEGRATION.md](./AI_INTEGRATION.md)** - AI implementation details and MCP integration

## 🔐 Security & Permissions

- **[PERMISSIONS_SYSTEM.md](./PERMISSIONS_SYSTEM.md)** - Permission system architecture
- **[permission-system.md](./permission-system.md)** - Permission implementation details
- **[user-management.md](./user-management.md)** - User management system

## 📋 Implementation Tasks

- **[PERMISSIONS_TODO.md](./PERMISSIONS_TODO.md)** - Permission system implementation tasks
- **[REAL_IMPLEMENTATION_TODO.md](./REAL_IMPLEMENTATION_TODO.md)** - General implementation tasks
- **[ROLES_IMPLEMENTATION_TODO.md](./ROLES_IMPLEMENTATION_TODO.md)** - Role system implementation tasks

---

**Note**: For UAT testing, please use the documents in the root directory:
- [UAT_QUICK_START.md](../UAT_QUICK_START.md)
- [UAT_DOCUMENT.md](../UAT_DOCUMENT.md)
- [SETUP_UAT.md](../SETUP_UAT.md)
