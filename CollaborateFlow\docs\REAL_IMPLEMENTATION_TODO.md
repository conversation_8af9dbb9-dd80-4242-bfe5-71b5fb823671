# CollaborateFlow: Real Implementation TODOs

This document outlines the steps required to replace the mock API with real Supabase database integration.

## Database Connection & Setup

- [x] Configure proper Supabase client initialization with error handling
- [x] Set up connection pooling for optimal performance
- [x] Implement alternative REST API approach for Docker and cross-environment compatibility
- [x] Ensure proper environment variable configuration in all environments

## Data Models & Schemas

- [x] Define TypeScript interfaces for all entities (Users, Teams, Projects, Tasks, etc.)
- [x] Create corresponding database tables in Supabase
- [x] Set up proper relationships and foreign keys
- [x] Configure appropriate indexes for performance

## Storage Service Implementation

- [x] Create Supabase REST API implementation (SupabaseStorage) for cross-environment compatibility
- [x] Implement conditional storage selection based on environment
- [x] Ensure Docker container compatibility with proper network configuration

### Users & Authentication
- [x] Implement user profile retrieval and management
- [x] Connect Express session with Supabase authentication
- [ ] Add user preference and settings storage

### Teams
- [x] Implement `getTeams(userId)` - Get all teams a user belongs to
- [x] Implement `getTeam(teamId)` - Get a specific team
- [x] Implement `createTeam(team)` - Create a new team
- [x] Implement `updateTeam(teamId, data)` - Update team details
- [x] Implement `deleteTeam(teamId)` - Delete a team
- [x] Implement team member management functions

### Projects
- [x] Implement `getProjects(userId)` - Get all projects a user has access to
- [x] Implement `getProject(projectId)` - Get a specific project
- [x] Implement `createProject(project)` - Create a new project
- [x] Implement `updateProject(projectId, data)` - Update project details
- [x] Implement `deleteProject(projectId)` - Delete a project
- [x] Implement project member management functions

### Columns (Kanban Board)
- [x] Implement `getColumns(projectId)` - Get columns for a project
- [x] Implement `createColumn(column)` - Create a new column
- [x] Implement `updateColumn(columnId, data)` - Update column details
- [x] Implement `deleteColumn(columnId)` - Delete a column
- [x] Implement column reordering functionality

### Tasks
- [x] Implement `getTasks(projectId)` - Get tasks for a project
- [x] Implement `getTask(taskId)` - Get a specific task
- [x] Implement `createTask(task)` - Create a new task
- [x] Implement `updateTask(taskId, data)` - Update task details
- [x] Implement `deleteTask(taskId)` - Delete a task
- [x] Implement task assignment and status tracking

### Floor Plans & Estimations
- [x] Implement floor plan storage and versioning
- [ ] Connect with AI processing (if applicable)
- [x] Implement symbol detection and material calculations
- [x] Store and track estimation data

## Security & Authorization

- [x] Implement row-level security in Supabase
- [x] Add role-based access control
- [x] Set up data validation before database operations
- [x] Implement proper error handling with secure error messages
- [ ] Add audit logging for sensitive operations

## Performance Optimizations

- [x] Implement efficient query patterns
- [ ] Add caching for frequently accessed data
- [ ] Optimize response size with selective field returns
- [x] Set up appropriate indexes

## API URL Standardization and Client-side Improvements

### API URL Construction
- [ ] Standardize all components to use the centralized `apiRequest` utility from `queryClient.ts`
- [ ] Remove manual URL construction patterns (`window.location.hostname` checks)
- [ ] Ensure consistent error handling across all API calls
- [ ] Create unit tests for API URL construction edge cases

### Component Improvements
- [ ] Implement edit functionality for teams
- [ ] Implement edit functionality for organizations
- [ ] Implement edit functionality for projects
- [ ] Implement edit functionality for tasks
- [ ] Add deletion confirmation dialogs
- [ ] Implement consistent delete functionality for all entity types

### Dual ID System
- [ ] Create utility functions for consistent ID handling
- [ ] Add validation for ID types across the application
- [ ] Document the dual ID system for developers
- [ ] Implement automatic ID resolution on the server side

## Migration Strategy

- [x] Create a feature flag system to gradually switch from mock to real data
- [ ] Plan data migration for any existing development data
- [x] Develop a testing strategy for validating real implementations
- [x] Create a rollback plan if issues are encountered

## Example Implementation Pattern

```typescript
// Example pattern for real Supabase implementation
import { createClient } from '@supabase/supabase-js';
import { Team, TeamMember } from '../types';

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_KEY || ''
);

export async function getTeams(userId: number): Promise<Team[]> {
  try {
    // Get teams where the user is a member
    const { data: teamMembers, error: memberError } = await supabase
      .from('team_members')
      .select('team_id')
      .eq('user_id', userId);
    
    if (memberError) throw memberError;
    
    const teamIds = teamMembers.map(member => member.team_id);
    
    // Get the actual team data
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .in('id', teamIds);
    
    if (teamsError) throw teamsError;
    
    return teams;
  } catch (error) {
    console.error('Error fetching teams:', error);
    throw error;
  }
}

export async function createTeam(team: Omit<Team, 'id'>): Promise<Team> {
  try {
    const { data, error } = await supabase
      .from('teams')
      .insert(team)
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error creating team:', error);
    throw error;
  }
}
```

## Conditional Testing

You can implement a strategy to conditionally use mock or real data based on environment:

```typescript
// Conditionally use mock or real implementation
if (process.env.USE_MOCK_DATA === 'true' || process.env.NODE_ENV === 'development') {
  app.use(mockApiMiddleware);
} else {
  // Use real database connections
  // No middleware needed as the actual route handlers will use Supabase
}
```

This TODO list serves as a comprehensive guide for transitioning from mock data to real Supabase implementation in CollaborateFlow.
