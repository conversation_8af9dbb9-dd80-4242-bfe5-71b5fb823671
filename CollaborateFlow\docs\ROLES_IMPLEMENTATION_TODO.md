# Role-Based Team Management Implementation Plan

## Core Requirements

1. **Reserved IDs for Super Admin**
   - Organization ID 1 (Coelec) is reserved for super admin
   - Team ID 1 (Default Team) is reserved for super admin

2. **Role-Based Team Creation**
   - Super admins can assign any organization when creating a team
   - Regular admins can only create teams within their own organization

3. **Roles UI and Functionality**
   - Add UI for role management
   - Implement role-based access control

## Implementation Tasks

### 1. Database Schema Updates

- [ ] Add `role` field to the `users` table with values: 'super_admin', 'admin', 'user'
- [ ] Ensure organization ID 1 and team ID 1 are properly set up for super admin
- [ ] Add RLS (Row Level Security) policies in Supabase based on user roles

### 2. Backend Implementation

- [ ] Update TeamService to implement role-based logic:
  ```typescript
  // In TeamService.createTeam
  // Check user role before assigning organization
  const { data: userWithRole } = await supabase
    .from('users')
    .select('role, organization_id')
    .eq('id', userId)
    .single();
    
  // If super_admin, allow organization override
  // If regular admin, force organization to user's organization
  const orgId = 
    (userWithRole?.role === 'super_admin' && team.organizationId) ?
    team.organizationId : userWithRole?.organization_id || 1;
  ```

- [ ] Implement role check middleware for protected routes
  ```typescript
  // Role-based middleware
  export const requireRole = (roles: string[]) => {
    return async (req: Request, res: Response, next: NextFunction) => {
      const userId = req.user?.id;
      if (!userId) return res.status(401).json({ message: 'Unauthorized' });
      
      const { data } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();
        
      if (!data || !roles.includes(data.role)) {
        return res.status(403).json({ message: 'Forbidden' });
      }
      
      next();
    };
  };
  ```

- [ ] Update API routes to use role-based middleware
  ```typescript
  // Only super_admin can create teams with specific organization
  router.post('/with-org', requireRole(['super_admin']), async (req, res) => {
    // Handle team creation with specific organization
  });
  ```

### 3. Frontend Implementation

- [ ] Add role selection in user management UI
- [ ] Create role-based navigation and access control
- [ ] Update team creation form to show organization selection only for super admins
- [ ] Add visual indicators for reserved teams/organizations

### 4. Testing

- [ ] Test super admin can create teams in any organization
- [ ] Test regular admin can only create teams in their organization
- [ ] Test access control prevents unauthorized actions
- [ ] Verify reserved IDs work correctly

## Additional Considerations

- Consider implementing a more sophisticated RBAC (Role-Based Access Control) system for future scalability
- Document the role system in the application documentation
- Consider adding an audit log for role changes and sensitive operations
- Implement migration script to set up initial roles for existing users
