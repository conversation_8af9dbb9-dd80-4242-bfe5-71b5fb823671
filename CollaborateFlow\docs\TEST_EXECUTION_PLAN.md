# CollaborateFlow - Test Execution Plan

## Introduction

This Test Execution Plan outlines the approach for implementing, executing, and validating both Unit Tests and User Acceptance Tests (UAT) for the CollaborateFlow application. It provides a structured timeline and methodology for ensuring comprehensive test coverage across all critical components.

## Testing Scope

This plan covers two primary types of testing:

1. **Unit Testing**: Testing of individual components, functions, and classes in isolation
2. **User Acceptance Testing (UAT)**: Testing the application from an end-user perspective to validate functionality

## Test Environment Setup

### Unit Testing Environment

- **Framework**: Vitest with React Testing Library
- **Environment**: Local development with mocked dependencies
- **Dependencies**: JSDOM, MSW (Mock Service Worker), Testing Library User Event

### UAT Testing Environment

- **Environment**: Containerized Docker deployment
- **Database**: Supabase instance with test data
- **User Accounts**: Test accounts with various permission levels

## Test Data Requirements

### Unit Test Data

Unit tests will use mocked data for:

- User profiles with different roles (super_admin, admin, user)
- Teams and organizations
- Projects, tasks, and floor plans
- Mock API responses

### UAT Test Data

For UAT, prepare a real test database with:

- 3 organizations
- 5 teams across organizations
- 10 users with different roles
- 5 sample projects with floor plans
- Sample estimation data

## Test Execution Strategy

### Phase 1: Unit Test Implementation (Week 1-2)

#### Week 1: Authentication and Role-Based Access

1. **Day 1-2**: Implement authentication middleware tests
   - Test dual ID system (UUID/numeric ID)
   - Test super admin detection
   - Test authentication flows

2. **Day 3-4**: Implement SupabaseStorage tests
   - Test role-based team visibility
   - Test organization access control
   - Test camelCase to snake_case conversion

3. **Day 5**: Implement React hooks tests
   - Test useOrganizations hook
   - Test useSuperAdmin hook
   - Test useSupabaseAuth hook

#### Week 2: UI Components and Business Logic

1. **Day 1-2**: Implement component tests
   - Test AddTeamDialog component
   - Test ProjectCreationWizard component
   - Test FloorPlanViewer component

2. **Day 3-4**: Implement project management tests
   - Test project creation and update functions
   - Test Kanban board functionality
   - Test task management

3. **Day 5**: Implement estimation tests
   - Test material estimation logic
   - Test labor estimation logic
   - Test quote generation

### Phase 2: UAT Test Execution (Week 3-4)

#### Week 3: Authentication, Teams, and Projects

1. **Day 1**: UAT setup and test data preparation
   - Deploy test environment
   - Create test users and organizations
   - Prepare test scenarios

2. **Day 2-3**: Authentication and user management testing
   - Test AUTH-1 through AUTH-5 scenarios
   - Test role-based access (RBAC-1 through RBAC-4)

3. **Day 4-5**: Team and project management testing
   - Test TEAM-1 through TEAM-4 scenarios
   - Test PROJ-1 through PROJ-4 scenarios
   - Test KAN-1 through KAN-4 scenarios

#### Week 4: Floor Plans, Estimation, and Quotes

1. **Day 1-2**: Floor plan processing testing
   - Test FP-1 through FP-3 scenarios
   - Test SYM-1 through SYM-3 scenarios

2. **Day 3-4**: Estimation and quote management testing
   - Test MAT-1 through MAT-3 scenarios
   - Test LAB-1 through LAB-2 scenarios
   - Test QUOTE-1 through QUOTE-3 scenarios

3. **Day 5**: Integration and performance testing
   - Test E2E-1 through E2E-2 scenarios
   - Test PERF-1 through PERF-2 scenarios

## Test Prioritization

Tests should be prioritized based on:

1. **Critical Path Functionality**: Authentication, team management, and project creation
2. **User Role Validation**: Super admin, admin, and regular user permissions
3. **Data Integrity**: Ensuring proper database operations and field naming
4. **Integration Points**: Authentication sync between Supabase and Express

## Feature Test Implementation Plan

### 1. Role-Based Permissions System

Based on the existing multi-level permissions system with user roles, team membership roles, and module-specific permissions, implement tests that verify:

```typescript
// Example test structure for role-based access
describe('Role-Based Access Control', () => {
  it('super_admin can see all teams across organizations', async () => {
    // Arrange: Create super admin user
    // Act: Fetch teams
    // Assert: Can see teams from all organizations
  });
  
  it('admin can only see teams within their organization', async () => {
    // Arrange: Create admin user
    // Act: Fetch teams
    // Assert: Can only see teams in their organization
  });
  
  it('regular user can only see teams they are members of', async () => {
    // Arrange: Create regular user
    // Act: Fetch teams
    // Assert: Can only see teams they belong to
  });
});
```

### 2. Authentication Synchronization

Based on the dual ID system (UUID/numeric ID) and authentication sync between Supabase and Express:

```typescript
// Example test structure for auth synchronization
describe('Authentication Synchronization', () => {
  it('enriches user with numeric ID from database', async () => {
    // Arrange: Setup mock request with Supabase UUID
    // Act: Call enrichUserMiddleware
    // Assert: User object now contains numericId
  });
  
  it('properly synchronizes sign-out between Supabase and Express', async () => {
    // Test the sign-out flow
  });
});
```

### 3. Team Management

Based on the organization dropdown visibility for super admins:

```typescript
// Example test structure for AddTeamDialog
describe('AddTeamDialog Component', () => {
  it('shows organization dropdown for super admins', async () => {
    // Setup super admin mock
    // Render component
    // Assert organization dropdown is visible
  });
  
  it('hides organization dropdown for regular admins', async () => {
    // Setup admin mock
    // Render component
    // Assert organization dropdown is not visible
  });
});
```

## Test Reporting

### Unit Test Reporting

- Use Vitest's built-in coverage reporting
- Set up GitHub Actions to run tests on PR and report coverage
- Aim for at least 80% code coverage for critical modules

### UAT Test Reporting

- Create a shared spreadsheet tracking UAT test cases
- Update the UAT_DOCUMENT.md status column as tests are executed
- Document any issues found during testing with screenshots

## Addressing Partially Implemented Features

For features marked as partially implemented (⚠️) or not implemented (❌) in the UAT document:

1. **Floor Plan Processing (SYM-1)**: 
   - Implement mock symbol detection for testing
   - Add toggle for AI vs. manual detection modes

2. **Material Estimation (MAT-1)**:
   - Create test mode that generates placeholder estimates
   - Implement manual override capabilities

3. **Digital Signature (SIG-1, SIG-2, SIG-3)**:
   - Implement local-only signature capture for testing
   - Mock email notifications

## Common Test Scenarios

### Example: Testing Team Creation with Different User Roles

```typescript
// Unit test for team creation permissions
async function testTeamCreationPermissions() {
  // Test super admin can create team in any organization
  const superAdminResult = await createTeamWithRole('super_admin', {
    name: 'Test Team',
    organizationId: 999 // Different org
  });
  expect(superAdminResult.success).toBe(true);
  
  // Test admin can only create team in their organization
  const adminResult = await createTeamWithRole('admin', {
    name: 'Test Team',
    organizationId: 999 // Different org
  });
  expect(adminResult.success).toBe(false);
  expect(adminResult.error).toContain('permission');
  
  // Test with correct organization
  const validAdminResult = await createTeamWithRole('admin', {
    name: 'Test Team',
    organizationId: 1 // Their org
  });
  expect(validAdminResult.success).toBe(true);
}
```

### Example: UAT Scenario for End-to-End Project Flow

For E2E-1 (Complete Project Workflow):

1. Login as project manager
2. Create new project with test client
3. Upload sample floor plan PDF
4. Use manual annotation mode to mark electrical symbols
5. Generate estimation based on annotations
6. Create and send quote to test email
7. Login as client and approve quote
8. Verify project status updates accordingly

## Conclusion

This test execution plan provides a structured approach to implementing and running both unit tests and UAT tests for the CollaborateFlow application. By following this plan, the team can ensure comprehensive test coverage across all critical components and user workflows, leading to a more robust and reliable application.

## Appendix: Test Environment Setup Scripts

### Set Up Test Database

```bash
# Create test data for UAT
node scripts/setup-test-data.js
```

### Run All Unit Tests

```bash
# Run all unit tests with coverage
npm run test:coverage
```
