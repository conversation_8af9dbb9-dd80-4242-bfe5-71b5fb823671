# CollaborateFlow Permission System

## Compromise Approach Implementation

This document explains how we've implemented a compromise approach to the permission system in CollaborateFlow. This approach allows you to focus on project management features now while laying the groundwork for a full permission system later.

## What's Included

1. **Database Structure**
   - Tables for modules, role permissions, and custom roles
   - Database functions for permission checks (stubbed to return true)
   - Row-level security policies

2. **Permission Middleware**
   - Stub implementation of permission checks
   - Role-based middleware
   - Team membership verification

## How to Apply the Changes

Run the permission structure script:

```bash
node scripts/apply-permission-structure.js
```

This will create all necessary database tables and functions.

## Using the Permission Middleware

When implementing project management features, you can use the permission middleware to structure your code correctly for future implementation:

### 1. Import the Middleware

```typescript
import { requirePermission, requireRole, requireTeamMembership } from '../middleware/permissions';
```

### 2. Apply to Routes

```typescript
// Check module-specific permissions
router.post('/projects', 
  requirePermission('project_management', 'create'),
  async (req, res) => {
    // Create project logic
  }
);

// Check user role
router.get('/admin/settings', 
  requireRole(['super_admin', 'admin']),
  async (req, res) => {
    // Admin settings logic
  }
);

// Check team membership
router.get('/teams/:teamId/projects', 
  requireTeamMembership(),
  async (req, res) => {
    // Team projects logic
  }
);
```

## Current Behavior

With this compromise approach:

1. The `requirePermission` middleware will **always allow access** since the `hasPermission` function is stubbed to return `true`
2. The `requireRole` middleware **will check actual user roles** against the database
3. The `requireTeamMembership` middleware **will verify team membership** (with super_admin bypass)

## Future Implementation

When you're ready to implement the full permission system:

1. Update the `hasPermission` function in `permissions.ts` to check against the role_permissions table
2. Implement the UI for role and permission management
3. Update RLS policies to enforce permissions at the database level

All routes will already have the correct middleware in place, so you won't need to modify them when enabling the full permission system.

## Module Names

The following module names are pre-configured in the database:

- `project_management` - Core project management features
- `estimations` - Cost estimation features
- `quotes` - Client quote generation
- `floor_plans` - Floor plan management and analysis
- `approvals` - Client approval workflow

Use these exact names when calling `requirePermission`.
