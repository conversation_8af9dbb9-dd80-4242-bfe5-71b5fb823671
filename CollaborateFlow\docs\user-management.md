# CollaborateFlow User Management

## Creating Users

The CollaborateFlow application uses Supabase exclusively for both authentication and database operations. Users must be created in both Supabase Auth and the database users table with proper linking via the `supabase_id` field.

### Using the Sync Users Script

An interactive script has been created to manage users in both Supabase Auth and the database:

```bash
node fix-sync-users.js
```

This provides options to:
1. Create new users in both systems
2. Sync existing Auth users to the database
3. Reset the database
4. List Supabase Auth users

### Creating a User with fix-sync-users.js

1. Run the interactive script:

```bash
node fix-sync-users.js
```

2. Select option `1` to create a new user

3. You'll be prompted for:
   - Email address (used for login)
   - Password (must meet Supabase security requirements)
   - Full name
   - Role (super_admin, admin, or user)
   - Organization ID (defaults to 1)

4. The script will create the user in Supabase Auth first, then add the corresponding database record with proper `supabase_id` linking

### Example Session

```
===== CREATE NEW USER =====
This will create a user in both Supabase Auth and your database tables
------------------------------------------------
Email: <EMAIL>
Password: securepassword123
Full Name: John Doe
Role (super_admin, admin, user) [default: user]: super_admin
Organization ID [default: 1]: 1

Creating user in Supabase Auth...
Auth user created successfully!
Supabase Auth User ID: abc123-uuid-example

Creating user in database with ID: 1
User created successfully!
```

### Note on Docker Environments

When running in Docker, you can alternatively use the convenience script, but the interactive script is preferred for proper Auth-DB synchronization:

```bash
./create-user.sh <EMAIL> password "Full Name"
```

### What the Scripts Do

1. Create a user in the Supabase Authentication system first
2. Create a matching record in the database users table with the `supabase_id` field linking to Auth
3. Properly handle user synchronization to prevent 500 errors when accessing teams and projects
4. Optionally add users to teams with appropriate roles

## Notes

- The script must be run from the project root directory
- The Docker containers must be running for user creation to work
- If email verification is enabled in Supabase, users will need to verify their email before they can log in
- For security reasons, avoid using this script with sensitive passwords in production environments
