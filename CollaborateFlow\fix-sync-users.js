// fix-sync-users.js - A fixed version of the user creation script
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import readline from 'readline';

dotenv.config();

// Supabase connection info
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_KEY or SUPABASE_SERVICE_KEY is required');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function showMenu() {
  console.clear();
  console.log('===== SUPABASE USER MANAGEMENT =====');
  console.log('1. Create new user (Auth + Database)');
  console.log('2. Sync existing Auth user to database');
  console.log('3. Reset database (clear all data)');
  console.log('4. List Supabase Auth users');
  console.log('5. Exit');
  
  const choice = await askQuestion('\nEnter your choice (1-5): ');
  
  switch (choice) {
    case '1':
      await createNewUser();
      break;
    case '2':
      await syncExistingUser();
      break;
    case '3':
      await resetDatabase();
      break;
    case '4':
      await listAuthUsers();
      break;
    case '5':
      console.log('Exiting program.');
      rl.close();
      process.exit(0);
      break;
    default:
      console.log('Invalid choice. Please try again.');
  }
  
  await askQuestion('\nPress Enter to continue...');
  await showMenu();
}

async function createNewUser() {
  console.clear();
  console.log('===== CREATE NEW USER =====');
  console.log('This will create a user in both Supabase Auth and your database tables');
  console.log('------------------------------------------------');
  
  try {
    // Get user details
    const email = await askQuestion('Email: ');
    const password = await askQuestion('Password: ');
    const fullName = await askQuestion('Full Name: ');
    const role = await askQuestion('Role (super_admin, admin, user) [default: user]: ') || 'user';
    const organizationId = parseInt(await askQuestion('Organization ID [default: 1]: ') || '1');
    
    // Step 1: Create user in Supabase Auth
    console.log('\nCreating user in Supabase Auth...');
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: fullName
      }
    });
    
    if (authError) {
      console.error('Error creating user in Supabase Auth:', authError.message);
      
      // If this fails due to lacking admin rights, try regular signup
      console.log('Attempting regular signup...');
      const { data: signupUser, error: signupError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          }
        }
      });
      
      if (signupError) {
        console.error('Regular signup also failed:', signupError.message);
        return;
      }
      
      console.log('Auth user created successfully via regular signup!');
      console.log('Supabase Auth User ID:', signupUser.user.id);
      
      // Use signup user for database creation
      await createDatabaseUser(signupUser.user, email, fullName, role, organizationId);
      return;
    }
    
    console.log('Auth user created successfully via admin API!');
    console.log('Supabase Auth User ID:', authUser.user.id);
    
    // Step 2: Create database user
    await createDatabaseUser(authUser.user, email, fullName, role, organizationId);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

async function createDatabaseUser(authUser, email, fullName, role, organizationId) {
  try {
    // Find next available user ID
    const { data: maxUser } = await supabase
      .from('users')
      .select('id')
      .order('id', { ascending: false })
      .limit(1);
    
    const nextId = maxUser && maxUser[0] ? maxUser[0].id + 1 : 1; // Start from 1 if no users exist
    
    // Create user in database with minimal required fields
    console.log('\nCreating user in database with ID:', nextId);
    
    // Try with the most minimal fields first
    const minimalUser = {
      id: nextId,
      username: email,
      email: email,
      password: 'placeholder-not-real-password-uses-supabase-auth', // Required field
      supabase_id: authUser.id
    };
    
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .insert([minimalUser])
      .select();
    
    if (dbError) {
      console.error('Error creating user in database:', dbError.message);
      return null;
    }
    
    console.log('User created successfully!');
    console.log(dbUser);
    
    // Try to update additional fields
    try {
      await supabase
        .from('users')
        .update({
          full_name: fullName,
          organization_id: organizationId
        })
        .eq('id', dbUser[0].id);
      
      console.log('Additional user details updated.');
    } catch (updateError) {
      console.log('Note: Could not update additional fields - some columns may not exist.');
    }
    
    // Add to default team if super_admin
    if (role === 'super_admin') {
      await addUserToDefaultTeam(dbUser[0].id);
    }
    
    return dbUser[0];
  } catch (error) {
    console.error('Error creating database user:', error);
    return null;
  }
}

async function addUserToDefaultTeam(userId) {
  try {
    console.log('\nAdding user to default team...');
    
    const { error } = await supabase
      .from('team_members')
      .insert({
        team_id: 1,
        user_id: userId,
        role: 'admin',
        created_at: new Date().toISOString()
      });
    
    if (error) {
      console.error('Error adding user to default team:', error.message);
    } else {
      console.log('User added to default team successfully!');
    }
  } catch (error) {
    console.error('Error adding user to team:', error);
  }
}

async function syncExistingUser() {
  console.clear();
  console.log('===== SYNC EXISTING AUTH USER =====');
  console.log('This will create a database entry for an existing Supabase Auth user');
  console.log('------------------------------------------------');
  
  try {
    // First, list available Auth users
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error listing Auth users:', authError.message);
      return;
    }
    
    if (!authUsers || authUsers.users.length === 0) {
      console.log('No Auth users found. Please create a user first.');
      return;
    }
    
    // Display available users
    console.log('\nAvailable Auth users:');
    authUsers.users.forEach((user, index) => {
      console.log(`${index+1}. ${user.email} (ID: ${user.id})`);
    });
    
    // Get user selection
    const selection = await askQuestion('\nSelect user to sync (number): ');
    const selectedIndex = parseInt(selection) - 1;
    
    if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= authUsers.users.length) {
      console.log('Invalid selection.');
      return;
    }
    
    const selectedUser = authUsers.users[selectedIndex];
    console.log(`\nSelected user: ${selectedUser.email} (ID: ${selectedUser.id})`);
    
    // Get additional details
    const fullName = await askQuestion(`Full Name [${selectedUser.user_metadata?.full_name || ''}]: `) || 
                     selectedUser.user_metadata?.full_name || 
                     selectedUser.email.split('@')[0];
    
    const role = await askQuestion('Role (super_admin, admin, user) [default: user]: ') || 'user';
    const organizationId = parseInt(await askQuestion('Organization ID [default: 1]: ') || '1');
    
    // Create database user
    await createDatabaseUser(selectedUser, selectedUser.email, fullName, role, organizationId);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

async function resetDatabase() {
  console.clear();
  console.log('===== RESET DATABASE =====');
  console.log('This will clear ALL database tables including default records');
  console.log('WARNING: This does NOT clear Supabase Auth users');
  console.log('------------------------------------------------');
  
  const confirm = await askQuestion('Are you sure? (yes/no): ');
  
  if (confirm.toLowerCase() !== 'yes') {
    console.log('Operation cancelled');
    return;
  }
  
  try {
    console.log('Clearing ALL existing data...');
    
    // Delete in reverse order of dependencies (complete clean, no defaults kept)
    // Supabase requires a WHERE clause for DELETE operations
    const { error: teamMembersError } = await supabase.from('team_members').delete().gte('id', 0);
    if (teamMembersError) console.error('Error clearing team_members:', teamMembersError.message);
    
    const { error: teamsError } = await supabase.from('teams').delete().gte('id', 0);
    if (teamsError) console.error('Error clearing teams:', teamsError.message);
    
    const { error: usersError } = await supabase.from('users').delete().gte('id', 0);
    if (usersError) console.error('Error clearing users:', usersError.message);
    
    const { error: orgsError } = await supabase.from('organizations').delete().gte('id', 0);
    if (orgsError) console.error('Error clearing organizations:', orgsError.message);
    
    console.log('Existing data cleared successfully');

    // Create default organization
    console.log('\nCreating default organization...');
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .insert({
        id: 1,
        name: 'Coelec',
        created_by_id: 1,
        created_at: new Date().toISOString()
      })
      .select();
    
    if (orgError) {
      console.error('Error creating default organization:', orgError.message);
    } else {
      console.log('Default organization created successfully');
      
      // Create default team
      console.log('\nCreating default team...');
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .insert({
          id: 1,
          name: 'Default Team',
          organization_id: 1,
          created_by_id: 1,
          created_at: new Date().toISOString()
        })
        .select();
      
      if (teamError) {
        console.error('Error creating default team:', teamError.message);
      } else {
        console.log('Default team created successfully');
      }
    }
    
    console.log('\n✅ Database reset completed successfully');
    console.log('NOTE: Supabase Auth users were NOT affected');
    console.log('IMPORTANT: Now create a super_admin user and update the default team\'s created_by_id to match');
  } catch (error) {
    console.error('Unexpected error during reset:', error);
  }
}

async function listAuthUsers() {
  console.clear();
  console.log('===== LIST SUPABASE AUTH USERS =====');
  
  try {
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error listing Auth users:', authError.message);
      return;
    }
    
    if (!authUsers || authUsers.users.length === 0) {
      console.log('No Auth users found.');
      return;
    }
    
    console.log('\nAuth users:');
    authUsers.users.forEach((user) => {
      console.log(`- Email: ${user.email}`);
      console.log(`  ID: ${user.id}`);
      console.log(`  Created: ${new Date(user.created_at).toLocaleString()}`);
      console.log(`  Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);
      console.log('  -----------------------------');
    });
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Start the interactive menu
showMenu().catch(err => {
  console.error('Script execution failed:', err);
  process.exit(1);
});
