
#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const defaultEnv = `.env.example`;
const targetEnv = `.env`;

console.log('=== CoElec Local Setup ===');
console.log('This script will help you set up your local development environment.');

// Check if .env already exists
if (fs.existsSync(targetEnv)) {
  console.log('\n⚠️ .env file already exists!');
  rl.question('Do you want to overwrite it? (y/N): ', (answer) => {
    if (answer.toLowerCase() !== 'y') {
      console.log('Setup cancelled. Your .env file was not modified.');
      rl.close();
      return;
    }
    createEnvFile();
  });
} else {
  createEnvFile();
}

function createEnvFile() {
  const envTemplate = `# Database Configuration
DATABASE_URL=postgres://username:password@localhost:5432/database_name

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_service_key_here

# SendGrid Email Configuration
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>

# Server Configuration
PORT=5000
NODE_ENV=development
`;

  fs.writeFileSync(targetEnv, envTemplate);
  console.log('\n✅ Created .env file with template values');
  console.log('⚠️ Please update the .env file with your actual credentials');

  rl.question('\nDo you want to install dependencies now? (Y/n): ', (answer) => {
    if (answer.toLowerCase() !== 'n') {
      console.log('\nInstalling dependencies...');
      try {
        execSync('npm install', { stdio: 'inherit' });
        console.log('\n✅ Dependencies installed successfully!');
      } catch (error) {
        console.error('\n❌ Error installing dependencies:', error.message);
      }
    }

    rl.question('\nDo you want to run database setup (requires PostgreSQL installed)? (Y/n): ', (answer) => {
      if (answer.toLowerCase() !== 'n') {
        console.log('\nSetting up database schema...');
        try {
          execSync('npm run db:push', { stdio: 'inherit' });
          console.log('\n✅ Database schema setup complete!');
        } catch (error) {
          console.error('\n❌ Error setting up database:', error.message);
          console.log('Please make sure your DATABASE_URL is correctly configured in the .env file.');
        }
      }

      console.log('\n=== Setup Complete ===');
      console.log('To start the development server run:');
      console.log('npm run dev');
      rl.close();
    });
  });
}
