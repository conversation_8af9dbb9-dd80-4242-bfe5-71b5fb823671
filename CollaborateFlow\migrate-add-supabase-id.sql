-- migrate-add-supabase-id.sql
-- Migration to add supabase_id column to users table

-- Check if the column already exists
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 
                FROM information_schema.columns 
                WHERE table_name='users' 
                AND column_name='supabase_id') THEN
    -- Add the column if it doesn't exist
    ALTER TABLE users ADD COLUMN supabase_id UUID UNIQUE;
    
    RAISE NOTICE 'Added supabase_id column to users table';
  ELSE
    RAISE NOTICE 'supabase_id column already exists';
  END IF;
END $$;

-- Verification query
SELECT 
  column_name, 
  data_type 
FROM 
  information_schema.columns 
WHERE 
  table_name = 'users' 
ORDER BY 
  ordinal_position;
