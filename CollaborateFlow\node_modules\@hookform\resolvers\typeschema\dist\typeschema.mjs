import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";import{validate as o}from"@typeschema/main";import{appendErrors as t}from"react-hook-form";var s=function(r,e){for(var o={};r.length;){var s=r[0];if(s.path){var a=s.path.join(".");if(o[a]||(o[a]={message:s.message,type:""}),e){var i=o[a].types,n=i&&i[""];o[a]=t(a,e,o,"",n?[].concat(n,s.message):s.message)}r.shift()}}return o},a=function(t,a,i){return void 0===i&&(i={}),function(a,n,m){try{return Promise.resolve(o(t,a)).then(function(o){return m.shouldUseNativeValidation&&r({},m),o.success?{errors:{},values:i.raw?a:o.data}:{values:{},errors:e(s(o.issues,!m.shouldUseNativeValidation&&"all"===m.criteriaMode),m)}})}catch(r){return Promise.reject(r)}}};export{a as typeschemaResolver};
//# sourceMappingURL=typeschema.module.js.map
