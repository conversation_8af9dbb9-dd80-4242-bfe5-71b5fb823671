import{toNestErrors as s}from"@hookform/resolvers";import{appendErrors as e}from"react-hook-form";import{safeParseAsync as o,getDotPath as t}from"valibot";const r=(r,a,i={})=>async(m,n,c)=>{const p=!c.shouldUseNativeValidation&&"all"===c.criteriaMode,u=await o(r,m,Object.assign({},a,{abortPipeEarly:!p}));if(u.issues){const o={};for(;u.issues.length;){const s=u.issues[0],r=t(s);if(r&&(o[r]||(o[r]={message:s.message,type:s.type}),p)){const t=o[r].types,a=t&&t[s.type];o[r]=e(r,p,o,s.type,a?[].concat(a,s.message):s.message)}u.issues.shift()}return{values:{},errors:s(o,c)}}return{values:i.raw?m:u.output,errors:{}}};export{r as valibotResolver};
//# sourceMappingURL=valibot.modern.mjs.map
