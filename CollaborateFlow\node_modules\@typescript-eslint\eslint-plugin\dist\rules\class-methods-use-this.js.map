{"version": 3, "file": "class-methods-use-this.js", "sourceRoot": "", "sources": ["../../src/rules/class-methods-use-this.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAKiB;AAYjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,KAAK;SAC5B;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,aAAa,EAAE;wBACb,IAAI,EAAE,OAAO;wBACb,WAAW,EACT,4DAA4D;wBAC9D,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;oBACD,qBAAqB,EAAE;wBACrB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,4EAA4E;wBAC9E,OAAO,EAAE,IAAI;qBACd;oBACD,qBAAqB,EAAE;wBACrB,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,oDAAoD;qBAClE;oBACD,qCAAqC,EAAE;wBACrC,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,SAAS;gCACf,WAAW,EAAE,gDAAgD;6BAC9D;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,eAAe,CAAC;gCACvB,WAAW,EACT,sEAAsE;6BACzE;yBACF;wBACD,WAAW,EACT,2DAA2D;qBAC9D;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,+CAA+C;SAC7D;KACF;IACD,cAAc,EAAE;QACd;YACE,qBAAqB,EAAE,IAAI;YAC3B,aAAa,EAAE,EAAE;YACjB,qCAAqC,EAAE,KAAK;YAC5C,qBAAqB,EAAE,KAAK;SAC7B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,qBAAqB,EACrB,aAAa,EAAE,gBAAgB,EAC/B,qCAAqC,EACrC,qBAAqB,GACtB,EACF;QAED,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAchD,IAAI,KAAwB,CAAC;QAE7B,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C,SAAS,WAAW,CAClB,MAAgE;YAEhE,IAAI,MAAM,EAAE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,SAAS,EAAE,CAAC;gBACrD,KAAK,GAAG;oBACN,MAAM;oBACN,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,MAEO;oBAC5B,QAAQ,EAAE,KAAK;oBACf,MAAM,EAAE,KAAK;iBACd,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG;oBACN,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,KAAK;oBACf,MAAM,EAAE,KAAK;iBACd,CAAC;YACJ,CAAC;QACH,CAAC;QAED,SAAS,aAAa,CACpB,IAAoE;YAEpE,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EACtD,CAAC;gBACD,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,WAAW,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;QAED;;WAEG;QACH,SAAS,UAAU;YACjB,MAAM,QAAQ,GAAG,KAAK,CAAC;YACvB,KAAK,GAAG,KAAK,EAAE,MAAM,CAAC;YACtB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,SAAS,aAAa,CACpB,aAAiD;YAEjD,IAAI,CAAC,aAAa,IAAI,aAAa,KAAK,QAAQ,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;WAEG;QACH,SAAS,wBAAwB,CAC/B,IAAkC;YAElC,IACE,IAAI,CAAC,MAAM;gBACX,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBAC5C,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC;gBAC9B,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;oBAC9C,CAAC,qBAAqB,CAAC,EACzB,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,YAAY,GAChB,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,GACR,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBACtC,CAAC,CAAC,IAAA,2BAAoB,EAAC,IAAI,CAAC,GAAG,CAAC;gBAChC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;YAE1B,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;QACzD,CAAC;QAED;;;;WAIG;QACH,SAAS,YAAY,CACnB,IAAoE;YAEpE,MAAM,YAAY,GAAG,UAAU,EAAE,CAAC;YAClC,IACE,YAAY,EAAE,MAAM,IAAI,IAAI;gBAC5B,YAAY,CAAC,QAAQ;gBACrB,CAAC,qBAAqB,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACvD,CAAC,qCAAqC,KAAK,IAAI;oBAC7C,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC3C,CAAC,qCAAqC,KAAK,eAAe;oBACxD,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;oBACxC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,EACnD,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,wBAAwB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE,IAAA,yBAAkB,EAAC,IAAI,EAAE,UAAU,CAAC;oBACzC,SAAS,EAAE,aAAa;oBACxB,IAAI,EAAE;wBACJ,IAAI,EAAE,IAAA,8BAAuB,EAAC,IAAI,CAAC;qBACpC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,sDAAsD;YACtD,mBAAmB;gBACjB,WAAW,EAAE,CAAC;YAChB,CAAC;YACD,0BAA0B;gBACxB,UAAU,EAAE,CAAC;YACf,CAAC;YAED,kBAAkB,CAAC,IAAI;gBACrB,aAAa,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;YACD,yBAAyB,CAAC,IAAI;gBAC5B,YAAY,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;YACD,GAAG,CAAC,qBAAqB;gBACvB,CAAC,CAAC;oBACE,oDAAoD,CAClD,IAAsC;wBAEtC,aAAa,CAAC,IAAI,CAAC,CAAC;oBACtB,CAAC;oBACD,yDAAyD,CACvD,IAAsC;wBAEtC,YAAY,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC;iBACF;gBACH,CAAC,CAAC,EAAE,CAAC;YAEP;;eAEG;YACH,iCAAiC;gBAC/B,WAAW,EAAE,CAAC;YAChB,CAAC;YACD,yBAAyB;gBACvB,UAAU,EAAE,CAAC;YACf,CAAC;YAED;;;;;eAKG;YACH,WAAW;gBACT,WAAW,EAAE,CAAC;YAChB,CAAC;YACD,kBAAkB;gBAChB,UAAU,EAAE,CAAC;YACf,CAAC;YAED,uBAAuB;gBACrB,IAAI,KAAK,EAAE,CAAC;oBACV,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACxB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}