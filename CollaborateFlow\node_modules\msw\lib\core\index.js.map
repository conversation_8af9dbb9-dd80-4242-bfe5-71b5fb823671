{"version": 3, "sources": ["../../src/core/index.ts"], "sourcesContent": ["import { checkGlobals } from './utils/internal/checkGlobals'\n\nexport { SetupApi } from './SetupApi'\n\n/* HTTP handlers */\nexport { RequestHandler } from './handlers/RequestHandler'\nexport { http } from './http'\nexport { HttpHandler, HttpMethods } from './handlers/HttpHandler'\nexport { graphql } from './graphql'\nexport { GraphQLHandler } from './handlers/GraphQLHandler'\n\n/* WebSocket handler */\nexport { ws, type WebSocketLink } from './ws'\nexport {\n  WebSocketHandler,\n  type WebSocketHandlerEventMap,\n  type WebSocketHandlerConnection,\n} from './handlers/WebSocketHandler'\n\n/* Utils */\nexport { matchRequestUrl } from './utils/matching/matchRequestUrl'\nexport * from './utils/handleRequest'\nexport { getResponse } from './getResponse'\nexport { cleanUrl } from './utils/url/cleanUrl'\n\n/**\n * Type definitions.\n */\n\nexport type { SharedOptions, LifeCycleEventsMap } from './sharedOptions'\n\nexport type {\n  ResponseResolver,\n  ResponseResolverReturnType,\n  AsyncResponseResolverReturnType,\n  RequestHandlerOptions,\n  DefaultBodyType,\n  DefaultRequestMultipartBody,\n  JsonBodyType,\n} from './handlers/RequestHandler'\n\nexport type {\n  RequestQuery,\n  HttpRequestParsedResult,\n} from './handlers/HttpHandler'\nexport type { HttpRequestHandler, HttpResponseResolver } from './http'\n\nexport type {\n  GraphQLQuery,\n  GraphQLVariables,\n  GraphQLRequestBody,\n  GraphQLJsonRequestBody,\n} from './handlers/GraphQLHandler'\nexport type { GraphQLRequestHandler, GraphQLResponseResolver } from './graphql'\n\nexport type { WebSocketData, WebSocketEventListener } from './ws'\n\nexport type { Path, PathParams, Match } from './utils/matching/matchRequestUrl'\nexport type { ParsedGraphQLRequest } from './utils/internal/parseGraphQLRequest'\n\nexport * from './HttpResponse'\nexport * from './delay'\nexport { bypass } from './bypass'\nexport { passthrough } from './passthrough'\nexport { isCommonAssetRequest } from './isCommonAssetRequest'\n\n// Validate environmental globals before executing any code.\n// This ensures that the library gives user-friendly errors\n// when ran in the environments that require additional polyfills\n// from the end user.\ncheckGlobals()\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAA6B;AAE7B,sBAAyB;AAGzB,4BAA+B;AAC/B,kBAAqB;AACrB,yBAAyC;AACzC,qBAAwB;AACxB,4BAA+B;AAG/B,gBAAuC;AACvC,8BAIO;AAGP,6BAAgC;AAChC,0BAAc,kCArBd;AAsBA,yBAA4B;AAC5B,sBAAyB;AAqCzB,0BAAc,2BA5Dd;AA6DA,0BAAc,oBA7Dd;AA8DA,oBAAuB;AACvB,yBAA4B;AAC5B,kCAAqC;AAAA,IAMrC,kCAAa;", "names": []}