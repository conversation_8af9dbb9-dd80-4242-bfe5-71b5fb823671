{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "NODE_ENV=development tsx server/index.ts", "dev:client": "vite", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist && node copy-config.js", "start": "NODE_ENV=production node dist/index.js", "seed:test-data": "tsx scripts/seed-test-data.ts", "check": "tsc", "db:push": "drizzle-kit push", "setup:ai": "node scripts/setup-ai.js", "test:ai": "node scripts/test-ai.js", "test": "npm run test:unit && npm run test:integration", "test:unit": "jest --config=jest.config.js", "test:unit:watch": "jest --config=jest.config.js --watch", "test:unit:coverage": "jest --config=jest.config.js --coverage", "test:integration": "jest --config=jest.integration.config.js", "test:esm": "NODE_OPTIONS=--experimental-vm-modules npx jest --config=jest.esm.config.mjs", "test:vitest": "vitest run", "test:vitest:watch": "vitest", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:functions": "cd supabase/functions && deno test --allow-all", "test:all": "npm run test:unit && npm run test:integration && npm run test:functions", "test:ci": "npm run test:unit:coverage && npm run test:integration && npm run test:functions", "test:integration-suite": "node scripts/run-integration-tests.js", "test:e2e:headless": "cypress run --headless", "test:e2e:headed": "cypress run", "test:performance": "cypress run --spec \"cypress/e2e/performance-benchmarks.cy.ts\"", "test:accessibility": "cypress run --spec \"cypress/e2e/accessibility-usability.cy.ts\"", "test:cross-component": "cypress run --spec \"cypress/e2e/cross-component-integration.cy.ts\"", "test:complete-workflow": "cypress run --spec \"cypress/e2e/complete-workflow.cy.ts\"", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "tsc --noEmit", "validate": "npm run type-check && npm run lint && npm run format:check && npm run test:ci"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.60.5", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.12", "@types/pg": "^8.15.2", "@types/react-color": "^3.0.13", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.2", "next-themes": "^0.4.6", "openai": "^4.98.0", "openid-client": "^6.5.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.16.0", "postgres": "^3.4.5", "react": "^18.3.1", "react-color": "^2.19.3", "react-day-picker": "^8.10.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.7.0", "ws": "^8.18.0", "xlsx": "^0.18.5", "zod": "^3.24.4", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.1.2", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.1", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.18", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.8", "@types/node": "^20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/supertest": "^2.0.16", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "cypress": "^13.6.1", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "eslint": "^8.54.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.2.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jsdom": "^26.1.0", "lint-staged": "^15.2.0", "msw": "^2.0.8", "postcss": "^8.4.47", "prettier": "^3.1.0", "supertest": "^6.3.3", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14", "vitest": "^3.1.4"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}