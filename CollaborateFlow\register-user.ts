
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as readline from 'readline';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Set up readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function registerUser() {
  try {
    // Prompt for user details
    const email = await new Promise<string>(resolve => 
      rl.question('Enter email: ', resolve)
    );
    
    const password = await new Promise<string>(resolve => 
      rl.question('Enter password (min 6 chars): ', resolve)
    );
    
    const fullName = await new Promise<string>(resolve => 
      rl.question('Enter full name: ', resolve)
    );

    console.log('\nRegistering user...');
    
    // Register user with Supabase Auth
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      }
    });

    if (error) {
      console.error('Error registering user:', error.message);
    } else {
      console.log('User registered successfully!');
      console.log('User ID:', data.user?.id);
      
      if (data.user) {
        // Optionally add more user data to your users table if needed
        const { error: profileError } = await supabase
          .from('users')
          .insert([{
            email: email,
            username: email.split('@')[0],
            full_name: fullName,
          }]);
        
        if (profileError) {
          console.log('Note: Could not add user details to the users table:', profileError.message);
          console.log('This might be normal if you rely on triggers or if users data is handled differently.');
        } else {
          console.log('User details added to users table.');
        }
      }
      
      console.log('\nNote: If email confirmation is enabled, the user will need to confirm their email.');
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  } finally {
    rl.close();
  }
}

registerUser();
