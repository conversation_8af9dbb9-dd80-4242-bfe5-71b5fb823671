// Reset-db.js - <PERSON><PERSON><PERSON> to reset the database and recreate initial data
// NOTE: This script only resets database tables. To fully reset, you must:
// 1. FIRST delete users from Supabase Auth (through dashboard or Admin API)
// 2. THEN run this script to reset database tables
// 3. FINALLY create new users through your authentication process

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Supabase connection info
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_KEY or SUPABASE_SERVICE_KEY is required');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function resetDatabase() {
  console.log('Starting database reset...');

  try {
    // 1. Clear existing data
    console.log('Clearing existing data...');
    
    // Delete in reverse order of dependencies
    await supabase.from('team_members').delete().neq('id', 0);
    await supabase.from('teams').delete().neq('id', 1); // Keep default team
    await supabase.from('users').delete().neq('id', 1); // Keep super admin
    await supabase.from('organizations').delete().neq('id', 1); // Keep default org
    
    console.log('Existing data cleared successfully.');

    // 2. Create default organization (if it doesn't exist)
    console.log('Creating default organization...');
    const { data: existingOrg } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', 1)
      .single();

    if (!existingOrg) {
      await supabase.from('organizations').insert({
        id: 1,
        name: 'Coelec',
        created_at: new Date().toISOString()
      });
    }

    // 3. Create super admin user (if it doesn't exist)
    console.log('Creating super admin user...');
    const { data: existingAdmin } = await supabase
      .from('users')
      .select('*')
      .eq('id', 1)
      .single();

    if (!existingAdmin) {
      await supabase.from('users').insert({
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        full_name: 'System Administrator',
        organization_id: 1,
        role: 'super_admin',
        created_at: new Date().toISOString(),
        has_completed_setup: true
      });
    }

    // 4. Create default team (if it doesn't exist)
    console.log('Creating default team...');
    const { data: existingTeam } = await supabase
      .from('teams')
      .select('*')
      .eq('id', 1)
      .single();

    if (!existingTeam) {
      await supabase.from('teams').insert({
        id: 1,
        name: 'Default Team',
        description: 'This is the default team for CollaborateFlow',
        created_by_id: 1,
        organization_id: 1,
        created_at: new Date().toISOString()
      });
    }

    // 5. Add super admin as member of default team (if not already)
    console.log('Adding super admin to default team...');
    const { data: existingMembership } = await supabase
      .from('team_members')
      .select('*')
      .eq('team_id', 1)
      .eq('user_id', 1)
      .single();

    if (!existingMembership) {
      await supabase.from('team_members').insert({
        team_id: 1,
        user_id: 1,
        role: 'admin',
        created_at: new Date().toISOString()
      });
    }

    console.log('Database reset completed successfully!');
  } catch (error) {
    console.error('Error resetting database:', error);
  }
}

// Run the reset function
resetDatabase().catch(err => {
  console.error('Script execution failed:', err);
  process.exit(1);
});
