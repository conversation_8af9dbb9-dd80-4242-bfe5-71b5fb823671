// <PERSON>ript to add the default user to the default team
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to add default user to default team
async function addUserToDefaultTeam() {
  try {
    console.log('Checking if user membership already exists...');
    
    // Check if the membership already exists
    const { data: existingMembership, error: checkError } = await supabase
      .from('team_members')
      .select('*')
      .eq('team_id', 1)
      .eq('user_id', 1)
      .single();
    
    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking for existing membership:', checkError);
      return;
    }
    
    if (existingMembership) {
      console.log('User is already a member of the default team:', existingMembership);
      return existingMembership;
    }
    
    console.log('Adding user ID 1 to team ID 1...');
    
    // Add the user to the team
    const { data: newMembership, error: createError } = await supabase
      .from('team_members')
      .insert({
        team_id: 1,
        user_id: 1,
        role: 'admin'
        // Note: No created_at field in team_members table
      })
      .select();
    
    if (createError) {
      console.error('Error adding user to team:', createError);
      return;
    }
    
    console.log('Successfully added user to default team:', newMembership);
    return newMembership;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
addUserToDefaultTeam()
  .then(result => {
    if (result) {
      console.log('User added to default team successfully');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to add user to default team:', error);
    process.exit(1);
  });
