#!/usr/bin/env node

/**
 * Assembly Documents Migration Script
 * Applies assembly documents database schema directly to Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key] = valueParts.join('=').trim();
  }
});

console.log('🚀 Assembly Documents Migration Script');
console.log('=====================================');

// Initialize Supabase client
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

console.log('✅ Supabase configuration found');
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyAssemblyDocumentsMigration() {
  try {
    console.log('📊 Testing database connection...');

    // Test connection
    const { data: testData, error: testError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);

    if (testError) {
      console.error('❌ Database connection failed:', testError.message);
      return false;
    }

    console.log('✅ Database connection successful');

    // Step 1: Create assembly_documents table
    console.log('📝 Creating assembly_documents table...');

    try {
      const createAssemblyDocumentsSQL = `
        CREATE TABLE IF NOT EXISTS assembly_documents (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          organization_id UUID NOT NULL,
          
          -- Document Information
          document_name VARCHAR(255) NOT NULL,
          document_type VARCHAR(50) NOT NULL CHECK (document_type IN ('price_book', 'estimate_template', 'material_catalog', 'assembly_guide')),
          description TEXT,
          
          -- File Information
          file_path VARCHAR(500) NOT NULL,
          file_name VARCHAR(255) NOT NULL,
          file_size BIGINT NOT NULL,
          mime_type VARCHAR(100) NOT NULL,
          file_hash VARCHAR(64),
          
          -- Upload Information
          upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          uploaded_by UUID NOT NULL,
          
          -- Processing Information
          metadata JSONB DEFAULT '{}',
          processing_status VARCHAR(20) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
          last_processed TIMESTAMP WITH TIME ZONE,
          error_message TEXT,
          
          -- Organization and Tagging
          tags TEXT[] DEFAULT '{}',
          is_active BOOLEAN DEFAULT true,
          is_template BOOLEAN DEFAULT false,
          
          -- Versioning
          version_number INTEGER DEFAULT 1,
          parent_document_id UUID REFERENCES assembly_documents(id),
          
          -- Timestamps
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      const { error: createDocError } = await supabase.rpc('exec_sql', {
        sql: createAssemblyDocumentsSQL
      });

      if (createDocError && !createDocError.message.includes('already exists')) {
        console.warn('⚠️ Assembly documents table creation warning:', createDocError.message);
      } else {
        console.log('✅ Assembly documents table ready');
      }

    } catch (error) {
      console.error('❌ Assembly documents table setup failed:', error.message);
    }

    // Step 2: Create parsed_materials table
    console.log('📝 Creating parsed_materials table...');

    try {
      const createParsedMaterialsSQL = `
        CREATE TABLE IF NOT EXISTS parsed_materials (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          assembly_document_id UUID NOT NULL REFERENCES assembly_documents(id) ON DELETE CASCADE,
          organization_id UUID NOT NULL,
          
          -- Material Information
          material_name VARCHAR(255) NOT NULL,
          description TEXT,
          category VARCHAR(100),
          subcategory VARCHAR(100),
          
          -- Pricing Information
          unit_price DECIMAL(10,2) NOT NULL,
          unit VARCHAR(50) NOT NULL DEFAULT 'each',
          currency VARCHAR(3) DEFAULT 'USD',
          
          -- Labor Information
          labor_hours DECIMAL(6,2) DEFAULT 0,
          labor_rate DECIMAL(8,2),
          
          -- Specifications
          specifications JSONB DEFAULT '{}',
          attributes JSONB DEFAULT '{}',
          
          -- Source Information
          source_sheet VARCHAR(100),
          source_row INTEGER,
          source_data JSONB,
          
          -- Mapping Information
          mapped_to_catalog BOOLEAN DEFAULT false,
          catalog_material_id UUID,
          mapping_confidence DECIMAL(3,2),
          
          -- Status
          is_active BOOLEAN DEFAULT true,
          needs_review BOOLEAN DEFAULT false,
          review_notes TEXT,
          
          -- Timestamps
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      const { error: createMatError } = await supabase.rpc('exec_sql', {
        sql: createParsedMaterialsSQL
      });

      if (createMatError && !createMatError.message.includes('already exists')) {
        console.warn('⚠️ Parsed materials table creation warning:', createMatError.message);
      } else {
        console.log('✅ Parsed materials table ready');
      }

    } catch (error) {
      console.error('❌ Parsed materials table setup failed:', error.message);
    }

    // Step 3: Create assembly_templates table
    console.log('📝 Creating assembly_templates table...');

    try {
      const createTemplatesSQL = `
        CREATE TABLE IF NOT EXISTS assembly_templates (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          organization_id UUID NOT NULL,
          assembly_document_id UUID REFERENCES assembly_documents(id),
          
          -- Template Information
          template_name VARCHAR(255) NOT NULL,
          template_type VARCHAR(50) NOT NULL,
          description TEXT,
          
          -- Template Configuration
          default_markup_percentage DECIMAL(5,2) DEFAULT 20.00,
          default_overhead_percentage DECIMAL(5,2) DEFAULT 15.00,
          default_labor_rate DECIMAL(8,2),
          
          -- Regional Settings
          region_code VARCHAR(10),
          applicable_states TEXT[],
          
          -- Usage Information
          usage_count INTEGER DEFAULT 0,
          last_used TIMESTAMP WITH TIME ZONE,
          
          -- Status
          is_active BOOLEAN DEFAULT true,
          is_public BOOLEAN DEFAULT false,
          
          -- Timestamps
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      const { error: createTempError } = await supabase.rpc('exec_sql', {
        sql: createTemplatesSQL
      });

      if (createTempError && !createTempError.message.includes('already exists')) {
        console.warn('⚠️ Assembly templates table creation warning:', createTempError.message);
      } else {
        console.log('✅ Assembly templates table ready');
      }

    } catch (error) {
      console.error('❌ Assembly templates table setup failed:', error.message);
    }

    // Step 4: Create indexes
    console.log('📝 Creating indexes...');

    try {
      const createIndexesSQL = `
        CREATE INDEX IF NOT EXISTS idx_assembly_documents_org ON assembly_documents(organization_id);
        CREATE INDEX IF NOT EXISTS idx_assembly_documents_type ON assembly_documents(document_type);
        CREATE INDEX IF NOT EXISTS idx_assembly_documents_status ON assembly_documents(processing_status);
        CREATE INDEX IF NOT EXISTS idx_assembly_documents_active ON assembly_documents(is_active);
        CREATE INDEX IF NOT EXISTS idx_assembly_documents_upload_date ON assembly_documents(upload_date);

        CREATE INDEX IF NOT EXISTS idx_parsed_materials_document ON parsed_materials(assembly_document_id);
        CREATE INDEX IF NOT EXISTS idx_parsed_materials_org ON parsed_materials(organization_id);
        CREATE INDEX IF NOT EXISTS idx_parsed_materials_category ON parsed_materials(category);
        CREATE INDEX IF NOT EXISTS idx_parsed_materials_mapped ON parsed_materials(mapped_to_catalog);

        CREATE INDEX IF NOT EXISTS idx_assembly_templates_org ON assembly_templates(organization_id);
        CREATE INDEX IF NOT EXISTS idx_assembly_templates_type ON assembly_templates(template_type);
        CREATE INDEX IF NOT EXISTS idx_assembly_templates_active ON assembly_templates(is_active);
      `;

      const { error: indexError } = await supabase.rpc('exec_sql', {
        sql: createIndexesSQL
      });

      if (indexError && !indexError.message.includes('already exists')) {
        console.warn('⚠️ Index creation warning:', indexError.message);
      } else {
        console.log('✅ Indexes created');
      }

    } catch (error) {
      console.error('❌ Index creation failed:', error.message);
    }

    // Step 5: Verify migration
    console.log('🔍 Verifying migration results...');

    const { data: assemblyDocs, error: docsError } = await supabase
      .from('assembly_documents')
      .select('*')
      .limit(1);

    const { data: parsedMats, error: matsError } = await supabase
      .from('parsed_materials')
      .select('*')
      .limit(1);

    const { data: templates, error: templatesError } = await supabase
      .from('assembly_templates')
      .select('*')
      .limit(1);

    if (!docsError && !matsError && !templatesError) {
      console.log('✅ All tables accessible');
      console.log('\n🎉 Assembly Documents Migration Successful!');
      console.log('✅ Database schema created');
      console.log('✅ Tables and indexes ready');
      console.log('✅ Ready for assembly document uploads');
      return true;
    } else {
      console.log('\n⚠️ Migration verification issues:');
      if (docsError) console.log('- Assembly documents table:', docsError.message);
      if (matsError) console.log('- Parsed materials table:', matsError.message);
      if (templatesError) console.log('- Assembly templates table:', templatesError.message);
      return false;
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    return false;
  }
}

// Run migration
applyAssemblyDocumentsMigration().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Unexpected error:', error.message);
  process.exit(1);
});
