#!/usr/bin/env node

/**
 * Simple Migration Script for T1.2
 * Applies electrical symbols database schema directly to Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key] = valueParts.join('=').trim();
  }
});

console.log('🚀 T1.2 Simple Migration Script');
console.log('================================');

// Initialize Supabase client
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

console.log('✅ Supabase configuration found');
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigrations() {
  try {
    console.log('📊 Testing database connection...');
    
    // Test connection
    const { data: testData, error: testError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);
    
    if (testError) {
      console.error('❌ Database connection failed:', testError.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    
    // Step 1: Create categories table and data
    console.log('📝 Creating electrical symbol categories...');
    
    try {
      // Create categories table
      const createCategoriesSQL = `
        CREATE TABLE IF NOT EXISTS electrical_symbol_categories (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          display_order INTEGER DEFAULT 0,
          icon_name VARCHAR(50),
          color_code VARCHAR(7),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      const { error: createCatError } = await supabase.rpc('exec_sql', { 
        sql: createCategoriesSQL 
      });
      
      if (createCatError && !createCatError.message.includes('already exists')) {
        console.warn('⚠️ Categories table creation warning:', createCatError.message);
      } else {
        console.log('✅ Categories table ready');
      }
      
      // Insert categories data
      const categoriesData = [
        { name: 'outlets', description: 'Electrical outlets and receptacles', display_order: 1, icon_name: 'outlet', color_code: '#FF6B35' },
        { name: 'switches', description: 'Light switches and controls', display_order: 2, icon_name: 'switch', color_code: '#4ECDC4' },
        { name: 'lighting', description: 'Light fixtures and illumination', display_order: 3, icon_name: 'lightbulb', color_code: '#FFE66D' },
        { name: 'panels', description: 'Electrical panels and distribution', display_order: 4, icon_name: 'panel', color_code: '#A8E6CF' },
        { name: 'data_comm', description: 'Data and communication systems', display_order: 5, icon_name: 'network', color_code: '#88D8B0' },
        { name: 'hvac_electrical', description: 'HVAC electrical components', display_order: 6, icon_name: 'thermostat', color_code: '#C7CEEA' },
        { name: 'safety', description: 'Safety and emergency systems', display_order: 7, icon_name: 'shield', color_code: '#FF8B94' },
        { name: 'specialty', description: 'Specialty electrical equipment', display_order: 8, icon_name: 'tool', color_code: '#B4A7D6' }
      ];
      
      for (const category of categoriesData) {
        const { error: insertError } = await supabase
          .from('electrical_symbol_categories')
          .upsert(category, { onConflict: 'name' });
        
        if (insertError) {
          console.warn(`⚠️ Category insert warning for ${category.name}:`, insertError.message);
        }
      }
      
      console.log('✅ Categories data populated');
      
    } catch (error) {
      console.error('❌ Categories setup failed:', error.message);
    }
    
    // Step 2: Create symbols table
    console.log('📝 Creating electrical symbols table...');
    
    try {
      const createSymbolsSQL = `
        CREATE TABLE IF NOT EXISTS electrical_symbols (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          category_id UUID REFERENCES electrical_symbol_categories(id) ON DELETE CASCADE,
          symbol_code VARCHAR(20) NOT NULL UNIQUE,
          name VARCHAR(200) NOT NULL,
          description TEXT,
          voltage VARCHAR(20),
          amperage VARCHAR(20),
          wattage INTEGER,
          phase_type VARCHAR(20),
          mounting_type VARCHAR(50),
          dimensions JSONB,
          weight_lbs DECIMAL(8,2),
          nema_rating VARCHAR(20),
          ip_rating VARCHAR(10),
          ul_listed BOOLEAN DEFAULT true,
          energy_star BOOLEAN DEFAULT false,
          box_type VARCHAR(50),
          wire_gauge VARCHAR(20),
          circuit_breaker_size VARCHAR(20),
          base_material_cost DECIMAL(10,2),
          installation_time_minutes INTEGER,
          labor_complexity VARCHAR(20),
          detection_keywords TEXT[],
          common_variations TEXT[],
          symbol_shape VARCHAR(50),
          manufacturer VARCHAR(100),
          model_number VARCHAR(100),
          part_number VARCHAR(100),
          datasheet_url TEXT,
          image_url TEXT,
          symbol_drawing_url TEXT,
          is_active BOOLEAN DEFAULT true,
          is_standard BOOLEAN DEFAULT true,
          version INTEGER DEFAULT 1,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      const { error: createSymError } = await supabase.rpc('exec_sql', { 
        sql: createSymbolsSQL 
      });
      
      if (createSymError && !createSymError.message.includes('already exists')) {
        console.warn('⚠️ Symbols table creation warning:', createSymError.message);
      } else {
        console.log('✅ Symbols table ready');
      }
      
    } catch (error) {
      console.error('❌ Symbols table setup failed:', error.message);
    }
    
    // Step 3: Create material mappings table
    console.log('📝 Creating material mappings table...');
    
    try {
      const createMaterialsSQL = `
        CREATE TABLE IF NOT EXISTS symbol_material_mappings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          symbol_id UUID REFERENCES electrical_symbols(id) ON DELETE CASCADE,
          material_type VARCHAR(100) NOT NULL,
          material_name VARCHAR(200) NOT NULL,
          material_description TEXT,
          quantity DECIMAL(10,3) NOT NULL DEFAULT 1,
          unit VARCHAR(20) NOT NULL,
          unit_cost DECIMAL(10,2) NOT NULL,
          total_cost DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
          specifications JSONB,
          supplier_name VARCHAR(100),
          supplier_part_number VARCHAR(100),
          supplier_url TEXT,
          is_required BOOLEAN DEFAULT true,
          installation_notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      const { error: createMatError } = await supabase.rpc('exec_sql', { 
        sql: createMaterialsSQL 
      });
      
      if (createMatError && !createMatError.message.includes('already exists')) {
        console.warn('⚠️ Materials table creation warning:', createMatError.message);
      } else {
        console.log('✅ Materials table ready');
      }
      
    } catch (error) {
      console.error('❌ Materials table setup failed:', error.message);
    }
    
    // Step 4: Create detection history table
    console.log('📝 Creating detection history table...');
    
    try {
      const createHistorySQL = `
        CREATE TABLE IF NOT EXISTS symbol_detection_history (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          symbol_id UUID REFERENCES electrical_symbols(id) ON DELETE SET NULL,
          floor_plan_id UUID,
          project_id UUID,
          organization_id UUID NOT NULL,
          detected_coordinates JSONB,
          confidence_score DECIMAL(3,2),
          ai_model_used VARCHAR(100),
          detection_method VARCHAR(50),
          user_confirmed BOOLEAN,
          user_corrected_to UUID REFERENCES electrical_symbols(id) ON DELETE SET NULL,
          user_feedback TEXT,
          detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          confirmed_at TIMESTAMP WITH TIME ZONE
        );
      `;
      
      const { error: createHistError } = await supabase.rpc('exec_sql', { 
        sql: createHistorySQL 
      });
      
      if (createHistError && !createHistError.message.includes('already exists')) {
        console.warn('⚠️ History table creation warning:', createHistError.message);
      } else {
        console.log('✅ Detection history table ready');
      }
      
    } catch (error) {
      console.error('❌ Detection history table setup failed:', error.message);
    }
    
    // Step 5: Insert sample symbols
    console.log('📝 Inserting sample electrical symbols...');
    
    try {
      // Get outlet category ID
      const { data: outletCategory } = await supabase
        .from('electrical_symbol_categories')
        .select('id')
        .eq('name', 'outlets')
        .single();
      
      if (outletCategory) {
        const sampleSymbols = [
          {
            category_id: outletCategory.id,
            symbol_code: 'OUT-STD-15A',
            name: 'Standard 15A Duplex Outlet',
            description: 'Standard residential duplex outlet, 15 amp rating',
            voltage: '120V',
            amperage: '15A',
            wattage: 1800,
            phase_type: 'single',
            mounting_type: 'wall',
            dimensions: { width: 4.5, height: 2.75, depth: 1.5 },
            weight_lbs: 0.3,
            nema_rating: 'NEMA 5-15R',
            ul_listed: true,
            box_type: 'single_gang',
            wire_gauge: '14 AWG',
            circuit_breaker_size: '15A',
            base_material_cost: 2.50,
            installation_time_minutes: 15,
            labor_complexity: 'simple',
            detection_keywords: ['outlet', 'receptacle', 'duplex', 'standard', '15amp'],
            common_variations: ['wall outlet', 'power outlet', 'electrical outlet'],
            symbol_shape: 'rectangle',
            manufacturer: 'Leviton',
            model_number: '5320-W'
          },
          {
            category_id: outletCategory.id,
            symbol_code: 'OUT-GFCI-20A',
            name: 'GFCI Outlet 20A',
            description: 'Ground Fault Circuit Interrupter outlet for wet locations',
            voltage: '120V',
            amperage: '20A',
            wattage: 2400,
            phase_type: 'single',
            mounting_type: 'wall',
            dimensions: { width: 4.5, height: 2.75, depth: 2.0 },
            weight_lbs: 0.5,
            nema_rating: 'NEMA 5-20R',
            ul_listed: true,
            box_type: 'single_gang',
            wire_gauge: '12 AWG',
            circuit_breaker_size: '20A',
            base_material_cost: 18.50,
            installation_time_minutes: 25,
            labor_complexity: 'moderate',
            detection_keywords: ['gfci', 'outlet', 'ground fault', 'safety', '20amp'],
            common_variations: ['GFCI receptacle', 'GFI outlet', 'safety outlet'],
            symbol_shape: 'rectangle',
            manufacturer: 'Leviton',
            model_number: 'GFNT2-W'
          }
        ];
        
        for (const symbol of sampleSymbols) {
          const { error: insertError } = await supabase
            .from('electrical_symbols')
            .upsert(symbol, { onConflict: 'symbol_code' });
          
          if (insertError) {
            console.warn(`⚠️ Symbol insert warning for ${symbol.symbol_code}:`, insertError.message);
          }
        }
        
        console.log('✅ Sample symbols inserted');
      }
      
    } catch (error) {
      console.error('❌ Sample symbols insertion failed:', error.message);
    }
    
    // Step 6: Verify migration
    console.log('🔍 Verifying migration results...');
    
    const { data: categories } = await supabase
      .from('electrical_symbol_categories')
      .select('*');
    
    const { data: symbols } = await supabase
      .from('electrical_symbols')
      .select('*');
    
    console.log(`✅ Found ${categories?.length || 0} categories`);
    console.log(`✅ Found ${symbols?.length || 0} symbols`);
    
    if (categories && categories.length > 0 && symbols && symbols.length > 0) {
      console.log('\n🎉 T1.2 Migration Successful!');
      console.log('✅ Database schema created');
      console.log('✅ Sample data populated');
      console.log('✅ Ready for API testing');
      return true;
    } else {
      console.log('\n⚠️ Migration partially successful');
      console.log('Some tables may be empty');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    return false;
  }
}

// Run migration
applyMigrations().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Unexpected error:', error.message);
  process.exit(1);
});
