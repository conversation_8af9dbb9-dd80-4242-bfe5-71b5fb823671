// <PERSON>ript to create a default organization in Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to create a default organization
async function createDefaultOrganization() {
  try {
    console.log('Checking for existing organizations...');
    
    // Check if any organizations exist
    const { data: existingOrgs, error: checkError } = await supabase
      .from('organizations')
      .select('id, name')
      .limit(1);
    
    if (checkError) {
      console.error('Error checking for organizations:', checkError);
      return;
    }
    
    if (existingOrgs && existingOrgs.length > 0) {
      console.log('Default organization already exists:', existingOrgs[0]);
      return existingOrgs[0].id;
    }
    
    console.log('No organizations found. Creating default organization...');
    
    // Create default organization
    const { data: newOrg, error: createError } = await supabase
      .from('organizations')
      .insert({
        name: 'Default Organization',
        created_by_id: 1, // Assuming user 1 is the admin/default user
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (createError) {
      console.error('Error creating default organization:', createError);
      return;
    }
    
    console.log('Successfully created default organization:', newOrg);
    return newOrg.id;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
createDefaultOrganization()
  .then(orgId => {
    if (orgId) {
      console.log(`Default organization ID: ${orgId}`);
      console.log('You can now create teams using this organization ID.');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to create default organization:', error);
    process.exit(1);
  });
