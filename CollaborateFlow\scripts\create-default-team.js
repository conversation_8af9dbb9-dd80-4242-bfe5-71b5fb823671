// <PERSON>ript to create or update the default team with ID 1
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to create or update the default team
async function createDefaultTeam() {
  try {
    console.log('Checking if default team exists...');
    
    // Check if team with ID 1 exists
    const { data: existingTeam, error: checkError } = await supabase
      .from('teams')
      .select('*')
      .eq('id', 1)
      .single();
    
    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking for default team:', checkError);
      return;
    }
    
    const now = new Date().toISOString();
    
    if (existingTeam) {
      console.log('Default team already exists, updating it:', existingTeam);
      
      // Update the existing team
      const { data: updatedTeam, error: updateError } = await supabase
        .from('teams')
        .update({
          name: 'Default Team',
          description: 'This is the default team for CollaborateFlow'
          // Note: No updated_at field in teams table
        })
        .eq('id', 1)
        .select();
      
      if (updateError) {
        console.error('Error updating default team:', updateError);
        return;
      }
      
      console.log('Successfully updated default team:', updatedTeam);
      return updatedTeam;
    } else {
      console.log('Default team does not exist, creating it...');
      
      // Create a new team with ID 1
      const { data: newTeam, error: createError } = await supabase
        .from('teams')
        .insert({
          id: 1, // Specify ID 1
          name: 'Default Team',
          description: 'This is the default team for CollaborateFlow',
          created_by_id: 1, // Assuming user 1 is the admin
          organization_id: 1, // Coelec organization
          created_at: now
          // Note: No updated_at field in teams table
        })
        .select();
      
      if (createError) {
        console.error('Error creating default team:', createError);
        return;
      }
      
      console.log('Successfully created default team:', newTeam);
      
      // Add the admin user as a team member
      const { data: teamMember, error: memberError } = await supabase
        .from('team_members')
        .insert({
          team_id: 1,
          user_id: 1,
          role: 'admin',
          created_at: now
        })
        .select();
      
      if (memberError) {
        console.error('Error adding admin to default team:', memberError);
      } else {
        console.log('Added admin user to default team:', teamMember);
      }
      
      return newTeam;
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
createDefaultTeam()
  .then(result => {
    if (result) {
      console.log('Default team setup complete');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to set up default team:', error);
    process.exit(1);
  });
