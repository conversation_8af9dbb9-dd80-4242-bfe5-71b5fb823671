// <PERSON>ript to create a team directly using the Supabase client
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to create a team directly
async function createTeamDirectly() {
  try {
    console.log('Creating Engineering Team directly via Supabase client...');
    
    // Create the team with explicit values for all required fields
    const { data, error } = await supabase
      .from('teams')
      .insert({
        name: 'Engineering Team',
        description: 'Software engineering team for Coelec',
        created_by_id: 1,
        organization_id: 1, // Explicitly set to Coelec organization
        created_at: new Date().toISOString()
      })
      .select();
    
    if (error) {
      console.error('Error creating team:', error);
      return;
    }
    
    console.log('Successfully created team:', data);
    
    // Now add the team creator as a team member
    const teamId = data[0].id;
    console.log(`Adding user 1 as admin of team ${teamId}...`);
    
    const { data: membership, error: membershipError } = await supabase
      .from('team_members')
      .insert({
        team_id: teamId,
        user_id: 1,
        role: 'admin'
      })
      .select();
    
    if (membershipError) {
      console.error('Error adding team member:', membershipError);
      return;
    }
    
    console.log('Successfully added team member:', membership);
    return data;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
createTeamDirectly()
  .then(result => {
    if (result) {
      console.log('Team creation completed successfully');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to create team:', error);
    process.exit(1);
  });
