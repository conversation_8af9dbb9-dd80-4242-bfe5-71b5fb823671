-- Stored procedure for direct team creation
CREATE OR REPLACE FUNCTION create_team_direct(
  p_name TEXT,
  p_description TEXT,
  p_created_by_id INTEGER,
  p_organization_id INTEGER
) RETURNS JSONB AS $$
DECLARE
  v_team_id INTEGER;
  v_result JSONB;
BEGIN
  -- Insert the team record
  INSERT INTO teams (
    name,
    description,
    created_by_id,
    organization_id,
    created_at
  ) VALUES (
    p_name,
    p_description,
    p_created_by_id,
    p_organization_id,
    NOW()
  ) RETURNING id INTO v_team_id;
  
  -- Get the created team
  SELECT row_to_json(t)::jsonb INTO v_result
  FROM teams t
  WHERE t.id = v_team_id;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
