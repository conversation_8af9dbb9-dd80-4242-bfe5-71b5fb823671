// <PERSON>ript to inspect the teams table structure in Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to inspect the teams table
async function inspectTeamsTable() {
  try {
    console.log('Fetching teams table structure...');
    
    // Get table definition
    const { data: teams, error } = await supabase
      .from('teams')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('Error fetching teams:', error);
      return;
    }
    
    if (teams && teams.length > 0) {
      console.log('Teams table structure:');
      console.log(Object.keys(teams[0]).map(key => `${key}: ${typeof teams[0][key]}`));
      console.log('\nSample team data:');
      console.log(teams[0]);
    } else {
      console.log('No teams found.');
    }
    
    // Try to find out which columns are required
    console.log('\nAttempting to insert minimal team to identify required columns...');
    
    // Test 1: Just name and organization_id
    try {
      const { data: test1, error: test1Error } = await supabase
        .from('teams')
        .insert({
          name: 'Test Required Fields',
          organization_id: 1
        })
        .select();
      
      if (test1Error) {
        console.log('Test 1 failed:', test1Error.message);
      } else {
        console.log('Test 1 succeeded! Only name and organization_id are required.');
        console.log(test1);
      }
    } catch (err) {
      console.log('Test 1 exception:', err);
    }
    
    // Test 2: All fields except organization_id
    try {
      const { data: test2, error: test2Error } = await supabase
        .from('teams')
        .insert({
          name: 'Test Missing Org ID',
          description: 'Testing which fields are required',
          created_by_id: 1,
          created_at: new Date().toISOString()
        })
        .select();
      
      if (test2Error) {
        console.log('Test 2 failed:', test2Error.message);
      } else {
        console.log('Test 2 succeeded! organization_id is NOT required.');
        console.log(test2);
      }
    } catch (err) {
      console.log('Test 2 exception:', err);
    }
    
    // Test 3: Just organization_id with explicit type
    try {
      const orgId = 1; // Make sure it's a number, not a string
      console.log(`Test 3 using organization_id with type: ${typeof orgId}`);
      
      const { data: test3, error: test3Error } = await supabase
        .from('teams')
        .insert({
          name: 'Test Explicit Types',
          organization_id: orgId
        })
        .select();
      
      if (test3Error) {
        console.log('Test 3 failed:', test3Error.message);
      } else {
        console.log('Test 3 succeeded! organization_id works with explicit number type.');
        console.log(test3);
      }
    } catch (err) {
      console.log('Test 3 exception:', err);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
inspectTeamsTable()
  .then(() => {
    console.log('Inspection completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Inspection failed:', error);
    process.exit(1);
  });
