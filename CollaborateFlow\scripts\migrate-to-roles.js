// Migration script to add role-based management support to Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Migration steps
async function runMigration() {
  console.log('Starting migration to add role-based management...');
  
  try {
    // Step 1: Check for organizations table
    console.log('\nStep 1: Checking for organizations table...');
    const { data: orgCheck, error: orgCheckError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);
    
    if (orgCheckError && orgCheckError.code === '42P01') { // Table doesn't exist
      console.log('  Organizations table does not exist. Creating...');
      
      // Create organizations table
      const { error: createOrgError } = await supabase.rpc('create_organizations_table');
      
      if (createOrgError) {
        console.log('  Error creating organizations table. Adding directly through SQL is recommended.');
        console.log('  Error:', createOrgError);
      } else {
        console.log('  Organizations table created successfully!');
      }
    } else {
      console.log('  Organizations table already exists.');
    }
    
    // Step 2: Check for default organization (Coelec)
    console.log('\nStep 2: Checking for default organization...');
    const { data: defaultOrg, error: defaultOrgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', 1)
      .single();
    
    if (defaultOrgError || !defaultOrg) {
      console.log('  Default organization not found. Creating...');
      
      // Create default organization
      const { data: newOrg, error: newOrgError } = await supabase
        .from('organizations')
        .insert({
          id: 1,
          name: 'Coelec',
          description: 'Default organization for CollaborateFlow',
          created_at: new Date().toISOString()
        })
        .select();
      
      if (newOrgError) {
        console.log('  Error creating default organization:', newOrgError);
      } else {
        console.log('  Default organization created successfully!', newOrg);
      }
    } else {
      console.log('  Default organization already exists:', defaultOrg);
    }
    
    // Step 3: Check for role field in users table
    console.log('\nStep 3: Checking for role field in users table...');
    
    // Try to get users with role field
    const { data: userCheck, error: userCheckError } = await supabase
      .from('users')
      .select('role')
      .limit(1);
    
    if (userCheckError && userCheckError.message.includes('role')) {
      console.log('  Role field does not exist in users table. Adding...');
      
      // Add role field via RPC (this might fail with anon key)
      const { error: addRoleError } = await supabase.rpc('add_role_to_users');
      
      if (addRoleError) {
        console.log('  Error adding role field to users table. Adding directly through SQL is recommended.');
        console.log('  Error:', addRoleError);
      } else {
        console.log('  Role field added to users table successfully!');
      }
    } else {
      console.log('  Role field already exists in users table.');
    }
    
    // Step 4: Check for organization_id field in users table
    console.log('\nStep 4: Checking for organization_id field in users table...');
    
    // Try to get users with organization_id field
    const { data: userOrgCheck, error: userOrgCheckError } = await supabase
      .from('users')
      .select('organization_id')
      .limit(1);
    
    if (userOrgCheckError && userOrgCheckError.message.includes('organization_id')) {
      console.log('  Organization_id field does not exist in users table. Adding...');
      
      // Add organization_id field via RPC (this might fail with anon key)
      const { error: addOrgIdError } = await supabase.rpc('add_organization_id_to_users');
      
      if (addOrgIdError) {
        console.log('  Error adding organization_id field to users table. Adding directly through SQL is recommended.');
        console.log('  Error:', addOrgIdError);
      } else {
        console.log('  Organization_id field added to users table successfully!');
      }
    } else {
      console.log('  Organization_id field already exists in users table.');
    }
    
    // Step 5: Set up super admin user
    console.log('\nStep 5: Setting up super admin user...');
    
    // Check if user with ID 1 exists
    const { data: adminUser, error: adminUserError } = await supabase
      .from('users')
      .select('*')
      .eq('id', 1)
      .single();
    
    if (adminUserError || !adminUser) {
      console.log('  Super admin user not found. Please create a user with ID 1 first.');
    } else {
      // Update user to be super admin in organization 1
      const { data: updatedAdmin, error: updateAdminError } = await supabase
        .from('users')
        .update({
          role: 'super_admin',
          organization_id: 1
        })
        .eq('id', 1)
        .select();
      
      if (updateAdminError) {
        console.log('  Error updating user to super admin:', updateAdminError);
      } else {
        console.log('  User updated to super admin successfully!', updatedAdmin);
      }
    }
    
    // Step 6: Check for organization_id field in teams table
    console.log('\nStep 6: Checking for organization_id field in teams table...');
    
    // Try to get teams with organization_id field
    const { data: teamOrgCheck, error: teamOrgCheckError } = await supabase
      .from('teams')
      .select('organization_id')
      .limit(1);
    
    if (teamOrgCheckError && teamOrgCheckError.message.includes('organization_id')) {
      console.log('  Organization_id field does not exist in teams table. Adding...');
      
      // Add organization_id field via RPC (this might fail with anon key)
      const { error: addTeamOrgIdError } = await supabase.rpc('add_organization_id_to_teams');
      
      if (addTeamOrgIdError) {
        console.log('  Error adding organization_id field to teams table. Adding directly through SQL is recommended.');
        console.log('  Error:', addTeamOrgIdError);
      } else {
        console.log('  Organization_id field added to teams table successfully!');
      }
    } else {
      console.log('  Organization_id field already exists in teams table.');
    }
    
    // Step 7: Check for default team
    console.log('\nStep 7: Checking for default team...');
    const { data: defaultTeam, error: defaultTeamError } = await supabase
      .from('teams')
      .select('*')
      .eq('id', 1)
      .single();
    
    if (defaultTeamError || !defaultTeam) {
      console.log('  Default team not found. Creating...');
      
      // Create default team
      const { data: newTeam, error: newTeamError } = await supabase
        .from('teams')
        .insert({
          id: 1,
          name: 'Default Team',
          description: 'This is the default team for CollaborateFlow',
          created_by_id: 1,
          organization_id: 1,
          created_at: new Date().toISOString()
        })
        .select();
      
      if (newTeamError) {
        console.log('  Error creating default team:', newTeamError);
      } else {
        console.log('  Default team created successfully!', newTeam);
      }
    } else {
      // Update team if it exists but doesn't have organization_id
      if (!defaultTeam.organization_id) {
        const { data: updatedTeam, error: updateTeamError } = await supabase
          .from('teams')
          .update({ organization_id: 1 })
          .eq('id', 1)
          .select();
        
        if (updateTeamError) {
          console.log('  Error updating default team with organization ID:', updateTeamError);
        } else {
          console.log('  Default team updated with organization ID successfully!', updatedTeam);
        }
      } else {
        console.log('  Default team already exists with organization ID:', defaultTeam);
      }
    }
    
    // Step 8: Add super admin to default team if not already a member
    console.log('\nStep 8: Adding super admin to default team...');
    const { data: teamMember, error: teamMemberError } = await supabase
      .from('team_members')
      .select('*')
      .eq('team_id', 1)
      .eq('user_id', 1)
      .single();
    
    if (teamMemberError || !teamMember) {
      console.log('  Super admin not a member of default team. Adding...');
      
      // Add super admin to default team
      const { data: newTeamMember, error: newTeamMemberError } = await supabase
        .from('team_members')
        .insert({
          team_id: 1,
          user_id: 1,
          role: 'admin',
          created_at: new Date().toISOString()
        })
        .select();
      
      if (newTeamMemberError) {
        console.log('  Error adding super admin to default team:', newTeamMemberError);
      } else {
        console.log('  Super admin added to default team successfully!', newTeamMember);
      }
    } else {
      console.log('  Super admin is already a member of default team:', teamMember);
    }
    
    console.log('\n✅ Migration completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Check the schema.sql file for the complete role-based setup');
    console.log('2. Review the ROLES_IMPLEMENTATION_TODO.md file for UI implementation tasks');
    console.log('3. Restart the server to apply the changes');
    
  } catch (error) {
    console.error('\n❌ Migration failed with an unexpected error:', error);
  }
}

// Run the migration
runMigration();
