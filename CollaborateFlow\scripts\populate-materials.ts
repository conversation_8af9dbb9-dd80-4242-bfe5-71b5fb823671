#!/usr/bin/env node

/**
 * T1.3 MATERIAL ESTIMATION ENGINE - DATA SEEDING SCRIPT
 * Populates the database with comprehensive electrical materials for cost estimation
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key] = valueParts.join('=').trim();
  }
});

console.log('🔧 T1.3 Material Database Population Script');
console.log('===========================================');

// Initialize Supabase client
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Material Categories Data
const materialCategories = [
  { name: 'outlets_receptacles', description: 'Electrical outlets and receptacles', display_order: 1, icon_name: 'outlet', color_code: '#FF6B35' },
  { name: 'switches_controls', description: 'Light switches and electrical controls', display_order: 2, icon_name: 'switch', color_code: '#4ECDC4' },
  { name: 'lighting_fixtures', description: 'Light fixtures and illumination devices', display_order: 3, icon_name: 'lightbulb', color_code: '#FFE66D' },
  { name: 'electrical_panels', description: 'Electrical panels and distribution equipment', display_order: 4, icon_name: 'panel', color_code: '#A8E6CF' },
  { name: 'wiring_conduit', description: 'Electrical wiring and conduit systems', display_order: 5, icon_name: 'cable', color_code: '#88D8B0' },
  { name: 'circuit_protection', description: 'Circuit breakers and protection devices', display_order: 6, icon_name: 'shield', color_code: '#C7CEEA' },
  { name: 'data_communication', description: 'Data and communication wiring', display_order: 7, icon_name: 'network', color_code: '#FF8B94' },
  { name: 'specialty_equipment', description: 'Specialty electrical equipment', display_order: 8, icon_name: 'tool', color_code: '#B4A7D6' }
];

// Regional Pricing Data
const regionalPricing = [
  {
    region_code: 'US-DEFAULT',
    region_name: 'United States Default',
    state_province: 'National Average',
    country: 'US',
    labor_rate_multiplier: 1.00,
    material_cost_multiplier: 1.00,
    tax_rate: 8.50,
    permit_cost_base: 150.00,
    cost_of_living_index: 100.00
  },
  {
    region_code: 'US-CA-SF',
    region_name: 'San Francisco Bay Area',
    state_province: 'California',
    country: 'US',
    labor_rate_multiplier: 1.45,
    material_cost_multiplier: 1.15,
    tax_rate: 9.75,
    permit_cost_base: 350.00,
    cost_of_living_index: 145.00
  },
  {
    region_code: 'US-NY-NYC',
    region_name: 'New York City',
    state_province: 'New York',
    country: 'US',
    labor_rate_multiplier: 1.35,
    material_cost_multiplier: 1.12,
    tax_rate: 8.25,
    permit_cost_base: 275.00,
    cost_of_living_index: 135.00
  },
  {
    region_code: 'US-TX-DAL',
    region_name: 'Dallas-Fort Worth',
    state_province: 'Texas',
    country: 'US',
    labor_rate_multiplier: 0.90,
    material_cost_multiplier: 0.95,
    tax_rate: 8.25,
    permit_cost_base: 125.00,
    cost_of_living_index: 90.00
  }
];

// Comprehensive Electrical Materials Data
const electricalMaterials = [
  // OUTLETS & RECEPTACLES
  {
    material_code: 'OUT-STD-15A-WHT',
    name: 'Standard 15A Duplex Outlet - White',
    description: 'Standard residential duplex outlet, 15 amp rating, white finish',
    category: 'outlets_receptacles',
    subcategory: 'standard_outlets',
    manufacturer: 'Leviton',
    model_number: '5320-W',
    part_number: 'LEV-5320-W',
    specifications: { type: 'duplex', grounding: true, tamper_resistant: false },
    voltage_rating: '125V',
    amperage_rating: '15A',
    nema_rating: 'NEMA 5-15R',
    ul_listed: true,
    dimensions: { width: 4.5, height: 2.75, depth: 1.5, unit: 'inches' },
    weight_lbs: 0.3,
    color: 'white',
    mounting_type: 'wall',
    base_cost: 2.85,
    unit: 'each',
    installation_time_minutes: 15,
    labor_complexity: 'simple',
    required_tools: ['wire strippers', 'screwdriver', 'voltage tester'],
    safety_requirements: ['turn off circuit breaker', 'verify power off']
  },
  {
    material_code: 'OUT-GFCI-20A-WHT',
    name: 'GFCI Outlet 20A - White',
    description: 'Ground Fault Circuit Interrupter outlet for wet locations, 20 amp',
    category: 'outlets_receptacles',
    subcategory: 'gfci_outlets',
    manufacturer: 'Leviton',
    model_number: 'GFNT2-W',
    part_number: 'LEV-GFNT2-W',
    specifications: { type: 'gfci', grounding: true, test_reset: true },
    voltage_rating: '125V',
    amperage_rating: '20A',
    nema_rating: 'NEMA 5-20R',
    ul_listed: true,
    dimensions: { width: 4.5, height: 2.75, depth: 2.0, unit: 'inches' },
    weight_lbs: 0.6,
    color: 'white',
    mounting_type: 'wall',
    base_cost: 18.95,
    unit: 'each',
    installation_time_minutes: 25,
    labor_complexity: 'moderate',
    required_tools: ['wire strippers', 'screwdriver', 'voltage tester', 'GFCI tester'],
    safety_requirements: ['turn off circuit breaker', 'verify power off', 'test GFCI function']
  },
  {
    material_code: 'OUT-USB-15A-WHT',
    name: 'USB Outlet 15A with USB Ports - White',
    description: 'Duplex outlet with integrated USB charging ports, 15 amp',
    category: 'outlets_receptacles',
    subcategory: 'usb_outlets',
    manufacturer: 'Leviton',
    model_number: 'T5635-W',
    part_number: 'LEV-T5635-W',
    specifications: { type: 'duplex_usb', usb_ports: 2, usb_amperage: '3.6A' },
    voltage_rating: '125V',
    amperage_rating: '15A',
    nema_rating: 'NEMA 5-15R',
    ul_listed: true,
    dimensions: { width: 4.5, height: 2.75, depth: 1.8, unit: 'inches' },
    weight_lbs: 0.4,
    color: 'white',
    mounting_type: 'wall',
    base_cost: 24.50,
    unit: 'each',
    installation_time_minutes: 20,
    labor_complexity: 'simple',
    required_tools: ['wire strippers', 'screwdriver', 'voltage tester'],
    safety_requirements: ['turn off circuit breaker', 'verify power off']
  },

  // SWITCHES & CONTROLS
  {
    material_code: 'SW-SP-15A-WHT',
    name: 'Single Pole Switch 15A - White',
    description: 'Standard single pole light switch, 15 amp rating',
    category: 'switches_controls',
    subcategory: 'standard_switches',
    manufacturer: 'Leviton',
    model_number: '5501-W',
    part_number: 'LEV-5501-W',
    specifications: { type: 'single_pole', toggle: true },
    voltage_rating: '120V',
    amperage_rating: '15A',
    ul_listed: true,
    dimensions: { width: 4.5, height: 2.75, depth: 1.2, unit: 'inches' },
    weight_lbs: 0.2,
    color: 'white',
    mounting_type: 'wall',
    base_cost: 1.95,
    unit: 'each',
    installation_time_minutes: 10,
    labor_complexity: 'simple',
    required_tools: ['wire strippers', 'screwdriver', 'voltage tester'],
    safety_requirements: ['turn off circuit breaker', 'verify power off']
  },
  {
    material_code: 'SW-DIM-600W-WHT',
    name: 'Dimmer Switch 600W - White',
    description: 'Single pole dimmer switch, 600 watt capacity',
    category: 'switches_controls',
    subcategory: 'dimmer_switches',
    manufacturer: 'Lutron',
    model_number: 'DVCL-153P-WH',
    part_number: 'LUT-DVCL-153P-WH',
    specifications: { type: 'dimmer', wattage: 600, led_compatible: true },
    voltage_rating: '120V',
    wattage_rating: 600,
    ul_listed: true,
    dimensions: { width: 4.5, height: 2.75, depth: 1.5, unit: 'inches' },
    weight_lbs: 0.3,
    color: 'white',
    mounting_type: 'wall',
    base_cost: 28.75,
    unit: 'each',
    installation_time_minutes: 15,
    labor_complexity: 'moderate',
    required_tools: ['wire strippers', 'screwdriver', 'voltage tester'],
    safety_requirements: ['turn off circuit breaker', 'verify power off', 'check load compatibility']
  },

  // LIGHTING FIXTURES
  {
    material_code: 'LT-REC-LED-12W',
    name: 'LED Recessed Light 12W',
    description: '6-inch LED recessed downlight, 12 watt, 3000K',
    category: 'lighting_fixtures',
    subcategory: 'recessed_lights',
    manufacturer: 'Halo',
    model_number: 'RL56069WH6930R',
    part_number: 'HAL-RL56069WH6930R',
    specifications: { diameter: 6, color_temp: '3000K', lumens: 850, dimmable: true },
    voltage_rating: '120V',
    wattage_rating: 12,
    energy_star: true,
    dimensions: { diameter: 6.0, depth: 3.5, unit: 'inches' },
    weight_lbs: 1.2,
    color: 'white',
    mounting_type: 'ceiling_recessed',
    base_cost: 32.50,
    unit: 'each',
    installation_time_minutes: 45,
    labor_complexity: 'moderate',
    required_tools: ['hole saw', 'wire strippers', 'screwdriver', 'voltage tester'],
    safety_requirements: ['turn off circuit breaker', 'verify power off', 'check ceiling clearance']
  },
  {
    material_code: 'LT-PND-LED-24W',
    name: 'LED Pendant Light 24W',
    description: 'Modern LED pendant light, 24 watt, adjustable height',
    category: 'lighting_fixtures',
    subcategory: 'pendant_lights',
    manufacturer: 'Progress Lighting',
    model_number: 'P500024-031',
    part_number: 'PRO-P500024-031',
    specifications: { style: 'modern', color_temp: '3000K', lumens: 1800, adjustable: true },
    voltage_rating: '120V',
    wattage_rating: 24,
    energy_star: true,
    dimensions: { diameter: 12.0, height: 8.0, unit: 'inches' },
    weight_lbs: 3.5,
    color: 'black',
    mounting_type: 'ceiling_pendant',
    base_cost: 89.95,
    unit: 'each',
    installation_time_minutes: 60,
    labor_complexity: 'moderate',
    required_tools: ['wire strippers', 'screwdriver', 'voltage tester', 'ceiling box'],
    safety_requirements: ['turn off circuit breaker', 'verify power off', 'check ceiling support']
  },

  // ELECTRICAL PANELS
  {
    material_code: 'PNL-MAIN-200A',
    name: 'Main Electrical Panel 200A',
    description: '200 amp main electrical panel, 40 circuit capacity',
    category: 'electrical_panels',
    subcategory: 'main_panels',
    manufacturer: 'Square D',
    model_number: 'QO140M200PC',
    part_number: 'SQD-QO140M200PC',
    specifications: { amperage: 200, circuits: 40, type: 'main_breaker' },
    voltage_rating: '240V',
    amperage_rating: '200A',
    ul_listed: true,
    dimensions: { width: 14.0, height: 26.0, depth: 5.5, unit: 'inches' },
    weight_lbs: 25.0,
    mounting_type: 'wall',
    base_cost: 285.00,
    unit: 'each',
    installation_time_minutes: 240,
    labor_complexity: 'expert',
    required_tools: ['wire strippers', 'screwdriver', 'voltage tester', 'torque wrench', 'multimeter'],
    safety_requirements: ['utility disconnect required', 'permit required', 'inspection required']
  },

  // WIRING & CONDUIT
  {
    material_code: 'WIR-12AWG-CU-250',
    name: '12 AWG Copper Wire THHN - 250ft',
    description: '12 gauge copper THHN wire, 250 foot roll',
    category: 'wiring_conduit',
    subcategory: 'building_wire',
    manufacturer: 'Southwire',
    model_number: '12-THHN-250',
    part_number: 'SW-12-THHN-250',
    specifications: { gauge: 12, material: 'copper', insulation: 'THHN', length: 250 },
    voltage_rating: '600V',
    amperage_rating: '20A',
    ul_listed: true,
    weight_lbs: 15.5,
    base_cost: 89.50,
    unit: 'roll',
    installation_time_minutes: 2, // per linear foot
    labor_complexity: 'simple',
    required_tools: ['wire strippers', 'fish tape', 'wire nuts'],
    safety_requirements: ['turn off circuit breaker', 'verify power off']
  }
];

// Labor Rates Data
const laborRates = [
  {
    skill_level: 'apprentice',
    hourly_rate: 45.00,
    overtime_multiplier: 1.50,
    project_type_modifiers: { residential: 1.0, commercial: 1.1, industrial: 1.2 },
    complexity_modifiers: { simple: 1.0, moderate: 1.1, complex: 1.3, expert: 1.5 }
  },
  {
    skill_level: 'journeyman',
    hourly_rate: 75.00,
    overtime_multiplier: 1.50,
    project_type_modifiers: { residential: 1.0, commercial: 1.1, industrial: 1.2 },
    complexity_modifiers: { simple: 1.0, moderate: 1.1, complex: 1.3, expert: 1.5 }
  },
  {
    skill_level: 'master',
    hourly_rate: 95.00,
    overtime_multiplier: 1.50,
    project_type_modifiers: { residential: 1.0, commercial: 1.2, industrial: 1.4 },
    complexity_modifiers: { simple: 1.0, moderate: 1.1, complex: 1.3, expert: 1.5 }
  },
  {
    skill_level: 'foreman',
    hourly_rate: 105.00,
    overtime_multiplier: 1.50,
    project_type_modifiers: { residential: 1.0, commercial: 1.2, industrial: 1.4 },
    complexity_modifiers: { simple: 1.0, moderate: 1.1, complex: 1.3, expert: 1.5 }
  }
];

async function populateMaterialsDatabase() {
  try {
    console.log('🗄️ Starting material database population...');

    // 1. Populate Material Categories
    console.log('📂 Inserting material categories...');
    for (const category of materialCategories) {
      const { error } = await supabase
        .from('material_categories')
        .upsert(category, { onConflict: 'name' });
      
      if (error) {
        console.warn(`⚠️ Category insert warning for ${category.name}:`, error.message);
      }
    }
    console.log(`✅ Inserted ${materialCategories.length} material categories`);

    // 2. Populate Regional Pricing
    console.log('🌍 Inserting regional pricing data...');
    for (const region of regionalPricing) {
      const { error } = await supabase
        .from('regional_pricing')
        .upsert(region, { onConflict: 'region_code' });
      
      if (error) {
        console.warn(`⚠️ Regional pricing insert warning for ${region.region_code}:`, error.message);
      }
    }
    console.log(`✅ Inserted ${regionalPricing.length} regional pricing records`);

    // 3. Populate Labor Rates (for default region)
    console.log('👷 Inserting labor rates...');
    const { data: defaultRegion } = await supabase
      .from('regional_pricing')
      .select('id')
      .eq('region_code', 'US-DEFAULT')
      .single();

    if (defaultRegion) {
      for (const rate of laborRates) {
        const { error } = await supabase
          .from('labor_rates')
          .upsert({
            ...rate,
            region_id: defaultRegion.id,
            effective_date: new Date().toISOString().split('T')[0]
          }, { onConflict: 'region_id,skill_level' });
        
        if (error) {
          console.warn(`⚠️ Labor rate insert warning for ${rate.skill_level}:`, error.message);
        }
      }
      console.log(`✅ Inserted ${laborRates.length} labor rate records`);
    }

    // 4. Populate Electrical Materials
    console.log('🔌 Inserting electrical materials...');
    for (const material of electricalMaterials) {
      const { error } = await supabase
        .from('electrical_materials')
        .upsert(material, { onConflict: 'material_code' });
      
      if (error) {
        console.warn(`⚠️ Material insert warning for ${material.material_code}:`, error.message);
      }
    }
    console.log(`✅ Inserted ${electricalMaterials.length} electrical materials`);

    // 5. Verification
    console.log('🔍 Verifying material database population...');
    
    const { data: categoryCount } = await supabase
      .from('material_categories')
      .select('id', { count: 'exact' });
    
    const { data: materialCount } = await supabase
      .from('electrical_materials')
      .select('id', { count: 'exact' });
    
    const { data: regionCount } = await supabase
      .from('regional_pricing')
      .select('id', { count: 'exact' });

    console.log(`📊 Database Population Results:`);
    console.log(`   Categories: ${categoryCount?.length || 0}`);
    console.log(`   Materials: ${materialCount?.length || 0}`);
    console.log(`   Regions: ${regionCount?.length || 0}`);

    if ((materialCount?.length || 0) >= 10 && (categoryCount?.length || 0) >= 5) {
      console.log('\n🎉 T1.3 Material Database Population SUCCESSFUL!');
      console.log('✅ Comprehensive electrical materials catalog created');
      console.log('✅ Regional pricing and labor rates configured');
      console.log('✅ Material categories and specifications populated');
      console.log('🚀 Ready for material estimation engine testing');
      return true;
    } else {
      console.log('\n⚠️ Material database population incomplete');
      return false;
    }

  } catch (error) {
    console.error('❌ Material database population failed:', error.message);
    return false;
  }
}

// Run population script
if (import.meta.url === `file://${process.argv[1]}`) {
  populateMaterialsDatabase().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Population script failed:', error.message);
    process.exit(1);
  });
}

export { populateMaterialsDatabase };
