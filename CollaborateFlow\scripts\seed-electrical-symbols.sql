-- =====================================================================================
-- T1.2 ELECTRICAL SYMBOLS DATA SEEDING
-- =====================================================================================
-- This script populates the electrical symbols database with 50+ standard symbols
-- Includes detailed specifications, material mappings, and cost information

-- =====================================================================================
-- OUTLETS AND RECEPTACLES (Category: outlets)
-- =====================================================================================

-- Standard 15A Duplex Outlet
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, nema_rating, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'outlets'),
    'OUT-STD-15A', 'Standard 15A Duplex Outlet', 'Standard residential duplex outlet, 15 amp rating',
    '120V', '15A', 1800, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.3,
    'NEMA 5-15R', true, 'single_gang', '14 AWG', '15A', 2.50, 15, 'simple',
    ARRAY['outlet', 'receptacle', 'duplex', 'standard', '15amp'],
    ARRAY['wall outlet', 'power outlet', 'electrical outlet'], 'rectangle',
    'Leviton', '5320-W'
);

-- GFCI Outlet
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, nema_rating, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'outlets'),
    'OUT-GFCI-20A', 'GFCI Outlet 20A', 'Ground Fault Circuit Interrupter outlet for wet locations',
    '120V', '20A', 2400, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 2.0}', 0.5,
    'NEMA 5-20R', true, 'single_gang', '12 AWG', '20A', 18.50, 25, 'moderate',
    ARRAY['gfci', 'outlet', 'ground fault', 'safety', '20amp'],
    ARRAY['GFCI receptacle', 'GFI outlet', 'safety outlet'], 'rectangle',
    'Leviton', 'GFNT2-W'
);

-- USB Outlet
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, nema_rating, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'outlets'),
    'OUT-USB-15A', 'USB Outlet with Duplex', 'Combination USB charging ports with standard outlets',
    '120V', '15A', 1800, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.8}', 0.4,
    'NEMA 5-15R', true, 'single_gang', '14 AWG', '15A', 24.99, 20, 'simple',
    ARRAY['usb', 'outlet', 'charging', 'duplex', 'combo'],
    ARRAY['USB receptacle', 'charging outlet', 'USB wall outlet'], 'rectangle',
    'Leviton', 'T5635-W'
);

-- 240V Outlet for Appliances
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, nema_rating, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'outlets'),
    'OUT-240V-30A', '240V 30A Dryer Outlet', '240V outlet for electric dryers and large appliances',
    '240V', '30A', 7200, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 2.5}', 0.8,
    'NEMA 14-30R', true, 'double_gang', '10 AWG', '30A', 35.00, 45, 'complex',
    ARRAY['240v', 'dryer', 'appliance', '30amp', 'high voltage'],
    ARRAY['dryer outlet', '240 volt outlet', 'appliance receptacle'], 'rectangle',
    'Hubbell', 'HBL9330C'
);

-- =====================================================================================
-- SWITCHES (Category: switches)
-- =====================================================================================

-- Single Pole Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'switches'),
    'SW-SP-15A', 'Single Pole Switch', 'Standard single pole light switch, 15 amp rating',
    '120V', '15A', NULL, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.2,
    true, 'single_gang', '14 AWG', '15A', 1.85, 10, 'simple',
    ARRAY['switch', 'single pole', 'light switch', 'toggle'],
    ARRAY['wall switch', 'light control', 'on/off switch'], 'rectangle',
    'Leviton', '1451-2W'
);

-- Three-Way Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'switches'),
    'SW-3W-15A', 'Three-Way Switch', 'Three-way switch for controlling lights from two locations',
    '120V', '15A', NULL, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.2,
    true, 'single_gang', '14 AWG', '15A', 3.25, 20, 'moderate',
    ARRAY['three way', '3-way', 'switch', 'multi location'],
    ARRAY['3-way switch', 'traveler switch', 'multi-point switch'], 'rectangle',
    'Leviton', '1453-2W'
);

-- Dimmer Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'switches'),
    'SW-DIM-600W', 'Dimmer Switch 600W', 'Electronic dimmer switch for incandescent and LED lights',
    '120V', '5A', 600, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 2.0}', 0.3,
    true, 'single_gang', '14 AWG', '15A', 18.50, 15, 'simple',
    ARRAY['dimmer', 'switch', 'variable', 'led compatible'],
    ARRAY['dimmer control', 'light dimmer', 'variable switch'], 'rectangle',
    'Lutron', 'DVCL-153P-WH'
);

-- Smart Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'switches'),
    'SW-SMART-15A', 'Smart WiFi Switch', 'WiFi-enabled smart switch with app control',
    '120V', '15A', NULL, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.8}', 0.4,
    true, 'single_gang', '14 AWG', '15A', 35.00, 25, 'moderate',
    ARRAY['smart', 'wifi', 'switch', 'app control', 'automation'],
    ARRAY['smart switch', 'WiFi switch', 'connected switch'], 'rectangle',
    'TP-Link', 'HS200'
);

-- =====================================================================================
-- LIGHTING FIXTURES (Category: lighting)
-- =====================================================================================

-- Recessed Light
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'lighting'),
    'LT-REC-LED-12W', 'LED Recessed Light 12W', '6-inch LED recessed downlight, dimmable',
    '120V', '0.1A', 12, 'single', 'ceiling', '{"width": 6, "height": 6, "depth": 4}', 1.2,
    true, 'round', '14 AWG', '15A', 28.50, 30, 'moderate',
    ARRAY['recessed', 'downlight', 'led', 'can light', 'ceiling'],
    ARRAY['can light', 'pot light', 'ceiling light'], 'circle',
    'Halo', 'RL560WH6930R'
);

-- Pendant Light
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'lighting'),
    'LT-PEN-60W', 'Pendant Light Fixture', 'Decorative pendant light for kitchen islands and dining',
    '120V', '0.5A', 60, 'single', 'ceiling', '{"width": 12, "height": 18, "depth": 12}', 3.5,
    true, 'octagon', '14 AWG', '15A', 85.00, 45, 'moderate',
    ARRAY['pendant', 'hanging', 'decorative', 'island light'],
    ARRAY['hanging light', 'suspended light', 'drop light'], 'circle',
    'Progress Lighting', 'P500024-031'
);

-- Chandelier
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'lighting'),
    'LT-CHAN-300W', 'Chandelier Fixture', 'Multi-light chandelier for dining rooms and foyers',
    '120V', '2.5A', 300, 'single', 'ceiling', '{"width": 24, "height": 30, "depth": 24}', 15.0,
    true, 'octagon', '14 AWG', '15A', 250.00, 90, 'complex',
    ARRAY['chandelier', 'multi light', 'decorative', 'formal'],
    ARRAY['crystal light', 'formal lighting', 'dining light'], 'circle',
    'Kichler', '43185NIL18'
);

-- Emergency Exit Light
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'lighting'),
    'LT-EXIT-LED', 'LED Exit Sign', 'Emergency exit sign with battery backup',
    '120V', '0.1A', 5, 'single', 'wall', '{"width": 13, "height": 7, "depth": 2}', 2.0,
    true, 'single_gang', '14 AWG', '15A', 45.00, 30, 'simple',
    ARRAY['exit', 'emergency', 'safety', 'led', 'battery backup'],
    ARRAY['exit sign', 'emergency light', 'safety sign'], 'rectangle',
    'Lithonia', 'LQM S W 3 G EL N'
);

-- =====================================================================================
-- ELECTRICAL PANELS (Category: panels)
-- =====================================================================================

-- Main Electrical Panel
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'panels'),
    'PAN-MAIN-200A', 'Main Electrical Panel 200A', '200 amp main electrical service panel',
    '240V', '200A', NULL, 'single', 'wall', '{"width": 14, "height": 20, "depth": 4}', 25.0,
    true, NULL, '200A', 185.00, 240, 'expert',
    ARRAY['main panel', 'service panel', 'breaker box', '200amp'],
    ARRAY['electrical panel', 'breaker panel', 'load center'], 'rectangle',
    'Square D', 'QO120M200PC'
);

-- Sub Panel
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'panels'),
    'PAN-SUB-100A', 'Sub Panel 100A', '100 amp electrical sub panel for additions',
    '240V', '100A', NULL, 'single', 'wall', '{"width": 12, "height": 16, "depth": 4}', 15.0,
    true, NULL, '100A', 125.00, 180, 'expert',
    ARRAY['sub panel', 'subpanel', 'distribution', '100amp'],
    ARRAY['sub-panel', 'secondary panel', 'branch panel'], 'rectangle',
    'Eaton', 'BR1220B100'
);

-- =====================================================================================
-- DATA AND COMMUNICATION (Category: data_comm)
-- =====================================================================================

-- Ethernet Jack
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'data_comm'),
    'DATA-ETH-CAT6', 'Ethernet Jack Cat6', 'Category 6 ethernet wall jack for network connections',
    'low_voltage', NULL, NULL, 'dc', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.2,
    true, 'single_gang', 'Cat6', 8.50, 20, 'moderate',
    ARRAY['ethernet', 'network', 'cat6', 'data', 'rj45'],
    ARRAY['network jack', 'data jack', 'internet outlet'], 'rectangle',
    'Leviton', '61110-RW6'
);

-- Phone Jack
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'data_comm'),
    'DATA-PHONE-RJ11', 'Phone Jack RJ11', 'Standard telephone wall jack',
    'low_voltage', NULL, NULL, 'dc', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.15,
    true, 'single_gang', 'Cat3', 3.25, 15, 'simple',
    ARRAY['phone', 'telephone', 'rj11', 'landline'],
    ARRAY['telephone jack', 'phone outlet', 'landline jack'], 'rectangle',
    'Leviton', '40253-W'
);

-- Cable TV Jack
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'data_comm'),
    'DATA-COAX-F', 'Coaxial Cable Jack', 'F-connector coaxial jack for cable TV and internet',
    'low_voltage', NULL, NULL, 'dc', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.2,
    true, 'single_gang', 'RG6', 4.75, 15, 'simple',
    ARRAY['coax', 'cable', 'tv', 'f-connector', 'coaxial'],
    ARRAY['cable jack', 'TV outlet', 'coax outlet'], 'rectangle',
    'Leviton', '40681-W'
);

-- =====================================================================================
-- HVAC ELECTRICAL (Category: hvac_electrical)
-- =====================================================================================

-- Thermostat
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'hvac_electrical'),
    'HVAC-THERM-PROG', 'Programmable Thermostat', 'Digital programmable thermostat for HVAC control',
    '24V', '0.1A', 5, 'dc', 'wall', '{"width": 4.5, "height": 3.5, "depth": 1.5}', 0.8,
    true, 'single_gang', '18 AWG', 35.00, 30, 'moderate',
    ARRAY['thermostat', 'hvac', 'temperature', 'programmable'],
    ARRAY['temp control', 'climate control', 'HVAC control'], 'rectangle',
    'Honeywell', 'RTH7600D'
);

-- Smart Thermostat
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'hvac_electrical'),
    'HVAC-SMART-THERM', 'Smart WiFi Thermostat', 'WiFi-enabled smart thermostat with app control',
    '24V', '0.2A', 8, 'dc', 'wall', '{"width": 4.5, "height": 3.5, "depth": 1.5}', 1.0,
    true, 'single_gang', '18 AWG', 125.00, 45, 'moderate',
    ARRAY['smart thermostat', 'wifi', 'app control', 'nest', 'ecobee'],
    ARRAY['smart temp control', 'connected thermostat', 'WiFi thermostat'], 'rectangle',
    'Nest', 'T3007ES'
);

-- =====================================================================================
-- SAFETY SYSTEMS (Category: safety)
-- =====================================================================================

-- Smoke Detector
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'safety'),
    'SAFE-SMOKE-HARD', 'Hardwired Smoke Detector', 'Hardwired smoke detector with battery backup',
    '120V', '0.1A', 5, 'single', 'ceiling', '{"width": 6, "height": 6, "depth": 2}', 0.8,
    true, 'octagon', '14 AWG', '15A', 25.00, 25, 'simple',
    ARRAY['smoke detector', 'fire alarm', 'hardwired', 'safety'],
    ARRAY['smoke alarm', 'fire detector', 'smoke sensor'], 'circle',
    'First Alert', 'SA320CN'
);

-- Carbon Monoxide Detector
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'safety'),
    'SAFE-CO-HARD', 'Hardwired CO Detector', 'Hardwired carbon monoxide detector with digital display',
    '120V', '0.1A', 3, 'single', 'wall', '{"width": 5, "height": 5, "depth": 1.5}', 0.6,
    true, 'single_gang', '14 AWG', '15A', 35.00, 20, 'simple',
    ARRAY['carbon monoxide', 'co detector', 'gas detector', 'safety'],
    ARRAY['CO alarm', 'carbon monoxide alarm', 'gas alarm'], 'rectangle',
    'First Alert', 'CO710'
);

-- =====================================================================================
-- SPECIALTY EQUIPMENT (Category: specialty)
-- =====================================================================================

-- Electric Vehicle Charger
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'specialty'),
    'SPEC-EV-40A', 'EV Charger 40A', 'Level 2 electric vehicle charging station',
    '240V', '40A', 9600, 'single', 'wall', '{"width": 12, "height": 18, "depth": 6}', 20.0,
    true, NULL, '6 AWG', '50A', 450.00, 180, 'expert',
    ARRAY['ev charger', 'electric vehicle', 'level 2', 'car charger'],
    ARRAY['EV charging station', 'electric car charger', 'vehicle charger'], 'rectangle',
    'ChargePoint', 'CPH25-L2-P'
);

-- Outdoor Outlet (Weatherproof)
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, nema_rating, ip_rating, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'specialty'),
    'SPEC-OUT-WP-20A', 'Weatherproof Outdoor Outlet', 'GFCI weatherproof outlet for outdoor use',
    '120V', '20A', 2400, 'single', 'wall', '{"width": 4.5, "height": 4.5, "depth": 3}', 1.2,
    'NEMA 5-20R', 'IP65', true, 'single_gang', '12 AWG', '20A', 35.00, 35, 'moderate',
    ARRAY['outdoor', 'weatherproof', 'gfci', 'exterior', 'wet location'],
    ARRAY['exterior outlet', 'outdoor receptacle', 'weather resistant'], 'rectangle',
    'Leviton', '8899-W'
);

-- Whole House Surge Protector
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'specialty'),
    'SPEC-SURGE-WHOLE', 'Whole House Surge Protector', 'Main panel surge protection device',
    '240V', '200A', NULL, 'single', 'panel', '{"width": 4, "height": 6, "depth": 3}', 3.0,
    true, NULL, '10 AWG', '200A', 185.00, 60, 'expert',
    ARRAY['surge protector', 'whole house', 'lightning protection', 'spd'],
    ARRAY['surge protection device', 'lightning protector', 'SPD'], 'rectangle',
    'Eaton', 'CHSPT2SURGE'
);

-- Generator Transfer Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'specialty'),
    'SPEC-GEN-TRANS-100A', 'Generator Transfer Switch', 'Automatic transfer switch for backup generator',
    '240V', '100A', NULL, 'single', 'wall', '{"width": 16, "height": 20, "depth": 6}', 35.0,
    true, NULL, '4 AWG', '100A', 850.00, 360, 'expert',
    ARRAY['generator', 'transfer switch', 'backup power', 'automatic'],
    ARRAY['gen transfer', 'backup switch', 'power transfer'], 'rectangle',
    'Generac', '6854'
);

-- Pool/Spa GFCI Breaker
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'specialty'),
    'SPEC-POOL-GFCI-50A', 'Pool/Spa GFCI Breaker', 'GFCI circuit breaker for pool and spa equipment',
    '240V', '50A', NULL, 'single', 'panel', '{"width": 2, "height": 4, "depth": 3}', 1.5,
    true, NULL, '6 AWG', '50A', 125.00, 45, 'expert',
    ARRAY['pool', 'spa', 'gfci breaker', 'water', 'safety'],
    ARRAY['pool breaker', 'spa breaker', 'water GFCI'], 'rectangle',
    'Square D', 'QO250GFICP'
);

-- Ceiling Fan with Light
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'lighting'),
    'LT-FAN-52IN', 'Ceiling Fan with Light', '52-inch ceiling fan with integrated LED light kit',
    '120V', '1.5A', 75, 'single', 'ceiling', '{"width": 52, "height": 12, "depth": 52}', 25.0,
    true, 'octagon', '14 AWG', '15A', 125.00, 90, 'complex',
    ARRAY['ceiling fan', 'fan light', 'combo', '52 inch'],
    ARRAY['fan with light', 'ceiling fan light', 'combo fan'], 'circle',
    'Hunter', '59270'
);

-- Track Lighting
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'lighting'),
    'LT-TRACK-4FT', 'Track Lighting 4ft', '4-foot track lighting system with adjustable heads',
    '120V', '2A', 120, 'single', 'ceiling', '{"width": 48, "height": 3, "depth": 4}', 8.0,
    true, 'octagon', '14 AWG', '15A', 85.00, 60, 'moderate',
    ARRAY['track lighting', 'adjustable', 'directional', 'spot'],
    ARRAY['track lights', 'rail lighting', 'spot track'], 'rectangle',
    'Lithonia', 'LTH4 MW M4'
);

-- Under Cabinet LED Strip
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'lighting'),
    'LT-UC-LED-24IN', 'Under Cabinet LED Strip', '24-inch LED strip light for under cabinet installation',
    '120V', '0.3A', 18, 'single', 'surface', '{"width": 24, "height": 1, "depth": 0.5}', 1.0,
    true, 'single_gang', '14 AWG', '15A', 35.00, 30, 'simple',
    ARRAY['under cabinet', 'led strip', 'task lighting', 'kitchen'],
    ARRAY['cabinet lighting', 'strip light', 'task light'], 'rectangle',
    'Kichler', '12313WH27'
);

-- Bathroom Exhaust Fan
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'hvac_electrical'),
    'HVAC-EXFAN-80CFM', 'Bathroom Exhaust Fan', '80 CFM bathroom exhaust fan with humidity sensor',
    '120V', '0.8A', 45, 'single', 'ceiling', '{"width": 11, "height": 4, "depth": 11}', 6.0,
    true, 'octagon', '14 AWG', '15A', 65.00, 75, 'moderate',
    ARRAY['exhaust fan', 'bathroom fan', 'ventilation', 'humidity'],
    ARRAY['bath fan', 'vent fan', 'bathroom vent'], 'rectangle',
    'Broan', '678'
);

-- Attic Fan
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'hvac_electrical'),
    'HVAC-ATTIC-1200CFM', 'Attic Exhaust Fan', '1200 CFM attic exhaust fan with thermostat control',
    '120V', '3A', 180, 'single', 'ceiling', '{"width": 24, "height": 8, "depth": 24}', 15.0,
    true, 'octagon', '12 AWG', '20A', 185.00, 120, 'complex',
    ARRAY['attic fan', 'roof fan', 'ventilation', 'thermostat'],
    ARRAY['roof vent', 'attic vent', 'exhaust fan'], 'circle',
    'Master Flow', 'EGV5'
);

-- Doorbell Transformer
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'specialty'),
    'SPEC-DB-TRANS-16V', 'Doorbell Transformer', '16V doorbell transformer for chime systems',
    '120V', '0.5A', 30, 'single', 'wall', '{"width": 3, "height": 4, "depth": 2}', 1.0,
    true, 'single_gang', '14 AWG', '15A', 18.00, 20, 'simple',
    ARRAY['doorbell', 'transformer', 'chime', '16v', 'low voltage'],
    ARRAY['door chime transformer', 'bell transformer'], 'rectangle',
    'Nutone', 'C907'
);

-- Motion Sensor Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'switches'),
    'SW-MOTION-600W', 'Motion Sensor Switch', 'Occupancy sensor switch with manual override',
    '120V', '5A', 600, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 2}', 0.4,
    true, 'single_gang', '14 AWG', '15A', 28.50, 20, 'simple',
    ARRAY['motion sensor', 'occupancy', 'automatic', 'pir'],
    ARRAY['occupancy sensor', 'motion switch', 'auto switch'], 'rectangle',
    'Lutron', 'MS-OPS5M-WH'
);

-- Timer Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'switches'),
    'SW-TIMER-15A', 'Timer Switch', 'Digital timer switch for automatic lighting control',
    '120V', '15A', NULL, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 2}', 0.5,
    true, 'single_gang', '14 AWG', '15A', 22.00, 15, 'simple',
    ARRAY['timer', 'switch', 'automatic', 'schedule', 'digital'],
    ARRAY['timer control', 'scheduled switch', 'time switch'], 'rectangle',
    'Leviton', 'DDS15-1LZ'
);

-- Photocell Switch
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'switches'),
    'SW-PHOTO-1000W', 'Photocell Switch', 'Dusk-to-dawn photocell switch for outdoor lighting',
    '120V', '8A', 1000, 'single', 'wall', '{"width": 4.5, "height": 2.75, "depth": 2}', 0.6,
    true, 'single_gang', '14 AWG', '15A', 35.00, 25, 'moderate',
    ARRAY['photocell', 'dusk to dawn', 'light sensor', 'outdoor'],
    ARRAY['photo switch', 'light sensor switch', 'dawn switch'], 'rectangle',
    'Intermatic', 'EI600WC'
);

-- Arc Fault Breaker
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'panels'),
    'PAN-AFCI-20A', 'Arc Fault Circuit Breaker', '20A AFCI breaker for bedroom and living area circuits',
    '120V', '20A', NULL, 'single', 'panel', '{"width": 1, "height": 4, "depth": 3}', 0.8,
    true, NULL, '12 AWG', '20A', 45.00, 15, 'simple',
    ARRAY['afci', 'arc fault', 'breaker', 'safety', '20amp'],
    ARRAY['arc fault breaker', 'AFCI breaker', 'arc protection'], 'rectangle',
    'Square D', 'QO120AFIC'
);

-- GFCI Breaker
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'panels'),
    'PAN-GFCI-20A', 'GFCI Circuit Breaker', '20A GFCI breaker for wet location protection',
    '120V', '20A', NULL, 'single', 'panel', '{"width": 1, "height": 4, "depth": 3}', 1.0,
    true, NULL, '12 AWG', '20A', 65.00, 20, 'moderate',
    ARRAY['gfci breaker', 'ground fault', 'safety', '20amp'],
    ARRAY['GFCI breaker', 'ground fault breaker', 'GFI breaker'], 'rectangle',
    'Square D', 'QO120GFIC'
);

-- Double Pole Breaker
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'panels'),
    'PAN-DP-30A', 'Double Pole Breaker 30A', '30A double pole breaker for 240V circuits',
    '240V', '30A', NULL, 'single', 'panel', '{"width": 2, "height": 4, "depth": 3}', 1.2,
    true, NULL, '10 AWG', '30A', 25.00, 10, 'simple',
    ARRAY['double pole', '240v', 'breaker', '30amp', 'two pole'],
    ARRAY['2-pole breaker', '240V breaker', 'double breaker'], 'rectangle',
    'Square D', 'QO230CP'
);

-- Security System Panel
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, circuit_breaker_size, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'safety'),
    'SAFE-SEC-PANEL', 'Security System Panel', 'Main security system control panel with backup battery',
    '120V', '2A', 50, 'single', 'wall', '{"width": 12, "height": 16, "depth": 4}', 8.0,
    true, 'single_gang', '14 AWG', '15A', 185.00, 120, 'complex',
    ARRAY['security', 'alarm', 'panel', 'control', 'monitoring'],
    ARRAY['alarm panel', 'security control', 'monitoring panel'], 'rectangle',
    'Honeywell', 'VISTA-20P'
);

-- Intercom System
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'data_comm'),
    'DATA-INTERCOM', 'Intercom Station', 'Two-way intercom communication station',
    'low_voltage', NULL, 10, 'dc', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.8,
    true, 'single_gang', '18 AWG', 85.00, 45, 'moderate',
    ARRAY['intercom', 'communication', 'two way', 'talk'],
    ARRAY['intercom station', 'comm station', 'talk station'], 'rectangle',
    'Nutone', 'NM200WH'
);

-- Fiber Optic Jack
INSERT INTO electrical_symbols (
    category_id, symbol_code, name, description, voltage, amperage, wattage,
    phase_type, mounting_type, dimensions, weight_lbs, ul_listed,
    box_type, wire_gauge, base_material_cost,
    installation_time_minutes, labor_complexity, detection_keywords,
    common_variations, symbol_shape, manufacturer, model_number
) VALUES (
    (SELECT id FROM electrical_symbol_categories WHERE name = 'data_comm'),
    'DATA-FIBER-SC', 'Fiber Optic Jack', 'Single mode fiber optic wall jack with SC connector',
    'low_voltage', NULL, NULL, 'dc', 'wall', '{"width": 4.5, "height": 2.75, "depth": 1.5}', 0.2,
    true, 'single_gang', 'Fiber', 25.00, 30, 'complex',
    ARRAY['fiber', 'optic', 'sc connector', 'high speed'],
    ARRAY['fiber jack', 'optical jack', 'fiber outlet'], 'rectangle',
    'Panduit', 'CJ5E88TGYW'
);

-- =====================================================================================
-- COMPLETION MESSAGE
-- =====================================================================================
-- This seeding script has populated the database with 50+ electrical symbols
-- covering all major categories: outlets, switches, lighting, panels, data/comm,
-- HVAC electrical, safety systems, and specialty equipment
-- Each symbol includes comprehensive specifications and material information
