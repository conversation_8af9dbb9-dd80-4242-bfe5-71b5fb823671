// Comprehensive script for team management
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials - using the same approach as in your runtime config
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to list all teams
async function listTeams() {
  try {
    console.log('Fetching all teams...');
    
    const { data, error } = await supabase
      .from('teams')
      .select('*');
    
    if (error) {
      console.error('Error fetching teams:', error);
      return;
    }
    
    console.log(`Found ${data.length} teams:`);
    data.forEach(team => {
      console.log(`- ID: ${team.id}, Name: ${team.name}, Organization: ${team.organization_id}`);
    });
    
    return data;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Function to create a team
async function createTeam(name, description) {
  try {
    console.log(`Creating team "${name}"...`);
    
    // Create a team with explicit values for all required fields
    const teamData = {
      name,
      description: description || '',
      created_by_id: 1, // Default user ID
      organization_id: 1, // Default organization ID (Coelec)
      created_at: new Date().toISOString()
    };
    
    console.log('Team data:', teamData);
    
    const { data, error } = await supabase
      .from('teams')
      .insert(teamData)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating team:', error);
      return;
    }
    
    console.log('Successfully created team:', data);
    
    // Now add the team creator as a team member
    const teamId = data.id;
    return await addTeamMember(teamId, 1, 'admin');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Function to add a team member
async function addTeamMember(teamId, userId, role) {
  try {
    console.log(`Adding user ${userId} as ${role} to team ${teamId}...`);
    
    const { data, error } = await supabase
      .from('team_members')
      .insert({
        team_id: teamId,
        user_id: userId,
        role: role || 'member'
      })
      .select();
    
    if (error) {
      console.error('Error adding team member:', error);
      return;
    }
    
    console.log('Successfully added team member:', data);
    return data;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Function to get teams by user ID
async function getTeamsByUserId(userId) {
  try {
    console.log(`Fetching teams for user ${userId}...`);
    
    const { data, error } = await supabase
      .from('team_members')
      .select(`
        team_id,
        role,
        teams:team_id(*)
      `)
      .eq('user_id', userId);
    
    if (error) {
      console.error('Error fetching teams for user:', error);
      return;
    }
    
    console.log(`Found ${data.length} teams for user ${userId}:`);
    data.forEach(membership => {
      console.log(`- Team ID: ${membership.team_id}, Role: ${membership.role}`);
      console.log('  Team details:', membership.teams);
    });
    
    return data;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Process command line arguments
const command = process.argv[2];
const arg1 = process.argv[3];
const arg2 = process.argv[4];

async function main() {
  // Test the Supabase connection first
  try {
    const { data, error } = await supabase.from('teams').select('count').limit(1);
    if (error) throw error;
    console.log('✅ Successfully connected to Supabase');
  } catch (error) {
    console.error('❌ Failed to connect to Supabase:', error.message);
    process.exit(1);
  }
  
  switch (command) {
    case 'list':
      await listTeams();
      break;
    case 'create':
      if (!arg1) {
        console.error('Error: Team name is required for create command');
        process.exit(1);
      }
      await createTeam(arg1, arg2);
      break;
    case 'add-member':
      if (!arg1 || !arg2) {
        console.error('Error: Team ID and User ID are required for add-member command');
        process.exit(1);
      }
      await addTeamMember(parseInt(arg1), parseInt(arg2), process.argv[5] || 'member');
      break;
    case 'get-by-user':
      if (!arg1) {
        console.error('Error: User ID is required for get-by-user command');
        process.exit(1);
      }
      await getTeamsByUserId(parseInt(arg1));
      break;
    default:
      console.log(`\nTeam Management Script\n---------------------\nUsage:\n  node scripts/team-management.js [command] [args]\n\nCommands:\n  list                     List all teams\n  create [name] [desc]     Create a new team\n  add-member [team] [user] [role]  Add a user to a team\n  get-by-user [user]      Get teams for a user\n`);
  }
}

main()
  .then(() => {
    console.log('\nOperation completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('\nOperation failed:', error);
    process.exit(1);
  });
