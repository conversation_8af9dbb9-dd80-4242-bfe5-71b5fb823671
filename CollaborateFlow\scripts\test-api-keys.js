#!/usr/bin/env node

/**
 * API KEY VERIFICATION SCRIPT
 * Tests all required API keys and third-party service access
 */

console.log('🔑 API Key Verification Test');
console.log('============================');

const fs = require('fs');
const https = require('https');

const apiTests = {
  passed: 0,
  failed: 0,
  total: 0,
  results: []
};

async function testAPIKey(serviceName, testFn) {
  apiTests.total++;
  console.log(`\n🔍 Testing ${serviceName}...`);
  
  try {
    const result = await testFn();
    if (result.success) {
      console.log(`✅ ${serviceName}: ${result.message}`);
      apiTests.passed++;
      apiTests.results.push({ service: serviceName, status: 'PASS', message: result.message });
    } else {
      console.log(`❌ ${serviceName}: ${result.message}`);
      apiTests.failed++;
      apiTests.results.push({ service: serviceName, status: 'FAIL', message: result.message });
    }
  } catch (error) {
    console.log(`❌ ${serviceName}: ERROR - ${error.message}`);
    apiTests.failed++;
    apiTests.results.push({ service: serviceName, status: 'ERROR', message: error.message });
  }
}

// =============================================================================
// ENVIRONMENT VARIABLE CHECKS
// =============================================================================

async function checkEnvironmentFile() {
  if (!fs.existsSync('.env.local')) {
    return { success: false, message: 'Environment file .env.local not found' };
  }
  
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const requiredVars = [
    'OPENROUTER_API_KEY',
    'DOCUSIGN_INTEGRATION_KEY',
    'DOCUSIGN_SECRET_KEY',
    'SENDGRID_API_KEY'
  ];
  
  const missingVars = requiredVars.filter(varName => !envContent.includes(varName));
  
  if (missingVars.length > 0) {
    return { success: false, message: `Missing environment variables: ${missingVars.join(', ')}` };
  }
  
  return { success: true, message: 'All required environment variables present' };
}

// =============================================================================
// API KEY TESTS
// =============================================================================

async function testOpenRouterAPI() {
  const apiKey = process.env.OPENROUTER_API_KEY;
  
  if (!apiKey) {
    return { success: false, message: 'OPENROUTER_API_KEY not found in environment' };
  }
  
  if (apiKey.length < 20) {
    return { success: false, message: 'OPENROUTER_API_KEY appears to be invalid (too short)' };
  }
  
  // Test actual API call
  try {
    const response = await makeHTTPSRequest({
      hostname: 'openrouter.ai',
      path: '/api/v1/models',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.statusCode === 200) {
      return { success: true, message: 'OpenRouter API key valid and accessible' };
    } else {
      return { success: false, message: `OpenRouter API returned status ${response.statusCode}` };
    }
  } catch (error) {
    return { success: false, message: `OpenRouter API test failed: ${error.message}` };
  }
}

async function testDocuSignAPI() {
  const integrationKey = process.env.DOCUSIGN_INTEGRATION_KEY;
  const secretKey = process.env.DOCUSIGN_SECRET_KEY;
  
  if (!integrationKey || !secretKey) {
    return { success: false, message: 'DocuSign credentials not found in environment' };
  }
  
  if (integrationKey.length < 30 || secretKey.length < 30) {
    return { success: false, message: 'DocuSign credentials appear to be invalid (too short)' };
  }
  
  // For now, just validate format since DocuSign requires OAuth flow
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(integrationKey)) {
    return { success: false, message: 'DocuSign integration key format invalid' };
  }
  
  return { success: true, message: 'DocuSign credentials present and properly formatted' };
}

async function testSendGridAPI() {
  const apiKey = process.env.SENDGRID_API_KEY;
  
  if (!apiKey) {
    return { success: false, message: 'SENDGRID_API_KEY not found in environment' };
  }
  
  if (!apiKey.startsWith('SG.')) {
    return { success: false, message: 'SendGrid API key format invalid (should start with SG.)' };
  }
  
  // Test actual API call
  try {
    const response = await makeHTTPSRequest({
      hostname: 'api.sendgrid.com',
      path: '/v3/user/profile',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.statusCode === 200) {
      return { success: true, message: 'SendGrid API key valid and accessible' };
    } else {
      return { success: false, message: `SendGrid API returned status ${response.statusCode}` };
    }
  } catch (error) {
    return { success: false, message: `SendGrid API test failed: ${error.message}` };
  }
}

async function testSupplierAPIs() {
  // Check for supplier API configurations
  const supplierConfigs = [
    process.env.SUPPLIER_API_KEY_1,
    process.env.SUPPLIER_API_KEY_2,
    process.env.ELECTRICAL_SUPPLIER_API_KEY
  ].filter(Boolean);
  
  if (supplierConfigs.length === 0) {
    return { success: false, message: 'No supplier API keys found in environment' };
  }
  
  return { success: true, message: `${supplierConfigs.length} supplier API configuration(s) found` };
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

function makeHTTPSRequest(options) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// =============================================================================
// MAIN EXECUTION
// =============================================================================

async function runAPIKeyTests() {
  console.log('🚀 Starting API Key Verification...\n');
  
  // Load environment variables
  if (fs.existsSync('.env.local')) {
    const envContent = fs.readFileSync('.env.local', 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  }
  
  // Run all API tests
  await testAPIKey('Environment File', checkEnvironmentFile);
  await testAPIKey('OpenRouter API', testOpenRouterAPI);
  await testAPIKey('DocuSign API', testDocuSignAPI);
  await testAPIKey('SendGrid API', testSendGridAPI);
  await testAPIKey('Supplier APIs', testSupplierAPIs);
  
  // Results Summary
  console.log('\n' + '='.repeat(50));
  console.log('🔑 API KEY VERIFICATION RESULTS');
  console.log('='.repeat(50));
  
  const passRate = ((apiTests.passed / apiTests.total) * 100).toFixed(1);
  console.log(`\n📊 Overall: ${apiTests.passed}/${apiTests.total} API tests passed (${passRate}%)`);
  
  if (apiTests.passed === apiTests.total) {
    console.log('\n🎉 EXCELLENT! All API keys verified and functional');
    console.log('✅ Ready to proceed with implementation');
  } else {
    console.log('\n⚠️  API key issues detected');
    console.log('❌ Resolve API access before proceeding');
  }
  
  console.log('\n📋 Detailed Results:');
  apiTests.results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`  ${status} ${result.service}: ${result.message}`);
  });
  
  console.log('\n🔧 Required Actions:');
  const failedTests = apiTests.results.filter(r => r.status !== 'PASS');
  
  if (failedTests.length === 0) {
    console.log('  ✅ No actions required - all API keys functional');
  } else {
    failedTests.forEach(test => {
      console.log(`  ❌ Fix ${test.service}: ${test.message}`);
    });
    
    console.log('\n🚨 BLOCKERS IDENTIFIED:');
    console.log('  1. Obtain missing API keys from appropriate sources');
    console.log('  2. Verify API key permissions and access levels');
    console.log('  3. Test API connectivity and authentication');
    console.log('  4. Do NOT proceed with mock implementations');
  }
  
  return apiTests.passed === apiTests.total;
}

// Run the API key verification
runAPIKeyTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('API key verification failed:', error);
  process.exit(1);
});
