#!/usr/bin/env node

/**
 * API KEY VERIFICATION SCRIPT
 * Tests all required API keys and third-party service access
 */

console.log('🔑 API Key Verification Test');
console.log('============================');

import fs from 'fs';
import https from 'https';

const apiTests = {
  passed: 0,
  failed: 0,
  total: 0,
  results: []
};

async function testAPIKey(serviceName, testFn) {
  apiTests.total++;
  console.log(`\n🔍 Testing ${serviceName}...`);

  try {
    const result = await testFn();
    if (result.success) {
      console.log(`✅ ${serviceName}: ${result.message}`);
      apiTests.passed++;
      apiTests.results.push({ service: serviceName, status: 'PASS', message: result.message });
    } else {
      console.log(`❌ ${serviceName}: ${result.message}`);
      apiTests.failed++;
      apiTests.results.push({ service: serviceName, status: 'FAIL', message: result.message });
    }
  } catch (error) {
    console.log(`❌ ${serviceName}: ERROR - ${error.message}`);
    apiTests.failed++;
    apiTests.results.push({ service: serviceName, status: 'ERROR', message: error.message });
  }
}

// =============================================================================
// ENVIRONMENT VARIABLE CHECKS
// =============================================================================

async function checkEnvironmentFile() {
  if (!fs.existsSync('.env.local')) {
    return { success: false, message: 'Environment file .env.local not found' };
  }

  const envContent = fs.readFileSync('.env.local', 'utf8');
  const requiredVars = [
    'OPENROUTER_API_KEY',
    'USE_CUSTOM_SIGNATURE',
    'EMAIL_FALLBACK_MODE',
    'USE_CLIENT_SUPPLIER_AUTH'
  ];

  const missingVars = requiredVars.filter(varName => !envContent.includes(varName));

  if (missingVars.length > 0) {
    return { success: false, message: `Missing environment variables: ${missingVars.join(', ')}` };
  }

  return { success: true, message: 'All required environment variables present' };
}

// =============================================================================
// API KEY TESTS
// =============================================================================

async function testOpenRouterAPI() {
  const apiKey = process.env.OPENROUTER_API_KEY;

  if (!apiKey) {
    return { success: false, message: 'OPENROUTER_API_KEY not found in environment' };
  }

  if (apiKey.length < 20) {
    return { success: false, message: 'OPENROUTER_API_KEY appears to be invalid (too short)' };
  }

  // Test actual API call
  try {
    const response = await makeHTTPSRequest({
      hostname: 'openrouter.ai',
      path: '/api/v1/models',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.statusCode === 200) {
      return { success: true, message: 'OpenRouter API key valid and accessible' };
    } else {
      return { success: false, message: `OpenRouter API returned status ${response.statusCode}` };
    }
  } catch (error) {
    return { success: false, message: `OpenRouter API test failed: ${error.message}` };
  }
}

async function testCustomSignatureAPI() {
  const useCustomSignature = process.env.USE_CUSTOM_SIGNATURE;
  const encryptionKey = process.env.SIGNATURE_ENCRYPTION_KEY;

  if (useCustomSignature !== 'true') {
    return { success: false, message: 'Custom signature not enabled in environment' };
  }

  if (!encryptionKey || encryptionKey.length < 20) {
    return { success: false, message: 'Signature encryption key missing or too short' };
  }

  // Check for required signature configuration
  const requiredSignatureVars = [
    'SIGNATURE_HASH_ALGORITHM',
    'SIGNATURE_CANVAS_WIDTH',
    'SIGNATURE_CANVAS_HEIGHT'
  ];

  const envContent = fs.readFileSync('.env.local', 'utf8');
  const missingVars = requiredSignatureVars.filter(varName => !envContent.includes(varName));

  if (missingVars.length > 0) {
    return { success: false, message: `Missing signature configuration: ${missingVars.join(', ')}` };
  }

  return { success: true, message: 'Custom signature system configured and ready' };
}

async function testEmailFallbackAPI() {
  const fallbackMode = process.env.EMAIL_FALLBACK_MODE;
  const consoleLogging = process.env.USE_CONSOLE_EMAIL_LOGGING;

  if (fallbackMode !== 'true') {
    return { success: false, message: 'Email fallback mode not enabled' };
  }

  if (consoleLogging !== 'true') {
    return { success: false, message: 'Console email logging not enabled' };
  }

  // Check if email service exists
  if (!fs.existsSync('server/services/emailService.ts')) {
    return { success: false, message: 'Email service file not found' };
  }

  return { success: true, message: 'Email fallback system configured (console logging enabled)' };
}

async function testClientSupplierAuth() {
  const clientAuth = process.env.USE_CLIENT_SUPPLIER_AUTH;
  const fallbackMode = process.env.SUPPLIER_FALLBACK_MODE;
  const manualEntry = process.env.ENABLE_MANUAL_PRICE_ENTRY;

  if (clientAuth !== 'true') {
    return { success: false, message: 'Client supplier authentication not enabled' };
  }

  if (fallbackMode !== 'true') {
    return { success: false, message: 'Supplier fallback mode not enabled' };
  }

  if (manualEntry !== 'true') {
    return { success: false, message: 'Manual price entry not enabled' };
  }

  // Check for supported suppliers configuration
  const supportedSuppliers = process.env.SUPPORTED_SUPPLIERS;
  if (!supportedSuppliers || supportedSuppliers.split(',').length < 3) {
    return { success: false, message: 'Insufficient supported suppliers configured' };
  }

  return { success: true, message: `Client-based supplier authentication ready (${supportedSuppliers.split(',').length} suppliers)` };
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

function makeHTTPSRequest(options) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// =============================================================================
// MAIN EXECUTION
// =============================================================================

async function runAPIKeyTests() {
  console.log('🚀 Starting API Key Verification...\n');

  // Load environment variables
  if (fs.existsSync('.env.local')) {
    const envContent = fs.readFileSync('.env.local', 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  }

  // Run all API tests with alternative approach
  await testAPIKey('Environment File', checkEnvironmentFile);
  await testAPIKey('OpenRouter API', testOpenRouterAPI);
  await testAPIKey('Custom Signature System', testCustomSignatureAPI);
  await testAPIKey('Email Fallback System', testEmailFallbackAPI);
  await testAPIKey('Client Supplier Authentication', testClientSupplierAuth);

  // Results Summary
  console.log('\n' + '='.repeat(50));
  console.log('🔑 API KEY VERIFICATION RESULTS');
  console.log('='.repeat(50));

  const passRate = ((apiTests.passed / apiTests.total) * 100).toFixed(1);
  console.log(`\n📊 Overall: ${apiTests.passed}/${apiTests.total} API tests passed (${passRate}%)`);

  if (apiTests.passed === apiTests.total) {
    console.log('\n🎉 EXCELLENT! All API keys verified and functional');
    console.log('✅ Ready to proceed with implementation');
  } else {
    console.log('\n⚠️  API key issues detected');
    console.log('❌ Resolve API access before proceeding');
  }

  console.log('\n📋 Detailed Results:');
  apiTests.results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`  ${status} ${result.service}: ${result.message}`);
  });

  console.log('\n🔧 Required Actions:');
  const failedTests = apiTests.results.filter(r => r.status !== 'PASS');

  if (failedTests.length === 0) {
    console.log('  ✅ No actions required - all API keys functional');
  } else {
    failedTests.forEach(test => {
      console.log(`  ❌ Fix ${test.service}: ${test.message}`);
    });

    console.log('\n🚨 BLOCKERS IDENTIFIED:');
    console.log('  1. Obtain missing API keys from appropriate sources');
    console.log('  2. Verify API key permissions and access levels');
    console.log('  3. Test API connectivity and authentication');
    console.log('  4. Do NOT proceed with mock implementations');
  }

  return apiTests.passed === apiTests.total;
}

// Run the API key verification
runAPIKeyTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('API key verification failed:', error);
  process.exit(1);
});
