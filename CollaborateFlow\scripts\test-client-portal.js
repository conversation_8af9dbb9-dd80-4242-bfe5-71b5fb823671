#!/usr/bin/env node

/**
 * T2.2 CLIENT PORTAL TEST SCRIPT
 * Tests the client portal implementation including authentication, quotes, and change requests
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001';
const TEST_CLIENT_ID = 'client_test_001';
const TEST_ACCESS_TOKEN = 'test_access_token_123';

console.log('🧪 T2.2 Client Portal Test Suite');
console.log('=================================');

async function testClientAuthentication() {
  console.log('\n🔐 Test 1: Client Authentication');
  
  try {
    const response = await fetch(`${BASE_URL}/api/client/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        client_id: TEST_CLIENT_ID,
        access_token: TEST_ACCESS_TOKEN
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Client authentication successful');
      console.log(`   - Client: ${data.client.name}`);
      console.log(`   - Email: ${data.client.email}`);
      console.log(`   - Company: ${data.client.company}`);
      console.log(`   - Session Token: ${data.session_token ? 'Generated' : 'Missing'}`);
      return data.session_token;
    } else {
      console.log('❌ Client authentication failed:', data.error || data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Client authentication error:', error.message);
    return null;
  }
}

async function testClientQuotes(sessionToken) {
  console.log('\n📋 Test 2: Client Quotes Retrieval');
  
  try {
    const response = await fetch(`${BASE_URL}/api/client/quotes`, {
      method: 'GET',
      headers: {
        'X-Client-Id': TEST_CLIENT_ID,
        'X-Session-Token': sessionToken
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Client quotes retrieved successfully');
      console.log(`   - Total quotes: ${data.total}`);
      console.log(`   - Quotes found: ${data.quotes.length}`);
      
      if (data.quotes.length > 0) {
        const quote = data.quotes[0];
        console.log(`   - Sample quote: ${quote.number} - ${quote.title}`);
        console.log(`   - Status: ${quote.status}`);
        console.log(`   - Amount: $${quote.total_cost}`);
        return quote.id;
      }
      
      return null;
    } else {
      console.log('❌ Client quotes retrieval failed:', data.error || data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Client quotes error:', error.message);
    return null;
  }
}

async function testQuoteDetails(sessionToken, quoteId) {
  console.log('\n📄 Test 3: Quote Details');
  
  if (!quoteId) {
    console.log('⚠️  Skipping quote details test - no quote ID available');
    return false;
  }
  
  try {
    const response = await fetch(`${BASE_URL}/api/client/quotes/${quoteId}`, {
      method: 'GET',
      headers: {
        'X-Client-Id': TEST_CLIENT_ID,
        'X-Session-Token': sessionToken
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Quote details retrieved successfully');
      console.log(`   - Quote ID: ${data.quote.id}`);
      console.log(`   - Title: ${data.quote.title}`);
      console.log(`   - Status: ${data.quote.status}`);
      console.log(`   - Materials: ${data.quote.quote_materials?.length || 0} items`);
      console.log(`   - Labor items: ${data.quote.quote_labor_items?.length || 0} items`);
      return true;
    } else {
      console.log('❌ Quote details failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Quote details error:', error.message);
    return false;
  }
}

async function testQuoteApproval(sessionToken, quoteId) {
  console.log('\n✅ Test 4: Quote Approval');
  
  if (!quoteId) {
    console.log('⚠️  Skipping quote approval test - no quote ID available');
    return false;
  }
  
  try {
    const mockSignature = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    const response = await fetch(`${BASE_URL}/api/client/quotes/${quoteId}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': TEST_CLIENT_ID,
        'X-Session-Token': sessionToken
      },
      body: JSON.stringify({
        signature: mockSignature,
        approval_notes: 'Test approval from client portal'
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Quote approval successful');
      console.log(`   - Quote ID: ${data.quote_id}`);
      console.log(`   - New Status: ${data.status}`);
      console.log(`   - Message: ${data.message}`);
      return true;
    } else {
      console.log('❌ Quote approval failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Quote approval error:', error.message);
    return false;
  }
}

async function testChangeRequestSubmission(sessionToken, quoteId) {
  console.log('\n📝 Test 5: Change Request Submission');
  
  if (!quoteId) {
    console.log('⚠️  Skipping change request test - no quote ID available');
    return false;
  }
  
  try {
    const response = await fetch(`${BASE_URL}/api/client/change-requests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': TEST_CLIENT_ID,
        'X-Session-Token': sessionToken
      },
      body: JSON.stringify({
        quote_id: quoteId,
        title: 'Add Additional Outlets',
        description: 'Please add 3 more outlets in the conference room area for additional equipment.',
        priority: 'medium'
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Change request submitted successfully');
      console.log(`   - Request ID: ${data.change_request.id}`);
      console.log(`   - Request Number: ${data.change_request.request_number}`);
      console.log(`   - Title: ${data.change_request.title}`);
      console.log(`   - Status: ${data.change_request.status}`);
      return data.change_request.id;
    } else {
      console.log('❌ Change request submission failed:', data.error || data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Change request error:', error.message);
    return null;
  }
}

async function testChangeRequestsRetrieval(sessionToken) {
  console.log('\n📋 Test 6: Change Requests Retrieval');
  
  try {
    const response = await fetch(`${BASE_URL}/api/client/change-requests`, {
      method: 'GET',
      headers: {
        'X-Client-Id': TEST_CLIENT_ID,
        'X-Session-Token': sessionToken
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Change requests retrieved successfully');
      console.log(`   - Total requests: ${data.total}`);
      console.log(`   - Requests found: ${data.change_requests.length}`);
      
      if (data.change_requests.length > 0) {
        const request = data.change_requests[0];
        console.log(`   - Sample request: ${request.request_number} - ${request.title}`);
        console.log(`   - Status: ${request.status}`);
      }
      
      return true;
    } else {
      console.log('❌ Change requests retrieval failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Change requests error:', error.message);
    return false;
  }
}

async function testClientProfile(sessionToken) {
  console.log('\n👤 Test 7: Client Profile');
  
  try {
    const response = await fetch(`${BASE_URL}/api/client/profile`, {
      method: 'GET',
      headers: {
        'X-Client-Id': TEST_CLIENT_ID,
        'X-Session-Token': sessionToken
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Client profile retrieved successfully');
      console.log(`   - Name: ${data.profile.name}`);
      console.log(`   - Email: ${data.profile.email}`);
      console.log(`   - Company: ${data.profile.company}`);
      console.log(`   - Phone: ${data.profile.phone}`);
      console.log(`   - Created: ${new Date(data.profile.created_at).toLocaleDateString()}`);
      return true;
    } else {
      console.log('❌ Client profile failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Client profile error:', error.message);
    return false;
  }
}

async function testClientLogout(sessionToken) {
  console.log('\n🚪 Test 8: Client Logout');
  
  try {
    const response = await fetch(`${BASE_URL}/api/client/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        session_token: sessionToken
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Client logout successful');
      console.log(`   - Message: ${data.message}`);
      return true;
    } else {
      console.log('❌ Client logout failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Client logout error:', error.message);
    return false;
  }
}

async function testClientPortalComponents() {
  console.log('\n🎨 Test 9: Client Portal Components');
  
  try {
    // Test ClientPortal component import
    const { ClientPortal } = await import('../client/src/pages/ClientPortal.tsx');
    
    if (typeof ClientPortal === 'function') {
      console.log('✅ ClientPortal component structure correct');
    } else {
      console.log('❌ ClientPortal component missing or invalid');
      return false;
    }

    // Test QuoteApproval component import
    const { default: QuoteApproval } = await import('../client/src/components/QuoteApproval.tsx');
    
    if (typeof QuoteApproval === 'function') {
      console.log('✅ QuoteApproval component structure correct');
    } else {
      console.log('❌ QuoteApproval component missing or invalid');
      return false;
    }

    return true;
  } catch (error) {
    console.log('❌ Component verification error:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('Starting T2.2 Client Portal tests...\n');
  
  const tests = [
    { name: 'Client Authentication', fn: testClientAuthentication, returnValue: true },
    { name: 'Client Quotes Retrieval', fn: null, dependency: 'sessionToken' },
    { name: 'Quote Details', fn: null, dependency: 'both' },
    { name: 'Quote Approval', fn: null, dependency: 'both' },
    { name: 'Change Request Submission', fn: null, dependency: 'both' },
    { name: 'Change Requests Retrieval', fn: null, dependency: 'sessionToken' },
    { name: 'Client Profile', fn: null, dependency: 'sessionToken' },
    { name: 'Client Logout', fn: null, dependency: 'sessionToken' },
    { name: 'Client Portal Components', fn: testClientPortalComponents }
  ];

  const results = [];
  let sessionToken = null;
  let quoteId = null;
  
  for (const test of tests) {
    try {
      let result;
      
      if (test.name === 'Client Authentication') {
        sessionToken = await test.fn();
        result = !!sessionToken;
      } else if (test.name === 'Client Quotes Retrieval') {
        quoteId = await testClientQuotes(sessionToken);
        result = !!quoteId;
      } else if (test.name === 'Quote Details') {
        result = await testQuoteDetails(sessionToken, quoteId);
      } else if (test.name === 'Quote Approval') {
        result = await testQuoteApproval(sessionToken, quoteId);
      } else if (test.name === 'Change Request Submission') {
        const changeRequestId = await testChangeRequestSubmission(sessionToken, quoteId);
        result = !!changeRequestId;
      } else if (test.name === 'Change Requests Retrieval') {
        result = await testChangeRequestsRetrieval(sessionToken);
      } else if (test.name === 'Client Profile') {
        result = await testClientProfile(sessionToken);
      } else if (test.name === 'Client Logout') {
        result = await testClientLogout(sessionToken);
      } else if (test.fn) {
        result = await test.fn();
      } else {
        result = false;
      }
      
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.log(`❌ ${test.name} failed with error:`, error.message);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  console.log('\n📋 Test Results Summary');
  console.log('=======================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('\n🎉 All T2.2 Client Portal tests passed!');
    console.log('✅ Complete client portal with authentication: WORKING');
    console.log('✅ Quote viewing and approval interface: WORKING');
    console.log('✅ Change request submission system: WORKING');
    return true;
  } else {
    console.log('\n⚠️  Some tests failed. Check implementation.');
    return false;
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

export { runAllTests };
