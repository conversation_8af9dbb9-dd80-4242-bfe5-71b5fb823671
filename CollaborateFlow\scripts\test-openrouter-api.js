#!/usr/bin/env node

/**
 * OpenRouter API Key Test
 * Tests the real OpenRouter API key with symbol detection
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key] = valueParts.join('=').trim();
  }
});

console.log('🔑 OpenRouter API Key Test');
console.log('==========================');

const apiKey = envVars.OPENROUTER_API_KEY;

if (!apiKey || apiKey.includes('placeholder')) {
  console.error('❌ No valid OpenRouter API key found');
  process.exit(1);
}

console.log('✅ OpenRouter API key found');
console.log(`🔍 Key format: ${apiKey.substring(0, 20)}...${apiKey.substring(apiKey.length - 10)}`);

async function testOpenRouterAPI() {
  try {
    console.log('📡 Testing OpenRouter API connection...');
    
    // Test with a simple text completion first
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://coelec.app',
        'X-Title': 'CoElec Symbol Detection Test'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-haiku',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant. Respond with a simple JSON object containing a test message.'
          },
          {
            role: 'user',
            content: 'Please respond with: {"status": "success", "message": "OpenRouter API is working", "timestamp": "current_time"}'
          }
        ],
        max_tokens: 100,
        temperature: 0.1
      })
    });

    console.log(`📊 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API request failed:', errorText);
      return false;
    }

    const data = await response.json();
    console.log('✅ API response received');
    
    if (data.choices && data.choices[0] && data.choices[0].message) {
      const content = data.choices[0].message.content;
      console.log('📝 AI Response:', content);
      
      // Check usage information
      if (data.usage) {
        console.log('💰 Token usage:');
        console.log(`  - Prompt tokens: ${data.usage.prompt_tokens}`);
        console.log(`  - Completion tokens: ${data.usage.completion_tokens}`);
        console.log(`  - Total tokens: ${data.usage.total_tokens}`);
      }
      
      console.log('\n🎉 OpenRouter API Test SUCCESSFUL!');
      console.log('✅ API key is valid and working');
      console.log('✅ Claude-3-Haiku model accessible');
      console.log('✅ Ready for electrical symbol detection');
      
      return true;
    } else {
      console.error('❌ Unexpected response format:', data);
      return false;
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    return false;
  }
}

// Test symbol detection with a mock electrical floor plan description
async function testSymbolDetection() {
  try {
    console.log('\n🔌 Testing Electrical Symbol Detection...');
    
    const symbolDetectionPrompt = `You are an expert electrical engineer specializing in analyzing floor plans and identifying electrical symbols. Your task is to detect and classify electrical symbols in floor plan images with high accuracy.

ELECTRICAL SYMBOLS TO DETECT:
- Outlets (standard, GFCI, USB, 240V)
- Switches (single-pole, 3-way, dimmer, smart)
- Lights (recessed, pendant, chandelier, emergency)
- Panels (main panel, sub-panel, distribution)
- Data/Communication (ethernet, phone, cable, fiber)
- HVAC electrical (thermostats, controls)
- Safety (smoke detectors, carbon monoxide)
- Specialty (appliance connections, outdoor outlets)

RESPONSE FORMAT:
Return a JSON array of detected symbols with this exact structure:
[
  {
    "type": "outlet|switch|light|panel|data|hvac|safety|specialty",
    "subtype": "specific type (e.g., gfci, dimmer, recessed)",
    "x": number (center x coordinate),
    "y": number (center y coordinate),
    "width": number (bounding box width),
    "height": number (bounding box height),
    "confidence": number (0.0-1.0),
    "properties": {
      "voltage": "120V|240V|low_voltage",
      "amperage": "15A|20A|30A|etc",
      "notes": "any additional details"
    }
  }
]

IMPORTANT:
- Only return valid JSON, no additional text
- Be precise with coordinates and dimensions
- Confidence should reflect actual detection certainty
- Include all visible electrical symbols
- Use standard electrical symbol classifications`;

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://coelec.app',
        'X-Title': 'CoElec Symbol Detection Test'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-sonnet',
        messages: [
          {
            role: 'system',
            content: symbolDetectionPrompt
          },
          {
            role: 'user',
            content: 'Analyze this mock floor plan description: "A residential kitchen with 4 standard outlets along the counters, 2 GFCI outlets near the sink, 3 recessed lights in the ceiling, 1 pendant light over the island, 2 single-pole switches by the entrance, and 1 dimmer switch for the pendant light." Return detected symbols as JSON.'
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Symbol detection request failed:', errorText);
      return false;
    }

    const data = await response.json();
    
    if (data.choices && data.choices[0] && data.choices[0].message) {
      const content = data.choices[0].message.content;
      console.log('🔍 Symbol Detection Response:');
      console.log(content);
      
      // Try to parse as JSON
      try {
        const symbols = JSON.parse(content);
        if (Array.isArray(symbols)) {
          console.log(`\n✅ Successfully detected ${symbols.length} electrical symbols:`);
          symbols.forEach((symbol, index) => {
            console.log(`  ${index + 1}. ${symbol.type} (${symbol.subtype || 'standard'}) - Confidence: ${symbol.confidence}`);
          });
          
          // Validate confidence scores
          const validConfidences = symbols.every(s => s.confidence >= 0 && s.confidence <= 1);
          if (validConfidences) {
            console.log('✅ All confidence scores are in valid range (0-1)');
          } else {
            console.log('⚠️ Some confidence scores are outside valid range');
          }
          
          console.log('\n🎉 Symbol Detection Test SUCCESSFUL!');
          console.log('✅ AI can detect electrical symbols');
          console.log('✅ JSON response format is correct');
          console.log('✅ Confidence scoring implemented');
          console.log('✅ T1.1 OpenRouter integration is WORKING!');
          
          return true;
        } else {
          console.log('⚠️ Response is not a JSON array');
          return false;
        }
      } catch (parseError) {
        console.log('⚠️ Could not parse response as JSON:', parseError.message);
        console.log('Raw response:', content);
        return false;
      }
    } else {
      console.error('❌ Unexpected response format:', data);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Symbol detection test failed:', error.message);
    return false;
  }
}

// Run tests
async function runTests() {
  const basicTest = await testOpenRouterAPI();
  
  if (basicTest) {
    const symbolTest = await testSymbolDetection();
    
    if (symbolTest) {
      console.log('\n🏆 ALL TESTS PASSED!');
      console.log('🎯 T1.1 OpenRouter AI Integration is FULLY FUNCTIONAL');
      console.log('🔗 Ready to integrate with T1.2 Electrical Symbol Database');
      process.exit(0);
    } else {
      console.log('\n⚠️ Basic API works, but symbol detection needs refinement');
      process.exit(1);
    }
  } else {
    console.log('\n❌ Basic API test failed');
    process.exit(1);
  }
}

runTests().catch(error => {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
});
