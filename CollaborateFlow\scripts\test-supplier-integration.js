#!/usr/bin/env node

/**
 * SUPPLIER INTEGRATION TEST SCRIPT
 * Tests the T1.4 supplier integration implementation
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001';
const TEST_ORG_ID = 'test-org-123';

console.log('🧪 T1.4 Supplier Integration Test Suite');
console.log('=====================================');

async function testSupplierSearch() {
  console.log('\n📋 Test 1: Supplier Product Search');
  
  try {
    const response = await fetch(`${BASE_URL}/api/supplier-integration/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Organization-Id': TEST_ORG_ID
      },
      body: JSON.stringify({
        query: 'electrical outlet',
        max_results_per_supplier: 5,
        include_out_of_stock: false,
        price_range: { min: 5, max: 100 }
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Product search successful');
      console.log(`   - Found ${data.summary.total_products} products`);
      console.log(`   - Searched ${data.summary.suppliers_searched} suppliers`);
      console.log(`   - Average price: $${data.summary.average_price}`);
      console.log(`   - Search time: ${data.summary.search_time_ms}ms`);
      return true;
    } else {
      console.log('❌ Product search failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Product search error:', error.message);
    return false;
  }
}

async function testSupplierPricing() {
  console.log('\n💰 Test 2: Supplier Pricing');
  
  try {
    const response = await fetch(`${BASE_URL}/api/supplier-integration/pricing`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Organization-Id': TEST_ORG_ID
      },
      body: JSON.stringify({
        materials: [
          {
            internal_id: 'outlet_001',
            name: 'Standard Electrical Outlet',
            category: 'outlets',
            quantity: 10
          },
          {
            internal_id: 'switch_001',
            name: 'Light Switch',
            category: 'switches',
            quantity: 5
          }
        ],
        location: {
          zip_code: '10001',
          state: 'NY',
          city: 'New York'
        }
      })
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Pricing request successful');
      console.log(`   - Received quotes from ${data.comparison.suppliers_responded} suppliers`);
      console.log(`   - Best price: $${data.comparison.best_price}`);
      console.log(`   - Average price: $${data.comparison.average_price}`);
      console.log(`   - Potential savings: $${data.comparison.potential_savings}`);
      console.log(`   - Processing time: ${data.processing_time_ms}ms`);
      return true;
    } else {
      console.log('❌ Pricing request failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Pricing request error:', error.message);
    return false;
  }
}

async function testSupplierList() {
  console.log('\n🏢 Test 3: Supplier List');
  
  try {
    const response = await fetch(`${BASE_URL}/api/supplier-integration/suppliers?active_only=true`, {
      method: 'GET',
      headers: {
        'X-Organization-Id': TEST_ORG_ID
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Supplier list retrieved successfully');
      console.log(`   - Found ${data.total} active suppliers`);
      
      data.suppliers.forEach(supplier => {
        console.log(`   - ${supplier.name} (${supplier.type}): ${supplier.sync_status}`);
      });
      
      return true;
    } else {
      console.log('❌ Supplier list failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Supplier list error:', error.message);
    return false;
  }
}

async function testPriceUpdates() {
  console.log('\n🔄 Test 4: Price Updates');
  
  try {
    const response = await fetch(`${BASE_URL}/api/supplier-integration/price-updates/all`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Organization-Id': TEST_ORG_ID
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Price updates completed successfully');
      console.log(`   - Updated ${data.summary.successful_updates}/${data.summary.total_suppliers} suppliers`);
      console.log(`   - Total products updated: ${data.summary.total_products_updated}`);
      console.log(`   - Processing time: ${data.summary.total_processing_time_ms}ms`);
      return true;
    } else {
      console.log('❌ Price updates failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Price updates error:', error.message);
    return false;
  }
}

async function testSupplierAnalytics() {
  console.log('\n📊 Test 5: Supplier Analytics');
  
  try {
    const response = await fetch(`${BASE_URL}/api/supplier-integration/analytics?timeframe=30d`, {
      method: 'GET',
      headers: {
        'X-Organization-Id': TEST_ORG_ID
      }
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Analytics retrieved successfully');
      console.log(`   - Timeframe: ${data.analytics.timeframe}`);
      console.log(`   - Total searches: ${data.analytics.search_analytics.total_searches}`);
      console.log(`   - Success rate: ${data.analytics.search_analytics.successful_searches}/${data.analytics.search_analytics.total_searches}`);
      console.log(`   - Average price change: ${data.analytics.price_trends.average_price_change_percentage}%`);
      return true;
    } else {
      console.log('❌ Analytics failed:', data.error || data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Analytics error:', error.message);
    return false;
  }
}

async function testGraybarAdapter() {
  console.log('\n🔧 Test 6: Graybar Adapter');
  
  try {
    // Test Graybar adapter directly
    const { default: GraybarAdapter } = await import('../server/adapters/graybarAdapter.js');
    
    const adapter = new GraybarAdapter({
      api_key: 'test-key',
      customer_number: 'test-customer',
      base_url: 'https://api.graybar.com/v1',
      timeout_ms: 30000
    });

    // Test product search
    const products = await adapter.searchProducts('electrical outlet', { max_results: 3 });
    
    if (products && products.length > 0) {
      console.log('✅ Graybar adapter working');
      console.log(`   - Found ${products.length} products`);
      console.log(`   - Sample product: ${products[0].name} - $${products[0].price}`);
      return true;
    } else {
      console.log('❌ Graybar adapter returned no products');
      return false;
    }
  } catch (error) {
    console.log('❌ Graybar adapter error:', error.message);
    return false;
  }
}

async function testCacheService() {
  console.log('\n💾 Test 7: Cache Service');
  
  try {
    const { default: SupplierCacheService } = await import('../server/services/supplierCacheService.js');
    
    const cacheService = new SupplierCacheService(TEST_ORG_ID);
    
    // Test cache operations
    const testData = { test: 'data', timestamp: Date.now() };
    await cacheService.setProductSearch('test_supplier', 'test_query', {}, [testData]);
    
    const cachedData = await cacheService.getProductSearch('test_supplier', 'test_query', {});
    
    if (cachedData && cachedData.length > 0) {
      console.log('✅ Cache service working');
      console.log(`   - Cached and retrieved data successfully`);
      return true;
    } else {
      console.log('❌ Cache service failed to retrieve data');
      return false;
    }
  } catch (error) {
    console.log('❌ Cache service error:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('Starting T1.4 Supplier Integration tests...\n');
  
  const tests = [
    { name: 'Supplier Search', fn: testSupplierSearch },
    { name: 'Supplier Pricing', fn: testSupplierPricing },
    { name: 'Supplier List', fn: testSupplierList },
    { name: 'Price Updates', fn: testPriceUpdates },
    { name: 'Analytics', fn: testSupplierAnalytics },
    { name: 'Graybar Adapter', fn: testGraybarAdapter },
    { name: 'Cache Service', fn: testCacheService }
  ];

  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.log(`❌ ${test.name} failed with error:`, error.message);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  console.log('\n📋 Test Results Summary');
  console.log('=======================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All T1.4 Supplier Integration tests passed!');
    console.log('✅ Real-time pricing from 3+ suppliers: WORKING');
    console.log('✅ Price comparison and recommendations: WORKING');
    console.log('✅ Caching for performance: WORKING');
    return true;
  } else {
    console.log('⚠️  Some tests failed. Check implementation.');
    return false;
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

export { runAllTests };
