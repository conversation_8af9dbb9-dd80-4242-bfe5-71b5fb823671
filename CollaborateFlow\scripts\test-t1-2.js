#!/usr/bin/env node

/**
 * T1.2 VERIFICATION TEST
 * Tests the electrical symbols database implementation without requiring full server
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables manually
try {
  const envPath = path.join(__dirname, '..', '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        if (!process.env[key]) {
          process.env[key] = value;
        }
      }
    });
  }
} catch (error) {
  console.warn('Could not load .env.local file:', error.message);
}

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function testT1_2Implementation() {
  log('🔍 T1.2 VERIFICATION: Electrical Symbol Database', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  let passedTests = 0;
  let totalTests = 0;
  const results = [];

  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      logError('Missing Supabase configuration');
      logError('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
      process.exit(1);
    }
    
    logInfo('Connecting to Supabase...');
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test 1: Database Connection
    totalTests++;
    logInfo('Test 1: Testing database connection...');
    
    try {
      const { data: testData, error: testError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .limit(1);
      
      if (testError) {
        logError(`Database connection failed: ${testError.message}`);
        results.push({ test: 'Database Connection', status: 'FAIL' });
      } else {
        logSuccess('Database connection successful');
        passedTests++;
        results.push({ test: 'Database Connection', status: 'PASS' });
      }
    } catch (error) {
      logError(`Database connection error: ${error.message}`);
      results.push({ test: 'Database Connection', status: 'FAIL' });
    }

    // Test 2: Electrical Symbol Categories Table
    totalTests++;
    logInfo('Test 2: Checking electrical symbol categories table...');
    
    try {
      const { data: categories, error: catError } = await supabase
        .from('electrical_symbol_categories')
        .select('*')
        .order('display_order');
      
      if (catError) {
        logError(`Categories table error: ${catError.message}`);
        results.push({ test: 'Categories Table', status: 'FAIL' });
      } else if (!categories || categories.length === 0) {
        logWarning('Categories table exists but is empty');
        results.push({ test: 'Categories Table', status: 'PARTIAL' });
      } else {
        logSuccess(`Found ${categories.length} electrical symbol categories`);
        categories.slice(0, 3).forEach(cat => {
          log(`  - ${cat.name}: ${cat.description}`, 'blue');
        });
        passedTests++;
        results.push({ test: 'Categories Table', status: 'PASS' });
      }
    } catch (error) {
      logError(`Categories test failed: ${error.message}`);
      results.push({ test: 'Categories Table', status: 'FAIL' });
    }

    // Test 3: Electrical Symbols Table
    totalTests++;
    logInfo('Test 3: Checking electrical symbols table...');
    
    try {
      const { data: symbols, error: symError } = await supabase
        .from('electrical_symbols')
        .select('*')
        .eq('is_active', true)
        .order('name');
      
      if (symError) {
        logError(`Symbols table error: ${symError.message}`);
        results.push({ test: 'Symbols Table', status: 'FAIL' });
      } else if (!symbols || symbols.length === 0) {
        logWarning('Symbols table exists but is empty');
        results.push({ test: 'Symbols Table', status: 'PARTIAL' });
      } else {
        logSuccess(`Found ${symbols.length} electrical symbols`);
        
        // Check for required symbol types
        const symbolTypes = [...new Set(symbols.map(s => s.voltage))].filter(Boolean);
        const categories = [...new Set(symbols.map(s => s.category_id))].filter(Boolean);
        
        log(`  - Voltage types: ${symbolTypes.join(', ')}`, 'blue');
        log(`  - Categories: ${categories.length} different categories`, 'blue');
        
        // Sample symbols
        symbols.slice(0, 3).forEach(sym => {
          log(`  - ${sym.symbol_code}: ${sym.name} (${sym.voltage || 'N/A'})`, 'blue');
        });
        
        if (symbols.length >= 20) {
          passedTests++;
          results.push({ test: 'Symbols Table', status: 'PASS' });
        } else {
          logWarning(`Only ${symbols.length} symbols found, expected 50+`);
          results.push({ test: 'Symbols Table', status: 'PARTIAL' });
        }
      }
    } catch (error) {
      logError(`Symbols test failed: ${error.message}`);
      results.push({ test: 'Symbols Table', status: 'FAIL' });
    }

    // Test 4: Material Mappings Table
    totalTests++;
    logInfo('Test 4: Checking symbol material mappings table...');
    
    try {
      const { data: materials, error: matError } = await supabase
        .from('symbol_material_mappings')
        .select('*')
        .order('material_type');
      
      if (matError) {
        logError(`Material mappings table error: ${matError.message}`);
        results.push({ test: 'Material Mappings', status: 'FAIL' });
      } else if (!materials || materials.length === 0) {
        logWarning('Material mappings table exists but is empty');
        results.push({ test: 'Material Mappings', status: 'PARTIAL' });
      } else {
        logSuccess(`Found ${materials.length} material mappings`);
        
        // Check material types
        const materialTypes = [...new Set(materials.map(m => m.material_type))];
        log(`  - Material types: ${materialTypes.join(', ')}`, 'blue');
        
        // Sample materials
        materials.slice(0, 3).forEach(mat => {
          log(`  - ${mat.material_name}: $${mat.unit_cost}/${mat.unit}`, 'blue');
        });
        
        passedTests++;
        results.push({ test: 'Material Mappings', status: 'PASS' });
      }
    } catch (error) {
      logError(`Material mappings test failed: ${error.message}`);
      results.push({ test: 'Material Mappings', status: 'FAIL' });
    }

    // Test 5: Symbol Detection History Table
    totalTests++;
    logInfo('Test 5: Checking symbol detection history table...');
    
    try {
      const { data: history, error: histError } = await supabase
        .from('symbol_detection_history')
        .select('*')
        .limit(5);
      
      if (histError) {
        logError(`Detection history table error: ${histError.message}`);
        results.push({ test: 'Detection History', status: 'FAIL' });
      } else {
        logSuccess(`Detection history table accessible (${history?.length || 0} records)`);
        passedTests++;
        results.push({ test: 'Detection History', status: 'PASS' });
      }
    } catch (error) {
      logError(`Detection history test failed: ${error.message}`);
      results.push({ test: 'Detection History', status: 'FAIL' });
    }

    // Test 6: Complex Query Test (Join Categories and Symbols)
    totalTests++;
    logInfo('Test 6: Testing complex queries (joins)...');
    
    try {
      const { data: joinData, error: joinError } = await supabase
        .from('electrical_symbols')
        .select(`
          id,
          symbol_code,
          name,
          voltage,
          base_material_cost,
          category:electrical_symbol_categories(name, description)
        `)
        .eq('is_active', true)
        .limit(5);
      
      if (joinError) {
        logError(`Complex query failed: ${joinError.message}`);
        results.push({ test: 'Complex Queries', status: 'FAIL' });
      } else if (!joinData || joinData.length === 0) {
        logWarning('Complex query returned no results');
        results.push({ test: 'Complex Queries', status: 'PARTIAL' });
      } else {
        logSuccess(`Complex query successful (${joinData.length} results)`);
        joinData.forEach(item => {
          log(`  - ${item.symbol_code}: ${item.name} (${item.category?.name || 'No category'})`, 'blue');
        });
        passedTests++;
        results.push({ test: 'Complex Queries', status: 'PASS' });
      }
    } catch (error) {
      logError(`Complex query test failed: ${error.message}`);
      results.push({ test: 'Complex Queries', status: 'FAIL' });
    }

    // Display Results
    log('\n' + '='.repeat(60), 'cyan');
    log('T1.2 VERIFICATION RESULTS', 'cyan');
    log('='.repeat(60), 'cyan');
    
    results.forEach(result => {
      const status = result.status === 'PASS' ? '✅ PASS' : 
                     result.status === 'PARTIAL' ? '⚠️  PARTIAL' : '❌ FAIL';
      log(`${result.test.padEnd(25)} ${status}`, 
          result.status === 'PASS' ? 'green' : 
          result.status === 'PARTIAL' ? 'yellow' : 'red');
    });
    
    const successRate = (passedTests / totalTests * 100).toFixed(1);
    log(`\nSuccess Rate: ${passedTests}/${totalTests} (${successRate}%)`, 
        successRate >= 80 ? 'green' : successRate >= 60 ? 'yellow' : 'red');
    
    // Final Assessment
    log('\n' + '='.repeat(60), 'cyan');
    log('T1.2 COMPLETION ASSESSMENT', 'cyan');
    log('='.repeat(60), 'cyan');
    
    if (passedTests >= 5) {
      logSuccess('🎉 T1.2 (Electrical Symbol Database) is COMPLETE!');
      logSuccess('✅ Database schema implemented successfully');
      logSuccess('✅ Symbol categories and data populated');
      logSuccess('✅ Material mappings configured');
      logSuccess('✅ Complex queries working');
      logSuccess('✅ Ready for integration with T1.1 AI detection');
      
      log('\nNext Steps:', 'bright');
      log('1. ✅ T1.2 database implementation complete', 'green');
      log('2. 🚀 Test API endpoints when server is running', 'blue');
      log('3. 🧪 Run UAT tests to verify integration', 'blue');
      log('4. 🔗 Verify T1.1 + T1.2 integration works', 'blue');
      
      return true;
    } else {
      logError('❌ T1.2 implementation needs additional work');
      logError(`Only ${passedTests}/${totalTests} tests passed`);
      
      log('\nRequired Actions:', 'bright');
      results.filter(r => r.status === 'FAIL').forEach(result => {
        log(`- Fix ${result.test} implementation`, 'red');
      });
      
      return false;
    }
    
  } catch (error) {
    logError(`T1.2 verification failed: ${error.message}`);
    console.error(error);
    return false;
  }
}

// Run verification
if (import.meta.url === `file://${process.argv[1]}`) {
  testT1_2Implementation().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    logError(`Verification failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export { testT1_2Implementation };
