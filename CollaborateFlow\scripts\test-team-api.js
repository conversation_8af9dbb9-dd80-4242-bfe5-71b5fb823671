// Script to test the teams API directly against Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to create a team
async function createTeam(name, description) {
  console.log(`Creating team "${name}"...`);
  
  // Create a team with explicit values for all required fields
  const teamData = {
    name,
    description: description || '',
    created_by_id: 1,
    organization_id: 1,
    created_at: new Date().toISOString()
  };
  
  console.log('Team data:', teamData);
  
  const { data, error } = await supabase
    .from('teams')
    .insert(teamData)
    .select()
    .single();
  
  if (error) {
    console.error('Error creating team:', error);
    return null;
  }
  
  console.log('Successfully created team:', data);
  
  // Add the creator as a team member
  await addTeamMember(data.id, 1, 'admin');
  
  return data;
}

// Function to get a list of teams
async function listTeams() {
  console.log('Listing all teams...');
  
  const { data, error } = await supabase
    .from('teams')
    .select('*');
  
  if (error) {
    console.error('Error listing teams:', error);
    return [];
  }
  
  console.log(`Found ${data.length} teams:`);
  data.forEach(team => {
    console.log(`- ID: ${team.id}, Name: ${team.name}, Organization: ${team.organization_id}`);
  });
  
  return data;
}

// Function to add a team member
async function addTeamMember(teamId, userId, role) {
  console.log(`Adding user ${userId} as ${role} to team ${teamId}...`);
  
  const { data, error } = await supabase
    .from('team_members')
    .insert({
      team_id: teamId,
      user_id: userId,
      role: role || 'member'
    })
    .select();
  
  if (error) {
    console.error('Error adding team member:', error);
    return null;
  }
  
  console.log('Successfully added team member:', data);
  return data;
}

// Function to get team members
async function getTeamMembers(teamId) {
  console.log(`Getting members for team ${teamId}...`);
  
  const { data, error } = await supabase
    .from('team_members')
    .select(`
      user_id,
      role,
      users:user_id(*)
    `)
    .eq('team_id', teamId);
  
  if (error) {
    console.error('Error getting team members:', error);
    return [];
  }
  
  console.log(`Found ${data.length} members for team ${teamId}:`);
  data.forEach(member => {
    console.log(`- User ID: ${member.user_id}, Role: ${member.role}`);
  });
  
  return data;
}

// Function to get teams by user ID
async function getTeamsByUserId(userId) {
  console.log(`Getting teams for user ${userId}...`);
  
  const { data, error } = await supabase
    .from('team_members')
    .select(`
      team_id,
      role,
      teams:team_id(*)
    `)
    .eq('user_id', userId);
  
  if (error) {
    console.error('Error getting teams for user:', error);
    return [];
  }
  
  console.log(`Found ${data.length} teams for user ${userId}:`);
  data.forEach(membership => {
    console.log(`- Team ID: ${membership.team_id}, Role: ${membership.role}`);
    console.log('  Team details:', membership.teams);
  });
  
  return data;
}

// Main function to test the team API
async function main() {
  try {
    // Test connection
    console.log('Testing Supabase connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('teams')
      .select('count');
    
    if (connectionError) {
      console.error('Connection error:', connectionError);
      process.exit(1);
    }
    
    console.log('\n\u2705 Supabase connection successful!');
    
    // List existing teams
    console.log('\n--- Existing Teams ---');
    await listTeams();
    
    // Create a new team
    console.log('\n--- Creating a New Team ---');
    const newTeam = await createTeam('QA Team', 'Quality Assurance team for Coelec');
    
    if (newTeam) {
      // Get team members
      console.log('\n--- Team Members ---');
      await getTeamMembers(newTeam.id);
      
      // Get teams for user 1
      console.log('\n--- Teams for User 1 ---');
      await getTeamsByUserId(1);
    }
    
    console.log('\n\u2705 Team API tests completed successfully!');
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the tests
main();
