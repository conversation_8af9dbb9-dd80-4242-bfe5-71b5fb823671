// <PERSON><PERSON>t to test getting the user's organization ID when creating a team
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to get a user's organization
async function getUserOrganization(userId) {
  console.log(`Getting organization for user ${userId}...`);
  
  const { data, error } = await supabase
    .from('users')
    .select('organization_id, email')
    .eq('id', userId)
    .single();
  
  if (error) {
    console.error('Error getting user organization:', error);
    return null;
  }
  
  console.log(`User ${userId} (${data.email}) belongs to organization ID: ${data.organization_id}`);
  return data;
}

// Function to create a team using the user's organization
async function createTeamWithUserOrg(name, description, userId, orgId) {
  console.log(`Creating team "${name}" with user ${userId} and organization ${orgId}...`);
  
  // Create a team with the user's organization ID
  const teamData = {
    name,
    description: description || '',
    created_by_id: userId,
    organization_id: orgId,
    created_at: new Date().toISOString()
  };
  
  console.log('Team data:', teamData);
  
  const { data, error } = await supabase
    .from('teams')
    .insert(teamData)
    .select()
    .single();
  
  if (error) {
    console.error('Error creating team:', error);
    return null;
  }
  
  console.log('Successfully created team:', data);
  return data;
}

// Function to list all users
async function listUsers() {
  console.log('Listing all users...');
  
  const { data, error } = await supabase
    .from('users')
    .select('id, email, organization_id');
  
  if (error) {
    console.error('Error listing users:', error);
    return [];
  }
  
  console.log(`Found ${data.length} users:`);
  data.forEach(user => {
    console.log(`- ID: ${user.id}, Email: ${user.email}, Organization: ${user.organization_id}`);
  });
  
  return data;
}

// Main function to test
async function main() {
  try {
    // Test connection
    console.log('Testing Supabase connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count');
    
    if (connectionError) {
      console.error('Connection error:', connectionError);
      process.exit(1);
    }
    
    console.log('\n\u2705 Supabase connection successful!');
    
    // List all users and their organizations
    console.log('\n--- All Users ---');
    const users = await listUsers();
    
    if (users.length === 0) {
      console.error('No users found in the database!');
      process.exit(1);
    }
    
    // Get organization for user ID 1
    console.log('\n--- User Organization ---');
    const user = await getUserOrganization(1);
    
    if (!user) {
      console.error('Could not find user with ID 1!');
      process.exit(1);
    }
    
    // Create a team with the user's organization
    console.log('\n--- Creating Team with User Organization ---');
    const teamName = `Organization Test Team - ${new Date().toISOString().substring(0, 16)}`;
    const team = await createTeamWithUserOrg(
      teamName, 
      `Team created for testing organization ID: ${user.organization_id}`,
      1,
      user.organization_id
    );
    
    if (team) {
      console.log('\n\u2705 Successfully created team with user\'s organization!');
      console.log(`Team ID: ${team.id}`);
      console.log(`Team Name: ${team.name}`);
      console.log(`Organization ID: ${team.organization_id}`);
      
      // Verify the organization ID matches
      if (team.organization_id === user.organization_id) {
        console.log('\n\u2705 VERIFICATION: Team organization ID matches user organization ID!');
      } else {
        console.error('\n\u274c ERROR: Team organization ID does NOT match user organization ID!');
        console.error(`User org: ${user.organization_id}, Team org: ${team.organization_id}`);
      }
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the test
main();
