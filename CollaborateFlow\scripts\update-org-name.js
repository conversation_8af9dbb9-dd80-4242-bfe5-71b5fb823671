// Script to update the default organization name from Her<PERSON> to Coelec
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || 'https://nzhvukfaolebykcquedd.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to update the organization name
async function updateOrganizationName() {
  try {
    console.log('Updating organization name from "Hermes" to "Coelec"...');
    
    // Update the organization name
    const { data, error } = await supabase
      .from('organizations')
      .update({ name: 'Coelec' })
      .eq('id', 1)
      .select();
    
    if (error) {
      console.error('Error updating organization name:', error);
      return;
    }
    
    console.log('Successfully updated organization:', data);
    return data;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
updateOrganizationName()
  .then(result => {
    if (result) {
      console.log('Organization name updated to "Coelec"');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to update organization name:', error);
    process.exit(1);
  });
