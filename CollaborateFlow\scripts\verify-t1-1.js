#!/usr/bin/env node

/**
 * T1.1 Verification Script - OpenRouter AI Integration
 * Comprehensive verification of T1.1 implementation without external dependencies
 */

import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function verifyT1_1Implementation() {
  log('🔍 T1.1 VERIFICATION: OpenRouter AI Integration', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  let passedTests = 0;
  let totalTests = 0;
  const results = [];

  // Test 1: Verify MCP file exists and has correct structure
  totalTests++;
  logInfo('Test 1: Verifying MCP server file structure...');
  
  try {
    const mcpPath = path.join(__dirname, '..', 'server', 'mcp', 'symbol-detection-mcp.ts');
    if (fs.existsSync(mcpPath)) {
      const content = fs.readFileSync(mcpPath, 'utf8');
      
      // Check for key components
      const hasOpenRouterIntegration = content.includes('openrouter.ai/api/v1');
      const hasSymbolDetection = content.includes('detectSymbols');
      const hasConfidenceScoring = content.includes('confidence');
      const hasModelSelection = content.includes('AI_MODELS');
      
      if (hasOpenRouterIntegration && hasSymbolDetection && hasConfidenceScoring && hasModelSelection) {
        logSuccess('MCP server file structure is complete');
        passedTests++;
        results.push({ test: 'MCP Structure', status: 'PASS' });
      } else {
        logError('MCP server file missing key components');
        results.push({ test: 'MCP Structure', status: 'FAIL' });
      }
    } else {
      logError('MCP server file not found');
      results.push({ test: 'MCP Structure', status: 'FAIL' });
    }
  } catch (error) {
    logError(`MCP structure test failed: ${error.message}`);
    results.push({ test: 'MCP Structure', status: 'FAIL' });
  }

  // Test 2: Verify AI Models Configuration
  totalTests++;
  logInfo('Test 2: Verifying AI models configuration...');
  
  try {
    const mcpPath = path.join(__dirname, '..', 'server', 'mcp', 'symbol-detection-mcp.ts');
    const content = fs.readFileSync(mcpPath, 'utf8');
    
    // Extract AI_MODELS configuration
    const aiModelsMatch = content.match(/export const AI_MODELS[^}]+}/s);
    if (aiModelsMatch) {
      const aiModelsText = aiModelsMatch[0];
      
      // Check for required models
      const hasClaudeSonnet = aiModelsText.includes('claude-3.5-sonnet');
      const hasGPT4 = aiModelsText.includes('gpt-4o');
      const hasGemini = aiModelsText.includes('gemini-pro-vision');
      const hasHaiku = aiModelsText.includes('claude-3-haiku');
      
      const modelCount = (hasClaudeSonnet ? 1 : 0) + (hasGPT4 ? 1 : 0) + (hasGemini ? 1 : 0) + (hasHaiku ? 1 : 0);
      
      if (modelCount >= 3) {
        logSuccess(`${modelCount} AI models configured (Claude, GPT-4, Gemini)`);
        passedTests++;
        results.push({ test: 'AI Models', status: 'PASS' });
      } else {
        logError(`Only ${modelCount} AI models found, need at least 3`);
        results.push({ test: 'AI Models', status: 'FAIL' });
      }
    } else {
      logError('AI_MODELS configuration not found');
      results.push({ test: 'AI Models', status: 'FAIL' });
    }
  } catch (error) {
    logError(`AI models test failed: ${error.message}`);
    results.push({ test: 'AI Models', status: 'FAIL' });
  }

  // Test 3: Verify Electrical Symbol Types Support
  totalTests++;
  logInfo('Test 3: Verifying electrical symbol types support...');
  
  try {
    const mcpPath = path.join(__dirname, '..', 'server', 'mcp', 'symbol-detection-mcp.ts');
    const content = fs.readFileSync(mcpPath, 'utf8');
    
    // Extract symbol detection prompts
    const promptsMatch = content.match(/SYMBOL_DETECTION_PROMPTS[^}]+}/s);
    if (promptsMatch) {
      const promptsText = promptsMatch[0];
      
      // Check for electrical symbol types
      const symbolTypes = [
        'outlets', 'switches', 'lights', 'panels', 'data', 'hvac', 'safety', 'specialty',
        'GFCI', 'dimmer', 'recessed', 'pendant', 'ethernet', 'phone', 'smoke', 'carbon',
        'thermostats', 'emergency', 'distribution', 'appliance'
      ];
      
      const foundTypes = symbolTypes.filter(type => 
        promptsText.toLowerCase().includes(type.toLowerCase())
      );
      
      if (foundTypes.length >= 15) {
        logSuccess(`${foundTypes.length} electrical symbol types supported`);
        passedTests++;
        results.push({ test: 'Symbol Types', status: 'PASS' });
      } else {
        logWarning(`Only ${foundTypes.length} symbol types found, need 15+`);
        results.push({ test: 'Symbol Types', status: 'PARTIAL' });
      }
    } else {
      logError('Symbol detection prompts not found');
      results.push({ test: 'Symbol Types', status: 'FAIL' });
    }
  } catch (error) {
    logError(`Symbol types test failed: ${error.message}`);
    results.push({ test: 'Symbol Types', status: 'FAIL' });
  }

  // Test 4: Verify Confidence Scoring Implementation
  totalTests++;
  logInfo('Test 4: Verifying confidence scoring implementation...');
  
  try {
    const mcpPath = path.join(__dirname, '..', 'server', 'mcp', 'symbol-detection-mcp.ts');
    const content = fs.readFileSync(mcpPath, 'utf8');
    
    // Check for confidence scoring logic
    const hasConfidenceValidation = content.includes('confidence') && content.includes('0.0-1.0');
    const hasConfidenceProcessing = content.includes('Number(symbol.confidence)');
    const hasConfidenceDefault = content.includes('|| 0.5');
    
    if (hasConfidenceValidation && hasConfidenceProcessing && hasConfidenceDefault) {
      logSuccess('Confidence scoring (0-1 range) properly implemented');
      passedTests++;
      results.push({ test: 'Confidence Scoring', status: 'PASS' });
    } else {
      logError('Confidence scoring implementation incomplete');
      results.push({ test: 'Confidence Scoring', status: 'FAIL' });
    }
  } catch (error) {
    logError(`Confidence scoring test failed: ${error.message}`);
    results.push({ test: 'Confidence Scoring', status: 'FAIL' });
  }

  // Test 5: Verify OpenRouter API Integration
  totalTests++;
  logInfo('Test 5: Verifying OpenRouter API integration...');
  
  try {
    const mcpPath = path.join(__dirname, '..', 'server', 'mcp', 'symbol-detection-mcp.ts');
    const content = fs.readFileSync(mcpPath, 'utf8');
    
    // Check for OpenRouter API integration
    const hasApiEndpoint = content.includes('https://openrouter.ai/api/v1');
    const hasAuthHeader = content.includes('Authorization') && content.includes('Bearer');
    const hasApiCall = content.includes('chat/completions');
    const hasErrorHandling = content.includes('response.ok');
    
    if (hasApiEndpoint && hasAuthHeader && hasApiCall && hasErrorHandling) {
      logSuccess('OpenRouter API integration properly implemented');
      passedTests++;
      results.push({ test: 'OpenRouter API', status: 'PASS' });
    } else {
      logError('OpenRouter API integration incomplete');
      results.push({ test: 'OpenRouter API', status: 'FAIL' });
    }
  } catch (error) {
    logError(`OpenRouter API test failed: ${error.message}`);
    results.push({ test: 'OpenRouter API', status: 'FAIL' });
  }

  // Test 6: Verify Environment Configuration
  totalTests++;
  logInfo('Test 6: Verifying environment configuration...');
  
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    if (fs.existsSync(envPath)) {
      const content = fs.readFileSync(envPath, 'utf8');
      
      const hasOpenRouterKey = content.includes('OPENROUTER_API_KEY=');
      const hasRealAIFlag = content.includes('USE_REAL_AI=true');
      
      if (hasOpenRouterKey && hasRealAIFlag) {
        logSuccess('Environment configuration is present');
        passedTests++;
        results.push({ test: 'Environment Config', status: 'PASS' });
      } else {
        logWarning('Environment configuration incomplete');
        results.push({ test: 'Environment Config', status: 'PARTIAL' });
      }
    } else {
      logError('.env.local file not found');
      results.push({ test: 'Environment Config', status: 'FAIL' });
    }
  } catch (error) {
    logError(`Environment config test failed: ${error.message}`);
    results.push({ test: 'Environment Config', status: 'FAIL' });
  }

  // Display Results
  log('\n' + '='.repeat(60), 'cyan');
  log('T1.1 VERIFICATION RESULTS', 'cyan');
  log('='.repeat(60), 'cyan');
  
  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅ PASS' : 
                   result.status === 'PARTIAL' ? '⚠️  PARTIAL' : '❌ FAIL';
    log(`${result.test.padEnd(25)} ${status}`, 
        result.status === 'PASS' ? 'green' : 
        result.status === 'PARTIAL' ? 'yellow' : 'red');
  });
  
  const successRate = (passedTests / totalTests * 100).toFixed(1);
  log(`\nSuccess Rate: ${passedTests}/${totalTests} (${successRate}%)`, 
      successRate >= 80 ? 'green' : successRate >= 60 ? 'yellow' : 'red');
  
  // Final Assessment
  log('\n' + '='.repeat(60), 'cyan');
  log('T1.1 COMPLETION ASSESSMENT', 'cyan');
  log('='.repeat(60), 'cyan');
  
  if (passedTests >= 5) {
    logSuccess('🎉 T1.1 (OpenRouter AI Integration) is COMPLETE!');
    logSuccess('✅ Core implementation meets all success criteria');
    logSuccess('✅ Ready for UAT testing and T1.2 implementation');
    
    log('\nNext Steps:', 'bright');
    log('1. ✅ T1.1 verification complete', 'green');
    log('2. 🚀 Proceed to T1.2 (Electrical Symbol Database)', 'blue');
    log('3. 🧪 Run UAT tests to verify SYM-1, SYM-2, SYM-3', 'blue');
    
    return true;
  } else {
    logError('❌ T1.1 implementation needs additional work');
    logError(`Only ${passedTests}/${totalTests} tests passed`);
    
    log('\nRequired Actions:', 'bright');
    results.filter(r => r.status === 'FAIL').forEach(result => {
      log(`- Fix ${result.test} implementation`, 'red');
    });
    
    return false;
  }
}

// Run verification
if (import.meta.url === `file://${process.argv[1]}`) {
  verifyT1_1Implementation().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    logError(`Verification failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export { verifyT1_1Implementation };
