#!/usr/bin/env node

/**
 * T2.2 CLIENT PORTAL VERIFICATION
 * Simple verification that the client portal is properly implemented
 */

console.log('🧪 T2.2 Client Portal Verification');
console.log('==================================');

async function verifyFileStructure() {
  console.log('\n📁 Verifying File Structure...');

  const fs = await import('fs');
  const path = await import('path');

  const requiredFiles = [
    'client/src/pages/ClientPortal.tsx',
    'client/src/components/QuoteApproval.tsx',
    'server/routes/client.ts',
    'server/database/migrations/client_portal.sql'
  ];

  let allFilesExist = true;

  for (const file of requiredFiles) {
    const filePath = path.resolve(file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - NOT FOUND`);
      allFilesExist = false;
    }
  }

  return allFilesExist;
}

async function verifyDatabaseSchema() {
  console.log('\n🗄️  Verifying Database Schema...');

  try {
    const fs = await import('fs');
    const schemaContent = fs.readFileSync('server/database/migrations/client_portal.sql', 'utf8');

    const requiredTables = [
      'client_profiles',
      'client_access_tokens',
      'quote_change_requests',
      'client_activity_log',
      'client_notifications',
      'client_portal_sessions'
    ];

    let allTablesFound = true;

    for (const table of requiredTables) {
      if (schemaContent.includes(`CREATE TABLE IF NOT EXISTS ${table}`)) {
        console.log(`✅ Table: ${table}`);
      } else {
        console.log(`❌ Table: ${table} - NOT FOUND`);
        allTablesFound = false;
      }
    }

    // Check for RLS policies
    if (schemaContent.includes('ENABLE ROW LEVEL SECURITY')) {
      console.log('✅ RLS policies defined');
    } else {
      console.log('❌ RLS policies missing');
      allTablesFound = false;
    }

    // Check for client portal functions
    if (schemaContent.includes('generate_client_access_token') &&
        schemaContent.includes('get_client_portal_statistics')) {
      console.log('✅ Client portal management functions defined');
    } else {
      console.log('❌ Client portal management functions missing');
      allTablesFound = false;
    }

    return allTablesFound;
  } catch (error) {
    console.log('❌ Database schema verification failed:', error.message);
    return false;
  }
}

async function verifyClientPortalComponent() {
  console.log('\n🎨 Verifying ClientPortal Component...');

  try {
    const fs = await import('fs');
    const componentContent = fs.readFileSync('client/src/pages/ClientPortal.tsx', 'utf8');

    const requiredFeatures = [
      'authenticateClient',
      'loadQuotes',
      'loadChangeRequests',
      'handleQuoteApproval',
      'handleChangeRequest',
      'ClientPortalProps'
    ];

    let allFeaturesFound = true;

    for (const feature of requiredFeatures) {
      if (componentContent.includes(feature)) {
        console.log(`✅ Feature: ${feature}`);
      } else {
        console.log(`❌ Feature: ${feature} - NOT FOUND`);
        allFeaturesFound = false;
      }
    }

    // Check for tab structure
    if (componentContent.includes('TabsContent value="quotes"') &&
        componentContent.includes('TabsContent value="requests"') &&
        componentContent.includes('TabsContent value="profile"')) {
      console.log('✅ Tab-based interface structure');
    } else {
      console.log('❌ Tab-based interface structure missing');
      allFeaturesFound = false;
    }

    return allFeaturesFound;
  } catch (error) {
    console.log('❌ ClientPortal component verification failed:', error.message);
    return false;
  }
}

async function verifyQuoteApprovalComponent() {
  console.log('\n📋 Verifying QuoteApproval Component...');

  try {
    const fs = await import('fs');
    const componentContent = fs.readFileSync('client/src/components/QuoteApproval.tsx', 'utf8');

    const requiredFeatures = [
      'QuoteApprovalProps',
      'signatureCanvasRef',
      'handleApprovalWithSignature',
      'handleRejection',
      'handleChangeRequestSubmission',
      'startDrawing',
      'draw',
      'stopDrawing'
    ];

    let allFeaturesFound = true;

    for (const feature of requiredFeatures) {
      if (componentContent.includes(feature)) {
        console.log(`✅ Feature: ${feature}`);
      } else {
        console.log(`❌ Feature: ${feature} - NOT FOUND`);
        allFeaturesFound = false;
      }
    }

    // Check for signature canvas
    if (componentContent.includes('<canvas') &&
        componentContent.includes('onMouseDown={startDrawing}')) {
      console.log('✅ Digital signature canvas');
    } else {
      console.log('❌ Digital signature canvas missing');
      allFeaturesFound = false;
    }

    // Check for dialog components
    if (componentContent.includes('showSignatureDialog') &&
        componentContent.includes('showRejectDialog') &&
        componentContent.includes('showChangeRequestDialog')) {
      console.log('✅ Dialog components for approval workflow');
    } else {
      console.log('❌ Dialog components missing');
      allFeaturesFound = false;
    }

    return allFeaturesFound;
  } catch (error) {
    console.log('❌ QuoteApproval component verification failed:', error.message);
    return false;
  }
}

async function verifyClientRoutes() {
  console.log('\n🛣️  Verifying Client API Routes...');

  try {
    const fs = await import('fs');
    const routeContent = fs.readFileSync('server/routes/client.ts', 'utf8');

    const requiredEndpoints = [
      'router.post("/auth"',
      'router.post("/logout"',
      'router.get("/quotes"',
      'router.get("/quotes/:id"',
      'router.post("/quotes/:id/approve"',
      'router.post("/quotes/:id/reject"',
      'router.get("/change-requests"',
      'router.post("/change-requests"',
      'router.get("/profile"'
    ];

    let allEndpointsFound = true;

    for (const endpoint of requiredEndpoints) {
      if (routeContent.includes(endpoint)) {
        console.log(`✅ Endpoint: ${endpoint.replace('router.', '').replace('"', '')}`);
      } else {
        console.log(`❌ Endpoint: ${endpoint} - NOT FOUND`);
        allEndpointsFound = false;
      }
    }

    // Check for authentication middleware
    if (routeContent.includes("'x-client-id'") &&
        routeContent.includes("'x-session-token'")) {
      console.log('✅ Client authentication headers');
    } else {
      console.log('❌ Client authentication headers missing');
      allEndpointsFound = false;
    }

    return allEndpointsFound;
  } catch (error) {
    console.log('❌ Client routes verification failed:', error.message);
    return false;
  }
}

async function verifyRouteRegistration() {
  console.log('\n🔗 Verifying Route Registration...');

  try {
    const fs = await import('fs');
    const indexContent = fs.readFileSync('server/routes/api/index.ts', 'utf8');

    // Check for client router import
    if (indexContent.includes("import clientRouter from '../client'")) {
      console.log('✅ Client router import');
    } else {
      console.log('❌ Client router import missing');
      return false;
    }

    // Check for client router registration
    if (indexContent.includes("router.use('/client', clientRouter)")) {
      console.log('✅ Client router registration');
    } else {
      console.log('❌ Client router registration missing');
      return false;
    }

    return true;
  } catch (error) {
    console.log('❌ Route registration verification failed:', error.message);
    return false;
  }
}

async function verifyAppRoutes() {
  console.log('\n📱 Verifying App Routes...');

  try {
    const fs = await import('fs');
    const appContent = fs.readFileSync('client/src/App.tsx', 'utf8');

    // Check for ClientPortal import
    if (appContent.includes("import { ClientPortal } from '@/pages/ClientPortal'")) {
      console.log('✅ ClientPortal import');
    } else {
      console.log('❌ ClientPortal import missing');
      return false;
    }

    // Check for client portal routes
    if (appContent.includes('/client-portal/:clientId/:accessToken') &&
        appContent.includes('ClientPortal clientId={clientId} accessToken={accessToken}')) {
      console.log('✅ Client portal routes');
    } else {
      console.log('❌ Client portal routes missing');
      return false;
    }

    return true;
  } catch (error) {
    console.log('❌ App routes verification failed:', error.message);
    return false;
  }
}

async function verifyComponentStructure() {
  console.log('\n🏗️  Verifying Component Structure...');

  try {
    // Test component imports (simplified check)
    const fs = await import('fs');

    // Check ClientPortal export
    const clientPortalContent = fs.readFileSync('client/src/pages/ClientPortal.tsx', 'utf8');
    if (clientPortalContent.includes('export function ClientPortal') &&
        clientPortalContent.includes('export default ClientPortal')) {
      console.log('✅ ClientPortal component exports');
    } else {
      console.log('❌ ClientPortal component exports missing');
      return false;
    }

    // Check QuoteApproval export
    const quoteApprovalContent = fs.readFileSync('client/src/components/QuoteApproval.tsx', 'utf8');
    if (quoteApprovalContent.includes('export function QuoteApproval') &&
        quoteApprovalContent.includes('export default QuoteApproval')) {
      console.log('✅ QuoteApproval component exports');
    } else {
      console.log('❌ QuoteApproval component exports missing');
      return false;
    }

    return true;
  } catch (error) {
    console.log('❌ Component structure verification failed:', error.message);
    return false;
  }
}

async function runVerification() {
  console.log('Starting T2.2 verification...\n');

  const tests = [
    { name: 'File Structure', fn: verifyFileStructure },
    { name: 'Database Schema', fn: verifyDatabaseSchema },
    { name: 'ClientPortal Component', fn: verifyClientPortalComponent },
    { name: 'QuoteApproval Component', fn: verifyQuoteApprovalComponent },
    { name: 'Client API Routes', fn: verifyClientRoutes },
    { name: 'Route Registration', fn: verifyRouteRegistration },
    { name: 'App Routes', fn: verifyAppRoutes },
    { name: 'Component Structure', fn: verifyComponentStructure }
  ];

  const results = [];

  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.log(`❌ ${test.name} failed with error:`, error.message);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  console.log('\n📋 Verification Results');
  console.log('=======================');

  const passed = results.filter(r => r.passed).length;
  const total = results.length;

  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });

  console.log(`\n🎯 Overall: ${passed}/${total} verifications passed`);

  if (passed === total) {
    console.log('\n🎉 T2.2 Client Portal Implementation VERIFIED!');
    console.log('✅ All required files created');
    console.log('✅ All components properly structured');
    console.log('✅ Database schema complete');
    console.log('✅ API routes implemented');
    console.log('✅ Route registration complete');
    console.log('\n📋 T2.2 SUCCESS CRITERIA MET:');
    console.log('✅ Complete client portal with authentication');
    console.log('✅ Quote viewing and approval interface');
    console.log('✅ Change request submission system');
    return true;
  } else {
    console.log('\n⚠️  Some verifications failed. Check implementation.');
    return false;
  }
}

// Run verification
runVerification().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Verification failed:', error);
  process.exit(1);
});
