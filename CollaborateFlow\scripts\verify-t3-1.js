#!/usr/bin/env node

/**
 * T3.1 CRUD OPERATIONS VERIFICATION
 * Simple verification that the CRUD operations are properly implemented
 */

console.log('🧪 T3.1 CRUD Operations Verification');
console.log('===================================');

async function verifyFileStructure() {
  console.log('\n📁 Verifying File Structure...');
  
  const fs = await import('fs');
  const path = await import('path');
  
  const requiredFiles = [
    'client/src/components/EditTeamDialog.tsx',
    'client/src/components/EditProjectDialog.tsx',
    'client/src/components/EditTaskDialog.tsx'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    const filePath = path.resolve(file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - NOT FOUND`);
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

async function verifyEditTeamDialog() {
  console.log('\n👥 Verifying EditTeamDialog Component...');
  
  try {
    const fs = await import('fs');
    const componentContent = fs.readFileSync('client/src/components/EditTeamDialog.tsx', 'utf8');
    
    const requiredFeatures = [
      'EditTeamDialog',
      'teamFormSchema',
      'updateTeamMutation',
      'deleteTeamMutation',
      'AlertDialog',
      'Permission Denied',
      'canEdit',
      'canDelete',
      'role === \'admin\'',
      'Save Changes',
      'Delete Team'
    ];
    
    let allFeaturesFound = true;
    
    for (const feature of requiredFeatures) {
      if (componentContent.includes(feature)) {
        console.log(`✅ Feature: ${feature}`);
      } else {
        console.log(`❌ Feature: ${feature} - NOT FOUND`);
        allFeaturesFound = false;
      }
    }
    
    // Check for form validation
    if (componentContent.includes('zodResolver') && 
        componentContent.includes('useForm') &&
        componentContent.includes('FormField')) {
      console.log('✅ Form validation with React Hook Form');
    } else {
      console.log('❌ Form validation missing');
      allFeaturesFound = false;
    }
    
    // Check for permission-based UI
    if (componentContent.includes('read-only access') &&
        componentContent.includes('AlertTriangle')) {
      console.log('✅ Permission-based UI warnings');
    } else {
      console.log('❌ Permission-based UI warnings missing');
      allFeaturesFound = false;
    }
    
    return allFeaturesFound;
  } catch (error) {
    console.log('❌ EditTeamDialog verification failed:', error.message);
    return false;
  }
}

async function verifyEditProjectDialog() {
  console.log('\n📁 Verifying EditProjectDialog Component...');
  
  try {
    const fs = await import('fs');
    const componentContent = fs.readFileSync('client/src/components/EditProjectDialog.tsx', 'utf8');
    
    const requiredFeatures = [
      'EditProjectDialog',
      'projectFormSchema',
      'updateProjectMutation',
      'deleteProjectMutation',
      'status',
      'planning',
      'in_progress',
      'completed',
      'on_hold',
      'statusConfig',
      'Select',
      'SelectItem'
    ];
    
    let allFeaturesFound = true;
    
    for (const feature of requiredFeatures) {
      if (componentContent.includes(feature)) {
        console.log(`✅ Feature: ${feature}`);
      } else {
        console.log(`❌ Feature: ${feature} - NOT FOUND`);
        allFeaturesFound = false;
      }
    }
    
    // Check for status management
    if (componentContent.includes('Select') && 
        componentContent.includes('SelectValue') &&
        componentContent.includes('SelectContent')) {
      console.log('✅ Status selection dropdown');
    } else {
      console.log('❌ Status selection dropdown missing');
      allFeaturesFound = false;
    }
    
    return allFeaturesFound;
  } catch (error) {
    console.log('❌ EditProjectDialog verification failed:', error.message);
    return false;
  }
}

async function verifyEditTaskDialog() {
  console.log('\n✅ Verifying EditTaskDialog Component...');
  
  try {
    const fs = await import('fs');
    const componentContent = fs.readFileSync('client/src/components/EditTaskDialog.tsx', 'utf8');
    
    const requiredFeatures = [
      'EditTaskDialog',
      'taskFormSchema',
      'updateTaskMutation',
      'deleteTaskMutation',
      'assigneeId',
      'TeamMember',
      'useQuery',
      '/api/projects/',
      '/members',
      'Unassigned'
    ];
    
    let allFeaturesFound = true;
    
    for (const feature of requiredFeatures) {
      if (componentContent.includes(feature)) {
        console.log(`✅ Feature: ${feature}`);
      } else {
        console.log(`❌ Feature: ${feature} - NOT FOUND`);
        allFeaturesFound = false;
      }
    }
    
    // Check for assignee management
    if (componentContent.includes('teamMembers.map') && 
        componentContent.includes('member.name') &&
        componentContent.includes('member.role')) {
      console.log('✅ Team member assignment');
    } else {
      console.log('❌ Team member assignment missing');
      allFeaturesFound = false;
    }
    
    return allFeaturesFound;
  } catch (error) {
    console.log('❌ EditTaskDialog verification failed:', error.message);
    return false;
  }
}

async function verifyAPIEnhancements() {
  console.log('\n🛣️  Verifying API Route Enhancements...');
  
  try {
    const fs = await import('fs');
    const teamsContent = fs.readFileSync('server/routes/api/teams.ts', 'utf8');
    
    const requiredValidations = [
      'Invalid team ID',
      'Team name is required',
      'must be less than 100 characters',
      'must be less than 500 characters',
      'Team not found',
      'Cannot delete team with multiple members',
      'foreign key',
      'Dependency conflict'
    ];
    
    let allValidationsFound = true;
    
    for (const validation of requiredValidations) {
      if (teamsContent.includes(validation)) {
        console.log(`✅ Validation: ${validation}`);
      } else {
        console.log(`❌ Validation: ${validation} - NOT FOUND`);
        allValidationsFound = false;
      }
    }
    
    // Check for enhanced error handling
    if (teamsContent.includes('isNaN(teamId)') && 
        teamsContent.includes('name.trim()') &&
        teamsContent.includes('status(409)')) {
      console.log('✅ Enhanced error handling and validation');
    } else {
      console.log('❌ Enhanced error handling missing');
      allValidationsFound = false;
    }
    
    return allValidationsFound;
  } catch (error) {
    console.log('❌ API enhancements verification failed:', error.message);
    return false;
  }
}

async function verifyExistingAPIRoutes() {
  console.log('\n🔗 Verifying Existing API Routes...');
  
  try {
    const fs = await import('fs');
    
    // Check projects API
    const projectsContent = fs.readFileSync('server/routes/api/projects.ts', 'utf8');
    if (projectsContent.includes('router.put') && 
        projectsContent.includes('router.delete')) {
      console.log('✅ Projects API has PUT/DELETE endpoints');
    } else {
      console.log('❌ Projects API missing PUT/DELETE endpoints');
      return false;
    }
    
    // Check tasks API
    const tasksContent = fs.readFileSync('server/routes/api/tasks.ts', 'utf8');
    if (tasksContent.includes('router.put') && 
        tasksContent.includes('router.delete')) {
      console.log('✅ Tasks API has PUT/DELETE endpoints');
    } else {
      console.log('❌ Tasks API missing PUT/DELETE endpoints');
      return false;
    }
    
    // Check teams API
    const teamsContent = fs.readFileSync('server/routes/api/teams.ts', 'utf8');
    if (teamsContent.includes('router.put') && 
        teamsContent.includes('router.delete')) {
      console.log('✅ Teams API has PUT/DELETE endpoints');
    } else {
      console.log('❌ Teams API missing PUT/DELETE endpoints');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Existing API routes verification failed:', error.message);
    return false;
  }
}

async function verifyPermissionSystem() {
  console.log('\n🔐 Verifying Permission System...');
  
  try {
    const fs = await import('fs');
    
    const dialogFiles = [
      'client/src/components/EditTeamDialog.tsx',
      'client/src/components/EditProjectDialog.tsx',
      'client/src/components/EditTaskDialog.tsx'
    ];
    
    let allPermissionsFound = true;
    
    for (const file of dialogFiles) {
      const content = fs.readFileSync(file, 'utf8');
      
      if (content.includes('canEdit') && 
          content.includes('canDelete') &&
          content.includes('role === \'admin\'') &&
          content.includes('Permission Denied')) {
        console.log(`✅ ${file.split('/').pop()} has permission system`);
      } else {
        console.log(`❌ ${file.split('/').pop()} missing permission system`);
        allPermissionsFound = false;
      }
    }
    
    return allPermissionsFound;
  } catch (error) {
    console.log('❌ Permission system verification failed:', error.message);
    return false;
  }
}

async function verifyFormValidation() {
  console.log('\n📝 Verifying Form Validation...');
  
  try {
    const fs = await import('fs');
    
    const dialogFiles = [
      'client/src/components/EditTeamDialog.tsx',
      'client/src/components/EditProjectDialog.tsx',
      'client/src/components/EditTaskDialog.tsx'
    ];
    
    let allValidationFound = true;
    
    for (const file of dialogFiles) {
      const content = fs.readFileSync(file, 'utf8');
      
      if (content.includes('zodResolver') && 
          content.includes('useForm') &&
          content.includes('FormField') &&
          content.includes('FormMessage')) {
        console.log(`✅ ${file.split('/').pop()} has form validation`);
      } else {
        console.log(`❌ ${file.split('/').pop()} missing form validation`);
        allValidationFound = false;
      }
    }
    
    return allValidationFound;
  } catch (error) {
    console.log('❌ Form validation verification failed:', error.message);
    return false;
  }
}

async function runVerification() {
  console.log('Starting T3.1 verification...\n');
  
  const tests = [
    { name: 'File Structure', fn: verifyFileStructure },
    { name: 'EditTeamDialog', fn: verifyEditTeamDialog },
    { name: 'EditProjectDialog', fn: verifyEditProjectDialog },
    { name: 'EditTaskDialog', fn: verifyEditTaskDialog },
    { name: 'API Enhancements', fn: verifyAPIEnhancements },
    { name: 'Existing API Routes', fn: verifyExistingAPIRoutes },
    { name: 'Permission System', fn: verifyPermissionSystem },
    { name: 'Form Validation', fn: verifyFormValidation }
  ];

  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.log(`❌ ${test.name} failed with error:`, error.message);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  console.log('\n📋 Verification Results');
  console.log('=======================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} verifications passed`);
  
  if (passed === total) {
    console.log('\n🎉 T3.1 CRUD Operations Implementation VERIFIED!');
    console.log('✅ All required files created');
    console.log('✅ All dialog components properly structured');
    console.log('✅ API routes enhanced with validation');
    console.log('✅ Permission system implemented');
    console.log('✅ Form validation configured');
    console.log('\n📋 T3.1 SUCCESS CRITERIA MET:');
    console.log('✅ Edit functionality for teams, projects, tasks');
    console.log('✅ Delete operations with confirmation');
    console.log('✅ Permission-based access control');
    return true;
  } else {
    console.log('\n⚠️  Some verifications failed. Check implementation.');
    return false;
  }
}

// Run verification
runVerification().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Verification failed:', error);
  process.exit(1);
});
