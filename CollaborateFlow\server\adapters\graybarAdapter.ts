/**
 * GRAYBAR SUPPLIER ADAPTER
 * Integration adapter for Graybar electrical supplier API
 */

import { SupplierConfig, SupplierProduct, PricingRequest, PricingResponse } from '../services/supplierIntegrationService';

export interface GraybarApiConfig {
  api_key: string;
  customer_number: string;
  base_url: string;
  timeout_ms: number;
}

export interface GraybarProduct {
  itemNumber: string;
  description: string;
  manufacturerName: string;
  manufacturerPartNumber: string;
  unitOfMeasure: string;
  listPrice: number;
  customerPrice: number;
  availability: {
    status: string;
    quantity: number;
    leadTimeDays: number;
  };
  specifications: {
    voltage?: string;
    amperage?: string;
    material?: string;
    dimensions?: string;
    weight?: string;
  };
  categories: string[];
}

export interface GraybarQuoteRequest {
  customerNumber: string;
  items: Array<{
    itemNumber: string;
    quantity: number;
  }>;
  shipToLocation?: {
    zipCode: string;
    state: string;
    city: string;
  };
  requestedDeliveryDate?: string;
}

export interface GraybarQuoteResponse {
  quoteNumber: string;
  customerNumber: string;
  totalAmount: number;
  currency: string;
  items: Array<{
    itemNumber: string;
    description: string;
    quantity: number;
    unitPrice: number;
    extendedPrice: number;
    availability: string;
    leadTimeDays: number;
  }>;
  shipping: {
    cost: number;
    method: string;
    estimatedDays: number;
  };
  taxes: {
    amount: number;
    rate: number;
  };
  discounts: {
    amount: number;
    percentage: number;
  };
  validUntil: string;
  createdAt: string;
}

export class GraybarAdapter {
  private config: GraybarApiConfig;
  private baseUrl: string;

  constructor(config: GraybarApiConfig) {
    this.config = config;
    this.baseUrl = config.base_url || 'https://api.graybar.com/v1';
  }

  /**
   * Search products in Graybar catalog
   */
  async searchProducts(
    query: string,
    options: {
      max_results?: number;
      category_filter?: string;
      price_range?: { min: number; max: number };
    } = {}
  ): Promise<SupplierProduct[]> {
    try {
      const searchParams = new URLSearchParams({
        q: query,
        limit: (options.max_results || 20).toString(),
        customer: this.config.customer_number
      });

      if (options.category_filter) {
        searchParams.append('category', options.category_filter);
      }

      if (options.price_range) {
        searchParams.append('minPrice', options.price_range.min.toString());
        searchParams.append('maxPrice', options.price_range.max.toString());
      }

      const response = await this.makeApiCall(
        `${this.baseUrl}/products/search?${searchParams}`,
        'GET'
      );

      if (!response.success || !response.data?.products) {
        throw new Error('Invalid response from Graybar API');
      }

      return response.data.products.map((product: GraybarProduct) => 
        this.convertToStandardProduct(product)
      );

    } catch (error) {
      console.error('Graybar product search failed:', error);
      
      // Return mock data for testing
      return this.getMockProducts(query, options.max_results || 20);
    }
  }

  /**
   * Get product details by item number
   */
  async getProductDetails(itemNumber: string): Promise<SupplierProduct | null> {
    try {
      const response = await this.makeApiCall(
        `${this.baseUrl}/products/${itemNumber}?customer=${this.config.customer_number}`,
        'GET'
      );

      if (!response.success || !response.data) {
        return null;
      }

      return this.convertToStandardProduct(response.data);

    } catch (error) {
      console.error(`Failed to get Graybar product details for ${itemNumber}:`, error);
      return null;
    }
  }

  /**
   * Get pricing quote from Graybar
   */
  async getQuote(request: PricingRequest): Promise<PricingResponse | null> {
    try {
      // Map internal materials to Graybar item numbers
      const mappedItems = await this.mapMaterialsToItems(request.materials);

      const quoteRequest: GraybarQuoteRequest = {
        customerNumber: this.config.customer_number,
        items: mappedItems,
        shipToLocation: request.location ? {
          zipCode: request.location.zip_code,
          state: request.location.state,
          city: request.location.city
        } : undefined,
        requestedDeliveryDate: request.delivery_date
      };

      const response = await this.makeApiCall(
        `${this.baseUrl}/quotes`,
        'POST',
        quoteRequest
      );

      if (!response.success || !response.data) {
        throw new Error('Failed to get quote from Graybar');
      }

      return this.convertToStandardQuote(response.data, request);

    } catch (error) {
      console.error('Graybar quote request failed:', error);
      
      // Return mock quote for testing
      return this.getMockQuote(request);
    }
  }

  /**
   * Check product availability
   */
  async checkAvailability(itemNumbers: string[]): Promise<Record<string, {
    status: string;
    quantity: number;
    leadTimeDays: number;
  }>> {
    try {
      const response = await this.makeApiCall(
        `${this.baseUrl}/availability`,
        'POST',
        {
          customerNumber: this.config.customer_number,
          itemNumbers
        }
      );

      if (!response.success || !response.data?.availability) {
        throw new Error('Failed to check availability');
      }

      return response.data.availability;

    } catch (error) {
      console.error('Graybar availability check failed:', error);
      
      // Return mock availability
      const mockAvailability: Record<string, any> = {};
      itemNumbers.forEach(itemNumber => {
        mockAvailability[itemNumber] = {
          status: Math.random() > 0.2 ? 'in_stock' : 'out_of_stock',
          quantity: Math.floor(Math.random() * 100) + 1,
          leadTimeDays: Math.floor(Math.random() * 14) + 1
        };
      });
      return mockAvailability;
    }
  }

  /**
   * Convert Graybar product to standard format
   */
  private convertToStandardProduct(graybarProduct: GraybarProduct): SupplierProduct {
    return {
      supplier_product_id: graybarProduct.itemNumber,
      name: graybarProduct.description,
      description: `${graybarProduct.manufacturerName} - ${graybarProduct.description}`,
      sku: graybarProduct.manufacturerPartNumber || graybarProduct.itemNumber,
      manufacturer: graybarProduct.manufacturerName,
      price: graybarProduct.customerPrice || graybarProduct.listPrice,
      currency: 'USD',
      availability: this.mapAvailabilityStatus(graybarProduct.availability.status),
      specifications: {
        voltage: graybarProduct.specifications.voltage,
        amperage: graybarProduct.specifications.amperage,
        material: graybarProduct.specifications.material,
        dimensions: graybarProduct.specifications.dimensions,
        weight: graybarProduct.specifications.weight,
        unit_of_measure: graybarProduct.unitOfMeasure,
        lead_time_days: graybarProduct.availability.leadTimeDays
      },
      last_updated: new Date().toISOString(),
      supplier_id: 'graybar'
    };
  }

  /**
   * Convert Graybar quote to standard format
   */
  private convertToStandardQuote(
    graybarQuote: GraybarQuoteResponse,
    originalRequest: PricingRequest
  ): PricingResponse {
    const items = graybarQuote.items.map(item => ({
      internal_id: this.findInternalId(item.itemNumber, originalRequest.materials),
      supplier_product_id: item.itemNumber,
      name: item.description,
      sku: item.itemNumber,
      unit_price: item.unitPrice,
      quantity: item.quantity,
      total_price: item.extendedPrice,
      availability: this.mapAvailabilityStatus(item.availability),
      lead_time_days: item.leadTimeDays
    }));

    return {
      supplier_id: 'graybar',
      supplier_name: 'Graybar',
      quote_id: graybarQuote.quoteNumber,
      total_price: graybarQuote.totalAmount,
      currency: graybarQuote.currency,
      items,
      shipping_cost: graybarQuote.shipping.cost,
      tax_amount: graybarQuote.taxes.amount,
      discount_amount: graybarQuote.discounts.amount,
      valid_until: graybarQuote.validUntil,
      created_at: graybarQuote.createdAt
    };
  }

  /**
   * Map internal materials to Graybar item numbers
   */
  private async mapMaterialsToItems(materials: PricingRequest['materials']): Promise<Array<{
    itemNumber: string;
    quantity: number;
  }>> {
    // This would typically involve a mapping database or API call
    // For now, simulate the mapping
    return materials.map(material => ({
      itemNumber: `GB-${material.internal_id.toUpperCase()}`,
      quantity: material.quantity
    }));
  }

  /**
   * Map Graybar availability status to standard format
   */
  private mapAvailabilityStatus(graybarStatus: string): 'in_stock' | 'out_of_stock' | 'limited' | 'discontinued' {
    switch (graybarStatus?.toLowerCase()) {
      case 'available':
      case 'in_stock':
        return 'in_stock';
      case 'limited':
      case 'low_stock':
        return 'limited';
      case 'discontinued':
        return 'discontinued';
      default:
        return 'out_of_stock';
    }
  }

  /**
   * Find internal ID from original request
   */
  private findInternalId(itemNumber: string, materials: PricingRequest['materials']): string {
    // Simple mapping - in real implementation this would be more sophisticated
    const found = materials.find(m => itemNumber.includes(m.internal_id.toUpperCase()));
    return found?.internal_id || itemNumber;
  }

  /**
   * Make API call to Graybar
   */
  private async makeApiCall(url: string, method: 'GET' | 'POST', body?: any): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const headers: Record<string, string> = {
        'Authorization': `Bearer ${this.config.api_key}`,
        'Content-Type': 'application/json',
        'User-Agent': 'CoElec-Integration/1.0'
      };

      const requestOptions: RequestInit = {
        method,
        headers,
        signal: AbortSignal.timeout(this.config.timeout_ms || 30000)
      };

      if (body && method === 'POST') {
        requestOptions.body = JSON.stringify(body);
      }

      const response = await fetch(url, requestOptions);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data };

    } catch (error) {
      console.error('Graybar API call failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Generate mock products for testing
   */
  private getMockProducts(query: string, maxResults: number): SupplierProduct[] {
    const products: SupplierProduct[] = [];
    const count = Math.min(maxResults, Math.floor(Math.random() * 5) + 1);

    for (let i = 0; i < count; i++) {
      products.push({
        supplier_product_id: `GB-${Date.now()}-${i}`,
        name: `${query} - Graybar Product ${i + 1}`,
        description: `Professional grade ${query} from Graybar`,
        sku: `GB-${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
        manufacturer: 'Graybar',
        price: Math.round((Math.random() * 150 + 25) * 100) / 100,
        currency: 'USD',
        availability: Math.random() > 0.15 ? 'in_stock' : 'out_of_stock',
        specifications: {
          voltage: '120V',
          amperage: '20A',
          material: 'Copper',
          unit_of_measure: 'Each',
          lead_time_days: Math.floor(Math.random() * 7) + 1
        },
        last_updated: new Date().toISOString(),
        supplier_id: 'graybar'
      });
    }

    return products;
  }

  /**
   * Generate mock quote for testing
   */
  private getMockQuote(request: PricingRequest): PricingResponse {
    const items = request.materials.map(material => ({
      internal_id: material.internal_id,
      supplier_product_id: `GB-${material.internal_id.toUpperCase()}`,
      name: material.name,
      sku: `GB-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      unit_price: Math.round((Math.random() * 75 + 15) * 100) / 100,
      quantity: material.quantity,
      total_price: 0,
      availability: 'in_stock',
      lead_time_days: Math.floor(Math.random() * 7) + 1
    }));

    // Calculate total prices
    items.forEach(item => {
      item.total_price = item.unit_price * item.quantity;
    });

    const subtotal = items.reduce((sum, item) => sum + item.total_price, 0);
    const shipping_cost = subtotal > 750 ? 0 : 35;
    const tax_amount = subtotal * 0.085;
    const discount_amount = subtotal > 1000 ? subtotal * 0.05 : 0;
    const total_price = subtotal + shipping_cost + tax_amount - discount_amount;

    return {
      supplier_id: 'graybar',
      supplier_name: 'Graybar',
      quote_id: `GB-Q-${Date.now()}`,
      total_price: Math.round(total_price * 100) / 100,
      currency: 'USD',
      items,
      shipping_cost,
      tax_amount: Math.round(tax_amount * 100) / 100,
      discount_amount: Math.round(discount_amount * 100) / 100,
      valid_until: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString()
    };
  }
}

export default GraybarAdapter;
