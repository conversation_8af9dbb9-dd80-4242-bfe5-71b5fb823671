-- Migration: Add Permission Structure Tables
-- This establishes the database foundation for the permissions system
-- while allowing a phased implementation approach

-- Create modules table without any dependency on user roles

-- Create modules table
CREATE TABLE IF NOT EXISTS modules (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default modules
INSERT INTO modules (name, description) VALUES
('project_management', 'Core project management features'),
('estimations', 'Cost estimation features'),
('quotes', 'Client quote generation'),
('floor_plans', 'Floor plan management and analysis'),
('approvals', 'Client approval workflow')
ON CONFLICT (name) DO NOTHING;

-- Create team_role_permissions table
CREATE TABLE IF NOT EXISTS team_role_permissions (
  id SERIAL PRIMARY KEY,
  team_role TEXT NOT NULL,
  module_id INTEGER REFERENCES modules(id) ON DELETE CASCADE,
  can_view BOOLEAN DEFAULT FALSE,
  can_create BOOLEAN DEFAULT FALSE,
  can_edit BOOLEAN DEFAULT FALSE,
  can_delete BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_role, module_id)
);

-- Insert default permissions for team roles
INSERT INTO team_role_permissions (team_role, module_id, can_view, can_create, can_edit, can_delete) 
SELECT 'admin', id, TRUE, TRUE, TRUE, TRUE FROM modules
ON CONFLICT (team_role, module_id) DO NOTHING;

INSERT INTO team_role_permissions (team_role, module_id, can_view, can_create, can_edit, can_delete) 
SELECT 'member', id, TRUE, TRUE, TRUE, FALSE FROM modules
ON CONFLICT (team_role, module_id) DO NOTHING;

INSERT INTO team_role_permissions (team_role, module_id, can_view, can_create, can_edit, can_delete) 
SELECT 'viewer', id, TRUE, FALSE, FALSE, FALSE FROM modules
ON CONFLICT (team_role, module_id) DO NOTHING;

-- Create custom roles tables (for future use)
CREATE TABLE IF NOT EXISTS custom_roles (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  base_role TEXT NOT NULL,
  organization_id BIGINT REFERENCES organizations(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(name, organization_id)
);

CREATE TABLE IF NOT EXISTS custom_role_permissions (
  id SERIAL PRIMARY KEY,
  custom_role_id INTEGER REFERENCES custom_roles(id) ON DELETE CASCADE,
  module_id INTEGER REFERENCES modules(id) ON DELETE CASCADE,
  can_view BOOLEAN DEFAULT FALSE,
  can_create BOOLEAN DEFAULT FALSE,
  can_edit BOOLEAN DEFAULT FALSE,
  can_delete BOOLEAN DEFAULT FALSE,
  UNIQUE(custom_role_id, module_id)
);

-- Add custom_role_id field to users table (for future use)
ALTER TABLE users
ADD COLUMN IF NOT EXISTS custom_role_id INTEGER REFERENCES custom_roles(id) ON DELETE SET NULL;

-- Create a stub permission check function
CREATE OR REPLACE FUNCTION has_permission(p_user_id BIGINT, p_team_id BIGINT, p_module TEXT, p_action TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- For now, always return TRUE (stub implementation)
  -- Later, this will check the team_role_permissions tables based on team membership
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create helper function to check team role permissions
CREATE OR REPLACE FUNCTION check_team_role_permission(p_team_role TEXT, p_module TEXT, p_action TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  has_perm BOOLEAN;
BEGIN
  -- Look up the permission in the team_role_permissions table
  SELECT 
    CASE
      WHEN p_action = 'view' THEN can_view
      WHEN p_action = 'create' THEN can_create
      WHEN p_action = 'edit' THEN can_edit
      WHEN p_action = 'delete' THEN can_delete
      ELSE FALSE
    END INTO has_perm
  FROM team_role_permissions trp
  JOIN modules m ON trp.module_id = m.id
  WHERE trp.team_role = p_team_role AND m.name = p_module;
  
  RETURN COALESCE(has_perm, FALSE);
END;
$$ LANGUAGE plpgsql;

-- Enable RLS on new tables
ALTER TABLE modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_role_permissions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for new tables
CREATE POLICY view_modules ON modules
  FOR SELECT USING (TRUE);  -- Everyone can view modules

CREATE POLICY view_team_role_permissions ON team_role_permissions
  FOR SELECT USING (TRUE);  -- Everyone can view team role permissions

-- Only team admins can modify team_role_permissions
CREATE POLICY modify_team_role_permissions ON team_role_permissions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      JOIN users u ON tm.user_id = u.id
      WHERE u.supabase_id = auth.uid() AND tm.role = 'admin'
    )
  );

-- Custom roles policies (for future use)
-- For now, let's allow organization owners to view their custom roles
CREATE POLICY view_custom_roles ON custom_roles
  FOR SELECT USING (
    custom_roles.organization_id IN (
      SELECT organization_id FROM users
      WHERE users.supabase_id = auth.uid()
    )
  );

-- Allow users to modify custom roles in their organization
CREATE POLICY modify_custom_roles ON custom_roles
  FOR ALL USING (
    custom_roles.organization_id IN (
      SELECT organization_id FROM users
      WHERE users.supabase_id = auth.uid()
    )
  );
