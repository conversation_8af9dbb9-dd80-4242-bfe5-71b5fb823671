-- =====================================================================================
-- T1.2 IMPLEMENTATION: Electrical Symbol Database Schema
-- =====================================================================================
-- This migration creates the comprehensive electrical symbols database with material mappings
-- Supports 50+ electrical symbols with detailed specifications and cost calculations

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================================================
-- ELECTRICAL SYMBOL CATEGORIES TABLE
-- =====================================================================================
CREATE TABLE IF NOT EXISTS electrical_symbol_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    icon_name VARCHAR(50),
    color_code VARCHAR(7), -- Hex color for UI display
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert standard electrical symbol categories
INSERT INTO electrical_symbol_categories (name, description, display_order, icon_name, color_code) VALUES
('outlets', 'Electrical outlets and receptacles', 1, 'outlet', '#FF6B35'),
('switches', 'Light switches and controls', 2, 'switch', '#4ECDC4'),
('lighting', 'Light fixtures and illumination', 3, 'lightbulb', '#FFE66D'),
('panels', 'Electrical panels and distribution', 4, 'panel', '#A8E6CF'),
('data_comm', 'Data and communication systems', 5, 'network', '#88D8B0'),
('hvac_electrical', 'HVAC electrical components', 6, 'thermostat', '#C7CEEA'),
('safety', 'Safety and emergency systems', 7, 'shield', '#FF8B94'),
('specialty', 'Specialty electrical equipment', 8, 'tool', '#B4A7D6')
ON CONFLICT (name) DO NOTHING;

-- =====================================================================================
-- ELECTRICAL SYMBOLS TABLE
-- =====================================================================================
CREATE TABLE IF NOT EXISTS electrical_symbols (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES electrical_symbol_categories(id) ON DELETE CASCADE,
    
    -- Symbol identification
    symbol_code VARCHAR(20) NOT NULL UNIQUE, -- e.g., 'OUT-STD-15A', 'SW-SP-120V'
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Technical specifications
    voltage VARCHAR(20), -- '120V', '240V', '12V', 'low_voltage'
    amperage VARCHAR(20), -- '15A', '20A', '30A', '50A'
    wattage INTEGER, -- Power consumption in watts
    phase_type VARCHAR(20), -- 'single', 'three_phase', 'dc'
    
    -- Physical properties
    mounting_type VARCHAR(50), -- 'wall', 'ceiling', 'floor', 'panel', 'surface', 'recessed'
    dimensions JSONB, -- {"width": 4.5, "height": 2.75, "depth": 1.5} in inches
    weight_lbs DECIMAL(8,2),
    
    -- Electrical properties
    nema_rating VARCHAR(20), -- 'NEMA 5-15R', 'NEMA 6-20R', etc.
    ip_rating VARCHAR(10), -- 'IP65', 'IP44', etc.
    ul_listed BOOLEAN DEFAULT true,
    energy_star BOOLEAN DEFAULT false,
    
    -- Installation requirements
    box_type VARCHAR(50), -- 'single_gang', 'double_gang', 'round', 'octagon'
    wire_gauge VARCHAR(20), -- '12 AWG', '14 AWG', '10 AWG'
    circuit_breaker_size VARCHAR(20), -- '15A', '20A', '30A'
    conduit_required BOOLEAN DEFAULT false,
    
    -- Cost and material information
    base_material_cost DECIMAL(10,2), -- Cost of the symbol/device itself
    installation_time_minutes INTEGER, -- Estimated installation time
    labor_complexity VARCHAR(20), -- 'simple', 'moderate', 'complex', 'expert'
    
    -- Symbol detection properties
    detection_keywords TEXT[], -- Keywords for AI symbol detection
    common_variations TEXT[], -- Common name variations
    symbol_shape VARCHAR(50), -- 'circle', 'square', 'rectangle', 'triangle', 'custom'
    
    -- Metadata
    manufacturer VARCHAR(100),
    model_number VARCHAR(100),
    part_number VARCHAR(100),
    datasheet_url TEXT,
    image_url TEXT,
    symbol_drawing_url TEXT, -- URL to standard electrical symbol drawing
    
    -- Status and versioning
    is_active BOOLEAN DEFAULT true,
    is_standard BOOLEAN DEFAULT true, -- Standard vs custom symbols
    version INTEGER DEFAULT 1,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_voltage CHECK (voltage IN ('120V', '240V', '277V', '480V', '12V', '24V', 'low_voltage', 'variable')),
    CONSTRAINT valid_phase CHECK (phase_type IN ('single', 'three_phase', 'dc', 'variable')),
    CONSTRAINT valid_complexity CHECK (labor_complexity IN ('simple', 'moderate', 'complex', 'expert')),
    CONSTRAINT positive_cost CHECK (base_material_cost >= 0),
    CONSTRAINT positive_time CHECK (installation_time_minutes >= 0)
);

-- Create indexes for performance
CREATE INDEX idx_electrical_symbols_category ON electrical_symbols(category_id);
CREATE INDEX idx_electrical_symbols_code ON electrical_symbols(symbol_code);
CREATE INDEX idx_electrical_symbols_voltage ON electrical_symbols(voltage);
CREATE INDEX idx_electrical_symbols_active ON electrical_symbols(is_active);
CREATE INDEX idx_electrical_symbols_detection ON electrical_symbols USING GIN(detection_keywords);

-- =====================================================================================
-- SYMBOL MATERIAL MAPPINGS TABLE
-- =====================================================================================
CREATE TABLE IF NOT EXISTS symbol_material_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol_id UUID NOT NULL REFERENCES electrical_symbols(id) ON DELETE CASCADE,
    
    -- Material information
    material_type VARCHAR(100) NOT NULL, -- 'device', 'box', 'wire', 'conduit', 'connector', 'mounting'
    material_name VARCHAR(200) NOT NULL,
    material_description TEXT,
    
    -- Quantity and specifications
    quantity DECIMAL(10,3) NOT NULL DEFAULT 1, -- Can be fractional for wire lengths, etc.
    unit VARCHAR(20) NOT NULL, -- 'each', 'feet', 'meters', 'pounds', 'linear_feet'
    
    -- Cost information
    unit_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
    
    -- Material specifications
    specifications JSONB, -- Flexible storage for material-specific specs
    
    -- Supplier information
    supplier_name VARCHAR(100),
    supplier_part_number VARCHAR(100),
    supplier_url TEXT,
    
    -- Installation details
    is_required BOOLEAN DEFAULT true,
    installation_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_quantity CHECK (quantity > 0),
    CONSTRAINT positive_unit_cost CHECK (unit_cost >= 0)
);

-- Create indexes for material mappings
CREATE INDEX idx_symbol_materials_symbol ON symbol_material_mappings(symbol_id);
CREATE INDEX idx_symbol_materials_type ON symbol_material_mappings(material_type);
CREATE INDEX idx_symbol_materials_required ON symbol_material_mappings(is_required);

-- =====================================================================================
-- SYMBOL COMPATIBILITY TABLE
-- =====================================================================================
CREATE TABLE IF NOT EXISTS symbol_compatibility (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol_id UUID NOT NULL REFERENCES electrical_symbols(id) ON DELETE CASCADE,
    compatible_with_id UUID NOT NULL REFERENCES electrical_symbols(id) ON DELETE CASCADE,
    compatibility_type VARCHAR(50) NOT NULL, -- 'requires', 'works_with', 'replaces', 'conflicts'
    compatibility_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent self-reference and duplicate entries
    CONSTRAINT no_self_compatibility CHECK (symbol_id != compatible_with_id),
    CONSTRAINT unique_compatibility UNIQUE (symbol_id, compatible_with_id, compatibility_type)
);

-- =====================================================================================
-- SYMBOL DETECTION HISTORY TABLE
-- =====================================================================================
CREATE TABLE IF NOT EXISTS symbol_detection_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol_id UUID REFERENCES electrical_symbols(id) ON DELETE SET NULL,
    
    -- Detection context
    floor_plan_id UUID, -- Reference to floor plan if available
    project_id UUID, -- Reference to project if available
    organization_id UUID NOT NULL,
    
    -- Detection details
    detected_coordinates JSONB, -- {"x": 100, "y": 150, "width": 20, "height": 20}
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    ai_model_used VARCHAR(100),
    detection_method VARCHAR(50), -- 'ai_automatic', 'user_manual', 'user_correction'
    
    -- User feedback
    user_confirmed BOOLEAN,
    user_corrected_to UUID REFERENCES electrical_symbols(id) ON DELETE SET NULL,
    user_feedback TEXT,
    
    -- Timestamps
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT valid_confidence CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- Create indexes for detection history
CREATE INDEX idx_detection_history_symbol ON symbol_detection_history(symbol_id);
CREATE INDEX idx_detection_history_org ON symbol_detection_history(organization_id);
CREATE INDEX idx_detection_history_confidence ON symbol_detection_history(confidence_score);
CREATE INDEX idx_detection_history_detected_at ON symbol_detection_history(detected_at);

-- =====================================================================================
-- UPDATE TRIGGERS
-- =====================================================================================
-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers to all tables
CREATE TRIGGER update_electrical_symbol_categories_updated_at 
    BEFORE UPDATE ON electrical_symbol_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_electrical_symbols_updated_at 
    BEFORE UPDATE ON electrical_symbols 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_symbol_material_mappings_updated_at 
    BEFORE UPDATE ON symbol_material_mappings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================================================
-- Enable RLS on all tables
ALTER TABLE electrical_symbol_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE electrical_symbols ENABLE ROW LEVEL SECURITY;
ALTER TABLE symbol_material_mappings ENABLE ROW LEVEL SECURITY;
ALTER TABLE symbol_compatibility ENABLE ROW LEVEL SECURITY;
ALTER TABLE symbol_detection_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies for electrical_symbol_categories (public read, admin write)
CREATE POLICY "Public read access to symbol categories" ON electrical_symbol_categories
    FOR SELECT USING (true);

CREATE POLICY "Admin write access to symbol categories" ON electrical_symbol_categories
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- RLS Policies for electrical_symbols (public read, admin write)
CREATE POLICY "Public read access to electrical symbols" ON electrical_symbols
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admin full access to electrical symbols" ON electrical_symbols
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- RLS Policies for symbol_material_mappings (public read, admin write)
CREATE POLICY "Public read access to material mappings" ON symbol_material_mappings
    FOR SELECT USING (true);

CREATE POLICY "Admin write access to material mappings" ON symbol_material_mappings
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- RLS Policies for symbol_compatibility (public read, admin write)
CREATE POLICY "Public read access to symbol compatibility" ON symbol_compatibility
    FOR SELECT USING (true);

CREATE POLICY "Admin write access to symbol compatibility" ON symbol_compatibility
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- RLS Policies for symbol_detection_history (organization-based access)
CREATE POLICY "Organization access to detection history" ON symbol_detection_history
    FOR ALL USING (organization_id::text = auth.jwt() ->> 'organization_id');

-- =====================================================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================================================
COMMENT ON TABLE electrical_symbol_categories IS 'Categories for organizing electrical symbols (outlets, switches, lighting, etc.)';
COMMENT ON TABLE electrical_symbols IS 'Comprehensive database of electrical symbols with technical specifications and material costs';
COMMENT ON TABLE symbol_material_mappings IS 'Material requirements and costs for each electrical symbol installation';
COMMENT ON TABLE symbol_compatibility IS 'Compatibility relationships between different electrical symbols';
COMMENT ON TABLE symbol_detection_history IS 'History of AI symbol detection results and user feedback for continuous learning';

COMMENT ON COLUMN electrical_symbols.symbol_code IS 'Unique identifier code for the symbol (e.g., OUT-STD-15A)';
COMMENT ON COLUMN electrical_symbols.detection_keywords IS 'Keywords used by AI for symbol detection and classification';
COMMENT ON COLUMN electrical_symbols.base_material_cost IS 'Base cost of the electrical device/symbol itself';
COMMENT ON COLUMN electrical_symbols.installation_time_minutes IS 'Estimated time for professional installation';
COMMENT ON COLUMN symbol_material_mappings.total_cost IS 'Automatically calculated total cost (quantity × unit_cost)';
COMMENT ON COLUMN symbol_detection_history.confidence_score IS 'AI confidence score for symbol detection (0.00-1.00)';

-- =====================================================================================
-- MIGRATION COMPLETE
-- =====================================================================================
