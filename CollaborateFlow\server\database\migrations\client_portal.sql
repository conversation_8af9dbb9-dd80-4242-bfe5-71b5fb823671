-- CLIENT PORTAL DATABASE SCHEMA
-- Complete client portal infrastructure with authentication and access management

-- =============================================================================
-- CLIENT PROFILES TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS client_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Client Information
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  company VARCHAR(255),
  phone VARCHAR(50),
  address TEXT,
  
  -- Contact Preferences
  preferred_contact_method VARCHAR(20) DEFAULT 'email' CHECK (preferred_contact_method IN ('email', 'phone', 'sms')),
  timezone VARCHAR(50) DEFAULT 'America/New_York',
  language VARCHAR(10) DEFAULT 'en',
  
  -- Portal Settings
  portal_access_enabled BOOLEAN DEFAULT true,
  email_notifications_enabled BOOLEAN DEFAULT true,
  sms_notifications_enabled BOOLEAN DEFAULT false,
  
  -- Profile Metadata
  avatar_url TEXT,
  notes TEXT,
  tags TEXT[],
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE,
  
  -- Constraints
  UNIQUE(organization_id, email)
);

-- =============================================================================
-- CLIENT ACCESS TOKENS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS client_access_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  client_id UUID NOT NULL REFERENCES client_profiles(id) ON DELETE CASCADE,
  
  -- Token Information
  access_token VARCHAR(255) NOT NULL UNIQUE,
  token_type VARCHAR(20) DEFAULT 'portal_access' CHECK (token_type IN ('portal_access', 'quote_view', 'document_access')),
  
  -- Token Scope and Permissions
  permissions JSONB DEFAULT '{}',
  allowed_resources TEXT[] DEFAULT '{}',
  
  -- Token Lifecycle
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed TIMESTAMP WITH TIME ZONE,
  access_count INTEGER DEFAULT 0,
  
  -- Security
  created_by UUID,
  revoked_at TIMESTAMP WITH TIME ZONE,
  revoked_by UUID,
  revoke_reason TEXT,
  
  -- Request Metadata
  created_ip INET,
  last_access_ip INET,
  user_agent TEXT
);

-- =============================================================================
-- QUOTE CHANGE REQUESTS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS quote_change_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  quote_id UUID NOT NULL,
  client_id UUID NOT NULL REFERENCES client_profiles(id),
  
  -- Request Information
  request_number VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  
  -- Request Status
  status VARCHAR(30) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected', 'implemented')),
  
  -- Response Information
  response_message TEXT,
  estimated_cost_impact DECIMAL(10,2),
  estimated_time_impact INTEGER, -- in hours
  
  -- Approval Workflow
  reviewed_by UUID,
  reviewed_at TIMESTAMP WITH TIME ZONE,
  approved_by UUID,
  approved_at TIMESTAMP WITH TIME ZONE,
  
  -- Implementation
  implemented_by UUID,
  implemented_at TIMESTAMP WITH TIME ZONE,
  implementation_notes TEXT,
  
  -- Attachments and References
  attachments JSONB DEFAULT '[]',
  related_documents TEXT[],
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(organization_id, request_number)
);

-- =============================================================================
-- CLIENT ACTIVITY LOG TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS client_activity_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  client_id UUID NOT NULL REFERENCES client_profiles(id),
  
  -- Activity Information
  activity_type VARCHAR(50) NOT NULL,
  activity_description TEXT NOT NULL,
  activity_category VARCHAR(30) DEFAULT 'general',
  
  -- Related Entities
  related_entity_type VARCHAR(50),
  related_entity_id UUID,
  
  -- Activity Context
  activity_metadata JSONB DEFAULT '{}',
  session_id VARCHAR(255),
  
  -- Request Information
  ip_address INET,
  user_agent TEXT,
  request_method VARCHAR(10),
  request_path TEXT,
  
  -- Timestamps
  activity_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexing
  activity_date DATE GENERATED ALWAYS AS (activity_timestamp::DATE) STORED
);

-- =============================================================================
-- CLIENT NOTIFICATIONS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS client_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  client_id UUID NOT NULL REFERENCES client_profiles(id),
  
  -- Notification Information
  notification_type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  
  -- Notification Channels
  send_email BOOLEAN DEFAULT true,
  send_sms BOOLEAN DEFAULT false,
  send_push BOOLEAN DEFAULT false,
  show_in_portal BOOLEAN DEFAULT true,
  
  -- Delivery Status
  email_sent BOOLEAN DEFAULT false,
  email_sent_at TIMESTAMP WITH TIME ZONE,
  sms_sent BOOLEAN DEFAULT false,
  sms_sent_at TIMESTAMP WITH TIME ZONE,
  push_sent BOOLEAN DEFAULT false,
  push_sent_at TIMESTAMP WITH TIME ZONE,
  
  -- Portal Status
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMP WITH TIME ZONE,
  is_archived BOOLEAN DEFAULT false,
  archived_at TIMESTAMP WITH TIME ZONE,
  
  -- Related Information
  related_entity_type VARCHAR(50),
  related_entity_id UUID,
  action_url TEXT,
  action_label VARCHAR(100),
  
  -- Metadata
  notification_metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE
);

-- =============================================================================
-- CLIENT PORTAL SESSIONS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS client_portal_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  client_id UUID NOT NULL REFERENCES client_profiles(id),
  
  -- Session Information
  session_token VARCHAR(255) NOT NULL UNIQUE,
  access_token_id UUID REFERENCES client_access_tokens(id),
  
  -- Session Lifecycle
  is_active BOOLEAN DEFAULT true,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  
  -- Session Context
  ip_address INET,
  user_agent TEXT,
  device_type VARCHAR(50),
  browser VARCHAR(100),
  operating_system VARCHAR(100),
  
  -- Activity Tracking
  page_views INTEGER DEFAULT 0,
  actions_performed INTEGER DEFAULT 0,
  time_spent_seconds INTEGER DEFAULT 0,
  
  -- Security
  is_suspicious BOOLEAN DEFAULT false,
  security_flags TEXT[],
  
  -- Geolocation (optional)
  country VARCHAR(2),
  region VARCHAR(100),
  city VARCHAR(100)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Client profiles indexes
CREATE INDEX IF NOT EXISTS idx_client_profiles_org ON client_profiles(organization_id);
CREATE INDEX IF NOT EXISTS idx_client_profiles_email ON client_profiles(email);
CREATE INDEX IF NOT EXISTS idx_client_profiles_company ON client_profiles(company);
CREATE INDEX IF NOT EXISTS idx_client_profiles_portal_access ON client_profiles(portal_access_enabled);

-- Access tokens indexes
CREATE INDEX IF NOT EXISTS idx_client_access_tokens_org ON client_access_tokens(organization_id);
CREATE INDEX IF NOT EXISTS idx_client_access_tokens_client ON client_access_tokens(client_id);
CREATE INDEX IF NOT EXISTS idx_client_access_tokens_token ON client_access_tokens(access_token);
CREATE INDEX IF NOT EXISTS idx_client_access_tokens_active ON client_access_tokens(is_active);
CREATE INDEX IF NOT EXISTS idx_client_access_tokens_expires ON client_access_tokens(expires_at);

-- Change requests indexes
CREATE INDEX IF NOT EXISTS idx_quote_change_requests_org ON quote_change_requests(organization_id);
CREATE INDEX IF NOT EXISTS idx_quote_change_requests_quote ON quote_change_requests(quote_id);
CREATE INDEX IF NOT EXISTS idx_quote_change_requests_client ON quote_change_requests(client_id);
CREATE INDEX IF NOT EXISTS idx_quote_change_requests_status ON quote_change_requests(status);
CREATE INDEX IF NOT EXISTS idx_quote_change_requests_created ON quote_change_requests(created_at);

-- Activity log indexes
CREATE INDEX IF NOT EXISTS idx_client_activity_log_org ON client_activity_log(organization_id);
CREATE INDEX IF NOT EXISTS idx_client_activity_log_client ON client_activity_log(client_id);
CREATE INDEX IF NOT EXISTS idx_client_activity_log_type ON client_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_client_activity_log_date ON client_activity_log(activity_date);
CREATE INDEX IF NOT EXISTS idx_client_activity_log_timestamp ON client_activity_log(activity_timestamp);

-- Notifications indexes
CREATE INDEX IF NOT EXISTS idx_client_notifications_org ON client_notifications(organization_id);
CREATE INDEX IF NOT EXISTS idx_client_notifications_client ON client_notifications(client_id);
CREATE INDEX IF NOT EXISTS idx_client_notifications_type ON client_notifications(notification_type);
CREATE INDEX IF NOT EXISTS idx_client_notifications_read ON client_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_client_notifications_created ON client_notifications(created_at);

-- Sessions indexes
CREATE INDEX IF NOT EXISTS idx_client_portal_sessions_org ON client_portal_sessions(organization_id);
CREATE INDEX IF NOT EXISTS idx_client_portal_sessions_client ON client_portal_sessions(client_id);
CREATE INDEX IF NOT EXISTS idx_client_portal_sessions_token ON client_portal_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_client_portal_sessions_active ON client_portal_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_client_portal_sessions_started ON client_portal_sessions(started_at);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE client_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_access_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_change_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_portal_sessions ENABLE ROW LEVEL SECURITY;

-- Client profiles - Organization-based access
CREATE POLICY "Organization access for client profiles" ON client_profiles 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Access tokens - Organization-based access
CREATE POLICY "Organization access for client access tokens" ON client_access_tokens 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Change requests - Organization-based access
CREATE POLICY "Organization access for change requests" ON quote_change_requests 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Activity log - Organization-based access
CREATE POLICY "Organization access for client activity log" ON client_activity_log 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Notifications - Organization-based access
CREATE POLICY "Organization access for client notifications" ON client_notifications 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Sessions - Organization-based access
CREATE POLICY "Organization access for client sessions" ON client_portal_sessions 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- =============================================================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_client_profiles_updated_at BEFORE UPDATE ON client_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_quote_change_requests_updated_at BEFORE UPDATE ON quote_change_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- FUNCTIONS FOR CLIENT PORTAL MANAGEMENT
-- =============================================================================

-- Function to increment access count
CREATE OR REPLACE FUNCTION increment_access_count()
RETURNS INTEGER AS $$
BEGIN
  RETURN COALESCE(OLD.access_count, 0) + 1;
END;
$$ LANGUAGE plpgsql;

-- Function to generate client access token
CREATE OR REPLACE FUNCTION generate_client_access_token(
  p_organization_id UUID,
  p_client_id UUID,
  p_token_type VARCHAR DEFAULT 'portal_access',
  p_expires_in_days INTEGER DEFAULT 30
)
RETURNS VARCHAR AS $$
DECLARE
  new_token VARCHAR;
  expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Generate secure random token
  new_token := encode(gen_random_bytes(32), 'base64');
  new_token := replace(replace(replace(new_token, '+', '-'), '/', '_'), '=', '');
  
  -- Calculate expiration
  expires_at := NOW() + (p_expires_in_days || ' days')::INTERVAL;
  
  -- Insert token
  INSERT INTO client_access_tokens (
    organization_id,
    client_id,
    access_token,
    token_type,
    expires_at
  ) VALUES (
    p_organization_id,
    p_client_id,
    new_token,
    p_token_type,
    expires_at
  );
  
  RETURN new_token;
END;
$$ LANGUAGE plpgsql;

-- Function to get client portal statistics
CREATE OR REPLACE FUNCTION get_client_portal_statistics(p_organization_id UUID)
RETURNS TABLE (
  total_clients BIGINT,
  active_clients BIGINT,
  total_quotes BIGINT,
  pending_quotes BIGINT,
  approved_quotes BIGINT,
  total_change_requests BIGINT,
  pending_change_requests BIGINT,
  avg_response_time_hours NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM client_profiles WHERE organization_id = p_organization_id)::BIGINT,
    (SELECT COUNT(*) FROM client_profiles WHERE organization_id = p_organization_id AND portal_access_enabled = true)::BIGINT,
    (SELECT COUNT(*) FROM quotes WHERE organization_id = p_organization_id)::BIGINT,
    (SELECT COUNT(*) FROM quotes WHERE organization_id = p_organization_id AND status = 'pending')::BIGINT,
    (SELECT COUNT(*) FROM quotes WHERE organization_id = p_organization_id AND status = 'approved')::BIGINT,
    (SELECT COUNT(*) FROM quote_change_requests WHERE organization_id = p_organization_id)::BIGINT,
    (SELECT COUNT(*) FROM quote_change_requests WHERE organization_id = p_organization_id AND status = 'pending')::BIGINT,
    (SELECT ROUND(AVG(EXTRACT(EPOCH FROM (approved_at - created_at))/3600), 2) 
     FROM quotes 
     WHERE organization_id = p_organization_id AND approved_at IS NOT NULL)::NUMERIC;
END;
$$ LANGUAGE plpgsql;
