-- T1.3 MATERIAL ESTIMATION ENGINE - DATABASE SCHEMA
-- Comprehensive electrical materials database for cost estimation

-- =============================================================================
-- ELECTRICAL MATERIALS CATALOG
-- =============================================================================

CREATE TABLE IF NOT EXISTS electrical_materials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  material_code VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  subcategory VARCHAR(100),
  manufacturer VARCHAR(100),
  model_number VARCHAR(100),
  part_number VARCHAR(100),

  -- Specifications
  specifications JSONB DEFAULT '{}',
  voltage_rating VARCHAR(20),
  amperage_rating VARCHAR(20),
  wattage_rating INTEGER,
  phase_type VARCHAR(20),
  nema_rating VARCHAR(20),
  ul_listed BOOLEAN DEFAULT true,
  energy_star BOOLEAN DEFAULT false,

  -- Physical Properties
  dimensions JSONB, -- {width, height, depth, unit}
  weight_lbs DECIMAL(8,2),
  color VARCHAR(50),
  finish VARCHAR(50),
  mounting_type VARCHAR(50),

  -- Pricing Information
  base_cost DECIMAL(10,2) NOT NULL CHECK (base_cost > 0),
  unit VARCHAR(20) NOT NULL DEFAULT 'each',
  minimum_order_quantity INTEGER DEFAULT 1 CHECK (minimum_order_quantity > 0),
  bulk_discount_threshold INTEGER CHECK (bulk_discount_threshold IS NULL OR bulk_discount_threshold > 0),
  bulk_discount_percentage DECIMAL(5,2) CHECK (bulk_discount_percentage IS NULL OR (bulk_discount_percentage >= 0 AND bulk_discount_percentage <= 100)),

  -- Price validation fields
  price DECIMAL(10,2) GENERATED ALWAYS AS (base_cost) STORED,
  cost DECIMAL(10,2) GENERATED ALWAYS AS (base_cost) STORED,

  -- Regional Pricing Adjustments
  regional_adjustments JSONB DEFAULT '{}', -- {region: multiplier}

  -- Labor Information
  installation_time_minutes INTEGER,
  labor_complexity VARCHAR(20) CHECK (labor_complexity IN ('simple', 'moderate', 'complex', 'expert')),
  required_tools TEXT[],
  safety_requirements TEXT[],

  -- Supplier Information
  primary_supplier_id UUID,
  supplier_part_numbers JSONB DEFAULT '{}', -- {supplier_id: part_number}
  availability_status VARCHAR(20) DEFAULT 'available',
  lead_time_days INTEGER DEFAULT 0,

  -- Metadata
  is_active BOOLEAN DEFAULT true,
  is_standard BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- MATERIAL CATEGORIES
-- =============================================================================

CREATE TABLE IF NOT EXISTS material_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  parent_category_id UUID REFERENCES material_categories(id),
  display_order INTEGER DEFAULT 0,
  icon_name VARCHAR(50),
  color_code VARCHAR(7),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- REGIONAL PRICING
-- =============================================================================

CREATE TABLE IF NOT EXISTS regional_pricing (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  region_code VARCHAR(10) NOT NULL,
  region_name VARCHAR(100) NOT NULL,
  state_province VARCHAR(50),
  country VARCHAR(50) DEFAULT 'US',

  -- Cost Adjustments
  labor_rate_multiplier DECIMAL(4,2) DEFAULT 1.00,
  material_cost_multiplier DECIMAL(4,2) DEFAULT 1.00,
  tax_rate DECIMAL(5,2) DEFAULT 0.00,
  permit_cost_base DECIMAL(8,2) DEFAULT 0.00,

  -- Market Conditions
  market_conditions JSONB DEFAULT '{}',
  cost_of_living_index DECIMAL(5,2) DEFAULT 100.00,

  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  UNIQUE(region_code)
);

-- =============================================================================
-- LABOR RATES
-- =============================================================================

CREATE TABLE IF NOT EXISTS labor_rates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  region_id UUID REFERENCES regional_pricing(id),
  skill_level VARCHAR(20) NOT NULL CHECK (skill_level IN ('apprentice', 'journeyman', 'master', 'foreman')),
  hourly_rate DECIMAL(8,2) NOT NULL,
  overtime_multiplier DECIMAL(3,2) DEFAULT 1.50,

  -- Rate Modifiers
  project_type_modifiers JSONB DEFAULT '{}', -- {residential: 1.0, commercial: 1.2, industrial: 1.5}
  complexity_modifiers JSONB DEFAULT '{}', -- {simple: 1.0, moderate: 1.2, complex: 1.5}

  effective_date DATE NOT NULL,
  expiration_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- MATERIAL ASSEMBLIES
-- =============================================================================

CREATE TABLE IF NOT EXISTS material_assemblies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assembly_code VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100),

  -- Assembly Properties
  total_cost DECIMAL(10,2),
  total_labor_hours DECIMAL(6,2),
  complexity_level VARCHAR(20),

  -- Metadata
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ASSEMBLY COMPONENTS
-- =============================================================================

CREATE TABLE IF NOT EXISTS assembly_components (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assembly_id UUID NOT NULL REFERENCES material_assemblies(id) ON DELETE CASCADE,
  material_id UUID NOT NULL REFERENCES electrical_materials(id) ON DELETE CASCADE,
  quantity DECIMAL(10,3) NOT NULL DEFAULT 1,
  is_optional BOOLEAN DEFAULT false,
  notes TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  UNIQUE(assembly_id, material_id)
);

-- =============================================================================
-- ESTIMATION PROJECTS
-- =============================================================================

CREATE TABLE IF NOT EXISTS estimation_projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  project_name VARCHAR(200) NOT NULL,
  project_type VARCHAR(50) NOT NULL, -- residential, commercial, industrial
  location JSONB, -- {address, city, state, zip, region_code}

  -- Project Details
  square_footage INTEGER,
  number_of_floors INTEGER DEFAULT 1,
  building_type VARCHAR(50),
  construction_year INTEGER,

  -- Estimation Settings
  markup_percentage DECIMAL(5,2) DEFAULT 20.00,
  overhead_percentage DECIMAL(5,2) DEFAULT 15.00,
  profit_margin_percentage DECIMAL(5,2) DEFAULT 10.00,
  contingency_percentage DECIMAL(5,2) DEFAULT 5.00,

  -- Regional Settings
  region_id UUID REFERENCES regional_pricing(id),
  labor_rate_id UUID REFERENCES labor_rates(id),

  -- Status
  status VARCHAR(20) DEFAULT 'draft',
  created_by UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ESTIMATION RESULTS
-- =============================================================================

CREATE TABLE IF NOT EXISTS estimation_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES estimation_projects(id) ON DELETE CASCADE,
  symbol_detection_id UUID, -- Link to symbol detection results

  -- Cost Breakdown
  materials_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
  labor_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
  overhead_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
  markup_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
  tax_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
  permit_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
  contingency_cost DECIMAL(12,2) NOT NULL DEFAULT 0,
  total_cost DECIMAL(12,2) NOT NULL DEFAULT 0,

  -- Labor Breakdown
  total_labor_hours DECIMAL(8,2) NOT NULL DEFAULT 0,
  apprentice_hours DECIMAL(8,2) DEFAULT 0,
  journeyman_hours DECIMAL(8,2) DEFAULT 0,
  master_hours DECIMAL(8,2) DEFAULT 0,

  -- Metadata
  calculation_method VARCHAR(50) DEFAULT 'symbol_based',
  confidence_score DECIMAL(3,2),
  notes TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ESTIMATION LINE ITEMS
-- =============================================================================

CREATE TABLE IF NOT EXISTS estimation_line_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  estimation_id UUID NOT NULL REFERENCES estimation_results(id) ON DELETE CASCADE,
  symbol_id UUID, -- Reference to detected symbol
  material_id UUID REFERENCES electrical_materials(id),
  assembly_id UUID REFERENCES material_assemblies(id),

  -- Item Details
  item_type VARCHAR(20) NOT NULL CHECK (item_type IN ('material', 'assembly', 'labor', 'other')),
  description TEXT NOT NULL,
  quantity DECIMAL(10,3) NOT NULL DEFAULT 1,
  unit VARCHAR(20) NOT NULL DEFAULT 'each',

  -- Costs
  unit_cost DECIMAL(10,2) NOT NULL,
  total_cost DECIMAL(12,2) NOT NULL,
  labor_hours DECIMAL(6,2) DEFAULT 0,
  labor_cost DECIMAL(10,2) DEFAULT 0,

  -- Metadata
  line_number INTEGER,
  category VARCHAR(100),
  notes TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

CREATE INDEX IF NOT EXISTS idx_electrical_materials_category ON electrical_materials(category);
CREATE INDEX IF NOT EXISTS idx_electrical_materials_active ON electrical_materials(is_active);
CREATE INDEX IF NOT EXISTS idx_electrical_materials_code ON electrical_materials(material_code);
CREATE INDEX IF NOT EXISTS idx_regional_pricing_region ON regional_pricing(region_code);
CREATE INDEX IF NOT EXISTS idx_labor_rates_region ON labor_rates(region_id);
CREATE INDEX IF NOT EXISTS idx_estimation_projects_org ON estimation_projects(organization_id);
CREATE INDEX IF NOT EXISTS idx_estimation_results_project ON estimation_results(project_id);
CREATE INDEX IF NOT EXISTS idx_estimation_line_items_estimation ON estimation_line_items(estimation_id);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE electrical_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_pricing ENABLE ROW LEVEL SECURITY;
ALTER TABLE labor_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_assemblies ENABLE ROW LEVEL SECURITY;
ALTER TABLE assembly_components ENABLE ROW LEVEL SECURITY;
ALTER TABLE estimation_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE estimation_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE estimation_line_items ENABLE ROW LEVEL SECURITY;

-- Public read access for materials and categories (shared data)
CREATE POLICY "Public read access for materials" ON electrical_materials FOR SELECT USING (true);
CREATE POLICY "Public read access for categories" ON material_categories FOR SELECT USING (true);
CREATE POLICY "Public read access for regional pricing" ON regional_pricing FOR SELECT USING (true);
CREATE POLICY "Public read access for labor rates" ON labor_rates FOR SELECT USING (true);
CREATE POLICY "Public read access for assemblies" ON material_assemblies FOR SELECT USING (true);
CREATE POLICY "Public read access for assembly components" ON assembly_components FOR SELECT USING (true);

-- Organization-based access for estimation data
CREATE POLICY "Organization access for estimation projects" ON estimation_projects
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Organization access for estimation results" ON estimation_results
  FOR ALL USING (project_id IN (SELECT id FROM estimation_projects WHERE organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid())));

CREATE POLICY "Organization access for estimation line items" ON estimation_line_items
  FOR ALL USING (estimation_id IN (SELECT id FROM estimation_results WHERE project_id IN (SELECT id FROM estimation_projects WHERE organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()))));

-- =============================================================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_electrical_materials_updated_at BEFORE UPDATE ON electrical_materials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_material_assemblies_updated_at BEFORE UPDATE ON material_assemblies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_estimation_projects_updated_at BEFORE UPDATE ON estimation_projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_estimation_results_updated_at BEFORE UPDATE ON estimation_results FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
