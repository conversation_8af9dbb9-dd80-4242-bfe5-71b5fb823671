-- EMAIL INTEGRATION DATABASE SCHEMA
-- Complete email automation and tracking infrastructure

-- =============================================================================
-- EMAIL ACTIVITY LOG TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS email_activity_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Email Information
  email_type VARCHAR(50) NOT NULL,
  recipient_email VARCHAR(255) NOT NULL,
  sender_email VARCHAR(255) DEFAULT '<EMAIL>',
  subject TEXT NOT NULL,
  
  -- Message Tracking
  message_id VARCHAR(255),
  status VARCHAR(30) NOT NULL DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed', 'spam')),
  
  -- Reference Information
  reference_type VARCHAR(50),
  reference_id UUID,
  template_used VARCHAR(100),
  
  -- Delivery Tracking
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  delivered_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  bounced_at TIMESTAMP WITH TIME ZONE,
  
  -- Error Information
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  
  -- Email Content (for debugging)
  email_content JSONB,
  
  -- Metadata
  user_agent TEXT,
  ip_address INET,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- EMAIL TEMPLATES TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS email_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Template Information
  template_key VARCHAR(100) NOT NULL,
  template_name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(50) DEFAULT 'general',
  
  -- Template Content
  subject_template TEXT NOT NULL,
  html_template TEXT NOT NULL,
  text_template TEXT NOT NULL,
  
  -- Template Configuration
  variables JSONB DEFAULT '[]',
  default_from_email VARCHAR(255),
  default_from_name VARCHAR(255),
  
  -- Template Settings
  is_active BOOLEAN DEFAULT true,
  is_system_template BOOLEAN DEFAULT false,
  version INTEGER DEFAULT 1,
  
  -- Usage Statistics
  usage_count INTEGER DEFAULT 0,
  last_used TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID,
  
  -- Constraints
  UNIQUE(organization_id, template_key)
);

-- =============================================================================
-- EMAIL AUTOMATION RULES TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS email_automation_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Rule Information
  rule_name VARCHAR(255) NOT NULL,
  description TEXT,
  trigger_event VARCHAR(100) NOT NULL,
  
  -- Rule Configuration
  is_active BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 0,
  
  -- Trigger Conditions
  trigger_conditions JSONB DEFAULT '{}',
  delay_minutes INTEGER DEFAULT 0,
  
  -- Email Configuration
  template_id UUID REFERENCES email_templates(id),
  recipient_type VARCHAR(50) NOT NULL CHECK (recipient_type IN ('client', 'team_member', 'contractor', 'custom')),
  custom_recipients TEXT[],
  
  -- Execution Limits
  max_executions_per_day INTEGER,
  max_executions_total INTEGER,
  
  -- Execution Tracking
  executions_today INTEGER DEFAULT 0,
  total_executions INTEGER DEFAULT 0,
  last_executed TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID
);

-- =============================================================================
-- EMAIL AUTOMATION EXECUTIONS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS email_automation_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  automation_rule_id UUID NOT NULL REFERENCES email_automation_rules(id),
  
  -- Execution Information
  trigger_event VARCHAR(100) NOT NULL,
  trigger_data JSONB DEFAULT '{}',
  
  -- Execution Status
  status VARCHAR(30) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'skipped')),
  
  -- Email Information
  recipient_email VARCHAR(255),
  email_activity_id UUID REFERENCES email_activity_log(id),
  
  -- Execution Details
  executed_at TIMESTAMP WITH TIME ZONE,
  processing_time_ms INTEGER,
  error_message TEXT,
  
  -- Context
  context_data JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- EMAIL PREFERENCES TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS email_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Preference Owner
  user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('client', 'team_member')),
  user_id UUID NOT NULL,
  user_email VARCHAR(255) NOT NULL,
  
  -- Email Preferences
  quote_notifications BOOLEAN DEFAULT true,
  project_updates BOOLEAN DEFAULT true,
  signature_requests BOOLEAN DEFAULT true,
  change_request_notifications BOOLEAN DEFAULT true,
  marketing_emails BOOLEAN DEFAULT false,
  
  -- Frequency Preferences
  digest_frequency VARCHAR(20) DEFAULT 'immediate' CHECK (digest_frequency IN ('immediate', 'daily', 'weekly', 'never')),
  preferred_time TIME DEFAULT '09:00:00',
  timezone VARCHAR(50) DEFAULT 'America/New_York',
  
  -- Communication Preferences
  preferred_language VARCHAR(10) DEFAULT 'en',
  email_format VARCHAR(10) DEFAULT 'html' CHECK (email_format IN ('html', 'text')),
  
  -- Unsubscribe Information
  is_unsubscribed BOOLEAN DEFAULT false,
  unsubscribed_at TIMESTAMP WITH TIME ZONE,
  unsubscribe_reason TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(organization_id, user_type, user_id)
);

-- =============================================================================
-- EMAIL ANALYTICS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS email_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Analytics Period
  date_period DATE NOT NULL,
  email_type VARCHAR(50),
  template_used VARCHAR(100),
  
  -- Volume Metrics
  emails_sent INTEGER DEFAULT 0,
  emails_delivered INTEGER DEFAULT 0,
  emails_opened INTEGER DEFAULT 0,
  emails_clicked INTEGER DEFAULT 0,
  emails_bounced INTEGER DEFAULT 0,
  emails_failed INTEGER DEFAULT 0,
  
  -- Rate Metrics
  delivery_rate DECIMAL(5,2) DEFAULT 0.00,
  open_rate DECIMAL(5,2) DEFAULT 0.00,
  click_rate DECIMAL(5,2) DEFAULT 0.00,
  bounce_rate DECIMAL(5,2) DEFAULT 0.00,
  
  -- Performance Metrics
  avg_delivery_time_minutes INTEGER DEFAULT 0,
  avg_open_time_hours INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(organization_id, date_period, email_type, template_used)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Email activity log indexes
CREATE INDEX IF NOT EXISTS idx_email_activity_log_org ON email_activity_log(organization_id);
CREATE INDEX IF NOT EXISTS idx_email_activity_log_type ON email_activity_log(email_type);
CREATE INDEX IF NOT EXISTS idx_email_activity_log_recipient ON email_activity_log(recipient_email);
CREATE INDEX IF NOT EXISTS idx_email_activity_log_status ON email_activity_log(status);
CREATE INDEX IF NOT EXISTS idx_email_activity_log_sent ON email_activity_log(sent_at);
CREATE INDEX IF NOT EXISTS idx_email_activity_log_reference ON email_activity_log(reference_type, reference_id);

-- Email templates indexes
CREATE INDEX IF NOT EXISTS idx_email_templates_org ON email_templates(organization_id);
CREATE INDEX IF NOT EXISTS idx_email_templates_key ON email_templates(template_key);
CREATE INDEX IF NOT EXISTS idx_email_templates_active ON email_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_email_templates_category ON email_templates(category);

-- Automation rules indexes
CREATE INDEX IF NOT EXISTS idx_email_automation_rules_org ON email_automation_rules(organization_id);
CREATE INDEX IF NOT EXISTS idx_email_automation_rules_event ON email_automation_rules(trigger_event);
CREATE INDEX IF NOT EXISTS idx_email_automation_rules_active ON email_automation_rules(is_active);

-- Automation executions indexes
CREATE INDEX IF NOT EXISTS idx_email_automation_executions_org ON email_automation_executions(organization_id);
CREATE INDEX IF NOT EXISTS idx_email_automation_executions_rule ON email_automation_executions(automation_rule_id);
CREATE INDEX IF NOT EXISTS idx_email_automation_executions_status ON email_automation_executions(status);
CREATE INDEX IF NOT EXISTS idx_email_automation_executions_created ON email_automation_executions(created_at);

-- Email preferences indexes
CREATE INDEX IF NOT EXISTS idx_email_preferences_org ON email_preferences(organization_id);
CREATE INDEX IF NOT EXISTS idx_email_preferences_user ON email_preferences(user_type, user_id);
CREATE INDEX IF NOT EXISTS idx_email_preferences_email ON email_preferences(user_email);
CREATE INDEX IF NOT EXISTS idx_email_preferences_unsubscribed ON email_preferences(is_unsubscribed);

-- Email analytics indexes
CREATE INDEX IF NOT EXISTS idx_email_analytics_org ON email_analytics(organization_id);
CREATE INDEX IF NOT EXISTS idx_email_analytics_date ON email_analytics(date_period);
CREATE INDEX IF NOT EXISTS idx_email_analytics_type ON email_analytics(email_type);
CREATE INDEX IF NOT EXISTS idx_email_analytics_template ON email_analytics(template_used);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE email_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_automation_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_analytics ENABLE ROW LEVEL SECURITY;

-- Email activity log - Organization-based access
CREATE POLICY "Organization access for email activity log" ON email_activity_log 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Email templates - Organization-based access
CREATE POLICY "Organization access for email templates" ON email_templates 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Automation rules - Organization-based access
CREATE POLICY "Organization access for automation rules" ON email_automation_rules 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Automation executions - Organization-based access
CREATE POLICY "Organization access for automation executions" ON email_automation_executions 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Email preferences - Organization-based access
CREATE POLICY "Organization access for email preferences" ON email_preferences 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Email analytics - Organization-based access
CREATE POLICY "Organization access for email analytics" ON email_analytics 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- =============================================================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_email_activity_log_updated_at BEFORE UPDATE ON email_activity_log FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_automation_rules_updated_at BEFORE UPDATE ON email_automation_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_preferences_updated_at BEFORE UPDATE ON email_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_analytics_updated_at BEFORE UPDATE ON email_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- FUNCTIONS FOR EMAIL MANAGEMENT
-- =============================================================================

-- Function to update email analytics
CREATE OR REPLACE FUNCTION update_email_analytics(
  p_organization_id UUID,
  p_email_type VARCHAR(50),
  p_template_used VARCHAR(100),
  p_event_type VARCHAR(30)
)
RETURNS VOID AS $$
DECLARE
  today_date DATE := CURRENT_DATE;
BEGIN
  -- Insert or update analytics for today
  INSERT INTO email_analytics (
    organization_id,
    date_period,
    email_type,
    template_used,
    emails_sent,
    emails_delivered,
    emails_opened,
    emails_clicked,
    emails_bounced,
    emails_failed
  ) VALUES (
    p_organization_id,
    today_date,
    p_email_type,
    p_template_used,
    CASE WHEN p_event_type = 'sent' THEN 1 ELSE 0 END,
    CASE WHEN p_event_type = 'delivered' THEN 1 ELSE 0 END,
    CASE WHEN p_event_type = 'opened' THEN 1 ELSE 0 END,
    CASE WHEN p_event_type = 'clicked' THEN 1 ELSE 0 END,
    CASE WHEN p_event_type = 'bounced' THEN 1 ELSE 0 END,
    CASE WHEN p_event_type = 'failed' THEN 1 ELSE 0 END
  )
  ON CONFLICT (organization_id, date_period, email_type, template_used)
  DO UPDATE SET
    emails_sent = email_analytics.emails_sent + 
      CASE WHEN p_event_type = 'sent' THEN 1 ELSE 0 END,
    emails_delivered = email_analytics.emails_delivered + 
      CASE WHEN p_event_type = 'delivered' THEN 1 ELSE 0 END,
    emails_opened = email_analytics.emails_opened + 
      CASE WHEN p_event_type = 'opened' THEN 1 ELSE 0 END,
    emails_clicked = email_analytics.emails_clicked + 
      CASE WHEN p_event_type = 'clicked' THEN 1 ELSE 0 END,
    emails_bounced = email_analytics.emails_bounced + 
      CASE WHEN p_event_type = 'bounced' THEN 1 ELSE 0 END,
    emails_failed = email_analytics.emails_failed + 
      CASE WHEN p_event_type = 'failed' THEN 1 ELSE 0 END,
    delivery_rate = CASE 
      WHEN (email_analytics.emails_sent + CASE WHEN p_event_type = 'sent' THEN 1 ELSE 0 END) > 0 
      THEN 
        ((email_analytics.emails_delivered + CASE WHEN p_event_type = 'delivered' THEN 1 ELSE 0 END)::DECIMAL / 
         (email_analytics.emails_sent + CASE WHEN p_event_type = 'sent' THEN 1 ELSE 0 END)) * 100
      ELSE 0 
    END,
    open_rate = CASE 
      WHEN (email_analytics.emails_delivered + CASE WHEN p_event_type = 'delivered' THEN 1 ELSE 0 END) > 0 
      THEN 
        ((email_analytics.emails_opened + CASE WHEN p_event_type = 'opened' THEN 1 ELSE 0 END)::DECIMAL / 
         (email_analytics.emails_delivered + CASE WHEN p_event_type = 'delivered' THEN 1 ELSE 0 END)) * 100
      ELSE 0 
    END,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to get email statistics
CREATE OR REPLACE FUNCTION get_email_statistics(p_organization_id UUID, p_days INTEGER DEFAULT 30)
RETURNS TABLE (
  total_sent BIGINT,
  total_delivered BIGINT,
  total_opened BIGINT,
  total_clicked BIGINT,
  avg_delivery_rate NUMERIC,
  avg_open_rate NUMERIC,
  avg_click_rate NUMERIC,
  most_used_template VARCHAR(100)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    SUM(ea.emails_sent)::BIGINT as total_sent,
    SUM(ea.emails_delivered)::BIGINT as total_delivered,
    SUM(ea.emails_opened)::BIGINT as total_opened,
    SUM(ea.emails_clicked)::BIGINT as total_clicked,
    ROUND(AVG(ea.delivery_rate), 2) as avg_delivery_rate,
    ROUND(AVG(ea.open_rate), 2) as avg_open_rate,
    ROUND(AVG(ea.click_rate), 2) as avg_click_rate,
    (
      SELECT template_used 
      FROM email_analytics ea2 
      WHERE ea2.organization_id = p_organization_id 
        AND ea2.date_period >= CURRENT_DATE - (p_days || ' days')::INTERVAL 
      GROUP BY template_used 
      ORDER BY SUM(emails_sent) DESC 
      LIMIT 1
    ) as most_used_template
  FROM email_analytics ea
  WHERE ea.organization_id = p_organization_id 
    AND ea.date_period >= CURRENT_DATE - (p_days || ' days')::INTERVAL;
END;
$$ LANGUAGE plpgsql;
