-- SIGNATURE WORKFLOW DATABASE SCHEMA
-- Enhanced schema for signature workflow engine and DocuSign integration

-- =============================================================================
-- SIGNATURE WORKFLOWS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS signature_workflows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Workflow Configuration
  workflow_type VARCHAR(50) NOT NULL CHECK (workflow_type IN ('quote_approval', 'contract_signing', 'change_order', 'completion_certificate')),
  workflow_name VARCHAR(255) NOT NULL,
  workflow_description TEXT,
  
  -- Workflow Settings
  auto_send BOOLEAN DEFAULT true,
  require_all_signatures BOOLEAN DEFAULT true,
  signing_order_enforced BOOLEAN DEFAULT false,
  expiration_days INTEGER DEFAULT 30,
  reminder_frequency_days INTEGER DEFAULT 3,
  
  -- Document Configuration
  document_template_id UUID,
  signature_fields_template JSONB DEFAULT '[]',
  
  -- Notification Configuration
  email_template_id UUID,
  notification_settings JSONB DEFAULT '{}',
  
  -- Status and Tracking
  is_active BOOLEAN DEFAULT true,
  usage_count INTEGER DEFAULT 0,
  last_used TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- WORKFLOW EXECUTIONS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS workflow_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_id UUID REFERENCES signature_workflows(id),
  signature_request_id UUID,
  organization_id UUID NOT NULL,
  
  -- Execution Context
  workflow_type VARCHAR(50) NOT NULL,
  context_type VARCHAR(50) NOT NULL CHECK (context_type IN ('quote', 'project', 'contract', 'change_order')),
  context_id VARCHAR(255) NOT NULL,
  context_data JSONB DEFAULT '{}',
  
  -- Execution Status
  execution_status VARCHAR(30) NOT NULL DEFAULT 'pending' CHECK (execution_status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  
  -- Progress Tracking
  total_steps INTEGER DEFAULT 0,
  completed_steps INTEGER DEFAULT 0,
  current_step VARCHAR(100),
  
  -- Results
  signed_document_urls TEXT[] DEFAULT '{}',
  completion_certificate_url TEXT,
  
  -- Performance Metrics
  processing_time_ms INTEGER,
  provider_response_time_ms INTEGER,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- SIGNATURE PROVIDERS ENHANCED
-- =============================================================================

-- Add DocuSign specific configuration columns if not exists
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'signature_providers' AND column_name = 'provider_version') THEN
    ALTER TABLE signature_providers ADD COLUMN provider_version VARCHAR(20) DEFAULT '1.0';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'signature_providers' AND column_name = 'webhook_url') THEN
    ALTER TABLE signature_providers ADD COLUMN webhook_url TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'signature_providers' AND column_name = 'webhook_secret') THEN
    ALTER TABLE signature_providers ADD COLUMN webhook_secret VARCHAR(255);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'signature_providers' AND column_name = 'rate_limit_per_hour') THEN
    ALTER TABLE signature_providers ADD COLUMN rate_limit_per_hour INTEGER DEFAULT 1000;
  END IF;
END $$;

-- =============================================================================
-- SIGNATURE WEBHOOK EVENTS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS signature_webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  signature_request_id UUID,
  workflow_execution_id UUID REFERENCES workflow_executions(id),
  
  -- Webhook Details
  provider_name VARCHAR(50) NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  event_data JSONB NOT NULL,
  
  -- Processing Status
  processing_status VARCHAR(30) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  processed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  
  -- Request Details
  webhook_signature VARCHAR(255),
  source_ip INET,
  user_agent TEXT,
  
  -- Timestamps
  received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- SIGNATURE ANALYTICS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS signature_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Analytics Period
  date_period DATE NOT NULL,
  workflow_type VARCHAR(50),
  
  -- Workflow Metrics
  total_workflows_started INTEGER DEFAULT 0,
  total_workflows_completed INTEGER DEFAULT 0,
  total_workflows_failed INTEGER DEFAULT 0,
  completion_rate DECIMAL(5,2) DEFAULT 0.00,
  
  -- Performance Metrics
  avg_completion_time_hours DECIMAL(8,2) DEFAULT 0.00,
  avg_processing_time_ms INTEGER DEFAULT 0,
  avg_provider_response_time_ms INTEGER DEFAULT 0,
  
  -- Document Metrics
  total_documents_signed INTEGER DEFAULT 0,
  total_signers INTEGER DEFAULT 0,
  avg_signers_per_workflow DECIMAL(4,2) DEFAULT 0.00,
  
  -- Provider Metrics
  docusign_requests INTEGER DEFAULT 0,
  docusign_success_rate DECIMAL(5,2) DEFAULT 0.00,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(organization_id, date_period, workflow_type)
);

-- =============================================================================
-- SIGNATURE TEMPLATES TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS signature_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  
  -- Template Information
  template_name VARCHAR(255) NOT NULL,
  template_type VARCHAR(50) NOT NULL,
  description TEXT,
  
  -- Template Content
  document_template JSONB NOT NULL, -- PDF template or HTML template
  signature_fields JSONB DEFAULT '[]',
  form_fields JSONB DEFAULT '[]',
  
  -- Template Settings
  default_expiration_days INTEGER DEFAULT 30,
  require_all_signatures BOOLEAN DEFAULT true,
  signing_order_enforced BOOLEAN DEFAULT false,
  
  -- Usage Information
  usage_count INTEGER DEFAULT 0,
  last_used TIMESTAMP WITH TIME ZONE,
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Signature workflows indexes
CREATE INDEX IF NOT EXISTS idx_signature_workflows_org ON signature_workflows(organization_id);
CREATE INDEX IF NOT EXISTS idx_signature_workflows_type ON signature_workflows(workflow_type);
CREATE INDEX IF NOT EXISTS idx_signature_workflows_active ON signature_workflows(is_active);

-- Workflow executions indexes
CREATE INDEX IF NOT EXISTS idx_workflow_executions_org ON workflow_executions(organization_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_workflow ON workflow_executions(workflow_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(execution_status);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_context ON workflow_executions(context_type, context_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_started ON workflow_executions(started_at);

-- Webhook events indexes
CREATE INDEX IF NOT EXISTS idx_webhook_events_org ON signature_webhook_events(organization_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_request ON signature_webhook_events(signature_request_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_execution ON signature_webhook_events(workflow_execution_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_provider ON signature_webhook_events(provider_name);
CREATE INDEX IF NOT EXISTS idx_webhook_events_status ON signature_webhook_events(processing_status);
CREATE INDEX IF NOT EXISTS idx_webhook_events_received ON signature_webhook_events(received_at);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_signature_analytics_org ON signature_analytics(organization_id);
CREATE INDEX IF NOT EXISTS idx_signature_analytics_date ON signature_analytics(date_period);
CREATE INDEX IF NOT EXISTS idx_signature_analytics_type ON signature_analytics(workflow_type);

-- Templates indexes
CREATE INDEX IF NOT EXISTS idx_signature_templates_org ON signature_templates(organization_id);
CREATE INDEX IF NOT EXISTS idx_signature_templates_type ON signature_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_signature_templates_active ON signature_templates(is_active);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE signature_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE signature_webhook_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE signature_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE signature_templates ENABLE ROW LEVEL SECURITY;

-- Signature workflows - Organization-based access
CREATE POLICY "Organization access for signature workflows" ON signature_workflows 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Workflow executions - Organization-based access
CREATE POLICY "Organization access for workflow executions" ON workflow_executions 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Webhook events - Organization-based access
CREATE POLICY "Organization access for webhook events" ON signature_webhook_events 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Analytics - Organization-based access
CREATE POLICY "Organization access for signature analytics" ON signature_analytics 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Templates - Organization-based access
CREATE POLICY "Organization access for signature templates" ON signature_templates 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- =============================================================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_signature_workflows_updated_at BEFORE UPDATE ON signature_workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_executions_updated_at BEFORE UPDATE ON workflow_executions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_signature_analytics_updated_at BEFORE UPDATE ON signature_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_signature_templates_updated_at BEFORE UPDATE ON signature_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- FUNCTIONS FOR WORKFLOW MANAGEMENT
-- =============================================================================

-- Function to update workflow analytics
CREATE OR REPLACE FUNCTION update_signature_analytics(
  p_organization_id UUID,
  p_workflow_type VARCHAR(50),
  p_event_type VARCHAR(30)
)
RETURNS VOID AS $$
DECLARE
  today_date DATE := CURRENT_DATE;
BEGIN
  -- Insert or update analytics for today
  INSERT INTO signature_analytics (
    organization_id,
    date_period,
    workflow_type,
    total_workflows_started,
    total_workflows_completed,
    total_workflows_failed
  ) VALUES (
    p_organization_id,
    today_date,
    p_workflow_type,
    CASE WHEN p_event_type = 'started' THEN 1 ELSE 0 END,
    CASE WHEN p_event_type = 'completed' THEN 1 ELSE 0 END,
    CASE WHEN p_event_type = 'failed' THEN 1 ELSE 0 END
  )
  ON CONFLICT (organization_id, date_period, workflow_type)
  DO UPDATE SET
    total_workflows_started = signature_analytics.total_workflows_started + 
      CASE WHEN p_event_type = 'started' THEN 1 ELSE 0 END,
    total_workflows_completed = signature_analytics.total_workflows_completed + 
      CASE WHEN p_event_type = 'completed' THEN 1 ELSE 0 END,
    total_workflows_failed = signature_analytics.total_workflows_failed + 
      CASE WHEN p_event_type = 'failed' THEN 1 ELSE 0 END,
    completion_rate = CASE 
      WHEN (signature_analytics.total_workflows_started + 
            CASE WHEN p_event_type = 'started' THEN 1 ELSE 0 END) > 0 
      THEN 
        ((signature_analytics.total_workflows_completed + 
          CASE WHEN p_event_type = 'completed' THEN 1 ELSE 0 END)::DECIMAL / 
         (signature_analytics.total_workflows_started + 
          CASE WHEN p_event_type = 'started' THEN 1 ELSE 0 END)) * 100
      ELSE 0 
    END,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to get workflow statistics
CREATE OR REPLACE FUNCTION get_workflow_statistics(p_organization_id UUID, p_days INTEGER DEFAULT 30)
RETURNS TABLE (
  total_workflows BIGINT,
  completed_workflows BIGINT,
  failed_workflows BIGINT,
  completion_rate NUMERIC,
  avg_completion_time_hours NUMERIC,
  most_used_workflow_type VARCHAR(50)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    SUM(sa.total_workflows_started)::BIGINT as total_workflows,
    SUM(sa.total_workflows_completed)::BIGINT as completed_workflows,
    SUM(sa.total_workflows_failed)::BIGINT as failed_workflows,
    CASE 
      WHEN SUM(sa.total_workflows_started) > 0 THEN 
        ROUND((SUM(sa.total_workflows_completed)::NUMERIC / SUM(sa.total_workflows_started)) * 100, 2)
      ELSE 0 
    END as completion_rate,
    ROUND(AVG(sa.avg_completion_time_hours), 2) as avg_completion_time_hours,
    (
      SELECT workflow_type 
      FROM signature_analytics sa2 
      WHERE sa2.organization_id = p_organization_id 
        AND sa2.date_period >= CURRENT_DATE - INTERVAL '%s days' 
      GROUP BY workflow_type 
      ORDER BY SUM(total_workflows_started) DESC 
      LIMIT 1
    ) as most_used_workflow_type
  FROM signature_analytics sa
  WHERE sa.organization_id = p_organization_id 
    AND sa.date_period >= CURRENT_DATE - (p_days || ' days')::INTERVAL;
END;
$$ LANGUAGE plpgsql;
