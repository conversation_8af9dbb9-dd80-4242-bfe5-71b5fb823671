-- SUPPLIER CACHE DATABASE SCHEMA
-- High-performance caching for supplier pricing and product data

-- =============================================================================
-- SUPPLIER CACHE TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS supplier_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  supplier_id VARCHAR(50) NOT NULL,
  
  -- Cache Entry Information
  key VARCHAR(255) NOT NULL,
  cache_type VARCHAR(30) NOT NULL CHECK (cache_type IN ('product_search', 'pricing', 'availability', 'product_details')),
  
  -- Cached Data
  data JSONB NOT NULL,
  
  -- <PERSON><PERSON> Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  hit_count INTEGER DEFAULT 0,
  
  -- Cache Configuration
  ttl_hours INTEGER DEFAULT 24,
  cache_size_bytes INTEGER,
  
  -- Performance Tracking
  access_pattern JSONB DEFAULT '{}', -- Track access patterns for optimization
  
  -- Constraints
  UNIQUE(organization_id, key)
);

-- =============================================================================
-- SUPPLIER CACHE ANALYTICS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS supplier_cache_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  supplier_id VARCHAR(50) NOT NULL,
  
  -- Analytics Period
  date_period DATE NOT NULL,
  hour_period INTEGER, -- 0-23 for hourly analytics
  
  -- Cache Performance Metrics
  total_requests INTEGER DEFAULT 0,
  cache_hits INTEGER DEFAULT 0,
  cache_misses INTEGER DEFAULT 0,
  hit_rate DECIMAL(5,2) DEFAULT 0.00,
  
  -- Response Time Metrics
  avg_response_time_ms INTEGER DEFAULT 0,
  min_response_time_ms INTEGER DEFAULT 0,
  max_response_time_ms INTEGER DEFAULT 0,
  
  -- Cache Size Metrics
  total_cache_entries INTEGER DEFAULT 0,
  cache_size_mb DECIMAL(8,2) DEFAULT 0.00,
  expired_entries_cleaned INTEGER DEFAULT 0,
  
  -- Popular Cache Types
  product_search_requests INTEGER DEFAULT 0,
  pricing_requests INTEGER DEFAULT 0,
  availability_requests INTEGER DEFAULT 0,
  product_details_requests INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(organization_id, supplier_id, date_period, hour_period)
);

-- =============================================================================
-- SUPPLIER CACHE SETTINGS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS supplier_cache_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  supplier_id VARCHAR(50),
  
  -- Cache Configuration
  default_ttl_hours INTEGER DEFAULT 24,
  max_cache_size_mb INTEGER DEFAULT 100,
  auto_cleanup_enabled BOOLEAN DEFAULT true,
  cleanup_interval_hours INTEGER DEFAULT 1,
  
  -- Cache Type Specific Settings
  product_search_ttl_hours INTEGER DEFAULT 12,
  pricing_ttl_hours INTEGER DEFAULT 6,
  availability_ttl_hours INTEGER DEFAULT 1,
  product_details_ttl_hours INTEGER DEFAULT 24,
  
  -- Performance Settings
  memory_cache_limit INTEGER DEFAULT 1000,
  preload_popular_items BOOLEAN DEFAULT true,
  compression_enabled BOOLEAN DEFAULT false,
  
  -- Monitoring Settings
  analytics_enabled BOOLEAN DEFAULT true,
  alert_on_high_miss_rate BOOLEAN DEFAULT true,
  miss_rate_threshold DECIMAL(5,2) DEFAULT 50.00,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(organization_id, supplier_id)
);

-- =============================================================================
-- CACHE INVALIDATION LOG TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS cache_invalidation_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL,
  supplier_id VARCHAR(50) NOT NULL,
  
  -- Invalidation Details
  invalidation_type VARCHAR(30) NOT NULL CHECK (invalidation_type IN ('manual', 'automatic', 'expired', 'error', 'supplier_update')),
  cache_keys_affected TEXT[], -- Array of cache keys that were invalidated
  reason TEXT,
  
  -- Invalidation Scope
  cache_type VARCHAR(30), -- If specific to a cache type
  product_ids TEXT[], -- If specific to certain products
  
  -- Impact Metrics
  entries_invalidated INTEGER DEFAULT 0,
  cache_size_freed_mb DECIMAL(8,2) DEFAULT 0.00,
  
  -- Timestamps
  invalidated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  invalidated_by UUID, -- User ID if manual invalidation
  
  -- Performance Impact
  next_request_response_time_ms INTEGER, -- Track impact on next request
  cache_rebuild_time_ms INTEGER -- Time to rebuild cache if applicable
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Primary cache table indexes
CREATE INDEX IF NOT EXISTS idx_supplier_cache_org_key ON supplier_cache(organization_id, key);
CREATE INDEX IF NOT EXISTS idx_supplier_cache_supplier ON supplier_cache(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_cache_type ON supplier_cache(cache_type);
CREATE INDEX IF NOT EXISTS idx_supplier_cache_expires ON supplier_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_supplier_cache_accessed ON supplier_cache(last_accessed);
CREATE INDEX IF NOT EXISTS idx_supplier_cache_hits ON supplier_cache(hit_count DESC);

-- Analytics table indexes
CREATE INDEX IF NOT EXISTS idx_cache_analytics_org_supplier ON supplier_cache_analytics(organization_id, supplier_id);
CREATE INDEX IF NOT EXISTS idx_cache_analytics_date ON supplier_cache_analytics(date_period);
CREATE INDEX IF NOT EXISTS idx_cache_analytics_hour ON supplier_cache_analytics(date_period, hour_period);

-- Settings table indexes
CREATE INDEX IF NOT EXISTS idx_cache_settings_org ON supplier_cache_settings(organization_id);
CREATE INDEX IF NOT EXISTS idx_cache_settings_supplier ON supplier_cache_settings(supplier_id);

-- Invalidation log indexes
CREATE INDEX IF NOT EXISTS idx_invalidation_log_org ON cache_invalidation_log(organization_id);
CREATE INDEX IF NOT EXISTS idx_invalidation_log_supplier ON cache_invalidation_log(supplier_id);
CREATE INDEX IF NOT EXISTS idx_invalidation_log_date ON cache_invalidation_log(invalidated_at);
CREATE INDEX IF NOT EXISTS idx_invalidation_log_type ON cache_invalidation_log(invalidation_type);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE supplier_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplier_cache_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplier_cache_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE cache_invalidation_log ENABLE ROW LEVEL SECURITY;

-- Supplier cache - Organization-based access
CREATE POLICY "Organization access for supplier cache" ON supplier_cache 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Cache analytics - Organization-based access
CREATE POLICY "Organization access for cache analytics" ON supplier_cache_analytics 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Cache settings - Organization-based access
CREATE POLICY "Organization access for cache settings" ON supplier_cache_settings 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- Invalidation log - Organization-based access
CREATE POLICY "Organization access for invalidation log" ON cache_invalidation_log 
  FOR ALL USING (organization_id = (SELECT organization_id FROM user_profiles WHERE user_id = auth.uid()));

-- =============================================================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_supplier_cache_analytics_updated_at BEFORE UPDATE ON supplier_cache_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_supplier_cache_settings_updated_at BEFORE UPDATE ON supplier_cache_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- FUNCTIONS FOR CACHE MANAGEMENT
-- =============================================================================

-- Function to increment hit count
CREATE OR REPLACE FUNCTION increment_hit_count()
RETURNS INTEGER AS $$
BEGIN
  RETURN COALESCE(OLD.hit_count, 0) + 1;
END;
$$ LANGUAGE plpgsql;

-- Function to clean expired cache entries
CREATE OR REPLACE FUNCTION clean_expired_cache_entries(p_organization_id UUID)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM supplier_cache 
  WHERE organization_id = p_organization_id 
    AND expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log the cleanup
  INSERT INTO cache_invalidation_log (
    organization_id,
    supplier_id,
    invalidation_type,
    reason,
    entries_invalidated
  ) VALUES (
    p_organization_id,
    'all',
    'expired',
    'Automatic cleanup of expired cache entries',
    deleted_count
  );
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get cache statistics
CREATE OR REPLACE FUNCTION get_cache_statistics(p_organization_id UUID, p_supplier_id VARCHAR DEFAULT NULL)
RETURNS TABLE (
  total_entries BIGINT,
  total_hits BIGINT,
  total_size_mb NUMERIC,
  hit_rate NUMERIC,
  expired_entries BIGINT,
  most_popular_type VARCHAR,
  avg_ttl_hours NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::BIGINT as total_entries,
    SUM(sc.hit_count)::BIGINT as total_hits,
    ROUND(SUM(COALESCE(sc.cache_size_bytes, 0))::NUMERIC / (1024 * 1024), 2) as total_size_mb,
    CASE 
      WHEN COUNT(*) > 0 THEN 
        ROUND((SUM(sc.hit_count)::NUMERIC / COUNT(*)) * 100, 2)
      ELSE 0 
    END as hit_rate,
    COUNT(CASE WHEN sc.expires_at < NOW() THEN 1 END)::BIGINT as expired_entries,
    (
      SELECT cache_type 
      FROM supplier_cache sc2 
      WHERE sc2.organization_id = p_organization_id 
        AND (p_supplier_id IS NULL OR sc2.supplier_id = p_supplier_id)
      GROUP BY cache_type 
      ORDER BY COUNT(*) DESC 
      LIMIT 1
    ) as most_popular_type,
    ROUND(AVG(sc.ttl_hours)::NUMERIC, 1) as avg_ttl_hours
  FROM supplier_cache sc
  WHERE sc.organization_id = p_organization_id 
    AND (p_supplier_id IS NULL OR sc.supplier_id = p_supplier_id);
END;
$$ LANGUAGE plpgsql;

-- Function to invalidate cache by pattern
CREATE OR REPLACE FUNCTION invalidate_cache_by_pattern(
  p_organization_id UUID,
  p_supplier_id VARCHAR,
  p_pattern VARCHAR,
  p_reason TEXT DEFAULT 'Manual invalidation'
)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
  affected_keys TEXT[];
BEGIN
  -- Get affected keys before deletion
  SELECT ARRAY_AGG(key) INTO affected_keys
  FROM supplier_cache 
  WHERE organization_id = p_organization_id 
    AND supplier_id = p_supplier_id
    AND key LIKE p_pattern;
  
  -- Delete matching entries
  DELETE FROM supplier_cache 
  WHERE organization_id = p_organization_id 
    AND supplier_id = p_supplier_id
    AND key LIKE p_pattern;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log the invalidation
  INSERT INTO cache_invalidation_log (
    organization_id,
    supplier_id,
    invalidation_type,
    reason,
    cache_keys_affected,
    entries_invalidated
  ) VALUES (
    p_organization_id,
    p_supplier_id,
    'manual',
    p_reason,
    affected_keys,
    deleted_count
  );
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- DEFAULT CACHE SETTINGS
-- =============================================================================

-- Insert default cache settings for new organizations
CREATE OR REPLACE FUNCTION create_default_cache_settings(p_organization_id UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO supplier_cache_settings (
    organization_id,
    supplier_id,
    default_ttl_hours,
    max_cache_size_mb,
    auto_cleanup_enabled,
    cleanup_interval_hours,
    product_search_ttl_hours,
    pricing_ttl_hours,
    availability_ttl_hours,
    product_details_ttl_hours,
    memory_cache_limit,
    preload_popular_items,
    analytics_enabled,
    alert_on_high_miss_rate,
    miss_rate_threshold
  ) VALUES (
    p_organization_id,
    NULL, -- Global settings for organization
    24,   -- 24 hour default TTL
    100,  -- 100MB max cache size
    true, -- Auto cleanup enabled
    1,    -- Cleanup every hour
    12,   -- Product search: 12 hours
    6,    -- Pricing: 6 hours
    1,    -- Availability: 1 hour
    24,   -- Product details: 24 hours
    1000, -- 1000 entries in memory
    true, -- Preload popular items
    true, -- Analytics enabled
    true, -- Alert on high miss rate
    50.00 -- 50% miss rate threshold
  ) ON CONFLICT (organization_id, supplier_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql;
