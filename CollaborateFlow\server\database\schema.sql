-- CollaborateFlow Database Schema for Supabase

-- Organizations Table
CREATE TABLE organizations (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  created_by_id BIGINT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add default organization (Coelec - ID: 1)
INSERT INTO organizations (id, name, description, created_at)
VALUES (1, 'Coelec', 'Default organization for CollaborateFlow', NOW())
ON CONFLICT (id) DO NOTHING;

-- Users Table
-- Note: This will work alongside Supabase Auth
CREATE TABLE users (
  id BIGINT PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  email TEXT NOT NULL UNIQUE,
  full_name TEXT NOT NULL,
  organization_id BIGINT REFERENCES organizations(id),
  role TEXT NOT NULL CHECK (role IN ('super_admin', 'admin', 'user')) DEFAULT 'user',
  supabase_id UUID UNIQUE,
  has_completed_setup BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);


-- Teams Table
CREATE TABLE teams (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  created_by_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  organization_id BIGINT NOT NULL REFERENCES organizations(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add default team (Default Team - ID: 1) in the default organization
INSERT INTO teams (id, name, description, created_by_id, organization_id, created_at)
VALUES (1, 'Default Team', 'This is the default team for CollaborateFlow', 1, 1, NOW())
ON CONFLICT (id) DO NOTHING;

-- Add the super admin as a member of the default team
INSERT INTO team_members (team_id, user_id, role, created_at)
VALUES (1, 1, 'admin', NOW())
ON CONFLICT (team_id, user_id) DO NOTHING;

-- Team Members Table
CREATE TABLE team_members (
  id BIGSERIAL PRIMARY KEY,
  team_id BIGINT NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'member', 'viewer')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_id, user_id)
);

-- Projects Table
CREATE TABLE projects (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  team_id BIGINT NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  created_by_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('planning', 'in_progress', 'completed', 'on_hold')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Columns Table (for Kanban board)
CREATE TABLE columns (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  "order" INTEGER NOT NULL,
  project_id BIGINT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  UNIQUE(project_id, "order")
);

-- Tasks Table
CREATE TABLE tasks (
  id BIGSERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  column_id BIGINT NOT NULL REFERENCES columns(id) ON DELETE CASCADE,
  project_id BIGINT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  assignee_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
  "order" INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(column_id, "order")
);

-- Floor Plans Table
CREATE TABLE floor_plans (
  id BIGSERIAL PRIMARY KEY,
  project_id BIGINT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  version INTEGER NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('uploaded', 'processing', 'processed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, version)
);

-- Symbols Table (detected in floor plans)
CREATE TABLE symbols (
  id BIGSERIAL PRIMARY KEY,
  floor_plan_id BIGINT NOT NULL REFERENCES floor_plans(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  x NUMERIC NOT NULL,
  y NUMERIC NOT NULL,
  width NUMERIC NOT NULL,
  height NUMERIC NOT NULL,
  properties JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Quotes Table
CREATE TABLE quotes (
  id BIGSERIAL PRIMARY KEY,
  project_id BIGINT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('draft', 'pending', 'approved', 'rejected')),
  total_material_cost NUMERIC NOT NULL,
  total_labor_cost NUMERIC NOT NULL,
  markup_percentage NUMERIC NOT NULL,
  total_cost NUMERIC NOT NULL,
  created_by_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Materials Table
CREATE TABLE materials (
  id BIGSERIAL PRIMARY KEY,
  quote_id BIGINT NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  quantity NUMERIC NOT NULL,
  unit_price NUMERIC NOT NULL,
  total_price NUMERIC NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Labor Table
CREATE TABLE labor (
  id BIGSERIAL PRIMARY KEY,
  quote_id BIGINT NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  hours NUMERIC NOT NULL,
  rate NUMERIC NOT NULL,
  total_price NUMERIC NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stored Procedures for Column and Task Reordering

-- Reorder columns after a column is deleted
CREATE OR REPLACE FUNCTION reorder_columns_after_delete(p_project_id BIGINT, p_deleted_order INTEGER)
RETURNS VOID AS $$
BEGIN
  -- Shift all columns with order > deleted_order down by 1
  UPDATE columns
  SET "order" = "order" - 1
  WHERE project_id = p_project_id AND "order" > p_deleted_order;
END;
$$ LANGUAGE plpgsql;

-- Reorder tasks after a task is deleted
CREATE OR REPLACE FUNCTION reorder_tasks_after_delete(p_column_id BIGINT, p_deleted_order INTEGER)
RETURNS VOID AS $$
BEGIN
  -- Shift all tasks with order > deleted_order down by 1
  UPDATE tasks
  SET "order" = "order" - 1
  WHERE column_id = p_column_id AND "order" > p_deleted_order;
END;
$$ LANGUAGE plpgsql;

-- Move a task within the same column
CREATE OR REPLACE FUNCTION move_task_within_column(p_task_id BIGINT, p_column_id BIGINT, p_old_order INTEGER, p_new_order INTEGER)
RETURNS VOID AS $$
BEGIN
  IF p_old_order = p_new_order THEN
    -- Nothing to do
    RETURN;
  ELSIF p_old_order < p_new_order THEN
    -- Moving down: Shift tasks between old and new position up by 1
    UPDATE tasks
    SET "order" = "order" - 1
    WHERE column_id = p_column_id AND "order" > p_old_order AND "order" <= p_new_order;
  ELSE
    -- Moving up: Shift tasks between new and old position down by 1
    UPDATE tasks
    SET "order" = "order" + 1
    WHERE column_id = p_column_id AND "order" >= p_new_order AND "order" < p_old_order;
  END IF;
  
  -- Update the task itself
  UPDATE tasks
  SET "order" = p_new_order
  WHERE id = p_task_id;
END;
$$ LANGUAGE plpgsql;

-- Move a task between columns
CREATE OR REPLACE FUNCTION move_task_between_columns(p_task_id BIGINT, p_old_column_id BIGINT, p_new_column_id BIGINT, p_new_order INTEGER)
RETURNS VOID AS $$
DECLARE
  v_old_order INTEGER;
BEGIN
  -- Get the current order of the task
  SELECT "order" INTO v_old_order FROM tasks WHERE id = p_task_id;
  
  -- Shift tasks in the old column
  UPDATE tasks
  SET "order" = "order" - 1
  WHERE column_id = p_old_column_id AND "order" > v_old_order;
  
  -- Shift tasks in the new column
  UPDATE tasks
  SET "order" = "order" + 1
  WHERE column_id = p_new_column_id AND "order" >= p_new_order;
  
  -- Update the task itself
  UPDATE tasks
  SET column_id = p_new_column_id, "order" = p_new_order
  WHERE id = p_task_id;
END;
$$ LANGUAGE plpgsql;

-- Reorder columns
CREATE OR REPLACE FUNCTION reorder_columns(p_column_orders JSON, p_project_id BIGINT)
RETURNS VOID AS $$
DECLARE
  column_item JSON;
BEGIN
  -- First, set all columns to a temporary negative order to avoid unique constraint conflicts
  UPDATE columns
  SET "order" = -"order"
  WHERE project_id = p_project_id;
  
  -- Update each column with its new order
  FOR column_item IN SELECT * FROM json_array_elements(p_column_orders) LOOP
    UPDATE columns
    SET "order" = (column_item->>'order')::INTEGER
    WHERE id = (column_item->>'id')::BIGINT;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Row-Level Security Policies

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE columns ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE floor_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE symbols ENABLE ROW LEVEL SECURITY;
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE labor ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Users can see their own record
CREATE POLICY user_see_own ON users
  FOR SELECT USING (auth.uid() = id);

-- Team policies
CREATE POLICY team_member_select ON teams
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM team_members 
      WHERE team_members.team_id = teams.id AND team_members.user_id = auth.uid()
    )
  );

CREATE POLICY team_admin_insert ON teams
  FOR INSERT WITH CHECK (created_by_id = auth.uid());

CREATE POLICY team_admin_update ON teams
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM team_members 
      WHERE team_members.team_id = teams.id AND team_members.user_id = auth.uid() AND team_members.role = 'admin'
    )
  );

CREATE POLICY team_admin_delete ON teams
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM team_members 
      WHERE team_members.team_id = teams.id AND team_members.user_id = auth.uid() AND team_members.role = 'admin'
    )
  );

-- Similar policies for other tables...

-- Indexes for performance
CREATE INDEX idx_team_members_team_id ON team_members(team_id);
CREATE INDEX idx_team_members_user_id ON team_members(user_id);
CREATE INDEX idx_projects_team_id ON projects(team_id);
CREATE INDEX idx_columns_project_id ON columns(project_id);
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_tasks_column_id ON tasks(column_id);
CREATE INDEX idx_floor_plans_project_id ON floor_plans(project_id);
CREATE INDEX idx_symbols_floor_plan_id ON symbols(floor_plan_id);
CREATE INDEX idx_quotes_project_id ON quotes(project_id);
CREATE INDEX idx_materials_quote_id ON materials(quote_id);
CREATE INDEX idx_labor_quote_id ON labor(quote_id);
