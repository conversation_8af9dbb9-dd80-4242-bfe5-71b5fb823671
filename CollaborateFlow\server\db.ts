import { createClient } from '@supabase/supabase-js';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from "@shared/schema";
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get Supabase credentials from environment or runtime config
let supabaseUrl: string;
let supabaseKey: string;

// Check if we're in a browser environment
if (typeof window !== 'undefined' && window.RUNTIME_CONFIG) {
  supabaseUrl = window.RUNTIME_CONFIG.SUPABASE_URL;
  supabaseKey = window.RUNTIME_CONFIG.SUPABASE_KEY;
} else {
  // In Node.js environment
  supabaseUrl = process.env.SUPABASE_URL || '';
  supabaseKey = process.env.SUPABASE_KEY || '';
}

// Check for required Supabase credentials
if (!supabaseUrl) {
  console.error('SUPABASE_URL is missing. Using default configuration for development.');
  supabaseUrl = 'https://nzhvukfaolebykcquedd.supabase.co';
}

if (!supabaseKey) {
  console.error('SUPABASE_KEY is missing. Using default configuration for development.');
  supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';
}

// Create Supabase client for directly accessing the Supabase APIs
export const supabase = createClient(
  supabaseUrl,
  supabaseKey,
  {
    auth: {
      persistSession: false
    }
  }
);

// Extract the project reference from the Supabase URL
// Format: https://[project-ref].supabase.co
const urlParts = new URL(supabaseUrl);
const projectRef = urlParts.hostname.split('.')[0];

// Use IPv4 address instead of hostname
// We can get the IPv4 address using DNS lookup, but for now we'll use the hostname
// and let Docker's DNS resolver handle it
const dbHost = `db.${projectRef}.supabase.co`;

console.log(`Attempting to connect to Supabase PostgreSQL at ${dbHost}`);

// Create PostgreSQL pool for direct connection to Supabase PostgreSQL
export const pool = new Pool({
  host: dbHost,
  port: 5432,
  database: 'postgres',
  user: 'postgres',
  password: supabaseKey,
  ssl: { rejectUnauthorized: false },
  // Connection settings for stability
  max: 5, // Reduce connection pool size
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000, // Longer timeout
  // Force IPv4 connections
  // @ts-ignore - Adding custom option for node-postgres
  family: 4 // This forces IPv4 
});

// Add connection testing/logging
pool.on('error', (err) => {
  console.error('Unexpected PostgreSQL connection error:', err);
  // Log more details
  if (err.message && err.message.includes('ENETUNREACH')) {
    console.error('Network unreachable error. This usually indicates IPv6 connectivity issues.');
    console.error('Try connecting via IPv4 only or using the Supabase REST API instead.');
  }
});

pool.on('connect', () => {
  console.log('Successfully connected to Supabase PostgreSQL');
});

// Initialize Drizzle with our modified pool
export const db = drizzle(pool, { schema });