/**
 * Symbol Detection MCP Server
 *
 * This MCP server handles AI-powered electrical symbol detection from floor plans
 * using OpenRouter to access multiple AI models (<PERSON>, GPT-4o, Gemini Pro Vision)
 *
 * USE_REAL_AI: true - This service is configured for real OpenRouter API integration
 */

import { z } from 'zod';
import { getAICacheService } from '../services/aiCacheService';
import { createHash } from 'crypto';

// Types for symbol detection
export interface DetectedSymbol {
  id: string;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
  confidence: number;
  properties: Record<string, any>;
}

export interface SymbolDetectionResult {
  success: boolean;
  symbols: DetectedSymbol[];
  model_used: string;
  processing_time_ms: number;
  total_tokens?: number;
  cost_estimate?: number;
  cached?: boolean;
  error?: string;
}

// Configuration for AI models via OpenRouter
export interface ModelConfig {
  name: string;
  openrouter_model: string;
  max_tokens: number;
  temperature: number;
  cost_per_1k_tokens: number;
  use_case: string[];
}

// Available models configuration
export const AI_MODELS: Record<string, ModelConfig> = {
  'claude-3.5-sonnet': {
    name: 'Claude 3.5 Sonnet',
    openrouter_model: 'anthropic/claude-3.5-sonnet',
    max_tokens: 4096,
    temperature: 0.1,
    cost_per_1k_tokens: 0.003,
    use_case: ['complex', 'high-quality', 'detailed']
  },
  'gpt-4o': {
    name: 'GPT-4o',
    openrouter_model: 'openai/gpt-4o',
    max_tokens: 4096,
    temperature: 0.1,
    cost_per_1k_tokens: 0.005,
    use_case: ['mixed', 'versatile', 'complex']
  },
  'gemini-pro-vision': {
    name: 'Gemini Pro Vision',
    openrouter_model: 'google/gemini-pro-vision',
    max_tokens: 2048,
    temperature: 0.1,
    cost_per_1k_tokens: 0.00025,
    use_case: ['standard', 'fallback', 'cost-effective']
  },
  'claude-3-haiku': {
    name: 'Claude 3 Haiku',
    openrouter_model: 'anthropic/claude-3-haiku',
    max_tokens: 2048,
    temperature: 0.1,
    cost_per_1k_tokens: 0.00025,
    use_case: ['simple', 'preprocessing', 'fast']
  }
};

// Prompt templates for symbol detection
export const SYMBOL_DETECTION_PROMPTS = {
  v1: {
    system: `You are an expert electrical engineer specializing in analyzing floor plans and identifying electrical symbols. Your task is to detect and classify electrical symbols in floor plan images with high accuracy.

ELECTRICAL SYMBOLS TO DETECT:
- Outlets (standard, GFCI, USB, 240V)
- Switches (single-pole, 3-way, dimmer, smart)
- Lights (recessed, pendant, chandelier, emergency)
- Panels (main panel, sub-panel, distribution)
- Data/Communication (ethernet, phone, cable, fiber)
- HVAC electrical (thermostats, controls)
- Safety (smoke detectors, carbon monoxide)
- Specialty (appliance connections, outdoor outlets)

RESPONSE FORMAT:
Return a JSON array of detected symbols with this exact structure:
[
  {
    "type": "outlet|switch|light|panel|data|hvac|safety|specialty",
    "subtype": "specific type (e.g., gfci, dimmer, recessed)",
    "x": number (center x coordinate),
    "y": number (center y coordinate),
    "width": number (bounding box width),
    "height": number (bounding box height),
    "confidence": number (0.0-1.0),
    "properties": {
      "voltage": "120V|240V|low_voltage",
      "amperage": "15A|20A|30A|etc",
      "notes": "any additional details"
    }
  }
]

IMPORTANT:
- Only return valid JSON, no additional text
- Be precise with coordinates and dimensions
- Confidence should reflect actual detection certainty
- Include all visible electrical symbols
- Use standard electrical symbol classifications`,

    user: `Analyze this floor plan image and detect all electrical symbols. Return the results as a JSON array following the specified format.`
  }
};

/**
 * Symbol Detection MCP Server Class
 */
export class SymbolDetectionMCP {
  private openrouterApiKey: string;
  private baseUrl = 'https://openrouter.ai/api/v1';
  private defaultModel = 'claude-3.5-sonnet';
  private organizationId: string;

  constructor(apiKey: string, organizationId: string) {
    this.openrouterApiKey = apiKey;
    this.organizationId = organizationId;
  }

  /**
   * Generate image hash for caching
   */
  private async generateImageHash(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      const lastModified = response.headers.get('last-modified');
      const etag = response.headers.get('etag');

      // Create hash from URL + metadata for cache key
      const hashInput = `${imageUrl}:${contentLength}:${lastModified}:${etag}`;
      return createHash('sha256').update(hashInput).digest('hex');
    } catch (error) {
      console.warn('Failed to generate image hash from metadata, using URL hash:', error);
      return createHash('sha256').update(imageUrl).digest('hex');
    }
  }

  /**
   * Select the best AI model based on image complexity and requirements
   */
  private selectModel(options: {
    complexity?: 'simple' | 'standard' | 'complex';
    priority?: 'speed' | 'quality' | 'cost';
    fallback?: boolean;
  } = {}): ModelConfig {
    const { complexity = 'standard', priority = 'quality', fallback = false } = options;

    if (fallback) {
      return AI_MODELS['claude-3-haiku'];
    }

    if (priority === 'cost') {
      return AI_MODELS['gemini-pro-vision'];
    }

    if (priority === 'speed') {
      return AI_MODELS['claude-3-haiku'];
    }

    // Default to quality-based selection
    switch (complexity) {
      case 'simple':
        return AI_MODELS['gemini-pro-vision'];
      case 'complex':
        return AI_MODELS['claude-3.5-sonnet'];
      default:
        return AI_MODELS['gpt-4o'];
    }
  }

  /**
   * Call OpenRouter API with the selected model
   */
  private async callOpenRouter(
    model: ModelConfig,
    imageUrl: string,
    prompt: string
  ): Promise<any> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openrouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://coelec.app',
        'X-Title': 'CoElec Symbol Detection'
      },
      body: JSON.stringify({
        model: model.openrouter_model,
        messages: [
          {
            role: 'system',
            content: SYMBOL_DETECTION_PROMPTS.v1.system
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl
                }
              }
            ]
          }
        ],
        max_tokens: model.max_tokens,
        temperature: model.temperature
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  /**
   * Parse and validate AI response
   */
  private parseSymbolResponse(response: any): DetectedSymbol[] {
    try {
      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content in AI response');
      }

      // Extract JSON from response (handle potential markdown formatting)
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No JSON array found in response');
      }

      const symbols = JSON.parse(jsonMatch[0]);

      // Validate and transform symbols
      return symbols.map((symbol: any, index: number) => ({
        id: `symbol_${Date.now()}_${index}`,
        type: symbol.type || 'unknown',
        x: Number(symbol.x) || 0,
        y: Number(symbol.y) || 0,
        width: Number(symbol.width) || 20,
        height: Number(symbol.height) || 20,
        confidence: Number(symbol.confidence) || 0.8,
        properties: {
          subtype: symbol.subtype || '',
          voltage: symbol.properties?.voltage || '120V',
          amperage: symbol.properties?.amperage || '15A',
          notes: symbol.properties?.notes || '',
          ...symbol.properties
        }
      }));
    } catch (error) {
      console.error('Error parsing symbol response:', error);
      return [];
    }
  }

  /**
   * Main symbol detection method with caching
   */
  async detectSymbols(
    imageUrl: string,
    options: {
      complexity?: 'simple' | 'standard' | 'complex';
      priority?: 'speed' | 'quality' | 'cost';
      promptVersion?: string;
      bypassCache?: boolean;
    } = {}
  ): Promise<SymbolDetectionResult> {
    const startTime = Date.now();

    try {
      // Select appropriate model
      const model = this.selectModel(options);
      const promptVersion = options.promptVersion || 'v1';

      // Generate cache key
      const imageHash = await this.generateImageHash(imageUrl);
      const cacheKey = {
        imageHash,
        promptVersion,
        modelType: model.name,
        organizationId: this.organizationId
      };

      // Check cache first (unless bypassed)
      if (!options.bypassCache) {
        const cacheService = getAICacheService(this.organizationId);
        const cachedResponse = await cacheService.getCachedResponse(cacheKey);

        if (cachedResponse) {
          return {
            success: true,
            symbols: cachedResponse.symbols,
            model_used: `${cachedResponse.metadata.model_used} (cached)`,
            processing_time_ms: Date.now() - startTime,
            total_tokens: cachedResponse.metadata.total_tokens,
            cost_estimate: 0, // No cost for cached responses
            cached: true
          };
        }
      }

      // Get prompt
      const prompt = SYMBOL_DETECTION_PROMPTS.v1.user;

      // Call AI model
      const response = await this.callOpenRouter(model, imageUrl, prompt);

      // Parse results
      const symbols = this.parseSymbolResponse(response);

      const processingTime = Date.now() - startTime;
      const totalTokens = response.usage?.total_tokens || 0;
      const costEstimate = (totalTokens / 1000) * model.cost_per_1k_tokens;

      const result = {
        success: true,
        symbols,
        model_used: model.name,
        processing_time_ms: processingTime,
        total_tokens: totalTokens,
        cost_estimate: costEstimate
      };

      // Cache the successful response
      if (!options.bypassCache && symbols.length > 0) {
        try {
          const cacheService = getAICacheService(this.organizationId);
          const aiResponse = {
            symbols,
            metadata: {
              model_used: model.name,
              processing_time_ms: processingTime,
              total_tokens: totalTokens,
              cost_estimate: costEstimate
            },
            confidence_scores: symbols.map(s => s.confidence)
          };

          await cacheService.setCachedResponse(cacheKey, aiResponse);
        } catch (cacheError) {
          console.warn('Failed to cache AI response:', cacheError);
          // Don't fail the request if caching fails
        }
      }

      return result;

    } catch (error: any) {
      console.error('Symbol detection error:', error);

      // Try fallback model if primary fails
      if (!options.priority || options.priority !== 'cost') {
        try {
          const fallbackModel = this.selectModel({ fallback: true });
          const response = await this.callOpenRouter(fallbackModel, imageUrl, SYMBOL_DETECTION_PROMPTS.v1.user);
          const symbols = this.parseSymbolResponse(response);

          return {
            success: true,
            symbols,
            model_used: `${fallbackModel.name} (fallback)`,
            processing_time_ms: Date.now() - startTime,
            total_tokens: response.usage?.total_tokens || 0,
            cost_estimate: ((response.usage?.total_tokens || 0) / 1000) * fallbackModel.cost_per_1k_tokens
          };
        } catch (fallbackError) {
          console.error('Fallback model also failed:', fallbackError);
        }
      }

      return {
        success: false,
        symbols: [],
        model_used: 'none',
        processing_time_ms: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Preprocess image for better detection
   */
  async preprocessImage(
    imageUrl: string,
    options: {
      targetFormat?: 'png' | 'jpeg';
      maxWidth?: number;
      maxHeight?: number;
      enhanceContrast?: boolean;
      denoiseLevel?: 'none' | 'light' | 'medium' | 'heavy';
    } = {}
  ): Promise<{
    success: boolean;
    processedImageUrl?: string;
    originalDimensions?: { width: number; height: number };
    processedDimensions?: { width: number; height: number };
    processingSteps?: string[];
    error?: string;
  }> {
    try {
      const {
        targetFormat = 'png',
        maxWidth = 2048,
        maxHeight = 2048,
        enhanceContrast = true,
        denoiseLevel = 'light'
      } = options;

      // For now, return basic preprocessing info
      // In a full implementation, this would use image processing libraries
      // like Sharp (Node.js) or similar

      const processingSteps: string[] = [];

      // Simulate image analysis
      const originalDimensions = { width: 1920, height: 1080 }; // Would be detected from actual image

      // Calculate target dimensions maintaining aspect ratio
      const aspectRatio = originalDimensions.width / originalDimensions.height;
      let targetWidth = originalDimensions.width;
      let targetHeight = originalDimensions.height;

      if (targetWidth > maxWidth) {
        targetWidth = maxWidth;
        targetHeight = Math.round(targetWidth / aspectRatio);
        processingSteps.push(`Resized width to ${targetWidth}px`);
      }

      if (targetHeight > maxHeight) {
        targetHeight = maxHeight;
        targetWidth = Math.round(targetHeight * aspectRatio);
        processingSteps.push(`Resized height to ${targetHeight}px`);
      }

      if (targetFormat !== 'png') {
        processingSteps.push(`Converted to ${targetFormat} format`);
      }

      if (enhanceContrast) {
        processingSteps.push('Enhanced contrast for better symbol visibility');
      }

      if (denoiseLevel !== 'none') {
        processingSteps.push(`Applied ${denoiseLevel} denoising`);
      }

      // For now, return the original URL
      // In production, this would return the URL of the processed image
      return {
        success: true,
        processedImageUrl: imageUrl,
        originalDimensions,
        processedDimensions: { width: targetWidth, height: targetHeight },
        processingSteps
      };

    } catch (error: any) {
      console.error('Image preprocessing error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate tiles from a large floor plan image
   */
  async generateTiles(
    imageUrl: string,
    options: {
      tileSize?: number;
      overlapPercentage?: number;
      maxTiles?: number;
    } = {}
  ): Promise<{
    success: boolean;
    tiles?: Array<{
      id: string;
      x: number;
      y: number;
      width: number;
      height: number;
      imageUrl: string;
      overlapInfo: {
        left: boolean;
        right: boolean;
        top: boolean;
        bottom: boolean;
      };
    }>;
    totalTiles?: number;
    imageDimensions?: { width: number; height: number };
    error?: string;
  }> {
    try {
      const {
        tileSize = 640,
        overlapPercentage = 0.3,
        maxTiles = 50
      } = options;

      // For now, simulate tiling logic
      // In production, this would use actual image processing
      const imageDimensions = { width: 2048, height: 1536 }; // Would be detected from actual image

      const overlapPixels = Math.floor(tileSize * overlapPercentage);
      const stepSize = tileSize - overlapPixels;

      const tilesX = Math.ceil((imageDimensions.width - overlapPixels) / stepSize);
      const tilesY = Math.ceil((imageDimensions.height - overlapPixels) / stepSize);
      const totalTiles = tilesX * tilesY;

      if (totalTiles > maxTiles) {
        return {
          success: false,
          error: `Image too large: would generate ${totalTiles} tiles (max: ${maxTiles}). Consider using a smaller tile size or higher overlap.`
        };
      }

      const tiles = [];

      for (let row = 0; row < tilesY; row++) {
        for (let col = 0; col < tilesX; col++) {
          const x = col * stepSize;
          const y = row * stepSize;

          // Ensure tile doesn't exceed image boundaries
          const actualWidth = Math.min(tileSize, imageDimensions.width - x);
          const actualHeight = Math.min(tileSize, imageDimensions.height - y);

          tiles.push({
            id: `tile_${row}_${col}`,
            x,
            y,
            width: actualWidth,
            height: actualHeight,
            imageUrl: `${imageUrl}?tile=${row}_${col}&x=${x}&y=${y}&w=${actualWidth}&h=${actualHeight}`, // Simulated tile URL
            overlapInfo: {
              left: col > 0,
              right: col < tilesX - 1,
              top: row > 0,
              bottom: row < tilesY - 1
            }
          });
        }
      }

      return {
        success: true,
        tiles,
        totalTiles,
        imageDimensions
      };

    } catch (error: any) {
      console.error('Tile generation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Apply Non-Maximum Suppression to merge overlapping detections
   */
  private applyNMS(
    symbols: DetectedSymbol[],
    iouThreshold: number = 0.5
  ): DetectedSymbol[] {
    if (symbols.length === 0) return symbols;

    // Sort by confidence score (highest first)
    const sortedSymbols = [...symbols].sort((a, b) => b.confidence - a.confidence);
    const keepSymbols: DetectedSymbol[] = [];

    for (const symbol of sortedSymbols) {
      let shouldKeep = true;

      for (const keptSymbol of keepSymbols) {
        // Only compare symbols of the same type
        if (symbol.type === keptSymbol.type) {
          const iou = this.calculateIoU(symbol, keptSymbol);
          if (iou > iouThreshold) {
            shouldKeep = false;
            break;
          }
        }
      }

      if (shouldKeep) {
        keepSymbols.push(symbol);
      }
    }

    return keepSymbols;
  }

  /**
   * Calculate Intersection over Union (IoU) between two symbols
   */
  private calculateIoU(symbol1: DetectedSymbol, symbol2: DetectedSymbol): number {
    const x1 = Math.max(symbol1.x, symbol2.x);
    const y1 = Math.max(symbol1.y, symbol2.y);
    const x2 = Math.min(symbol1.x + symbol1.width, symbol2.x + symbol2.width);
    const y2 = Math.min(symbol1.y + symbol1.height, symbol2.y + symbol2.height);

    if (x2 <= x1 || y2 <= y1) return 0; // No intersection

    const intersectionArea = (x2 - x1) * (y2 - y1);
    const area1 = symbol1.width * symbol1.height;
    const area2 = symbol2.width * symbol2.height;
    const unionArea = area1 + area2 - intersectionArea;

    return intersectionArea / unionArea;
  }

  /**
   * Process floor plan with tiling strategy
   */
  async processFloorPlanWithTiling(
    imageUrl: string,
    options: {
      tileSize?: number;
      overlapPercentage?: number;
      complexity?: 'simple' | 'standard' | 'complex';
      priority?: 'speed' | 'quality' | 'cost';
      promptVersion?: string;
      applyNMS?: boolean;
      nmsThreshold?: number;
    } = {}
  ): Promise<{
    success: boolean;
    symbols?: DetectedSymbol[];
    tiles?: any[];
    processingStats?: {
      totalTiles: number;
      successfulTiles: number;
      failedTiles: number;
      totalProcessingTime: number;
      averageTimePerTile: number;
      totalCost: number;
    };
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      const {
        tileSize = 640,
        overlapPercentage = 0.3,
        applyNMS = true,
        nmsThreshold = 0.5,
        ...detectionOptions
      } = options;

      // Generate tiles
      const tilingResult = await this.generateTiles(imageUrl, {
        tileSize,
        overlapPercentage
      });

      if (!tilingResult.success || !tilingResult.tiles) {
        return {
          success: false,
          error: tilingResult.error || 'Failed to generate tiles'
        };
      }

      // Process each tile
      const tileResults = await Promise.allSettled(
        tilingResult.tiles.map(tile =>
          this.detectSymbols(tile.imageUrl, detectionOptions)
        )
      );

      // Aggregate results
      let allSymbols: DetectedSymbol[] = [];
      let totalCost = 0;
      let successfulTiles = 0;
      let failedTiles = 0;

      tileResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successfulTiles++;
          totalCost += result.value.cost_estimate || 0;

          // Adjust symbol coordinates to global image coordinates
          const tile = tilingResult.tiles![index];
          const adjustedSymbols = result.value.symbols.map(symbol => ({
            ...symbol,
            x: symbol.x + tile.x,
            y: symbol.y + tile.y
          }));

          allSymbols.push(...adjustedSymbols);
        } else {
          failedTiles++;
        }
      });

      // Apply Non-Maximum Suppression to remove duplicates
      if (applyNMS && allSymbols.length > 0) {
        allSymbols = this.applyNMS(allSymbols, nmsThreshold);
      }

      const totalProcessingTime = Date.now() - startTime;

      return {
        success: true,
        symbols: allSymbols,
        tiles: tilingResult.tiles,
        processingStats: {
          totalTiles: tilingResult.totalTiles || 0,
          successfulTiles,
          failedTiles,
          totalProcessingTime,
          averageTimePerTile: successfulTiles > 0 ? totalProcessingTime / successfulTiles : 0,
          totalCost
        }
      };

    } catch (error: any) {
      console.error('Floor plan processing error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Batch process multiple images or tiles
   */
  async detectSymbolsBatch(
    imageUrls: string[],
    options: Parameters<typeof this.detectSymbols>[1] = {}
  ): Promise<SymbolDetectionResult[]> {
    const results = await Promise.allSettled(
      imageUrls.map(url => this.detectSymbols(url, options))
    );

    return results.map(result =>
      result.status === 'fulfilled'
        ? result.value
        : {
            success: false,
            symbols: [],
            model_used: 'none',
            processing_time_ms: 0,
            error: result.reason?.message || 'Unknown error'
          }
    );
  }
}

// Export singleton instance
let mcpInstance: SymbolDetectionMCP | null = null;

export function getSymbolDetectionMCP(organizationId: string = 'default-org'): SymbolDetectionMCP {
  if (!mcpInstance) {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }
    mcpInstance = new SymbolDetectionMCP(apiKey, organizationId);
  }
  return mcpInstance;
}
