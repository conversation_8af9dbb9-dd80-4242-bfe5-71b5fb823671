/**
 * Permission Middleware for CollaborateFlow
 * 
 * This is a stub implementation that always grants permissions.
 * It provides the structure for a full permission system that will be implemented later.
 */

import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';

// Get Supabase credentials
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || '';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Check if a user is a super admin (member of Team ID 1)
 * 
 * @param {number} userId - The user ID to check
 * @returns {Promise<boolean>} - Whether the user is a super admin
 */
async function isSuperAdmin(userId: number): Promise<boolean> {
  try {
    const { data } = await supabase
      .from('team_members')
      .select('id')
      .eq('user_id', userId)
      .eq('team_id', 1)
      .single();
      
    return !!data;
  } catch (error) {
    console.error('Error checking super admin status:', error);
    return false;
  }
}

/**
 * Check if a user has permission for a specific action on a module within a team context
 * STUB: Currently always returns true
 * 
 * @param {number} userId - The user ID
 * @param {number} teamId - The team ID
 * @param {string} module - Module name (e.g., 'project_management')
 * @param {string} action - Action type ('view', 'create', 'edit', 'delete')
 * @returns {Promise<boolean>} - Whether the user has permission
 */
async function hasPermission(userId: number, teamId: number, module: string, action: string): Promise<boolean> {
  // When fully implemented, this will check the user's team role and the corresponding permissions
  // For now, we're using the stub implementation that allows all actions
  return true;
}

/**
 * Middleware to check if user has permission for specific module action
 * This version requires a teamId parameter in the request
 * 
 * @param {string} module - Module name (e.g., 'project_management')
 * @param {string} action - Action type ('view', 'create', 'edit', 'delete')
 * @param {string} teamIdParam - Request parameter containing the team ID (default: 'teamId')
 * @returns {Function} - Express middleware function
 */
function requirePermission(module: string, action: string, teamIdParam: string = 'teamId') {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Get user ID from the authenticated request
    const userId = req.user?.id;
    
    // If no user is authenticated, deny access
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    try {
      // First check if user is a super admin (member of Team ID 1)
      const isSuperAdminUser = await isSuperAdmin(userId);
      if (isSuperAdminUser) {
        // Super admins bypass all permission checks
        return next();
      }
      
      // Get team ID from request parameters
      const teamId = req.params[teamIdParam];
      
      if (!teamId) {
        return res.status(400).json({ error: `Team ID is required (parameter: ${teamIdParam})` });
      }
      
      // Check permission (currently stubbed to always return true)
      const hasAccess = await hasPermission(userId, parseInt(teamId), module, action);
      
      if (hasAccess) {
        // User has permission, proceed to the next middleware
        return next();
      } else {
        // User doesn't have permission
        return res.status(403).json({ error: 'Forbidden' });
      }
    } catch (error: any) {
      console.error(`Permission check error: ${error.message}`);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Middleware to check if user has a specific role
 * 
 * @param {string[]} roles - Array of allowed roles
 * @returns {Function} - Express middleware function
 */
function requireRole(roles: string[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    try {
      // Get user's role from the database
      const { data, error } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();
      
      if (error) {
        console.error(`Error fetching user role: ${error.message}`);
        return res.status(500).json({ error: 'Internal server error' });
      }
      
      if (data && roles.includes(data.role)) {
        // User has one of the required roles
        return next();
      } else {
        // User doesn't have a required role
        return res.status(403).json({ error: 'Forbidden' });
      }
    } catch (error: any) {
      console.error(`Role check error: ${error.message}`);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Middleware to check if user is a team member
 * 
 * @param {string} teamIdParam - Request parameter containing the team ID
 * @returns {Function} - Express middleware function
 */
function requireTeamMembership(teamIdParam: string = 'teamId') {
  return async (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user?.id;
    const teamId = req.params[teamIdParam];
    
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    if (!teamId) {
      return res.status(400).json({ error: 'Team ID is required' });
    }
    
    try {
      // First check if user is a super admin (member of Team ID 1)
      const isSuperAdminUser = await isSuperAdmin(userId);
      if (isSuperAdminUser) {
        // Super admins bypass all team membership checks
        return next();
      }
      
      // For other users, check team membership
      const { data, error } = await supabase
        .from('team_members')
        .select('id')
        .eq('team_id', teamId)
        .eq('user_id', userId)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is the 'not found' error
        console.error(`Error checking team membership: ${error.message}`);
        return res.status(500).json({ error: 'Internal server error' });
      }
      
      if (data) {
        // User is a team member
        return next();
      } else {
        // User is not a team member
        return res.status(403).json({ error: 'You are not a member of this team' });
      }
    } catch (error: any) {
      console.error(`Team membership check error: ${error.message}`);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Simpler middleware for checking module permissions without team context
 * This is useful for routes that don't operate in a specific team context
 * 
 * @param {string} module - Module name (e.g., 'project_management') 
 * @param {string} action - Action type ('view', 'create', 'edit', 'delete')
 * @returns {Function} - Express middleware function
 */
function requireModulePermission(module: string, action: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Get user ID from the authenticated request
    const userId = req.user?.id;
    
    // If no user is authenticated, deny access
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    try {
      // First check if user is a super admin (member of Team ID 1)
      const isSuperAdminUser = await isSuperAdmin(userId);
      if (isSuperAdminUser) {
        // Super admins bypass all permission checks
        return next();
      }
      
      // For now, always allow access in the stub implementation
      // When fully implemented, this will check the user's permissions across all their teams
      return next();
    } catch (error: any) {
      console.error(`Module permission check error: ${error.message}`);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

export {
  hasPermission,
  requirePermission,
  requireRole,
  requireTeamMembership,
  requireModulePermission
};
