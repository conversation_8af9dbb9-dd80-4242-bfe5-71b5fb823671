// Mock data for use in development and testing environments

export const mockTeams = [
  {
    id: 1,
    name: 'Team Alpha',
    description: 'Primary development team',
    createdById: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Team Beta',
    description: 'Secondary project team',
    createdById: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export const mockProjects = [
  {
    id: 1,
    name: 'Residential Building A',
    description: 'Electrical wiring for new residential complex',
    teamId: 1,
    createdById: 1,
    status: 'in_progress',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Commercial Office B',
    description: 'Commercial electrical installation project',
    teamId: 1,
    createdById: 1,
    status: 'planning',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    name: 'Renovation Project C',
    description: 'Electrical renovation for historic building',
    teamId: 2,
    createdById: 1,
    status: 'in_progress',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export const mockTeamMembers = [
  {
    id: 1,
    teamId: 1,
    userId: 1,
    role: 'admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    teamId: 2,
    userId: 1,
    role: 'admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export const mockColumns = [
  { id: 1, name: "New Project", order: 0, projectId: 1 },
  { id: 2, name: "Symbol Detection", order: 1, projectId: 1 },
  { id: 3, name: "Symbol Review", order: 2, projectId: 1 },
  { id: 4, name: "Material Estimation", order: 3, projectId: 1 },
  { id: 5, name: "Labor Estimation", order: 4, projectId: 1 },
  { id: 6, name: "Quote Generation", order: 5, projectId: 1 },
  { id: 7, name: "Client Approval", order: 6, projectId: 1 },
  // Project 2 columns
  { id: 8, name: "New Project", order: 0, projectId: 2 },
  { id: 9, name: "Symbol Detection", order: 1, projectId: 2 },
  { id: 10, name: "Symbol Review", order: 2, projectId: 2 },
  { id: 11, name: "Material Estimation", order: 3, projectId: 2 },
  { id: 12, name: "Labor Estimation", order: 4, projectId: 2 },
  { id: 13, name: "Quote Generation", order: 5, projectId: 2 },
  { id: 14, name: "Client Approval", order: 6, projectId: 2 },
];

export const mockTasks = [
  {
    id: 1,
    title: "Initial floor plan upload",
    description: "Upload the initial floor plan for analysis",
    columnId: 1,
    projectId: 1,
    assigneeId: 1,
    order: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    title: "Run symbol detection",
    description: "Process the floor plan through AI for symbol detection",
    columnId: 2,
    projectId: 1,
    assigneeId: 1,
    order: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Mock for floor plans
export const mockFloorPlans = [
  {
    id: 1,
    projectId: 1,
    fileName: "floor_plan_1.pdf",
    fileUrl: "/uploads/floor_plan_1.pdf",
    version: 1,
    status: "processed",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Mock for quotes
export const mockQuotes = [
  {
    id: 1,
    projectId: 1,
    name: "Initial Quote",
    status: "draft",
    totalMaterialCost: 12500.00,
    totalLaborCost: 7500.00,
    markupPercentage: 15,
    totalCost: 23000.00,
    createdById: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];
