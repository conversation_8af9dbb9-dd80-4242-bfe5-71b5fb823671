import { Request, Response, NextFunction } from 'express';
import { mockTeams, mockProjects, mockTeamMembers, mockColumns, mockTasks, mockFloorPlans, mockQuotes } from './mockData';

// This middleware intercepts specific API routes and returns mock data instead of querying the database
export function mockApiMiddleware(req: Request, res: Response, next: NextFunction) {
  const path = req.path;
  
  console.log('Mock API intercepted:', path);
  
  // GET /api/teams
  if (path === '/api/teams' && req.method === 'GET') {
    console.log('Returning mock teams');
    return res.json(mockTeams);
  }
  
  // GET /api/projects
  if (path === '/api/projects' && req.method === 'GET') {
    console.log('Returning mock projects');
    return res.json(mockProjects);
  }
  
  // GET /api/teams/:id
  if (path.match(/\/api\/teams\/\d+$/) && req.method === 'GET') {
    const id = parseInt(path.split('/').pop() || '0');
    const team = mockTeams.find(t => t.id === id);
    if (!team) {
      return res.status(404).json({ message: 'Team not found' });
    }
    return res.json(team);
  }
  
  // GET /api/teams/:id/members
  if (path.match(/\/api\/teams\/\d+\/members$/) && req.method === 'GET') {
    const teamId = parseInt(path.split('/')[3]);
    const members = mockTeamMembers.filter(m => m.teamId === teamId);
    return res.json(members);
  }
  
  // GET /api/projects/:id
  if (path.match(/\/api\/projects\/\d+$/) && req.method === 'GET') {
    const id = parseInt(path.split('/').pop() || '0');
    const project = mockProjects.find(p => p.id === id);
    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }
    return res.json(project);
  }
  
  // GET /api/projects/:id/columns
  if (path.match(/\/api\/projects\/\d+\/columns$/) && req.method === 'GET') {
    const projectId = parseInt(path.split('/')[3]);
    const columns = mockColumns.filter(c => c.projectId === projectId);
    return res.json(columns);
  }
  
  // GET /api/columns?projectId=x
  if (path === '/api/columns' && req.method === 'GET' && req.query.projectId) {
    const projectId = parseInt(req.query.projectId as string);
    const columns = mockColumns.filter(c => c.projectId === projectId);
    return res.json(columns);
  }
  
  // GET /api/projects/:id/tasks
  if (path.match(/\/api\/projects\/\d+\/tasks$/) && req.method === 'GET') {
    const projectId = parseInt(path.split('/')[3]);
    const tasks = mockTasks.filter(t => t.projectId === projectId);
    return res.json(tasks);
  }
  
  // GET /api/tasks?projectId=x
  if (path === '/api/tasks' && req.method === 'GET' && req.query.projectId) {
    const projectId = parseInt(req.query.projectId as string);
    const tasks = mockTasks.filter(t => t.projectId === projectId);
    return res.json(tasks);
  }
  
  // Default: continue to the next middleware
  next();
}
