import express, { Request, Response } from 'express';
import { ColumnService } from '../../services/serviceFactory';

const router = express.Router();

/**
 * Get columns for a project
 * GET /api/columns?projectId=1
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.query.projectId as string);
    
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    const columns = await ColumnService.getColumns(projectId);
    res.json(columns);
  } catch (error) {
    console.error('Error fetching columns:', error);
    res.status(500).json({ message: 'Failed to fetch columns', error: (error as Error).message });
  }
});

/**
 * Create a new column
 * POST /api/columns
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const { name, projectId, order } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'Column name is required' });
    }
    
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    if (order === undefined || order === null) {
      return res.status(400).json({ message: 'Order is required' });
    }
    
    const column = await ColumnService.createColumn({
      name,
      projectId,
      order
    });
    
    res.status(201).json(column);
  } catch (error) {
    console.error('Error creating column:', error);
    res.status(500).json({ message: 'Failed to create column', error: (error as Error).message });
  }
});

/**
 * Update a column
 * PUT /api/columns/:id
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const columnId = parseInt(req.params.id);
    const { name } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'Column name is required' });
    }
    
    const column = await ColumnService.updateColumn(columnId, {
      name
    });
    
    res.json(column);
  } catch (error) {
    console.error('Error updating column:', error);
    res.status(500).json({ message: 'Failed to update column', error: (error as Error).message });
  }
});

/**
 * Delete a column
 * DELETE /api/columns/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const columnId = parseInt(req.params.id);
    
    await ColumnService.deleteColumn(columnId);
    
    res.status(204).end();
  } catch (error) {
    console.error('Error deleting column:', error);
    res.status(500).json({ message: 'Failed to delete column', error: (error as Error).message });
  }
});

/**
 * Reorder columns
 * PUT /api/columns/reorder
 */
router.put('/reorder/:projectId', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.params.projectId);
    const { columnOrders } = req.body;
    
    // Validate required fields
    if (!columnOrders || !Array.isArray(columnOrders)) {
      return res.status(400).json({ message: 'Column orders array is required' });
    }
    
    // Validate each column order object
    for (const order of columnOrders) {
      if (!order.id || order.order === undefined || order.order === null) {
        return res.status(400).json({ message: 'Each column order must have id and order' });
      }
    }
    
    await ColumnService.reorderColumns(projectId, columnOrders);
    
    res.status(200).json({ message: 'Columns reordered successfully' });
  } catch (error) {
    console.error('Error reordering columns:', error);
    res.status(500).json({ message: 'Failed to reorder columns', error: (error as Error).message });
  }
});

export default router;
