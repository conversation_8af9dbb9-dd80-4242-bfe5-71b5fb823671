import express, { Request, Response } from 'express';
import { supabase } from '../../db';

const router = express.Router();

// Debug endpoint to directly create a team with explicit parameters
router.post('/', async (req: Request, res: Response) => {
  try {
    const { name, description } = req.body;
    
    // Log the request
    console.log('DEBUG: Received request with:', { name, description });
    console.log('DEBUG: Supabase client:', supabase ? 'Initialized' : 'Not initialized');
    
    // Create a manual team object with absolute hardcoded values
    const teamData = {
      name: name || 'Debug Team',
      description: description || 'Debug Team Description',
      created_by_id: 1,
      organization_id: 1,
      created_at: new Date().toISOString()
    };
    
    console.log('DEBUG: Team data to insert:', teamData);
    console.log('DEBUG: organization_id type:', typeof teamData.organization_id);
    
    // Debug Supabase configuration
    console.log('DEBUG: Supabase URL:', process.env.SUPABASE_URL);
    console.log('DEBUG: Supabase client initialized:', !!supabase);
    
    // Debug database connection
    const { data: dbCheck, error: dbError } = await supabase
      .from('teams')
      .select('count')
      .limit(1);
      
    if (dbError) {
      console.error('DEBUG: Database connection check failed:', dbError);
    } else {
      console.log('DEBUG: Database connection check successful:', dbCheck);
    }
    
    // Try with the regular Supabase client
    console.log('DEBUG: Attempting with Supabase client...');
    const { data, error } = await supabase
      .from('teams')
      .insert({
        name: teamData.name,
        description: teamData.description,
        created_by_id: teamData.created_by_id,
        organization_id: teamData.organization_id,
        created_at: teamData.created_at
      })
      .select()
      .single();
    
    if (error) {
      console.error('DEBUG: Supabase error:', error);
      return res.status(500).json({ error });
    }
    
    console.log('DEBUG: Supabase success:', data);
    return res.status(201).json({ message: 'Team created via Supabase client', team: data });
  } catch (error: any) {
    console.error('DEBUG: Exception:', error);
    return res.status(500).json({ error: error.message });
  }
});

export default router;
