/**
 * T1.3 MATERIAL ESTIMATION ENGINE - API ENDPOINTS
 * RESTful API for material estimation and cost calculation
 */

import express from 'express';
import { MaterialEstimationEngine, type DetectedSymbol, type ProjectContext, type EstimationSettings } from '../../services/materialEstimationEngine';
import { supabase } from '../../supabase';

const router = express.Router();

// Middleware to extract organization ID (simplified for development)
const getOrganizationId = (req: express.Request): string => {
  // In production, this would extract from authenticated user
  return req.headers['x-organization-id'] as string || 'default-org-id';
};

/**
 * POST /api/estimates/calculate
 * Calculate material estimation from detected symbols
 */
router.post('/calculate', async (req, res) => {
  try {
    console.log('🔧 Processing material estimation request...');

    const { symbols, projectContext, settings } = req.body;

    // Validate request data
    if (!symbols || !Array.isArray(symbols) || symbols.length === 0) {
      return res.status(400).json({
        error: 'Invalid symbols data',
        message: 'symbols array is required and must contain at least one symbol'
      });
    }

    if (!projectContext || !projectContext.projectType || !projectContext.location) {
      return res.status(400).json({
        error: 'Invalid project context',
        message: 'projectContext with projectType and location is required'
      });
    }

    // Default estimation settings
    const estimationSettings: EstimationSettings = {
      markupPercentage: settings?.markupPercentage || 20.0,
      overheadPercentage: settings?.overheadPercentage || 15.0,
      profitMarginPercentage: settings?.profitMarginPercentage || 10.0,
      contingencyPercentage: settings?.contingencyPercentage || 5.0,
      ...settings
    };

    console.log(`📊 Calculating estimation for ${symbols.length} symbols`);
    console.log(`🏠 Project: ${projectContext.projectType} in ${projectContext.location.city}, ${projectContext.location.state}`);

    // Initialize estimation engine
    const organizationId = getOrganizationId(req);
    const estimationEngine = new MaterialEstimationEngine(organizationId);

    // Calculate estimation
    const result = await estimationEngine.calculateEstimation(
      symbols as DetectedSymbol[],
      projectContext as ProjectContext,
      estimationSettings
    );

    console.log(`✅ Estimation complete: $${result.totalCost.toFixed(2)} total cost`);

    res.json({
      success: true,
      estimation: result,
      summary: {
        totalCost: result.totalCost,
        materialsCost: result.materialsCost,
        laborCost: result.laborCost,
        totalLaborHours: result.totalLaborHours,
        lineItemCount: result.lineItems.length,
        confidenceScore: result.confidenceScore
      }
    });

  } catch (error) {
    console.error('❌ Material estimation failed:', error);
    res.status(500).json({
      error: 'Estimation calculation failed',
      message: error.message
    });
  }
});

/**
 * GET /api/estimates/projects/:projectId
 * Get estimation results for a specific project
 */
router.get('/projects/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const organizationId = getOrganizationId(req);

    console.log(`📋 Retrieving estimation for project: ${projectId}`);

    // Get estimation results with line items
    const { data: estimation, error: estimationError } = await supabase
      .from('estimation_results')
      .select(`
        *,
        estimation_projects!inner (
          id,
          project_name,
          project_type,
          location,
          organization_id
        ),
        estimation_line_items (*)
      `)
      .eq('project_id', projectId)
      .eq('estimation_projects.organization_id', organizationId)
      .single();

    if (estimationError) {
      console.error('Database error:', estimationError);
      return res.status(500).json({
        error: 'Database query failed',
        message: estimationError.message
      });
    }

    if (!estimation) {
      return res.status(404).json({
        error: 'Estimation not found',
        message: `No estimation found for project ${projectId}`
      });
    }

    console.log(`✅ Retrieved estimation: $${estimation.total_cost} total cost`);

    res.json({
      success: true,
      estimation: {
        id: estimation.id,
        projectId: estimation.project_id,
        projectName: estimation.estimation_projects.project_name,
        projectType: estimation.estimation_projects.project_type,
        location: estimation.estimation_projects.location,
        materialsCost: estimation.materials_cost,
        laborCost: estimation.labor_cost,
        overheadCost: estimation.overhead_cost,
        markupCost: estimation.markup_cost,
        taxCost: estimation.tax_cost,
        permitCost: estimation.permit_cost,
        contingencyCost: estimation.contingency_cost,
        totalCost: estimation.total_cost,
        totalLaborHours: estimation.total_labor_hours,
        confidenceScore: estimation.confidence_score,
        calculationMethod: estimation.calculation_method,
        notes: estimation.notes,
        lineItems: estimation.estimation_line_items,
        createdAt: estimation.created_at,
        updatedAt: estimation.updated_at
      }
    });

  } catch (error) {
    console.error('❌ Failed to retrieve estimation:', error);
    res.status(500).json({
      error: 'Failed to retrieve estimation',
      message: error.message
    });
  }
});

/**
 * GET /api/estimates/materials/search
 * Search electrical materials for estimation
 */
router.get('/materials/search', async (req, res) => {
  try {
    const { query, category, limit = 20 } = req.query;

    console.log(`🔍 Searching materials: query="${query}", category="${category}"`);

    let queryBuilder = supabase
      .from('electrical_materials')
      .select('*')
      .eq('is_active', true)
      .limit(parseInt(limit as string));

    if (query) {
      queryBuilder = queryBuilder.or(`name.ilike.%${query}%,description.ilike.%${query}%,material_code.ilike.%${query}%`);
    }

    if (category) {
      queryBuilder = queryBuilder.eq('category', category);
    }

    const { data: materials, error } = await queryBuilder.order('name');

    if (error) {
      console.error('Material search error:', error);
      return res.status(500).json({
        error: 'Material search failed',
        message: error.message
      });
    }

    console.log(`✅ Found ${materials?.length || 0} materials`);

    res.json({
      success: true,
      materials: materials || [],
      count: materials?.length || 0
    });

  } catch (error) {
    console.error('❌ Material search failed:', error);
    res.status(500).json({
      error: 'Material search failed',
      message: error.message
    });
  }
});

/**
 * GET /api/estimates/categories
 * Get all material categories
 */
router.get('/categories', async (req, res) => {
  try {
    console.log('📂 Retrieving material categories...');

    const { data: categories, error } = await supabase
      .from('material_categories')
      .select('*')
      .order('display_order');

    if (error) {
      console.error('Categories query error:', error);
      return res.status(500).json({
        error: 'Failed to retrieve categories',
        message: error.message
      });
    }

    console.log(`✅ Retrieved ${categories?.length || 0} categories`);

    res.json({
      success: true,
      categories: categories || []
    });

  } catch (error) {
    console.error('❌ Failed to retrieve categories:', error);
    res.status(500).json({
      error: 'Failed to retrieve categories',
      message: error.message
    });
  }
});

/**
 * GET /api/estimates/regions
 * Get regional pricing information
 */
router.get('/regions', async (req, res) => {
  try {
    console.log('🌍 Retrieving regional pricing data...');

    const { data: regions, error } = await supabase
      .from('regional_pricing')
      .select('*')
      .eq('is_active', true)
      .order('region_name');

    if (error) {
      console.error('Regional pricing query error:', error);
      return res.status(500).json({
        error: 'Failed to retrieve regional pricing',
        message: error.message
      });
    }

    console.log(`✅ Retrieved ${regions?.length || 0} regions`);

    res.json({
      success: true,
      regions: regions || []
    });

  } catch (error) {
    console.error('❌ Failed to retrieve regional pricing:', error);
    res.status(500).json({
      error: 'Failed to retrieve regional pricing',
      message: error.message
    });
  }
});

/**
 * POST /api/estimates/test
 * Test endpoint for material estimation engine
 */
router.post('/test', async (req, res) => {
  try {
    console.log('🧪 Running material estimation test...');

    // Test data
    const testSymbols: DetectedSymbol[] = [
      {
        id: 'test-1',
        type: 'outlet',
        subtype: 'standard',
        x: 100,
        y: 200,
        width: 20,
        height: 30,
        confidence: 0.92,
        properties: { voltage: '120V', amperage: '15A' }
      },
      {
        id: 'test-2',
        type: 'outlet',
        subtype: 'gfci',
        x: 300,
        y: 200,
        width: 20,
        height: 30,
        confidence: 0.88,
        properties: { voltage: '120V', amperage: '20A' }
      },
      {
        id: 'test-3',
        type: 'light',
        subtype: 'recessed',
        x: 200,
        y: 50,
        width: 40,
        height: 40,
        confidence: 0.85,
        properties: { voltage: '120V', wattage: 12 }
      },
      {
        id: 'test-4',
        type: 'switch',
        subtype: 'single_pole',
        x: 50,
        y: 100,
        width: 15,
        height: 15,
        confidence: 0.90,
        properties: { voltage: '120V' }
      }
    ];

    const testProjectContext: ProjectContext = {
      projectType: 'residential',
      location: {
        city: 'San Francisco',
        state: 'CA',
        zip: '94102',
        regionCode: 'US-CA-SF'
      },
      squareFootage: 1200,
      numberOfFloors: 1,
      buildingType: 'single_family'
    };

    const testSettings: EstimationSettings = {
      markupPercentage: 20.0,
      overheadPercentage: 15.0,
      profitMarginPercentage: 10.0,
      contingencyPercentage: 5.0
    };

    // Run estimation
    const organizationId = getOrganizationId(req);
    const estimationEngine = new MaterialEstimationEngine(organizationId);
    
    const result = await estimationEngine.calculateEstimation(
      testSymbols,
      testProjectContext,
      testSettings
    );

    console.log(`✅ Test estimation complete: $${result.totalCost.toFixed(2)}`);

    res.json({
      success: true,
      message: 'Material estimation test completed successfully',
      testData: {
        symbols: testSymbols.length,
        projectType: testProjectContext.projectType,
        location: `${testProjectContext.location.city}, ${testProjectContext.location.state}`
      },
      result: {
        totalCost: result.totalCost,
        materialsCost: result.materialsCost,
        laborCost: result.laborCost,
        totalLaborHours: result.totalLaborHours,
        lineItems: result.lineItems.length,
        confidenceScore: result.confidenceScore
      }
    });

  } catch (error) {
    console.error('❌ Material estimation test failed:', error);
    res.status(500).json({
      error: 'Test failed',
      message: error.message
    });
  }
});

export default router;
