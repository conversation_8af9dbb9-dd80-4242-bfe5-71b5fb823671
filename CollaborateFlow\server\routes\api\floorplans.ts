import express, { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { FloorPlanService } from '../../services/serviceFactory';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Create uploads directory if it doesn't exist
    const uploadDir = path.join(process.cwd(), 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'floor-plan-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    // Accept only PDFs and images
    const allowedTypes = ['.pdf', '.jpg', '.jpeg', '.png'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Only PDF and image files are allowed'));
    }
  }
});

/**
 * Get all floor plans for a project
 * GET /api/floorplans?projectId=1
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.query.projectId as string);
    
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    const floorPlans = await FloorPlanService.getFloorPlans(projectId);
    res.json(floorPlans);
  } catch (error) {
    console.error('Error fetching floor plans:', error);
    res.status(500).json({ message: 'Failed to fetch floor plans', error: (error as Error).message });
  }
});

/**
 * Get a specific floor plan
 * GET /api/floorplans/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const floorPlanId = parseInt(req.params.id);
    
    const floorPlan = await FloorPlanService.getFloorPlan(floorPlanId);
    
    if (!floorPlan) {
      return res.status(404).json({ message: 'Floor plan not found' });
    }
    
    res.json(floorPlan);
  } catch (error) {
    console.error('Error fetching floor plan:', error);
    res.status(500).json({ message: 'Failed to fetch floor plan', error: (error as Error).message });
  }
});

/**
 * Upload a new floor plan
 * POST /api/floorplans
 */
router.post('/', upload.single('file'), async (req: Request, res: Response) => {
  try {
    const { projectId } = req.body;
    const file = req.file;
    
    // Validate required fields
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    if (!file) {
      return res.status(400).json({ message: 'Floor plan file is required' });
    }
    
    // Create the floor plan record
    const floorPlan = await FloorPlanService.createFloorPlan({
      projectId: parseInt(projectId),
      fileName: file.originalname,
      fileUrl: `/uploads/${file.filename}`,
      version: 1, // Version will be calculated in the service
      status: 'uploaded'
    });
    
    // In a real implementation, we would trigger AI processing here
    // For now, just update the status to simulate processing
    setTimeout(async () => {
      try {
        await FloorPlanService.updateFloorPlan(floorPlan.id, {
          status: 'processed'
        });
        console.log(`Floor plan ${floorPlan.id} marked as processed`);
      } catch (error) {
        console.error('Error updating floor plan status:', error);
      }
    }, 3000);
    
    res.status(201).json(floorPlan);
  } catch (error) {
    console.error('Error uploading floor plan:', error);
    res.status(500).json({ message: 'Failed to upload floor plan', error: (error as Error).message });
  }
});

/**
 * Update a floor plan
 * PUT /api/floorplans/:id
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const floorPlanId = parseInt(req.params.id);
    const { status } = req.body;
    
    // Validate status
    const validStatuses = ['uploaded', 'processing', 'processed', 'failed'];
    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({ 
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}` 
      });
    }
    
    const floorPlan = await FloorPlanService.updateFloorPlan(floorPlanId, {
      status
    });
    
    res.json(floorPlan);
  } catch (error) {
    console.error('Error updating floor plan:', error);
    res.status(500).json({ message: 'Failed to update floor plan', error: (error as Error).message });
  }
});

/**
 * Delete a floor plan
 * DELETE /api/floorplans/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const floorPlanId = parseInt(req.params.id);
    
    // Get the floor plan to find the file path
    const floorPlan = await FloorPlanService.getFloorPlan(floorPlanId);
    
    if (!floorPlan) {
      return res.status(404).json({ message: 'Floor plan not found' });
    }
    
    // Delete the file if it exists
    if (floorPlan.fileUrl) {
      const filePath = path.join(process.cwd(), floorPlan.fileUrl.replace(/^\/+/, ''));
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
    
    // Delete the floor plan record
    await FloorPlanService.deleteFloorPlan(floorPlanId);
    
    res.status(204).end();
  } catch (error) {
    console.error('Error deleting floor plan:', error);
    res.status(500).json({ message: 'Failed to delete floor plan', error: (error as Error).message });
  }
});

/**
 * Get symbols for a floor plan
 * GET /api/floorplans/:id/symbols
 */
router.get('/:id/symbols', async (req: Request, res: Response) => {
  try {
    const floorPlanId = parseInt(req.params.id);
    
    const symbols = await FloorPlanService.getSymbols(floorPlanId);
    res.json(symbols);
  } catch (error) {
    console.error('Error fetching symbols:', error);
    res.status(500).json({ message: 'Failed to fetch symbols', error: (error as Error).message });
  }
});

/**
 * Create a symbol for a floor plan
 * POST /api/floorplans/:id/symbols
 */
router.post('/:id/symbols', async (req: Request, res: Response) => {
  try {
    const floorPlanId = parseInt(req.params.id);
    const { type, x, y, width, height, properties } = req.body;
    
    // Validate required fields
    if (!type) {
      return res.status(400).json({ message: 'Symbol type is required' });
    }
    
    if (x === undefined || y === undefined || width === undefined || height === undefined) {
      return res.status(400).json({ message: 'Symbol coordinates and dimensions are required' });
    }
    
    const symbol = await FloorPlanService.createSymbol({
      floorPlanId,
      type,
      x,
      y,
      width,
      height,
      properties: properties || {}
    });
    
    res.status(201).json(symbol);
  } catch (error) {
    console.error('Error creating symbol:', error);
    res.status(500).json({ message: 'Failed to create symbol', error: (error as Error).message });
  }
});

/**
 * Bulk create symbols for a floor plan (e.g., from AI detection)
 * POST /api/floorplans/:id/symbols/bulk
 */
router.post('/:id/symbols/bulk', async (req: Request, res: Response) => {
  try {
    const floorPlanId = parseInt(req.params.id);
    const { symbols } = req.body;
    
    // Validate required fields
    if (!symbols || !Array.isArray(symbols) || symbols.length === 0) {
      return res.status(400).json({ message: 'Symbols array is required' });
    }
    
    // Add floor plan ID to each symbol
    const symbolsWithFloorPlanId = symbols.map(symbol => ({
      ...symbol,
      floorPlanId
    }));
    
    const createdSymbols = await FloorPlanService.bulkCreateSymbols(symbolsWithFloorPlanId);
    
    res.status(201).json(createdSymbols);
  } catch (error) {
    console.error('Error bulk creating symbols:', error);
    res.status(500).json({ message: 'Failed to bulk create symbols', error: (error as Error).message });
  }
});

export default router;
