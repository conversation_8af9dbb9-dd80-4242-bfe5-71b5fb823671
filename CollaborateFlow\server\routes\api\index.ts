import express from 'express';
import teamsRouter from './teams';
import teamDetailsRouter from './team-details';
import projectsRouter from './projects';
import columnsRouter from './columns';
import tasksRouter from './tasks';
import floorplansRouter from './floorplans';
import quotesRouter from './quotes';
import organizationsRouter from './organizations';
import testTeamRouter from './test-team';
import debugTeamRouter from './debug-team';
import aiRouter from '../ai';
import symbolMappingRouter from '../symbolMapping';
import costCalculationRouter from '../costCalculation';
import supplierIntegrationRouter from '../supplierIntegration';
import priceUpdatesRouter from '../priceUpdates';
import projectWorkflowRouter from '../projectWorkflow';
import documentManagementRouter from '../documentManagement';
import clientCommunicationRouter from '../clientCommunication';
import digitalSignatureRouter from '../digitalSignature';
import fileOptimizationRouter from '../fileOptimization';
import aiCacheRouter from '../aiCache';
import contextAwareMappingRouter from '../contextAwareMapping';
import visualFeedbackRouter from '../visualFeedback';
import mcpServiceDiscoveryRouter from '../mcpServiceDiscovery';
import clientRouter from '../client';

const router = express.Router();

// Register all API routes
router.use('/teams', teamsRouter);
router.use('/team', teamDetailsRouter); // New router for team details
router.use('/projects', projectsRouter);
router.use('/columns', columnsRouter);
router.use('/tasks', tasksRouter);
router.use('/floorplans', floorplansRouter);
router.use('/quotes', quotesRouter);
router.use('/team-test', testTeamRouter);
router.use('/debug-team', debugTeamRouter); // Use a different path name to avoid conflicts
router.use('/ai', aiRouter);
router.use('/symbol-mapping', symbolMappingRouter);
router.use('/cost-calculation', costCalculationRouter);
router.use('/supplier-integration', supplierIntegrationRouter);
router.use('/price-updates', priceUpdatesRouter);
router.use('/project-workflow', projectWorkflowRouter);
router.use('/documents', documentManagementRouter);
router.use('/communication', clientCommunicationRouter);
router.use('/signatures', digitalSignatureRouter);
router.use('/file-optimization', fileOptimizationRouter);
router.use('/ai-cache', aiCacheRouter);
router.use('/context-mapping', contextAwareMappingRouter);
router.use('/visual-feedback', visualFeedbackRouter);
router.use('/mcp-discovery', mcpServiceDiscoveryRouter);
router.use('/client', clientRouter);

export default router;
