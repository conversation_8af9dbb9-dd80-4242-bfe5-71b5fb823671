import express, { Request, Response } from 'express';
import { ProjectService } from '../../services/serviceFactory';

const router = express.Router();

/**
 * Get all projects a user has access to
 * GET /api/projects
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    // Get the user ID from the authenticated user
    const userId = req.user?.id || 1; // Fallback to 1 for development
    
    const projects = await ProjectService.getProjects(userId);
    res.json(projects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ message: 'Failed to fetch projects', error: (error as Error).message });
  }
});

/**
 * Get projects for a specific team
 * GET /api/projects?teamId=1
 */
router.get('/team/:teamId', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.teamId);
    
    const projects = await ProjectService.getTeamProjects(teamId);
    res.json(projects);
  } catch (error) {
    console.error('Error fetching team projects:', error);
    res.status(500).json({ message: 'Failed to fetch team projects', error: (error as Error).message });
  }
});

/**
 * Get a specific project
 * GET /api/projects/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.params.id);
    
    const project = await ProjectService.getProject(projectId);
    
    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }
    
    res.json(project);
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).json({ message: 'Failed to fetch project', error: (error as Error).message });
  }
});

/**
 * Create a new project
 * POST /api/projects
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    // Get the user ID from the authenticated user
    const userId = req.user?.id || 1; // Fallback to 1 for development
    
    const { name, description, teamId, status } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'Project name is required' });
    }
    
    if (!teamId) {
      return res.status(400).json({ message: 'Team ID is required' });
    }
    
    // Validate status
    const validStatuses = ['planning', 'in_progress', 'completed', 'on_hold'];
    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({ 
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}` 
      });
    }
    
    const project = await ProjectService.createProject({
      name,
      description: description || '',
      teamId,
      createdById: userId,
      status: status || 'planning'
    });
    
    res.status(201).json(project);
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ message: 'Failed to create project', error: (error as Error).message });
  }
});

/**
 * Update a project
 * PUT /api/projects/:id
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.params.id);
    const { name, description, status } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'Project name is required' });
    }
    
    // Validate status
    const validStatuses = ['planning', 'in_progress', 'completed', 'on_hold'];
    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({ 
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}` 
      });
    }
    
    const project = await ProjectService.updateProject(projectId, {
      name,
      description,
      status
    });
    
    res.json(project);
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).json({ message: 'Failed to update project', error: (error as Error).message });
  }
});

/**
 * Delete a project
 * DELETE /api/projects/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.params.id);
    
    await ProjectService.deleteProject(projectId);
    
    res.status(204).end();
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ message: 'Failed to delete project', error: (error as Error).message });
  }
});

export default router;
