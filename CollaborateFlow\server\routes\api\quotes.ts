import express, { Request, Response } from 'express';
import { QuoteService } from '../../services/serviceFactory';

const router = express.Router();

/**
 * Get all quotes for a project
 * GET /api/quotes?projectId=1
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.query.projectId as string);
    
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    const quotes = await QuoteService.getQuotes(projectId);
    res.json(quotes);
  } catch (error) {
    console.error('Error fetching quotes:', error);
    res.status(500).json({ message: 'Failed to fetch quotes', error: (error as Error).message });
  }
});

/**
 * Get a specific quote
 * GET /api/quotes/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    
    const quote = await QuoteService.getQuote(quoteId);
    
    if (!quote) {
      return res.status(404).json({ message: 'Quote not found' });
    }
    
    res.json(quote);
  } catch (error) {
    console.error('Error fetching quote:', error);
    res.status(500).json({ message: 'Failed to fetch quote', error: (error as Error).message });
  }
});

/**
 * Create a new quote
 * POST /api/quotes
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    // Get the user ID from the authenticated user
    const userId = req.user?.id || 1; // Fallback to 1 for development
    
    const { 
      projectId, 
      name, 
      status, 
      totalMaterialCost, 
      totalLaborCost, 
      markupPercentage, 
      totalCost 
    } = req.body;
    
    // Validate required fields
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    if (!name) {
      return res.status(400).json({ message: 'Quote name is required' });
    }
    
    // Validate status
    const validStatuses = ['draft', 'pending', 'approved', 'rejected'];
    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({ 
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}` 
      });
    }
    
    // Validate numeric fields
    if (totalMaterialCost === undefined || totalLaborCost === undefined || 
        markupPercentage === undefined || totalCost === undefined) {
      return res.status(400).json({ 
        message: 'totalMaterialCost, totalLaborCost, markupPercentage, and totalCost are required' 
      });
    }
    
    const quote = await QuoteService.createQuote({
      projectId,
      name,
      status: status || 'draft',
      totalMaterialCost,
      totalLaborCost,
      markupPercentage,
      totalCost,
      createdById: userId
    });
    
    res.status(201).json(quote);
  } catch (error) {
    console.error('Error creating quote:', error);
    res.status(500).json({ message: 'Failed to create quote', error: (error as Error).message });
  }
});

/**
 * Update a quote
 * PUT /api/quotes/:id
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    const { 
      name, 
      status, 
      totalMaterialCost, 
      totalLaborCost, 
      markupPercentage, 
      totalCost 
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'Quote name is required' });
    }
    
    // Validate status
    const validStatuses = ['draft', 'pending', 'approved', 'rejected'];
    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({ 
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}` 
      });
    }
    
    const quote = await QuoteService.updateQuote(quoteId, {
      name,
      status,
      totalMaterialCost,
      totalLaborCost,
      markupPercentage,
      totalCost
    });
    
    res.json(quote);
  } catch (error) {
    console.error('Error updating quote:', error);
    res.status(500).json({ message: 'Failed to update quote', error: (error as Error).message });
  }
});

/**
 * Delete a quote
 * DELETE /api/quotes/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    
    await QuoteService.deleteQuote(quoteId);
    
    res.status(204).end();
  } catch (error) {
    console.error('Error deleting quote:', error);
    res.status(500).json({ message: 'Failed to delete quote', error: (error as Error).message });
  }
});

/**
 * Get materials for a quote
 * GET /api/quotes/:id/materials
 */
router.get('/:id/materials', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    
    const materials = await QuoteService.getMaterials(quoteId);
    res.json(materials);
  } catch (error) {
    console.error('Error fetching materials:', error);
    res.status(500).json({ message: 'Failed to fetch materials', error: (error as Error).message });
  }
});

/**
 * Add a material to a quote
 * POST /api/quotes/:id/materials
 */
router.post('/:id/materials', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    const { name, description, quantity, unitPrice, totalPrice } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'Material name is required' });
    }
    
    if (quantity === undefined || unitPrice === undefined || totalPrice === undefined) {
      return res.status(400).json({ 
        message: 'quantity, unitPrice, and totalPrice are required' 
      });
    }
    
    const material = await QuoteService.createMaterial({
      quoteId,
      name,
      description: description || '',
      quantity,
      unitPrice,
      totalPrice
    });
    
    res.status(201).json(material);
  } catch (error) {
    console.error('Error adding material:', error);
    res.status(500).json({ message: 'Failed to add material', error: (error as Error).message });
  }
});

/**
 * Bulk add materials to a quote
 * POST /api/quotes/:id/materials/bulk
 */
router.post('/:id/materials/bulk', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    const { materials } = req.body;
    
    // Validate required fields
    if (!materials || !Array.isArray(materials) || materials.length === 0) {
      return res.status(400).json({ message: 'Materials array is required' });
    }
    
    // Add quote ID to each material
    const materialsWithQuoteId = materials.map(material => ({
      ...material,
      quoteId
    }));
    
    const createdMaterials = await QuoteService.bulkCreateMaterials(materialsWithQuoteId);
    
    res.status(201).json(createdMaterials);
  } catch (error) {
    console.error('Error bulk adding materials:', error);
    res.status(500).json({ message: 'Failed to bulk add materials', error: (error as Error).message });
  }
});

/**
 * Get labor items for a quote
 * GET /api/quotes/:id/labor
 */
router.get('/:id/labor', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    
    const laborItems = await QuoteService.getLaborItems(quoteId);
    res.json(laborItems);
  } catch (error) {
    console.error('Error fetching labor items:', error);
    res.status(500).json({ message: 'Failed to fetch labor items', error: (error as Error).message });
  }
});

/**
 * Add a labor item to a quote
 * POST /api/quotes/:id/labor
 */
router.post('/:id/labor', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    const { description, hours, rate, totalPrice } = req.body;
    
    // Validate required fields
    if (!description) {
      return res.status(400).json({ message: 'Labor description is required' });
    }
    
    if (hours === undefined || rate === undefined || totalPrice === undefined) {
      return res.status(400).json({ 
        message: 'hours, rate, and totalPrice are required' 
      });
    }
    
    const labor = await QuoteService.createLaborItem({
      quoteId,
      description,
      hours,
      rate,
      totalPrice
    });
    
    res.status(201).json(labor);
  } catch (error) {
    console.error('Error adding labor item:', error);
    res.status(500).json({ message: 'Failed to add labor item', error: (error as Error).message });
  }
});

/**
 * Bulk add labor items to a quote
 * POST /api/quotes/:id/labor/bulk
 */
router.post('/:id/labor/bulk', async (req: Request, res: Response) => {
  try {
    const quoteId = parseInt(req.params.id);
    const { laborItems } = req.body;
    
    // Validate required fields
    if (!laborItems || !Array.isArray(laborItems) || laborItems.length === 0) {
      return res.status(400).json({ message: 'Labor items array is required' });
    }
    
    // Add quote ID to each labor item
    const laborItemsWithQuoteId = laborItems.map(item => ({
      ...item,
      quoteId
    }));
    
    const createdLaborItems = await QuoteService.bulkCreateLaborItems(laborItemsWithQuoteId);
    
    res.status(201).json(createdLaborItems);
  } catch (error) {
    console.error('Error bulk adding labor items:', error);
    res.status(500).json({ message: 'Failed to bulk add labor items', error: (error as Error).message });
  }
});

export default router;
