import express, { Request, Response } from 'express';
import { TaskService } from '../../services/serviceFactory';

const router = express.Router();

/**
 * Get tasks for a project
 * GET /api/tasks?projectId=1
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const projectId = parseInt(req.query.projectId as string);
    
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    const tasks = await TaskService.getTasks(projectId);
    res.json(tasks);
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({ message: 'Failed to fetch tasks', error: (error as Error).message });
  }
});

/**
 * Get tasks for a column
 * GET /api/tasks/column/:columnId
 */
router.get('/column/:columnId', async (req: Request, res: Response) => {
  try {
    const columnId = parseInt(req.params.columnId);
    
    const tasks = await TaskService.getColumnTasks(columnId);
    res.json(tasks);
  } catch (error) {
    console.error('Error fetching column tasks:', error);
    res.status(500).json({ message: 'Failed to fetch column tasks', error: (error as Error).message });
  }
});

/**
 * Get a specific task
 * GET /api/tasks/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const taskId = parseInt(req.params.id);
    
    const task = await TaskService.getTask(taskId);
    
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }
    
    res.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({ message: 'Failed to fetch task', error: (error as Error).message });
  }
});

/**
 * Create a new task
 * POST /api/tasks
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const { title, description, columnId, projectId, assigneeId, order } = req.body;
    
    // Validate required fields
    if (!title) {
      return res.status(400).json({ message: 'Task title is required' });
    }
    
    if (!columnId) {
      return res.status(400).json({ message: 'Column ID is required' });
    }
    
    if (!projectId) {
      return res.status(400).json({ message: 'Project ID is required' });
    }
    
    if (order === undefined || order === null) {
      return res.status(400).json({ message: 'Order is required' });
    }
    
    const task = await TaskService.createTask({
      title,
      description: description || '',
      columnId,
      projectId,
      assigneeId: assigneeId || null,
      order
    });
    
    res.status(201).json(task);
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({ message: 'Failed to create task', error: (error as Error).message });
  }
});

/**
 * Update a task
 * PUT /api/tasks/:id
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const taskId = parseInt(req.params.id);
    const { title, description, assigneeId } = req.body;
    
    // Validate required fields
    if (!title) {
      return res.status(400).json({ message: 'Task title is required' });
    }
    
    const task = await TaskService.updateTask(taskId, {
      title,
      description,
      assigneeId
    });
    
    res.json(task);
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ message: 'Failed to update task', error: (error as Error).message });
  }
});

/**
 * Move a task to a different column or position
 * PUT /api/tasks/:id/move
 */
router.put('/:id/move', async (req: Request, res: Response) => {
  try {
    const taskId = parseInt(req.params.id);
    const { columnId, order } = req.body;
    
    // Validate required fields
    if (!columnId) {
      return res.status(400).json({ message: 'Column ID is required' });
    }
    
    if (order === undefined || order === null) {
      return res.status(400).json({ message: 'Order is required' });
    }
    
    const task = await TaskService.moveTask(taskId, columnId, order);
    
    res.json(task);
  } catch (error) {
    console.error('Error moving task:', error);
    res.status(500).json({ message: 'Failed to move task', error: (error as Error).message });
  }
});

/**
 * Delete a task
 * DELETE /api/tasks/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const taskId = parseInt(req.params.id);
    
    await TaskService.deleteTask(taskId);
    
    res.status(204).end();
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({ message: 'Failed to delete task', error: (error as Error).message });
  }
});

export default router;
