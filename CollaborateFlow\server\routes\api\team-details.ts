import express, { Request, Response } from 'express';
import * as TeamService from '../../services/teamService';
import { supabase } from '../../supabase';
import { storage } from '../../storage';

const router = express.Router();

// Middleware to handle authentication
router.use((req, res, next) => {
  // For development, allow requests without authentication
  if (process.env.NODE_ENV === 'development' && !req.user) {
    console.log('Development mode: Auto-authenticating request');
    // Use a default admin user for development
    req.user = {
      id: 1,  // Super admin user
      role: 'super_admin',
      organization_id: 1
    } as any;
  }
  next();
});

/**
 * Get details for a specific team
 * GET /api/teams/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);
    if (isNaN(teamId)) {
      return res.status(400).json({ error: 'Invalid team ID' });
    }
    
    console.log(`Fetching details for team ID: ${teamId}`);
    
    // Get the team details from the database with joined organization and creator info
    const { data: team, error } = await supabase
      .from('teams')
      .select(`
        *,
        organizations:organization_id(id, name),
        creator:created_by_id(id, username, full_name)
      `)
      .eq('id', teamId)
      .single();
    
    if (error) {
      console.error('Error fetching team details:', error);
      return res.status(500).json({ error: error.message });
    }
    
    if (!team) {
      return res.status(404).json({ error: 'Team not found' });
    }
    
    // Format the response with properly named fields and include related data
    const formattedTeam = {
      id: team.id,
      name: team.name,
      description: team.description,
      organization_id: team.organization_id,
      organization_name: team.organizations?.name,
      created_by_id: team.created_by_id,
      created_by_name: team.creator?.full_name || team.creator?.username,
      created_at: team.created_at,
      updated_at: team.updated_at,
      // Include the raw joined data for additional fields
      organizations: team.organizations,
      creator: team.creator
    };
    
    console.log('Successfully fetched team details:', formattedTeam);
    res.json(formattedTeam);
  } catch (error) {
    console.error('Error in GET /api/teams/:id:', error);
    res.status(500).json({ message: 'Failed to fetch team details', error: (error as Error).message });
  }
});

/**
 * Get members of a specific team
 * GET /api/teams/:id/members
 */
router.get('/:id/members', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);
    if (isNaN(teamId)) {
      return res.status(400).json({ error: 'Invalid team ID' });
    }
    
    console.log(`Fetching members for team ID: ${teamId}`);
    
    // Get the team members including user details
    const { data: members, error } = await supabase
      .from('team_members')
      .select(`
        *,
        users:user_id(id, username, email, full_name, avatar_url, role)
      `)
      .eq('team_id', teamId);
    
    console.log('Raw team members data:', members);
    
    if (error) {
      console.error('Error fetching team members:', error);
      return res.status(500).json({ error: error.message });
    }
    
    // Format the response with flattened user properties
    const formattedMembers = members.map(member => ({
      id: member.id,
      team_id: member.team_id,
      user_id: member.user_id,
      role: member.role,
      name: member.users?.full_name,
      email: member.users?.email,
      avatar_url: member.users?.avatar_url
    }));
    
    console.log(`Found ${formattedMembers.length} members for team ${teamId}`);
    res.json(formattedMembers);
  } catch (error) {
    console.error('Error in GET /api/teams/:id/members:', error);
    res.status(500).json({ message: 'Failed to fetch team members', error: (error as Error).message });
  }
});

/**
 * Update a team's details
 * PATCH /api/teams/:id
 */
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);
    if (isNaN(teamId)) {
      return res.status(400).json({ error: 'Invalid team ID' });
    }
    
    const { name, description } = req.body;
    if (!name) {
      return res.status(400).json({ error: 'Team name is required' });
    }
    
    console.log(`Updating team ${teamId} with name: ${name}, description: ${description || ''}`);
    
    // Get the user ID from the authenticated user
    const userId = req.user?.id || 1; // Fallback to 1 for development
    
    // Check if the user is an admin of the team
    const { data: memberCheck, error: memberError } = await supabase
      .from('team_members')
      .select('role')
      .eq('team_id', teamId)
      .eq('user_id', userId)
      .single();
    
    if (memberError && memberError.code !== 'PGRST116') { // Not found error
      console.error('Error checking team membership:', memberError);
      return res.status(500).json({ error: memberError.message });
    }
    
    // If user is not a team admin or super_admin, deny access
    if (!memberCheck || (memberCheck.role !== 'admin' && memberCheck.role !== 'super_admin')) {
      return res.status(403).json({ error: 'You do not have permission to update this team' });
    }
    
    // Update the team details
    const { data: team, error } = await supabase
      .from('teams')
      .update({
        name,
        description: description || '',
        updated_at: new Date().toISOString()
      })
      .eq('id', teamId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating team:', error);
      return res.status(500).json({ error: error.message });
    }
    
    console.log('Successfully updated team:', team);
    res.json(team);
  } catch (error) {
    console.error('Error in PATCH /api/teams/:id:', error);
    res.status(500).json({ message: 'Failed to update team', error: (error as Error).message });
  }
});

export default router;
