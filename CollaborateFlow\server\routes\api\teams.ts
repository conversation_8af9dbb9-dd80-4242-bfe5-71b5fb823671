import express, { Request, Response } from 'express';
import * as TeamService from '../../services/teamService';
import { supabase } from '../../db';
import CacheService from '../../services/cacheService';

const router = express.Router();

// Initialize cache service
const getCache = (organizationId: string) => new CacheService(organizationId || '1');

/**
 * Get all teams a user belongs to
 * GET /api/teams
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    // Get the user ID from the authenticated user
    const userId = req.user?.id || 1; // Fallback to 1 for development
    const organizationId = req.headers['x-organization-id'] as string || '1';

    const cache = getCache(organizationId);
    const cacheKey = `teams:user:${userId}`;

    // Try to get from cache first
    const cachedTeams = await cache.getCachedApiResponse('GET /api/teams', { userId });
    if (cachedTeams) {
      return res.json(cachedTeams);
    }

    const teams = await TeamService.getTeams(userId);

    // Cache the result for 5 minutes
    await cache.cacheApiResponse('GET /api/teams', { userId }, teams, 300);

    res.json(teams);
  } catch (error) {
    console.error('Error fetching teams:', error);
    res.status(500).json({ message: 'Failed to fetch teams', error: (error as Error).message });
  }
});

/**
 * Get a specific team
 * GET /api/teams/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);

    const team = await TeamService.getTeam(teamId);

    if (!team) {
      return res.status(404).json({ message: 'Team not found' });
    }

    res.json(team);
  } catch (error) {
    console.error('Error fetching team:', error);
    res.status(500).json({ message: 'Failed to fetch team', error: (error as Error).message });
  }
});

/**
 * Create a new team
 * POST /api/teams
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    console.log('Team creation request received with body:', req.body);

    // Get the user ID from the authenticated user
    const userId = req.user?.id || 1; // Fallback to 1 for development
    const { name, description, organization_id } = req.body;

    // Validate required fields
    if (!name) {
      console.log('Team name is required');
      return res.status(400).json({ error: 'Team name is required' });
    }

    // Create a clean team object with snake_case fields
    const teamData = {
      name,
      description: description || '',
      created_by_id: Number(userId),
      organization_id: Number(organization_id) || 1, // Default to org ID 1 if not provided
      created_at: new Date().toISOString()
    };

    console.log('Prepared team data:', teamData);

    // Direct database call with supabase client
    const { data: team, error } = await supabase
      .from('teams')
      .insert(teamData)
      .select()
      .single();

    if (error) {
      console.error('Error creating team:', error);
      return res.status(500).json({ error: error.message });
    }

    console.log('Successfully created team:', team);

    // Add the creator as a team admin - using direct SQL approach to avoid field name issues
    const teamId = team.id;
    // Use the same userId we already defined above

    console.log(`Adding user ${userId} as admin to team ${teamId}`);

    // Use a raw SQL query to avoid field name mismatches
    const { data: membership, error: membershipError } = await supabase
      .rpc('add_team_member', {
        p_team_id: teamId,
        p_user_id: userId,
        p_role: 'admin'
      });

    if (membershipError) {
      // Try direct insert as fallback
      console.log('RPC failed, trying direct insert');
      const { data: directMembership, error: directError } = await supabase
        .from('team_members')
        .insert({
          team_id: teamId,
          user_id: userId,
          role: 'admin'
        })
        .select();

      if (directError) {
        console.error('Direct insert also failed:', directError);
      } else {
        console.log('Direct insert succeeded:', directMembership);
        return res.status(201).json(team);
      }
    }

    if (membershipError) {
      console.error('Error adding team member:', membershipError);
      // Don't fail the request if adding the member fails
    } else {
      console.log('Added user as team admin:', membership);
    }

    // Invalidate cache for teams list
    const organizationId = req.headers['x-organization-id'] as string || '1';
    const cache = getCache(organizationId);
    await cache.invalidatePattern('teams:');

    // Return the created team
    res.status(201).json(team);
  } catch (error) {
    console.error('Error in team creation endpoint:', error);
    res.status(500).json({
      message: 'Failed to create team',
      error: (error as Error).message,
      stack: process.env.NODE_ENV === 'development' ? (error as Error).stack : undefined
    });
  }
});

/**
 * Update a team
 * PUT /api/teams/:id
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);
    const { name, description } = req.body;

    // Validate team ID
    if (isNaN(teamId)) {
      return res.status(400).json({ message: 'Invalid team ID' });
    }

    // Validate required fields
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({ message: 'Team name is required and must be a non-empty string' });
    }

    if (name.length > 100) {
      return res.status(400).json({ message: 'Team name must be less than 100 characters' });
    }

    if (description && typeof description !== 'string') {
      return res.status(400).json({ message: 'Description must be a string' });
    }

    if (description && description.length > 500) {
      return res.status(400).json({ message: 'Description must be less than 500 characters' });
    }

    // Check if team exists and user has permission
    const existingTeam = await TeamService.getTeam(teamId);
    if (!existingTeam) {
      return res.status(404).json({ message: 'Team not found' });
    }

    const team = await TeamService.updateTeam(teamId, {
      name: name.trim(),
      description: description?.trim() || ''
    });

    res.json(team);
  } catch (error) {
    console.error('Error updating team:', error);
    res.status(500).json({ message: 'Failed to update team', error: (error as Error).message });
  }
});

/**
 * Delete a team
 * DELETE /api/teams/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);

    // Validate team ID
    if (isNaN(teamId)) {
      return res.status(400).json({ message: 'Invalid team ID' });
    }

    // Check if team exists
    const existingTeam = await TeamService.getTeam(teamId);
    if (!existingTeam) {
      return res.status(404).json({ message: 'Team not found' });
    }

    // Check for dependent resources (projects, members)
    const teamMembers = await TeamService.getTeamMembers(teamId);
    if (teamMembers && teamMembers.length > 1) {
      return res.status(409).json({
        message: 'Cannot delete team with multiple members. Remove members first or transfer ownership.',
        memberCount: teamMembers.length
      });
    }

    await TeamService.deleteTeam(teamId);

    res.status(204).end();
  } catch (error) {
    console.error('Error deleting team:', error);
    if (error instanceof Error && error.message.includes('foreign key')) {
      res.status(409).json({
        message: 'Cannot delete team. It has associated projects or members that must be removed first.',
        error: 'Dependency conflict'
      });
    } else {
      res.status(500).json({ message: 'Failed to delete team', error: (error as Error).message });
    }
  }
});

/**
 * Get team members
 * GET /api/teams/:id/members
 */
router.get('/:id/members', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);

    const members = await TeamService.getTeamMembers(teamId);

    res.json(members);
  } catch (error) {
    console.error('Error fetching team members:', error);
    res.status(500).json({ message: 'Failed to fetch team members', error: (error as Error).message });
  }
});

/**
 * Add a team member
 * POST /api/teams/:id/members
 */
router.post('/:id/members', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);
    const { userId, role } = req.body;

    // Validate required fields
    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    if (!role || !['admin', 'member', 'viewer'].includes(role)) {
      return res.status(400).json({ message: 'Valid role is required (admin, member, or viewer)' });
    }

    const member = await TeamService.addTeamMember({
      teamId,
      userId,
      role
    });

    res.status(201).json(member);
  } catch (error) {
    console.error('Error adding team member:', error);
    res.status(500).json({ message: 'Failed to add team member', error: (error as Error).message });
  }
});

/**
 * Remove a team member
 * DELETE /api/teams/:id/members/:userId
 */
router.delete('/:id/members/:userId', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);
    const userId = parseInt(req.params.userId);

    await TeamService.removeTeamMember(teamId, userId);

    res.status(204).end();
  } catch (error) {
    console.error('Error removing team member:', error);
    res.status(500).json({ message: 'Failed to remove team member', error: (error as Error).message });
  }
});

/**
 * Update a team member's role
 * PUT /api/teams/:id/members/:userId
 */
router.put('/:id/members/:userId', async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.id);
    const userId = parseInt(req.params.userId);
    const { role } = req.body;

    // Validate required fields
    if (!role || !['admin', 'member', 'viewer'].includes(role)) {
      return res.status(400).json({ message: 'Valid role is required (admin, member, or viewer)' });
    }

    const member = await TeamService.updateTeamMemberRole(teamId, userId, role);

    res.json(member);
  } catch (error) {
    console.error('Error updating team member role:', error);
    res.status(500).json({ message: 'Failed to update team member role', error: (error as Error).message });
  }
});

export default router;
