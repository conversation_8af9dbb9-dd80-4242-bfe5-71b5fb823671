import { Router } from 'express';
import { supabase } from '../../db';

// Import the logger appropriately based on the project structure
const log = console.log;

const router = Router();

router.post('/', async (req, res) => {
  try {
    const { name, description } = req.body;
    
    log(`Test endpoint: Creating team ${name} directly via Supabase client`);
    
    // Create a minimal team object with hardcoded values for testing
    const teamData = {
      name: name || 'Test Team',
      description: description || 'Test team description',
      created_by_id: 1, // Hardcoded for testing
      organization_id: 1, // Hardcoded for testing
      created_at: new Date().toISOString()
    };
    
    log(`Test endpoint: Using team data: ${JSON.stringify(teamData)}`);
    
    // Insert the team with explicit values
    const { data, error } = await supabase
      .from('teams')
      .insert(teamData)
      .select()
      .single();
    
    if (error) {
      log(`Test endpoint ERROR: ${error.message}`);
      return res.status(500).json({ error: error.message });
    }
    
    log(`Test endpoint: Successfully created team with ID ${data.id}`);
    
    // Return the created team
    return res.status(201).json(data);
  } catch (error: any) {
    log(`Test endpoint ERROR: ${error.message}`);
    return res.status(500).json({ error: error.message });
  }
});

export default router;
