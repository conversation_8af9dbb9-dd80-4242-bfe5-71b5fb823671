/**
 * CLIENT API ROUTES
 * Handles client portal operations including authentication, quotes, and change requests
 */

import { Router } from "express";
import { supabase } from "../supabase";
import crypto from "crypto";

const router = Router();

// =============================================================================
// CLIENT AUTHENTICATION
// =============================================================================

/**
 * POST /api/client/auth
 * Authenticate client with access token
 */
router.post("/auth", async (req, res) => {
  try {
    const { client_id, access_token } = req.body;

    if (!client_id || !access_token) {
      return res.status(400).json({ 
        success: false,
        error: "Client ID and access token are required" 
      });
    }

    // Verify client access token
    const { data: clientAuth, error: authError } = await supabase
      .from('client_access_tokens')
      .select(`
        *,
        client_profiles(*)
      `)
      .eq('client_id', client_id)
      .eq('access_token', access_token)
      .eq('is_active', true)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (authError || !clientAuth) {
      return res.status(401).json({
        success: false,
        error: "Invalid or expired access token"
      });
    }

    // Update last accessed
    await supabase
      .from('client_access_tokens')
      .update({ 
        last_accessed: new Date().toISOString(),
        access_count: supabase.rpc('increment_access_count')
      })
      .eq('id', clientAuth.id);

    res.status(200).json({
      success: true,
      message: "Authentication successful",
      client: {
        id: clientAuth.client_profiles.id,
        name: clientAuth.client_profiles.name,
        email: clientAuth.client_profiles.email,
        company: clientAuth.client_profiles.company,
        phone: clientAuth.client_profiles.phone,
        address: clientAuth.client_profiles.address
      },
      session_token: clientAuth.access_token
    });

  } catch (error) {
    console.error("Client authentication error:", error);
    res.status(500).json({
      success: false,
      error: "Authentication failed",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * POST /api/client/logout
 * Logout client and invalidate session
 */
router.post("/logout", async (req, res) => {
  try {
    const { session_token } = req.body;

    if (session_token) {
      // Update token to mark as logged out
      await supabase
        .from('client_access_tokens')
        .update({ 
          last_accessed: new Date().toISOString(),
          is_active: false
        })
        .eq('access_token', session_token);
    }

    res.status(200).json({
      success: true,
      message: "Logged out successfully"
    });

  } catch (error) {
    console.error("Client logout error:", error);
    res.status(500).json({
      success: false,
      error: "Logout failed"
    });
  }
});

// =============================================================================
// CLIENT QUOTES
// =============================================================================

/**
 * GET /api/client/quotes
 * Get all quotes for authenticated client
 */
router.get("/quotes", async (req, res) => {
  try {
    const clientId = req.headers['x-client-id'] as string;
    const sessionToken = req.headers['x-session-token'] as string;

    if (!clientId || !sessionToken) {
      return res.status(401).json({ 
        success: false,
        error: "Client authentication required" 
      });
    }

    // Verify session
    const { data: session, error: sessionError } = await supabase
      .from('client_access_tokens')
      .select('*')
      .eq('client_id', clientId)
      .eq('access_token', sessionToken)
      .eq('is_active', true)
      .single();

    if (sessionError || !session) {
      return res.status(401).json({
        success: false,
        error: "Invalid session"
      });
    }

    // Get quotes for client
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select(`
        *,
        quote_materials(*),
        quote_labor_items(*),
        quote_feedback(*)
      `)
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });

    if (quotesError) {
      throw new Error(`Failed to fetch quotes: ${quotesError.message}`);
    }

    res.status(200).json({
      success: true,
      quotes: quotes || [],
      total: quotes?.length || 0
    });

  } catch (error) {
    console.error("Error fetching client quotes:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch quotes",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * GET /api/client/quotes/:id
 * Get specific quote details for client
 */
router.get("/quotes/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const clientId = req.headers['x-client-id'] as string;
    const sessionToken = req.headers['x-session-token'] as string;

    if (!clientId || !sessionToken) {
      return res.status(401).json({ 
        success: false,
        error: "Client authentication required" 
      });
    }

    // Get quote with all related data
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select(`
        *,
        quote_materials(*),
        quote_labor_items(*),
        quote_feedback(*),
        projects(name, description)
      `)
      .eq('id', id)
      .eq('client_id', clientId)
      .single();

    if (quoteError || !quote) {
      return res.status(404).json({
        success: false,
        error: "Quote not found"
      });
    }

    res.status(200).json({
      success: true,
      quote
    });

  } catch (error) {
    console.error("Error fetching quote:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch quote",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * POST /api/client/quotes/:id/approve
 * Approve a quote with digital signature
 */
router.post("/quotes/:id/approve", async (req, res) => {
  try {
    const { id } = req.params;
    const { signature, approval_notes } = req.body;
    const clientId = req.headers['x-client-id'] as string;
    const sessionToken = req.headers['x-session-token'] as string;

    if (!clientId || !sessionToken) {
      return res.status(401).json({ 
        success: false,
        error: "Client authentication required" 
      });
    }

    if (!signature) {
      return res.status(400).json({
        success: false,
        error: "Digital signature is required for approval"
      });
    }

    // Verify quote belongs to client
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', id)
      .eq('client_id', clientId)
      .single();

    if (quoteError || !quote) {
      return res.status(404).json({
        success: false,
        error: "Quote not found"
      });
    }

    if (quote.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: "Quote is not in pending status"
      });
    }

    // Update quote status and add signature
    const { error: updateError } = await supabase
      .from('quotes')
      .update({
        status: 'approved',
        approved_at: new Date().toISOString(),
        client_signature: signature,
        approval_notes: approval_notes || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (updateError) {
      throw new Error(`Failed to approve quote: ${updateError.message}`);
    }

    // Log approval activity
    await supabase
      .from('client_activity_log')
      .insert({
        client_id: clientId,
        activity_type: 'quote_approved',
        activity_description: `Quote ${quote.number} approved`,
        related_entity_type: 'quote',
        related_entity_id: id,
        activity_metadata: {
          quote_number: quote.number,
          approval_method: 'digital_signature'
        }
      });

    res.status(200).json({
      success: true,
      message: "Quote approved successfully",
      quote_id: id,
      status: 'approved'
    });

  } catch (error) {
    console.error("Error approving quote:", error);
    res.status(500).json({
      success: false,
      error: "Failed to approve quote",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * POST /api/client/quotes/:id/reject
 * Reject a quote with reason
 */
router.post("/quotes/:id/reject", async (req, res) => {
  try {
    const { id } = req.params;
    const { rejection_reason } = req.body;
    const clientId = req.headers['x-client-id'] as string;
    const sessionToken = req.headers['x-session-token'] as string;

    if (!clientId || !sessionToken) {
      return res.status(401).json({ 
        success: false,
        error: "Client authentication required" 
      });
    }

    if (!rejection_reason) {
      return res.status(400).json({
        success: false,
        error: "Rejection reason is required"
      });
    }

    // Verify quote belongs to client
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', id)
      .eq('client_id', clientId)
      .single();

    if (quoteError || !quote) {
      return res.status(404).json({
        success: false,
        error: "Quote not found"
      });
    }

    if (quote.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: "Quote is not in pending status"
      });
    }

    // Update quote status
    const { error: updateError } = await supabase
      .from('quotes')
      .update({
        status: 'rejected',
        rejected_at: new Date().toISOString(),
        rejection_reason: rejection_reason,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (updateError) {
      throw new Error(`Failed to reject quote: ${updateError.message}`);
    }

    // Log rejection activity
    await supabase
      .from('client_activity_log')
      .insert({
        client_id: clientId,
        activity_type: 'quote_rejected',
        activity_description: `Quote ${quote.number} rejected`,
        related_entity_type: 'quote',
        related_entity_id: id,
        activity_metadata: {
          quote_number: quote.number,
          rejection_reason: rejection_reason
        }
      });

    res.status(200).json({
      success: true,
      message: "Quote rejected successfully",
      quote_id: id,
      status: 'rejected'
    });

  } catch (error) {
    console.error("Error rejecting quote:", error);
    res.status(500).json({
      success: false,
      error: "Failed to reject quote",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// =============================================================================
// CHANGE REQUESTS
// =============================================================================

/**
 * GET /api/client/change-requests
 * Get all change requests for client
 */
router.get("/change-requests", async (req, res) => {
  try {
    const clientId = req.headers['x-client-id'] as string;
    const sessionToken = req.headers['x-session-token'] as string;

    if (!clientId || !sessionToken) {
      return res.status(401).json({ 
        success: false,
        error: "Client authentication required" 
      });
    }

    // Get change requests for client
    const { data: changeRequests, error } = await supabase
      .from('quote_change_requests')
      .select(`
        *,
        quotes(number, title)
      `)
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch change requests: ${error.message}`);
    }

    res.status(200).json({
      success: true,
      change_requests: changeRequests || [],
      total: changeRequests?.length || 0
    });

  } catch (error) {
    console.error("Error fetching change requests:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch change requests",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * POST /api/client/change-requests
 * Submit a new change request
 */
router.post("/change-requests", async (req, res) => {
  try {
    const { quote_id, title, description, priority } = req.body;
    const clientId = req.headers['x-client-id'] as string;
    const sessionToken = req.headers['x-session-token'] as string;

    if (!clientId || !sessionToken) {
      return res.status(401).json({ 
        success: false,
        error: "Client authentication required" 
      });
    }

    if (!quote_id || !title || !description) {
      return res.status(400).json({
        success: false,
        error: "Quote ID, title, and description are required"
      });
    }

    // Verify quote belongs to client
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quote_id)
      .eq('client_id', clientId)
      .single();

    if (quoteError || !quote) {
      return res.status(404).json({
        success: false,
        error: "Quote not found"
      });
    }

    // Create change request
    const { data: changeRequest, error: createError } = await supabase
      .from('quote_change_requests')
      .insert({
        quote_id: quote_id,
        client_id: clientId,
        title: title,
        description: description,
        priority: priority || 'medium',
        status: 'pending',
        request_number: `CR-${Date.now()}`,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      throw new Error(`Failed to create change request: ${createError.message}`);
    }

    // Log activity
    await supabase
      .from('client_activity_log')
      .insert({
        client_id: clientId,
        activity_type: 'change_request_submitted',
        activity_description: `Change request "${title}" submitted for quote ${quote.number}`,
        related_entity_type: 'change_request',
        related_entity_id: changeRequest.id,
        activity_metadata: {
          quote_id: quote_id,
          quote_number: quote.number,
          request_title: title
        }
      });

    res.status(201).json({
      success: true,
      message: "Change request submitted successfully",
      change_request: changeRequest
    });

  } catch (error) {
    console.error("Error creating change request:", error);
    res.status(500).json({
      success: false,
      error: "Failed to submit change request",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// =============================================================================
// CLIENT PROFILE
// =============================================================================

/**
 * GET /api/client/profile
 * Get client profile information
 */
router.get("/profile", async (req, res) => {
  try {
    const clientId = req.headers['x-client-id'] as string;
    const sessionToken = req.headers['x-session-token'] as string;

    if (!clientId || !sessionToken) {
      return res.status(401).json({ 
        success: false,
        error: "Client authentication required" 
      });
    }

    // Get client profile
    const { data: profile, error } = await supabase
      .from('client_profiles')
      .select('*')
      .eq('id', clientId)
      .single();

    if (error || !profile) {
      return res.status(404).json({
        success: false,
        error: "Client profile not found"
      });
    }

    res.status(200).json({
      success: true,
      profile: {
        id: profile.id,
        name: profile.name,
        email: profile.email,
        company: profile.company,
        phone: profile.phone,
        address: profile.address,
        created_at: profile.created_at
      }
    });

  } catch (error) {
    console.error("Error fetching client profile:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch profile",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

export default router;
