/**
 * Digital Signature API Routes
 *
 * Handles digital signature operations including:
 * - Signature request creation and management
 * - Signer management and workflow
 * - Template-based signature workflows
 * - Provider configuration and integration
 * - Webhook handling for status updates
 * - Signature analytics and reporting
 */

import { Router } from "express";
import { DigitalSignatureService } from "../services/digitalSignatureService";
import { getFeatureFlag } from "../services/featureFlags";
import SignatureWorkflowEngine from "../services/signatureWorkflowEngine";

const router = Router();

// Initialize digital signature service
function getDigitalSignatureService(organizationId: string): DigitalSignatureService {
  const supabaseUrl = process.env.SUPABASE_URL!;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  return new DigitalSignatureService(supabaseUrl, supabaseKey, organizationId);
}

// Create signature request
router.post("/requests", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const requestData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    if (!requestData.request_title) {
      return res.status(400).json({ error: "Request title is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const requestId = await signatureService.createSignatureRequest(requestData);

    if (requestId) {
      res.status(201).json({
        success: true,
        message: "Signature request created successfully",
        request_id: requestId
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to create signature request"
      });
    }
  } catch (error) {
    console.error("Signature request creation error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to create signature request"
    });
  }
});

// Get signature requests
router.get("/requests", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { project_id, request_status, limit = 50, offset = 0 } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const result = await signatureService.getSignatureRequests({
      project_id: project_id as string,
      request_status: request_status as string,
      limit: parseInt(limit.toString()),
      offset: parseInt(offset.toString())
    });

    res.status(200).json({
      success: true,
      requests: result.requests,
      pagination: {
        total: result.total,
        limit: parseInt(limit.toString()),
        offset: parseInt(offset.toString()),
        hasMore: result.total > parseInt(offset.toString()) + parseInt(limit.toString())
      }
    });
  } catch (error) {
    console.error("Signature requests fetch error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch signature requests"
    });
  }
});

// Get signature request by ID
router.get("/requests/:requestId", async (req, res) => {
  try {
    const { requestId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const request = await signatureService.getSignatureRequestById(requestId);

    if (request) {
      res.status(200).json({
        success: true,
        request
      });
    } else {
      res.status(404).json({
        success: false,
        message: "Signature request not found"
      });
    }
  } catch (error) {
    console.error("Signature request fetch error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch signature request"
    });
  }
});

// Add signers to signature request
router.post("/requests/:requestId/signers", async (req, res) => {
  try {
    const { requestId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    const { signers } = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    if (!signers || !Array.isArray(signers) || signers.length === 0) {
      return res.status(400).json({ error: "Signers array is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const success = await signatureService.addSigners(requestId, signers);

    if (success) {
      res.status(201).json({
        success: true,
        message: "Signers added successfully"
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to add signers"
      });
    }
  } catch (error) {
    console.error("Add signers error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to add signers"
    });
  }
});

// Send signature request
router.post("/requests/:requestId/send", async (req, res) => {
  try {
    const { requestId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const success = await signatureService.sendSignatureRequest(requestId);

    if (success) {
      res.status(200).json({
        success: true,
        message: "Signature request sent successfully"
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to send signature request"
      });
    }
  } catch (error) {
    console.error("Send signature request error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to send signature request"
    });
  }
});

// Webhook endpoint for signature status updates
router.post("/webhook/:requestId", async (req, res) => {
  try {
    const { requestId } = req.params;
    const { signer_email, status, metadata } = req.body;

    // Note: In production, verify webhook signature/authentication

    if (!signer_email || !status) {
      return res.status(400).json({ error: "Signer email and status are required" });
    }

    // Extract organization ID from request metadata or lookup
    const organizationId = metadata?.organization_id || req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const success = await signatureService.updateSignerStatus(requestId, signer_email, status, metadata);

    if (success) {
      res.status(200).json({
        success: true,
        message: "Signer status updated successfully"
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to update signer status"
      });
    }
  } catch (error) {
    console.error("Webhook processing error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to process webhook"
    });
  }
});

// Create signature template
router.post("/templates", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const templateData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    if (!templateData.template_name || !templateData.document_type || !templateData.signature_fields) {
      return res.status(400).json({
        error: "Template name, document type, and signature fields are required"
      });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const templateId = await signatureService.createSignatureTemplate(templateData);

    if (templateId) {
      res.status(201).json({
        success: true,
        message: "Signature template created successfully",
        template_id: templateId
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to create signature template"
      });
    }
  } catch (error) {
    console.error("Template creation error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to create signature template"
    });
  }
});

// Get signature templates
router.get("/templates", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { document_type } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const templates = await signatureService.getSignatureTemplates(document_type as string);

    res.status(200).json({
      success: true,
      templates,
      total: templates.length
    });
  } catch (error) {
    console.error("Templates fetch error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch signature templates"
    });
  }
});

// Configure signature provider
router.post("/providers", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const providerData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    if (!providerData.provider_name || !providerData.provider_display_name || !providerData.provider_config) {
      return res.status(400).json({
        error: "Provider name, display name, and configuration are required"
      });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const providerId = await signatureService.configureProvider(providerData);

    if (providerId) {
      res.status(201).json({
        success: true,
        message: "Signature provider configured successfully",
        provider_id: providerId
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to configure signature provider"
      });
    }
  } catch (error) {
    console.error("Provider configuration error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to configure signature provider"
    });
  }
});

// Get signature analytics
router.get("/analytics", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { timeframe } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const signatureService = getDigitalSignatureService(organizationId);
    const analytics = await signatureService.getSignatureAnalytics(timeframe as string);

    res.status(200).json({
      success: true,
      analytics,
      timeframe: timeframe || 'all_time'
    });
  } catch (error) {
    console.error("Signature analytics error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch signature analytics"
    });
  }
});

// =============================================================================
// SIGNATURE WORKFLOW ENGINE ROUTES - T2.1 IMPLEMENTATION
// =============================================================================

// Execute quote approval workflow
router.post("/workflows/quote-approval", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const workflowData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    if (!workflowData.quote_id || !workflowData.client_contact || !workflowData.quote_details) {
      return res.status(400).json({
        error: "Quote ID, client contact, and quote details are required"
      });
    }

    const workflowEngine = new SignatureWorkflowEngine(organizationId);
    const execution = await workflowEngine.executeQuoteApprovalWorkflow(workflowData);

    res.status(201).json({
      success: true,
      message: "Quote approval workflow started successfully",
      execution_id: execution.id,
      signature_request_id: execution.signature_request_id,
      execution
    });
  } catch (error) {
    console.error("Quote approval workflow error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to start quote approval workflow"
    });
  }
});

// Get workflow execution status
router.get("/workflows/executions/:executionId", async (req, res) => {
  try {
    const { executionId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const workflowEngine = new SignatureWorkflowEngine(organizationId);
    const execution = await workflowEngine.getWorkflowStatus(executionId);

    if (execution) {
      res.status(200).json({
        success: true,
        execution
      });
    } else {
      res.status(404).json({
        success: false,
        message: "Workflow execution not found"
      });
    }
  } catch (error) {
    console.error("Workflow status error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to get workflow status"
    });
  }
});

// Cancel workflow execution
router.post("/workflows/executions/:executionId/cancel", async (req, res) => {
  try {
    const { executionId } = req.params;
    const { reason } = req.body;
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const workflowEngine = new SignatureWorkflowEngine(organizationId);
    const success = await workflowEngine.cancelWorkflow(executionId, reason || 'Cancelled by user');

    if (success) {
      res.status(200).json({
        success: true,
        message: "Workflow cancelled successfully"
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to cancel workflow"
      });
    }
  } catch (error) {
    console.error("Workflow cancellation error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to cancel workflow"
    });
  }
});

// DocuSign webhook endpoint
router.post("/webhooks/docusign", async (req, res) => {
  try {
    const webhookData = req.body;
    const signature = req.headers['x-docusign-signature-1'] as string;

    // Verify webhook signature in production
    if (process.env.NODE_ENV === 'production' && !signature) {
      return res.status(401).json({ error: "Invalid webhook signature" });
    }

    console.log('📨 DocuSign webhook received:', {
      event: webhookData.event,
      envelopeId: webhookData.data?.envelopeId,
      status: webhookData.data?.envelopeSummary?.status
    });

    // Extract organization ID from custom fields or lookup
    const organizationId = webhookData.data?.envelopeSummary?.customFields?.textCustomFields
      ?.find((field: any) => field.name === 'coelec_organization_id')?.value;

    if (!organizationId) {
      console.warn('No organization ID found in webhook data');
      return res.status(200).json({ received: true });
    }

    // Process signature completion if envelope is completed
    if (webhookData.event === 'envelope-completed' ||
        webhookData.data?.envelopeSummary?.status === 'completed') {

      const signatureRequestId = webhookData.data?.envelopeSummary?.customFields?.textCustomFields
        ?.find((field: any) => field.name === 'coelec_request_id')?.value;

      if (signatureRequestId) {
        const workflowEngine = new SignatureWorkflowEngine(organizationId);
        await workflowEngine.processSignatureCompletion(signatureRequestId, webhookData);
      }
    }

    res.status(200).json({
      success: true,
      message: "Webhook processed successfully"
    });
  } catch (error) {
    console.error("DocuSign webhook processing error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to process webhook"
    });
  }
});

export default router;
