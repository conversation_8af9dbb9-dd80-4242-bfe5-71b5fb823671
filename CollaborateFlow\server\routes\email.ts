/**
 * EMAIL API ROUTES
 * Handles email automation, templates, and analytics
 */

import { Router } from "express";
import EmailService from "../services/emailService";
import EmailAutomationService from "../services/emailAutomationService";
import { supabase } from "../supabase";

const router = Router();

// =============================================================================
// EMAIL SENDING ENDPOINTS
// =============================================================================

/**
 * POST /api/email/send/quote-notification
 * Send quote notification email
 */
router.post("/send/quote-notification", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const emailData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const requiredFields = ['clientEmail', 'clientName', 'quoteName', 'totalAmount', 'viewUrl'];
    const missingFields = requiredFields.filter(field => !emailData[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({ 
        error: `Missing required fields: ${missingFields.join(', ')}` 
      });
    }

    const emailService = new EmailService(organizationId);
    const result = await emailService.sendQuoteNotification(emailData);

    res.status(200).json({
      success: true,
      message: "Quote notification sent successfully",
      message_id: result.messageId,
      status: result.status
    });

  } catch (error) {
    console.error("Quote notification email error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to send quote notification"
    });
  }
});

/**
 * POST /api/email/send/signature-request
 * Send signature request email
 */
router.post("/send/signature-request", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const emailData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const requiredFields = ['signerEmail', 'signerName', 'documentTitle', 'signingUrl'];
    const missingFields = requiredFields.filter(field => !emailData[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({ 
        error: `Missing required fields: ${missingFields.join(', ')}` 
      });
    }

    const emailService = new EmailService(organizationId);
    const result = await emailService.sendSignatureRequest(emailData);

    res.status(200).json({
      success: true,
      message: "Signature request sent successfully",
      message_id: result.messageId,
      status: result.status
    });

  } catch (error) {
    console.error("Signature request email error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to send signature request"
    });
  }
});

/**
 * POST /api/email/send/project-update
 * Send project update email
 */
router.post("/send/project-update", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const emailData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const requiredFields = ['clientEmail', 'clientName', 'projectName', 'updateMessage'];
    const missingFields = requiredFields.filter(field => !emailData[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({ 
        error: `Missing required fields: ${missingFields.join(', ')}` 
      });
    }

    const emailService = new EmailService(organizationId);
    const result = await emailService.sendProjectUpdate(emailData);

    res.status(200).json({
      success: true,
      message: "Project update sent successfully",
      message_id: result.messageId,
      status: result.status
    });

  } catch (error) {
    console.error("Project update email error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to send project update"
    });
  }
});

// =============================================================================
// EMAIL AUTOMATION ENDPOINTS
// =============================================================================

/**
 * GET /api/email/automation/rules
 * Get automation rules
 */
router.get("/automation/rules", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const automationService = new EmailAutomationService(organizationId);
    const rules = await automationService.getAutomationRules();

    res.status(200).json({
      success: true,
      rules: rules,
      total: rules.length
    });

  } catch (error) {
    console.error("Get automation rules error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch automation rules"
    });
  }
});

/**
 * POST /api/email/automation/rules
 * Create automation rule
 */
router.post("/automation/rules", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const ruleData = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const requiredFields = ['rule_name', 'trigger_event', 'recipient_type'];
    const missingFields = requiredFields.filter(field => !ruleData[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({ 
        error: `Missing required fields: ${missingFields.join(', ')}` 
      });
    }

    const automationService = new EmailAutomationService(organizationId);
    const ruleId = await automationService.createAutomationRule(ruleData);

    res.status(201).json({
      success: true,
      message: "Automation rule created successfully",
      rule_id: ruleId
    });

  } catch (error) {
    console.error("Create automation rule error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to create automation rule"
    });
  }
});

/**
 * PUT /api/email/automation/rules/:id
 * Update automation rule
 */
router.put("/automation/rules/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    const updates = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const automationService = new EmailAutomationService(organizationId);
    await automationService.updateAutomationRule(id, updates);

    res.status(200).json({
      success: true,
      message: "Automation rule updated successfully"
    });

  } catch (error) {
    console.error("Update automation rule error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to update automation rule"
    });
  }
});

/**
 * DELETE /api/email/automation/rules/:id
 * Delete automation rule
 */
router.delete("/automation/rules/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const automationService = new EmailAutomationService(organizationId);
    await automationService.deleteAutomationRule(id);

    res.status(200).json({
      success: true,
      message: "Automation rule deleted successfully"
    });

  } catch (error) {
    console.error("Delete automation rule error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to delete automation rule"
    });
  }
});

/**
 * POST /api/email/automation/trigger
 * Trigger automation manually
 */
router.post("/automation/trigger", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { trigger_event, trigger_data, context_data } = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    if (!trigger_event || !trigger_data) {
      return res.status(400).json({ 
        error: "Trigger event and trigger data are required" 
      });
    }

    const automationService = new EmailAutomationService(organizationId);
    await automationService.processTrigger(trigger_event, trigger_data, context_data || {});

    res.status(200).json({
      success: true,
      message: "Automation trigger processed successfully"
    });

  } catch (error) {
    console.error("Automation trigger error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to process automation trigger"
    });
  }
});

// =============================================================================
// EMAIL ANALYTICS ENDPOINTS
// =============================================================================

/**
 * GET /api/email/analytics
 * Get email analytics
 */
router.get("/analytics", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { timeframe = '30d', email_type, template_used } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    // Parse timeframe
    const days = parseInt(timeframe.toString().replace('d', '')) || 30;

    // Get email statistics
    const { data: stats, error: statsError } = await supabase
      .rpc('get_email_statistics', {
        p_organization_id: organizationId,
        p_days: days
      });

    if (statsError) {
      throw new Error(`Failed to fetch email statistics: ${statsError.message}`);
    }

    // Get detailed analytics
    let analyticsQuery = supabase
      .from('email_analytics')
      .select('*')
      .eq('organization_id', organizationId)
      .gte('date_period', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('date_period', { ascending: false });

    if (email_type) {
      analyticsQuery = analyticsQuery.eq('email_type', email_type);
    }

    if (template_used) {
      analyticsQuery = analyticsQuery.eq('template_used', template_used);
    }

    const { data: analytics, error: analyticsError } = await analyticsQuery;

    if (analyticsError) {
      throw new Error(`Failed to fetch analytics: ${analyticsError.message}`);
    }

    res.status(200).json({
      success: true,
      timeframe: `${days}d`,
      statistics: stats[0] || {},
      analytics: analytics || []
    });

  } catch (error) {
    console.error("Email analytics error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch email analytics"
    });
  }
});

/**
 * GET /api/email/activity
 * Get email activity log
 */
router.get("/activity", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { 
      limit = 50, 
      offset = 0, 
      email_type, 
      status, 
      recipient_email,
      start_date,
      end_date 
    } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    let query = supabase
      .from('email_activity_log')
      .select('*')
      .eq('organization_id', organizationId)
      .order('sent_at', { ascending: false })
      .range(parseInt(offset.toString()), parseInt(offset.toString()) + parseInt(limit.toString()) - 1);

    if (email_type) {
      query = query.eq('email_type', email_type);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (recipient_email) {
      query = query.ilike('recipient_email', `%${recipient_email}%`);
    }

    if (start_date) {
      query = query.gte('sent_at', start_date);
    }

    if (end_date) {
      query = query.lte('sent_at', end_date);
    }

    const { data: activity, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch email activity: ${error.message}`);
    }

    res.status(200).json({
      success: true,
      activity: activity || [],
      total: activity?.length || 0,
      limit: parseInt(limit.toString()),
      offset: parseInt(offset.toString())
    });

  } catch (error) {
    console.error("Email activity error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch email activity"
    });
  }
});

export default router;
