/**
 * ESTIMATION API ROUTES
 * API endpoints for material estimation and cost calculation
 */

import { Router } from 'express';
import { calculateMaterialCosts, generateCostBreakdown } from '../services/materialEstimationEngine';

const router = Router();

/**
 * POST /api/estimation/calculate-costs
 * Calculate material costs from detected symbols
 */
router.post('/calculate-costs', async (req, res) => {
  try {
    const { symbols, projectLocation } = req.body;

    if (!symbols || !Array.isArray(symbols)) {
      return res.status(400).json({
        error: 'Invalid symbols data',
        message: 'Symbols array is required'
      });
    }

    console.log(`🔧 Calculating costs for ${symbols.length} symbols`);

    const result = await calculateMaterialCosts(symbols, projectLocation);

    res.json(result);
  } catch (error) {
    console.error('❌ Cost calculation error:', error);
    res.status(500).json({
      error: 'Cost calculation failed',
      message: error.message
    });
  }
});

/**
 * POST /api/estimation/cost-breakdown
 * Generate detailed cost breakdown from estimation
 */
router.post('/cost-breakdown', async (req, res) => {
  try {
    const { estimate } = req.body;

    if (!estimate) {
      return res.status(400).json({
        error: 'Invalid estimate data',
        message: 'Estimate object is required'
      });
    }

    console.log('📊 Generating cost breakdown');

    const result = await generateCostBreakdown(estimate);

    res.json(result);
  } catch (error) {
    console.error('❌ Cost breakdown error:', error);
    res.status(500).json({
      error: 'Cost breakdown failed',
      message: error.message
    });
  }
});

/**
 * POST /api/estimation/validate
 * Validate estimation data
 */
router.post('/validate', async (req, res) => {
  try {
    const { estimation } = req.body;

    if (!estimation) {
      return res.status(400).json({
        error: 'Invalid estimation data',
        message: 'Estimation object is required'
      });
    }

    // Basic validation
    const isValid = estimation.totalCost > 0 && 
                   estimation.materialsCost >= 0 && 
                   estimation.laborCost >= 0;

    res.json({
      valid: isValid,
      errors: isValid ? [] : ['Invalid cost values'],
      warnings: []
    });
  } catch (error) {
    console.error('❌ Estimation validation error:', error);
    res.status(500).json({
      error: 'Validation failed',
      message: error.message
    });
  }
});

/**
 * GET /api/estimation/health
 * Health check for estimation service
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'estimation-api',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

export default router;
