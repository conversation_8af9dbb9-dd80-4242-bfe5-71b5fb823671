import express, { Router } from 'express';
import http from 'http';
import apiRoutes from './api';
import path from 'path';

export function registerRoutes(app: express.Express): http.Server {
  const server = http.createServer(app);

  // Register API routes
  app.use('/api', apiRoutes);

  // Serve static files from the uploads directory
  app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

  return server;
}
