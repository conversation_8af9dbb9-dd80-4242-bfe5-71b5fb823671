/**
 * Supplier Integration API Routes
 *
 * Handles supplier integration operations including:
 * - Supplier management and configuration
 * - Product search across suppliers
 * - Price updates and synchronization
 * - Supplier analytics and monitoring
 */

import { Router } from "express";
import { getSupplierIntegrationMCP } from "../mcp/supplier-integration-mcp";
import { getFeatureFlag } from "../services/featureFlags";
import SupplierIntegrationService from "../services/supplierIntegrationService";
import SupplierCacheService from "../services/supplierCacheService";
import GraybarAdapter from "../adapters/graybarAdapter";

const router = Router();

// Search products across suppliers
router.post("/search", async (req, res) => {
  try {
    const { query, supplier_ids, max_results_per_supplier = 20, include_out_of_stock = false, price_range } = req.body;
    const organizationId = req.headers['x-organization-id'] as string;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: "Search query is required" });
    }

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    // Use real supplier integration service
    const supplierService = new SupplierIntegrationService(organizationId);
    const startTime = Date.now();

    const products = await supplierService.searchProducts(query, {
      supplier_ids,
      max_results_per_supplier,
      include_out_of_stock,
      price_range
    });

    const searchTime = Date.now() - startTime;

    // Calculate summary statistics
    const totalProducts = products.length;
    const averagePrice = products.length > 0
      ? products.reduce((sum, p) => sum + p.price, 0) / products.length
      : 0;

    const priceRange = products.reduce((range, product) => {
      if (product.price < range.min) range.min = product.price;
      if (product.price > range.max) range.max = product.price;
      return range;
    }, { min: Infinity, max: -Infinity });

    // Group products by supplier for response format
    const supplierGroups = products.reduce((groups, product) => {
      if (!groups[product.supplier_id]) {
        groups[product.supplier_id] = [];
      }
      groups[product.supplier_id].push(product);
      return groups;
    }, {} as Record<string, any[]>);

    const results = Object.entries(supplierGroups).map(([supplierId, supplierProducts]) => ({
      supplier_id: supplierId,
      products: supplierProducts,
      search_metadata: {
        search_time_ms: Math.round(searchTime / Object.keys(supplierGroups).length),
        cache_hit: false,
        products_found: supplierProducts.length
      }
    }));

    res.status(200).json({
      success: true,
      message: `Found ${totalProducts} products from ${results.length} suppliers`,
      query,
      results,
      summary: {
        total_products: totalProducts,
        suppliers_searched: results.length,
        average_price: Math.round(averagePrice * 100) / 100,
        price_range: priceRange.min === Infinity ? null : priceRange,
        search_time_ms: searchTime
      }
    });
  } catch (error) {
    console.error("Supplier search error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to search supplier products"
    });
  }
});

// Get pricing from multiple suppliers
router.post("/pricing", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { materials, location, delivery_date } = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    if (!materials || !Array.isArray(materials) || materials.length === 0) {
      return res.status(400).json({ error: "Materials array is required" });
    }

    // Use real supplier integration service
    const supplierService = new SupplierIntegrationService(organizationId);
    const startTime = Date.now();

    const pricingResponses = await supplierService.getPricing({
      materials,
      location,
      delivery_date
    });

    const processingTime = Date.now() - startTime;

    // Calculate comparison metrics
    const bestPrice = pricingResponses.length > 0
      ? Math.min(...pricingResponses.map(p => p.total_price))
      : 0;

    const averagePrice = pricingResponses.length > 0
      ? pricingResponses.reduce((sum, p) => sum + p.total_price, 0) / pricingResponses.length
      : 0;

    const savings = pricingResponses.length > 1
      ? Math.max(...pricingResponses.map(p => p.total_price)) - bestPrice
      : 0;

    res.status(200).json({
      success: true,
      message: `Received pricing from ${pricingResponses.length} suppliers`,
      pricing_responses: pricingResponses,
      comparison: {
        best_price: Math.round(bestPrice * 100) / 100,
        average_price: Math.round(averagePrice * 100) / 100,
        potential_savings: Math.round(savings * 100) / 100,
        suppliers_responded: pricingResponses.length
      },
      processing_time_ms: processingTime
    });
  } catch (error) {
    console.error("Supplier pricing error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to get supplier pricing"
    });
  }
});

// Get supplier configurations
router.get("/suppliers", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { active_only = true } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    // Mock supplier data - replace with actual database query
    const mockSuppliers = [
      {
        id: 'home_depot',
        name: 'Home Depot',
        type: 'scraping',
        base_url: 'https://www.homedepot.com',
        is_active: true,
        priority_order: 1,
        rate_limit_per_minute: 30,
        cache_ttl_hours: 24,
        last_sync_at: '2024-01-15T10:30:00Z',
        sync_status: 'completed',
        contact_info: {
          email: '<EMAIL>',
          phone: '**************'
        },
        performance_metrics: {
          success_rate: 95.2,
          average_response_time_ms: 1200,
          products_available: 15000
        }
      },
      {
        id: 'lowes',
        name: 'Lowe\'s',
        type: 'scraping',
        base_url: 'https://www.lowes.com',
        is_active: true,
        priority_order: 2,
        rate_limit_per_minute: 25,
        cache_ttl_hours: 24,
        last_sync_at: '2024-01-15T09:15:00Z',
        sync_status: 'completed',
        contact_info: {
          email: '<EMAIL>',
          phone: '**************'
        },
        performance_metrics: {
          success_rate: 92.8,
          average_response_time_ms: 1500,
          products_available: 12000
        }
      },
      {
        id: 'grainger',
        name: 'Grainger',
        type: 'api',
        base_url: 'https://www.grainger.com',
        is_active: true,
        priority_order: 3,
        rate_limit_per_minute: 100,
        cache_ttl_hours: 12,
        last_sync_at: '2024-01-15T11:00:00Z',
        sync_status: 'completed',
        contact_info: {
          email: '<EMAIL>',
          phone: '**************'
        },
        performance_metrics: {
          success_rate: 98.5,
          average_response_time_ms: 800,
          products_available: 25000
        }
      }
    ];

    const filteredSuppliers = active_only === 'true'
      ? mockSuppliers.filter(s => s.is_active)
      : mockSuppliers;

    res.status(200).json({
      success: true,
      suppliers: filteredSuppliers,
      total: filteredSuppliers.length
    });
  } catch (error) {
    console.error("Suppliers fetch error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch suppliers"
    });
  }
});

// Update supplier configuration
router.put("/suppliers/:supplierId", async (req, res) => {
  try {
    const { supplierId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    const configUpdates = req.body;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const supplierMCP = getSupplierIntegrationMCP();
    const success = supplierMCP.updateSupplierConfig(supplierId, configUpdates);

    if (success) {
      res.status(200).json({
        success: true,
        message: `Supplier ${supplierId} configuration updated successfully`,
        supplier_id: supplierId,
        updated_fields: Object.keys(configUpdates)
      });
    } else {
      res.status(404).json({
        success: false,
        message: `Supplier ${supplierId} not found`
      });
    }
  } catch (error) {
    console.error("Supplier update error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to update supplier configuration"
    });
  }
});

// Trigger price updates for all suppliers
router.post("/price-updates/all", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    // Use real supplier integration service
    const supplierService = new SupplierIntegrationService(organizationId);
    const startTime = Date.now();

    const results = await supplierService.updateAllPrices();
    const processingTime = Date.now() - startTime;

    const summary = {
      total_suppliers: results.length,
      successful_updates: results.filter(r => r.success).length,
      failed_updates: results.filter(r => !r.success).length,
      total_products_updated: results.reduce((sum, r) => sum + r.products_updated, 0),
      total_processing_time_ms: processingTime
    };

    res.status(200).json({
      success: true,
      message: `Price update completed for ${summary.successful_updates}/${summary.total_suppliers} suppliers`,
      results,
      summary
    });
  } catch (error) {
    console.error("Price update error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to update prices"
    });
  }
});

// Trigger price update for specific supplier
router.post("/price-updates/:supplierId", async (req, res) => {
  try {
    const { supplierId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    const supplierMCP = getSupplierIntegrationMCP();
    const result = await supplierMCP.updateSupplierPrices(supplierId);

    if (result.success) {
      res.status(200).json({
        success: true,
        message: `Price update completed for supplier ${supplierId}`,
        result
      });
    } else {
      res.status(500).json({
        success: false,
        message: `Price update failed for supplier ${supplierId}`,
        result
      });
    }
  } catch (error) {
    console.error("Supplier price update error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to update supplier prices"
    });
  }
});

// Get supplier analytics
router.get("/analytics", async (req, res) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { timeframe = '30d' } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    // Mock analytics data
    const analytics = {
      timeframe,
      supplier_performance: [
        {
          supplier_id: 'home_depot',
          supplier_name: 'Home Depot',
          searches_performed: 245,
          success_rate: 95.2,
          average_response_time_ms: 1200,
          products_found: 3680,
          price_changes: 156,
          availability_changes: 23
        },
        {
          supplier_id: 'lowes',
          supplier_name: 'Lowe\'s',
          searches_performed: 198,
          success_rate: 92.8,
          average_response_time_ms: 1500,
          products_found: 2940,
          price_changes: 134,
          availability_changes: 18
        },
        {
          supplier_id: 'grainger',
          supplier_name: 'Grainger',
          searches_performed: 312,
          success_rate: 98.5,
          average_response_time_ms: 800,
          products_found: 4680,
          price_changes: 89,
          availability_changes: 12
        }
      ],
      price_trends: {
        average_price_change_percentage: 2.3,
        price_increases: 67,
        price_decreases: 23,
        stable_prices: 310
      },
      search_analytics: {
        total_searches: 755,
        successful_searches: 721,
        failed_searches: 34,
        average_results_per_search: 15.2,
        most_searched_categories: [
          { category: 'Outlets & Switches', searches: 156 },
          { category: 'Light Fixtures', searches: 134 },
          { category: 'Wiring & Cable', searches: 98 }
        ]
      }
    };

    res.status(200).json({
      success: true,
      analytics
    });
  } catch (error) {
    console.error("Analytics fetch error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch supplier analytics"
    });
  }
});

// Get sync logs for a supplier
router.get("/sync-logs/:supplierId", async (req, res) => {
  try {
    const { supplierId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    const { limit = 20, offset = 0 } = req.query;

    if (!organizationId) {
      return res.status(400).json({ error: "Organization ID is required" });
    }

    // Mock sync logs
    const mockLogs = [
      {
        id: 'log_001',
        sync_type: 'price_update',
        status: 'completed',
        products_processed: 1250,
        products_updated: 1180,
        products_failed: 70,
        processing_time_ms: 45000,
        started_at: '2024-01-15T10:30:00Z',
        completed_at: '2024-01-15T10:30:45Z',
        sync_metadata: {
          trigger: 'scheduled',
          changes_detected: 156
        }
      },
      {
        id: 'log_002',
        sync_type: 'full',
        status: 'completed',
        products_processed: 15000,
        products_updated: 14850,
        products_failed: 150,
        processing_time_ms: 300000,
        started_at: '2024-01-14T02:00:00Z',
        completed_at: '2024-01-14T02:05:00Z',
        sync_metadata: {
          trigger: 'manual',
          full_catalog_refresh: true
        }
      }
    ];

    res.status(200).json({
      success: true,
      logs: mockLogs,
      pagination: {
        total: mockLogs.length,
        limit: parseInt(limit.toString()),
        offset: parseInt(offset.toString()),
        hasMore: false
      }
    });
  } catch (error) {
    console.error("Sync logs fetch error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: "Failed to fetch sync logs"
    });
  }
});

export default router;
