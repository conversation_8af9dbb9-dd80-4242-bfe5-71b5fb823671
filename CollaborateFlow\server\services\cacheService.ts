/**
 * ENHANCED CACHE SERVICE
 * High-performance caching with Redis support and multi-level caching
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { createHash } from 'crypto';

// Redis client interface (can be replaced with actual Redis client)
interface RedisClient {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  flushall(): Promise<void>;
  keys(pattern: string): Promise<string[]>;
}

// Mock Redis client for development (replace with real Redis in production)
class MockRedisClient implements RedisClient {
  private store = new Map<string, { value: string; expires: number }>();

  async get(key: string): Promise<string | null> {
    const entry = this.store.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expires) {
      this.store.delete(key);
      return null;
    }

    return entry.value;
  }

  async set(key: string, value: string, ttl: number = 3600): Promise<void> {
    this.store.set(key, {
      value,
      expires: Date.now() + (ttl * 1000)
    });
  }

  async del(key: string): Promise<void> {
    this.store.delete(key);
  }

  async exists(key: string): Promise<boolean> {
    const entry = this.store.get(key);
    if (!entry) return false;

    if (Date.now() > entry.expires) {
      this.store.delete(key);
      return false;
    }

    return true;
  }

  async flushall(): Promise<void> {
    this.store.clear();
  }

  async keys(pattern: string): Promise<string[]> {
    const regex = new RegExp(pattern.replace('*', '.*'));
    return Array.from(this.store.keys()).filter(key => regex.test(key));
  }
}

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  useRedis?: boolean; // Whether to use Redis
  useMemory?: boolean; // Whether to use in-memory cache
  useDatabase?: boolean; // Whether to use database cache
}

export interface CacheMetrics {
  totalRequests: number;
  hits: number;
  misses: number;
  hitRate: number;
  averageResponseTime: number;
  memoryUsage: number;
  redisConnected: boolean;
}

export class CacheService {
  private redis: RedisClient;
  private memoryCache = new Map<string, { value: any; expires: number; hits: number }>();
  private supabase: SupabaseClient;
  private organizationId: string;
  private metrics: CacheMetrics = {
    totalRequests: 0,
    hits: 0,
    misses: 0,
    hitRate: 0,
    averageResponseTime: 0,
    memoryUsage: 0,
    redisConnected: false
  };
  private responseTimes: number[] = [];
  private maxMemoryEntries = 1000;
  private defaultTtl = 3600; // 1 hour

  constructor(organizationId: string, supabaseUrl?: string, supabaseKey?: string) {
    this.organizationId = organizationId;
    this.redis = new MockRedisClient(); // Replace with real Redis client in production

    if (supabaseUrl && supabaseKey) {
      this.supabase = createClient(supabaseUrl, supabaseKey);
    } else {
      // Use existing supabase instance
      this.supabase = require('../supabase').supabase;
    }

    // Initialize Redis connection check
    this.checkRedisConnection();

    // Clean up expired memory cache entries periodically
    setInterval(() => this.cleanupMemoryCache(), 60000); // Every minute
  }

  /**
   * Get value from cache with multi-level fallback
   */
  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Level 1: Memory cache (fastest)
      if (options.useMemory !== false) {
        const memoryResult = this.getFromMemory<T>(key);
        if (memoryResult !== null) {
          this.recordHit(startTime);
          return memoryResult;
        }
      }

      // Level 2: Redis cache (fast)
      if (options.useRedis !== false && this.metrics.redisConnected) {
        const redisResult = await this.getFromRedis<T>(key);
        if (redisResult !== null) {
          // Store in memory for faster access
          this.setInMemory(key, redisResult, options.ttl || this.defaultTtl);
          this.recordHit(startTime);
          return redisResult;
        }
      }

      // Level 3: Database cache (slower but persistent)
      if (options.useDatabase !== false) {
        const dbResult = await this.getFromDatabase<T>(key);
        if (dbResult !== null) {
          // Store in higher levels for faster access
          this.setInMemory(key, dbResult, options.ttl || this.defaultTtl);
          if (this.metrics.redisConnected) {
            await this.setInRedis(key, dbResult, options.ttl || this.defaultTtl);
          }
          this.recordHit(startTime);
          return dbResult;
        }
      }

      this.recordMiss(startTime);
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      this.recordMiss(startTime);
      return null;
    }
  }

  /**
   * Set value in cache across all levels
   */
  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const ttl = options.ttl || this.defaultTtl;

    try {
      // Set in memory cache
      if (options.useMemory !== false) {
        this.setInMemory(key, value, ttl);
      }

      // Set in Redis cache
      if (options.useRedis !== false && this.metrics.redisConnected) {
        await this.setInRedis(key, value, ttl);
      }

      // Set in database cache
      if (options.useDatabase !== false) {
        await this.setInDatabase(key, value, ttl);
      }
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  /**
   * Delete value from all cache levels
   */
  async delete(key: string): Promise<void> {
    try {
      // Delete from memory
      this.memoryCache.delete(key);

      // Delete from Redis
      if (this.metrics.redisConnected) {
        await this.redis.del(key);
      }

      // Delete from database
      await this.supabase
        .from('cache_performance')
        .delete()
        .eq('organization_id', this.organizationId)
        .eq('cache_key', key);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  /**
   * Get or set with fallback function
   */
  async getOrSet<T>(
    key: string,
    fallbackFn: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    const value = await fallbackFn();
    await this.set(key, value, options);
    return value;
  }

  /**
   * Cache API response
   */
  async cacheApiResponse<T>(
    endpoint: string,
    params: Record<string, any>,
    response: T,
    ttl: number = 300 // 5 minutes default for API responses
  ): Promise<void> {
    const cacheKey = this.generateApiCacheKey(endpoint, params);
    await this.set(cacheKey, response, { ttl });
  }

  /**
   * Get cached API response
   */
  async getCachedApiResponse<T>(
    endpoint: string,
    params: Record<string, any>
  ): Promise<T | null> {
    const cacheKey = this.generateApiCacheKey(endpoint, params);
    return await this.get<T>(cacheKey);
  }

  /**
   * Cache database query result
   */
  async cacheQueryResult<T>(
    queryHash: string,
    result: T,
    ttl: number = 600 // 10 minutes default for queries
  ): Promise<void> {
    const cacheKey = `query:${queryHash}`;
    await this.set(cacheKey, result, { ttl });
  }

  /**
   * Get cached query result
   */
  async getCachedQueryResult<T>(queryHash: string): Promise<T | null> {
    const cacheKey = `query:${queryHash}`;
    return await this.get<T>(cacheKey);
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      // Invalidate memory cache
      for (const key of this.memoryCache.keys()) {
        if (key.includes(pattern)) {
          this.memoryCache.delete(key);
        }
      }

      // Invalidate Redis cache
      if (this.metrics.redisConnected) {
        const keys = await this.redis.keys(`*${pattern}*`);
        for (const key of keys) {
          await this.redis.del(key);
        }
      }

      // Invalidate database cache
      await this.supabase
        .from('cache_performance')
        .delete()
        .eq('organization_id', this.organizationId)
        .like('cache_key', `%${pattern}%`);
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    this.metrics.hitRate = this.metrics.totalRequests > 0
      ? (this.metrics.hits / this.metrics.totalRequests) * 100
      : 0;

    this.metrics.averageResponseTime = this.responseTimes.length > 0
      ? this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length
      : 0;

    this.metrics.memoryUsage = this.memoryCache.size;

    return { ...this.metrics };
  }

  /**
   * Clear all caches
   */
  async clearAll(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear Redis cache
      if (this.metrics.redisConnected) {
        await this.redis.flushall();
      }

      // Clear database cache
      await this.supabase
        .from('cache_performance')
        .delete()
        .eq('organization_id', this.organizationId);

      // Reset metrics
      this.metrics = {
        totalRequests: 0,
        hits: 0,
        misses: 0,
        hitRate: 0,
        averageResponseTime: 0,
        memoryUsage: 0,
        redisConnected: this.metrics.redisConnected
      };
      this.responseTimes = [];
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  // Private helper methods
  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expires) {
      this.memoryCache.delete(key);
      return null;
    }

    entry.hits++;
    return entry.value as T;
  }

  private setInMemory<T>(key: string, value: T, ttl: number): void {
    // Implement LRU eviction if cache is full - performance optimization
    if (this.memoryCache.size >= this.maxMemoryEntries) {
      const oldestKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(oldestKey);
    }

    this.memoryCache.set(key, {
      value,
      expires: Date.now() + (ttl * 1000), // TTL-based expiration
      hits: 0
    });
  }

  private async getFromRedis<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  private async setInRedis<T>(key: string, value: T, ttl: number): Promise<void> {
    try {
      await this.redis.set(key, JSON.stringify(value), ttl);
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  private async getFromDatabase<T>(key: string): Promise<T | null> {
    try {
      const { data, error } = await this.supabase
        .from('cache_performance')
        .select('cached_data')
        .eq('organization_id', this.organizationId)
        .eq('cache_key', key)
        .gte('expires_at', new Date().toISOString())
        .single();

      if (error || !data) return null;
      return data.cached_data as T;
    } catch (error) {
      console.error('Database cache get error:', error);
      return null;
    }
  }

  private async setInDatabase<T>(key: string, value: T, ttl: number): Promise<void> {
    try {
      const expiresAt = new Date(Date.now() + ttl * 1000);

      await this.supabase
        .from('cache_performance')
        .upsert({
          organization_id: this.organizationId,
          cache_key: key,
          cached_data: value,
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString(),
          hit_count: 0
        });
    } catch (error) {
      console.error('Database cache set error:', error);
    }
  }

  private generateApiCacheKey(endpoint: string, params: Record<string, any>): string {
    const paramString = JSON.stringify(params, Object.keys(params).sort());
    const hash = createHash('md5').update(`${endpoint}:${paramString}`).digest('hex');
    return `api:${hash}`;
  }

  private recordHit(startTime: number): void {
    this.metrics.hits++;
    this.recordResponseTime(startTime);
  }

  private recordMiss(startTime: number): void {
    this.metrics.misses++;
    this.recordResponseTime(startTime);
  }

  private recordResponseTime(startTime: number): void {
    const responseTime = Date.now() - startTime;
    this.responseTimes.push(responseTime);

    // Keep only last 1000 response times
    if (this.responseTimes.length > 1000) {
      this.responseTimes.shift();
    }
  }

  private async checkRedisConnection(): Promise<void> {
    try {
      await this.redis.set('health_check', 'ok', 10);
      const result = await this.redis.get('health_check');
      this.metrics.redisConnected = result === 'ok';
      await this.redis.del('health_check');
    } catch (error) {
      this.metrics.redisConnected = false;
      console.warn('Redis not available, using fallback caching');
    }
  }

  private cleanupMemoryCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now > entry.expires) {
        this.memoryCache.delete(key);
      }
    }
  }
}

export default CacheService;
