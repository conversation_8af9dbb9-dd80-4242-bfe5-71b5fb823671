import { supabase, handleSupabaseError } from './supabase';
import { Column } from '../types';

/**
 * Get columns for a project
 * @param projectId Project ID
 * @returns Promise resolving to an array of columns
 */
export async function getColumns(projectId: number): Promise<Column[]> {
  try {
    const { data, error } = await supabase
      .from('columns')
      .select('*')
      .eq('project_id', projectId)
      .order('order', { ascending: true });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getColumns');
  }
}

/**
 * Create a new column
 * @param column Column data without ID
 * @returns Promise resolving to the created column
 */
export async function createColumn(column: Omit<Column, 'id'>): Promise<Column> {
  try {
    const { data, error } = await supabase
      .from('columns')
      .insert(column)
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createColumn');
  }
}

/**
 * Update column details
 * @param columnId Column ID
 * @param data Updated column data
 * @returns Promise resolving to the updated column
 */
export async function updateColumn(columnId: number, data: Partial<Omit<Column, 'id'>>): Promise<Column> {
  try {
    const { data: updatedColumn, error } = await supabase
      .from('columns')
      .update(data)
      .eq('id', columnId)
      .select()
      .single();
    
    if (error) throw error;
    
    return updatedColumn;
  } catch (error) {
    return handleSupabaseError(error, 'updateColumn');
  }
}

/**
 * Delete a column
 * @param columnId Column ID
 * @returns Promise resolving to true if successful
 */
export async function deleteColumn(columnId: number): Promise<boolean> {
  try {
    // First, get the column to determine its project and order
    const { data: column, error: getError } = await supabase
      .from('columns')
      .select('*')
      .eq('id', columnId)
      .single();
    
    if (getError) throw getError;
    
    // Delete the column
    const { error: deleteError } = await supabase
      .from('columns')
      .delete()
      .eq('id', columnId);
    
    if (deleteError) throw deleteError;
    
    // Reorder the remaining columns
    const { error: updateError } = await supabase
      .rpc('reorder_columns_after_delete', { 
        p_project_id: column.projectId, 
        p_deleted_order: column.order 
      });
    
    if (updateError) throw updateError;
    
    return true;
  } catch (error) {
    return handleSupabaseError(error, 'deleteColumn');
  }
}

/**
 * Reorder columns
 * @param projectId Project ID
 * @param columnOrders Array of objects with column ID and new order
 * @returns Promise resolving to true if successful
 */
export async function reorderColumns(projectId: number, columnOrders: { id: number; order: number }[]): Promise<boolean> {
  try {
    // Use a transaction to ensure all updates succeed or fail together
    const { error } = await supabase.rpc('reorder_columns', { 
      p_column_orders: columnOrders,
      p_project_id: projectId
    });
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    return handleSupabaseError(error, 'reorderColumns');
  }
}
