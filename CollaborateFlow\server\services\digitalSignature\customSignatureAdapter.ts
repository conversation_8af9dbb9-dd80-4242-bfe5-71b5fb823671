/**
 * CUSTOM SIGNATURE ADAPTER
 * Alternative to DocuSign - HTML5 Canvas-based signature capture system
 * Part of the alternative service architecture approach
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../types/database';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface CustomSignatureRequest {
  id: string;
  organization_id: string;
  request_title: string;
  request_message: string;
  document_data?: Buffer;
  request_status: 'draft' | 'sent' | 'signed' | 'completed' | 'cancelled' | 'expired';
  signers: CustomSigner[];
  created_at: string;
  expires_at?: string;
}

export interface CustomSigner {
  id: string;
  name: string;
  email: string;
  role: string;
  signature_data?: string; // Base64 encoded signature
  signed_at?: string;
  ip_address?: string;
  user_agent?: string;
}

export interface SignatureError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export class CustomSignatureAdapter {
  private organizationId: string;
  private errorLog: SignatureError[] = [];

  constructor(organizationId: string) {
    this.organizationId = organizationId;
  }

  /**
   * Create a new signature request
   */
  async createSignatureRequest(data: {
    request_title: string;
    request_message: string;
    document_data?: Buffer;
    signers: Omit<CustomSigner, 'id'>[];
    expires_in_days?: number;
  }): Promise<string> {
    try {
      console.log('🖊️ Creating custom signature request:', data.request_title);

      // Validate input data
      this.validateSignatureRequestData(data);

      const expiresAt = data.expires_in_days 
        ? new Date(Date.now() + data.expires_in_days * 24 * 60 * 60 * 1000)
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // Default 30 days

      // Create signature request
      const { data: request, error } = await supabase
        .from('signature_requests')
        .insert({
          organization_id: this.organizationId,
          request_title: data.request_title,
          request_message: data.request_message,
          request_type: 'custom',
          request_status: 'draft',
          document_count: data.document_data ? 1 : 0,
          signer_count: data.signers.length,
          completion_percentage: 0,
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single();

      if (error) {
        throw this.createError('CREATE_REQUEST_FAILED', `Failed to create signature request: ${error.message}`, error);
      }

      // Add signers
      for (const signer of data.signers) {
        await this.addSigner(request.id, signer);
      }

      console.log(`✅ Custom signature request created: ${request.id}`);
      return request.id;

    } catch (error) {
      this.logError('CREATE_REQUEST_ERROR', 'Failed to create signature request', error);
      throw error;
    }
  }

  /**
   * Add a signer to an existing request
   */
  async addSigner(requestId: string, signer: Omit<CustomSigner, 'id'>): Promise<string> {
    try {
      console.log(`👤 Adding signer: ${signer.name} (${signer.email})`);

      // Validate signer data
      this.validateSignerData(signer);

      const { data: signerRecord, error } = await supabase
        .from('signature_signers')
        .insert({
          signature_request_id: requestId,
          signer_name: signer.name,
          signer_email: signer.email,
          signer_role: signer.role,
          signing_order: 1,
          signer_status: 'pending'
        })
        .select()
        .single();

      if (error) {
        throw this.createError('ADD_SIGNER_FAILED', `Failed to add signer: ${error.message}`, error);
      }

      console.log(`✅ Signer added: ${signerRecord.id}`);
      return signerRecord.id;

    } catch (error) {
      this.logError('ADD_SIGNER_ERROR', 'Failed to add signer', error);
      throw error;
    }
  }

  /**
   * Send signature request to signers
   */
  async sendSignatureRequest(requestId: string): Promise<void> {
    try {
      console.log(`📤 Sending custom signature request: ${requestId}`);

      // Get request and signers
      const { data: request, error: requestError } = await supabase
        .from('signature_requests')
        .select(`
          *,
          signature_signers(*)
        `)
        .eq('id', requestId)
        .single();

      if (requestError || !request) {
        throw this.createError('REQUEST_NOT_FOUND', 'Signature request not found', requestError);
      }

      // Validate request can be sent
      if (request.request_status !== 'draft') {
        throw this.createError('INVALID_STATUS', 'Request is not in draft status');
      }

      if (!request.signature_signers || request.signature_signers.length === 0) {
        throw this.createError('NO_SIGNERS', 'No signers found for request');
      }

      // Update request status
      const { error: updateError } = await supabase
        .from('signature_requests')
        .update({
          request_status: 'sent',
          sent_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (updateError) {
        throw this.createError('UPDATE_STATUS_FAILED', `Failed to update request status: ${updateError.message}`, updateError);
      }

      // Send email notifications (mock implementation)
      for (const signer of request.signature_signers) {
        await this.sendSignerNotification(requestId, signer);
      }

      console.log(`✅ Custom signature request sent: ${requestId}`);

    } catch (error) {
      this.logError('SEND_REQUEST_ERROR', 'Failed to send signature request', error);
      throw error;
    }
  }

  /**
   * Capture signature from HTML5 Canvas
   */
  async captureSignature(requestId: string, signerId: string, signatureData: {
    signature_image: string; // Base64 encoded image
    ip_address?: string;
    user_agent?: string;
  }): Promise<void> {
    try {
      console.log(`✍️ Capturing signature for signer: ${signerId}`);

      // Validate signature data
      this.validateSignatureData(signatureData);

      // Update signer with signature
      const { error } = await supabase
        .from('signature_signers')
        .update({
          signer_status: 'signed',
          signed_at: new Date().toISOString(),
          signature_data: signatureData.signature_image,
          ip_address: signatureData.ip_address,
          user_agent: signatureData.user_agent
        })
        .eq('id', signerId)
        .eq('signature_request_id', requestId);

      if (error) {
        throw this.createError('CAPTURE_SIGNATURE_FAILED', `Failed to capture signature: ${error.message}`, error);
      }

      // Check if all signers have signed
      await this.checkRequestCompletion(requestId);

      console.log(`✅ Signature captured for signer: ${signerId}`);

    } catch (error) {
      this.logError('CAPTURE_SIGNATURE_ERROR', 'Failed to capture signature', error);
      throw error;
    }
  }

  /**
   * Get signature request status
   */
  async getRequestStatus(requestId: string): Promise<CustomSignatureRequest> {
    try {
      const { data: request, error } = await supabase
        .from('signature_requests')
        .select(`
          *,
          signature_signers(*)
        `)
        .eq('id', requestId)
        .eq('organization_id', this.organizationId)
        .single();

      if (error || !request) {
        throw this.createError('REQUEST_NOT_FOUND', 'Signature request not found', error);
      }

      return {
        id: request.id,
        organization_id: request.organization_id,
        request_title: request.request_title,
        request_message: request.request_message,
        request_status: request.request_status as any,
        signers: request.signature_signers.map(signer => ({
          id: signer.id,
          name: signer.signer_name,
          email: signer.signer_email,
          role: signer.signer_role,
          signature_data: signer.signature_data,
          signed_at: signer.signed_at,
          ip_address: signer.ip_address,
          user_agent: signer.user_agent
        })),
        created_at: request.created_at,
        expires_at: request.expires_at
      };

    } catch (error) {
      this.logError('GET_STATUS_ERROR', 'Failed to get request status', error);
      throw error;
    }
  }

  /**
   * Cancel signature request
   */
  async cancelRequest(requestId: string, reason?: string): Promise<void> {
    try {
      console.log(`❌ Cancelling signature request: ${requestId}`);

      const { error } = await supabase
        .from('signature_requests')
        .update({
          request_status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          cancellation_reason: reason
        })
        .eq('id', requestId)
        .eq('organization_id', this.organizationId);

      if (error) {
        throw this.createError('CANCEL_REQUEST_FAILED', `Failed to cancel request: ${error.message}`, error);
      }

      console.log(`✅ Signature request cancelled: ${requestId}`);

    } catch (error) {
      this.logError('CANCEL_REQUEST_ERROR', 'Failed to cancel signature request', error);
      throw error;
    }
  }

  /**
   * Get error logs
   */
  getErrorLogs(): SignatureError[] {
    return [...this.errorLog];
  }

  /**
   * Clear error logs
   */
  clearErrorLogs(): void {
    this.errorLog = [];
  }

  // Private helper methods

  private validateSignatureRequestData(data: any): void {
    if (!data.request_title || data.request_title.trim().length === 0) {
      throw this.createError('VALIDATION_ERROR', 'Request title is required');
    }

    if (!data.request_message || data.request_message.trim().length === 0) {
      throw this.createError('VALIDATION_ERROR', 'Request message is required');
    }

    if (!data.signers || !Array.isArray(data.signers) || data.signers.length === 0) {
      throw this.createError('VALIDATION_ERROR', 'At least one signer is required');
    }
  }

  private validateSignerData(signer: any): void {
    if (!signer.name || signer.name.trim().length === 0) {
      throw this.createError('VALIDATION_ERROR', 'Signer name is required');
    }

    if (!signer.email || !this.isValidEmail(signer.email)) {
      throw this.createError('VALIDATION_ERROR', 'Valid signer email is required');
    }

    if (!signer.role || signer.role.trim().length === 0) {
      throw this.createError('VALIDATION_ERROR', 'Signer role is required');
    }
  }

  private validateSignatureData(data: any): void {
    if (!data.signature_image || data.signature_image.trim().length === 0) {
      throw this.createError('VALIDATION_ERROR', 'Signature image data is required');
    }

    if (!data.signature_image.startsWith('data:image/')) {
      throw this.createError('VALIDATION_ERROR', 'Invalid signature image format');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private createError(code: string, message: string, details?: any): Error {
    const error = new Error(message);
    error.name = code;
    return error;
  }

  private logError(code: string, message: string, error?: any): void {
    const errorEntry: SignatureError = {
      code,
      message,
      details: error,
      timestamp: new Date().toISOString()
    };

    this.errorLog.push(errorEntry);
    console.error(`❌ [${code}] ${message}:`, error);

    // Keep only last 100 errors
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-100);
    }
  }

  private async sendSignerNotification(requestId: string, signer: any): Promise<void> {
    try {
      console.log(`📧 Sending notification to: ${signer.signer_email}`);
      
      // Mock email sending - in production, integrate with email service
      const signatureUrl = `${process.env.NEXT_PUBLIC_APP_URL}/sign/${requestId}/${signer.id}`;
      
      console.log(`📧 Signature URL: ${signatureUrl}`);
      console.log(`📧 Notification sent to: ${signer.signer_email}`);

    } catch (error) {
      this.logError('NOTIFICATION_ERROR', 'Failed to send signer notification', error);
    }
  }

  private async checkRequestCompletion(requestId: string): Promise<void> {
    try {
      // Get all signers for the request
      const { data: signers, error } = await supabase
        .from('signature_signers')
        .select('*')
        .eq('signature_request_id', requestId);

      if (error || !signers) {
        throw this.createError('CHECK_COMPLETION_FAILED', 'Failed to check request completion', error);
      }

      const totalSigners = signers.length;
      const signedCount = signers.filter(s => s.signer_status === 'signed').length;
      const completionPercentage = Math.round((signedCount / totalSigners) * 100);

      // Update completion percentage
      const updateData: any = {
        completion_percentage: completionPercentage
      };

      // If all signed, mark as completed
      if (signedCount === totalSigners) {
        updateData.request_status = 'completed';
        updateData.completed_at = new Date().toISOString();
      }

      const { error: updateError } = await supabase
        .from('signature_requests')
        .update(updateData)
        .eq('id', requestId);

      if (updateError) {
        throw this.createError('UPDATE_COMPLETION_FAILED', 'Failed to update completion status', updateError);
      }

      if (signedCount === totalSigners) {
        console.log(`🎉 Signature request completed: ${requestId}`);
      }

    } catch (error) {
      this.logError('CHECK_COMPLETION_ERROR', 'Failed to check request completion', error);
    }
  }
}

export default CustomSignatureAdapter;
