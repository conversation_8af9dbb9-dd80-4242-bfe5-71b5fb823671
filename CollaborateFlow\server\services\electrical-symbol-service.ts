/**
 * T1.2 ELECTRICAL SYMBOL SERVICE
 * Comprehensive service for managing electrical symbols, categories, and material mappings
 * Integrates with T1.1 AI symbol detection for complete symbol management
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from '../types/database';

// Types for electrical symbols
export interface ElectricalSymbolCategory {
  id: string;
  name: string;
  description: string | null;
  display_order: number;
  icon_name: string | null;
  color_code: string | null;
  created_at: string;
  updated_at: string;
}

export interface ElectricalSymbol {
  id: string;
  category_id: string;
  symbol_code: string;
  name: string;
  description: string | null;
  voltage: string | null;
  amperage: string | null;
  wattage: number | null;
  phase_type: string | null;
  mounting_type: string | null;
  dimensions: Record<string, any> | null;
  weight_lbs: number | null;
  nema_rating: string | null;
  ip_rating: string | null;
  ul_listed: boolean;
  energy_star: boolean;
  box_type: string | null;
  wire_gauge: string | null;
  circuit_breaker_size: string | null;
  base_material_cost: number | null;
  installation_time_minutes: number | null;
  labor_complexity: string | null;
  detection_keywords: string[] | null;
  common_variations: string[] | null;
  symbol_shape: string | null;
  manufacturer: string | null;
  model_number: string | null;
  part_number: string | null;
  datasheet_url: string | null;
  image_url: string | null;
  symbol_drawing_url: string | null;
  is_active: boolean;
  is_standard: boolean;
  version: number;
  created_at: string;
  updated_at: string;
  // Joined data
  category?: ElectricalSymbolCategory;
  materials?: SymbolMaterialMapping[];
}

export interface SymbolMaterialMapping {
  id: string;
  symbol_id: string;
  material_type: string;
  material_name: string;
  material_description: string | null;
  quantity: number;
  unit: string;
  unit_cost: number;
  total_cost: number;
  specifications: Record<string, any> | null;
  supplier_name: string | null;
  supplier_part_number: string | null;
  supplier_url: string | null;
  is_required: boolean;
  installation_notes: string | null;
  created_at: string;
  updated_at: string;
}

export interface SymbolDetectionHistory {
  id: string;
  symbol_id: string | null;
  floor_plan_id: string | null;
  project_id: string | null;
  organization_id: string;
  detected_coordinates: Record<string, any> | null;
  confidence_score: number | null;
  ai_model_used: string | null;
  detection_method: string | null;
  user_confirmed: boolean | null;
  user_corrected_to: string | null;
  user_feedback: string | null;
  detected_at: string;
  confirmed_at: string | null;
}

export interface SymbolSearchFilters {
  category_id?: string;
  voltage?: string;
  amperage?: string;
  mounting_type?: string;
  labor_complexity?: string;
  manufacturer?: string;
  is_active?: boolean;
  search_text?: string;
  min_cost?: number;
  max_cost?: number;
}

export interface SymbolCostCalculation {
  symbol_id: string;
  symbol_name: string;
  device_cost: number;
  material_cost: number;
  total_material_cost: number;
  installation_time_minutes: number;
  labor_complexity: string;
  estimated_labor_cost: number;
  total_cost: number;
  cost_breakdown: {
    device: number;
    materials: Array<{
      type: string;
      name: string;
      quantity: number;
      unit_cost: number;
      total_cost: number;
    }>;
    labor: number;
  };
}

/**
 * Electrical Symbol Service Class
 */
export class ElectricalSymbolService {
  private supabase: ReturnType<typeof createClient<Database>>;
  private organizationId: string;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Get all electrical symbol categories
   */
  async getCategories(): Promise<ElectricalSymbolCategory[]> {
    const { data, error } = await this.supabase
      .from('electrical_symbol_categories')
      .select('*')
      .order('display_order', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch symbol categories: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get electrical symbols with optional filtering
   */
  async getSymbols(filters: SymbolSearchFilters = {}): Promise<ElectricalSymbol[]> {
    let query = this.supabase
      .from('electrical_symbols')
      .select(`
        *,
        category:electrical_symbol_categories(*)
      `);

    // Apply filters
    if (filters.category_id) {
      query = query.eq('category_id', filters.category_id);
    }

    if (filters.voltage) {
      query = query.eq('voltage', filters.voltage);
    }

    if (filters.amperage) {
      query = query.eq('amperage', filters.amperage);
    }

    if (filters.mounting_type) {
      query = query.eq('mounting_type', filters.mounting_type);
    }

    if (filters.labor_complexity) {
      query = query.eq('labor_complexity', filters.labor_complexity);
    }

    if (filters.manufacturer) {
      query = query.ilike('manufacturer', `%${filters.manufacturer}%`);
    }

    if (filters.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }

    if (filters.search_text) {
      query = query.or(`
        name.ilike.%${filters.search_text}%,
        description.ilike.%${filters.search_text}%,
        symbol_code.ilike.%${filters.search_text}%,
        detection_keywords.cs.{${filters.search_text}}
      `);
    }

    if (filters.min_cost !== undefined) {
      query = query.gte('base_material_cost', filters.min_cost);
    }

    if (filters.max_cost !== undefined) {
      query = query.lte('base_material_cost', filters.max_cost);
    }

    query = query.order('name', { ascending: true });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch electrical symbols: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a specific electrical symbol by ID with materials
   */
  async getSymbolById(symbolId: string): Promise<ElectricalSymbol | null> {
    const { data, error } = await this.supabase
      .from('electrical_symbols')
      .select(`
        *,
        category:electrical_symbol_categories(*),
        materials:symbol_material_mappings(*)
      `)
      .eq('id', symbolId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to fetch electrical symbol: ${error.message}`);
    }

    return data;
  }

  /**
   * Get electrical symbol by symbol code
   */
  async getSymbolByCode(symbolCode: string): Promise<ElectricalSymbol | null> {
    const { data, error } = await this.supabase
      .from('electrical_symbols')
      .select(`
        *,
        category:electrical_symbol_categories(*),
        materials:symbol_material_mappings(*)
      `)
      .eq('symbol_code', symbolCode)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to fetch electrical symbol: ${error.message}`);
    }

    return data;
  }

  /**
   * Search symbols by detection keywords (for AI integration)
   */
  async searchSymbolsByKeywords(keywords: string[]): Promise<ElectricalSymbol[]> {
    const { data, error } = await this.supabase
      .from('electrical_symbols')
      .select(`
        *,
        category:electrical_symbol_categories(*)
      `)
      .overlaps('detection_keywords', keywords)
      .eq('is_active', true)
      .order('name', { ascending: true });

    if (error) {
      throw new Error(`Failed to search symbols by keywords: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get material mappings for a symbol
   */
  async getSymbolMaterials(symbolId: string): Promise<SymbolMaterialMapping[]> {
    const { data, error } = await this.supabase
      .from('symbol_material_mappings')
      .select('*')
      .eq('symbol_id', symbolId)
      .order('material_type', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch symbol materials: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Calculate total cost for a symbol installation
   */
  async calculateSymbolCost(
    symbolId: string,
    laborRatePerHour: number = 75
  ): Promise<SymbolCostCalculation> {
    const symbol = await this.getSymbolById(symbolId);
    if (!symbol) {
      throw new Error('Symbol not found');
    }

    const materials = await this.getSymbolMaterials(symbolId);

    // Calculate material costs
    const materialCosts = materials.map(material => ({
      type: material.material_type,
      name: material.material_name,
      quantity: material.quantity,
      unit_cost: material.unit_cost,
      total_cost: material.total_cost
    }));

    const totalMaterialCost = materials.reduce((sum, material) => sum + material.total_cost, 0);
    const deviceCost = symbol.base_material_cost || 0;

    // Calculate labor cost
    const installationTimeHours = (symbol.installation_time_minutes || 0) / 60;
    const estimatedLaborCost = installationTimeHours * laborRatePerHour;

    const totalCost = deviceCost + totalMaterialCost + estimatedLaborCost;

    return {
      symbol_id: symbolId,
      symbol_name: symbol.name,
      device_cost: deviceCost,
      material_cost: totalMaterialCost,
      total_material_cost: deviceCost + totalMaterialCost,
      installation_time_minutes: symbol.installation_time_minutes || 0,
      labor_complexity: symbol.labor_complexity || 'simple',
      estimated_labor_cost: estimatedLaborCost,
      total_cost: totalCost,
      cost_breakdown: {
        device: deviceCost,
        materials: materialCosts,
        labor: estimatedLaborCost
      }
    };
  }

  /**
   * Record symbol detection history (for AI learning)
   */
  async recordDetection(detection: {
    symbol_id?: string;
    floor_plan_id?: string;
    project_id?: string;
    detected_coordinates: Record<string, any>;
    confidence_score: number;
    ai_model_used: string;
    detection_method: string;
  }): Promise<SymbolDetectionHistory> {
    const { data, error } = await this.supabase
      .from('symbol_detection_history')
      .insert({
        ...detection,
        organization_id: this.organizationId,
        detected_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to record detection: ${error.message}`);
    }

    return data;
  }

  /**
   * Update detection with user feedback
   */
  async updateDetectionFeedback(
    detectionId: string,
    feedback: {
      user_confirmed?: boolean;
      user_corrected_to?: string;
      user_feedback?: string;
    }
  ): Promise<void> {
    const updateData: any = {
      ...feedback,
      confirmed_at: new Date().toISOString()
    };

    const { error } = await this.supabase
      .from('symbol_detection_history')
      .update(updateData)
      .eq('id', detectionId)
      .eq('organization_id', this.organizationId);

    if (error) {
      throw new Error(`Failed to update detection feedback: ${error.message}`);
    }
  }

  /**
   * Get detection accuracy statistics
   */
  async getDetectionStats(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<{
    total_detections: number;
    confirmed_detections: number;
    corrected_detections: number;
    accuracy_rate: number;
    average_confidence: number;
    top_detected_symbols: Array<{
      symbol_id: string;
      symbol_name: string;
      detection_count: number;
    }>;
  }> {
    const timeframeDays = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeframeDays);

    const { data, error } = await this.supabase
      .from('symbol_detection_history')
      .select(`
        *,
        symbol:electrical_symbols(name)
      `)
      .eq('organization_id', this.organizationId)
      .gte('detected_at', startDate.toISOString());

    if (error) {
      throw new Error(`Failed to fetch detection stats: ${error.message}`);
    }

    const detections = data || [];
    const totalDetections = detections.length;
    const confirmedDetections = detections.filter(d => d.user_confirmed === true).length;
    const correctedDetections = detections.filter(d => d.user_corrected_to !== null).length;
    const accuracyRate = totalDetections > 0 ? (confirmedDetections / totalDetections) * 100 : 0;
    const averageConfidence = totalDetections > 0
      ? detections.reduce((sum, d) => sum + (d.confidence_score || 0), 0) / totalDetections
      : 0;

    // Calculate top detected symbols
    const symbolCounts = detections.reduce((acc, detection) => {
      if (detection.symbol_id) {
        acc[detection.symbol_id] = (acc[detection.symbol_id] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const topDetectedSymbols = Object.entries(symbolCounts)
      .map(([symbolId, count]) => {
        const detection = detections.find(d => d.symbol_id === symbolId);
        return {
          symbol_id: symbolId,
          symbol_name: detection?.symbol?.name || 'Unknown',
          detection_count: count
        };
      })
      .sort((a, b) => b.detection_count - a.detection_count)
      .slice(0, 10);

    return {
      total_detections: totalDetections,
      confirmed_detections: confirmedDetections,
      corrected_detections: correctedDetections,
      accuracy_rate: accuracyRate,
      average_confidence: averageConfidence,
      top_detected_symbols: topDetectedSymbols
    };
  }

  /**
   * Validate electrical symbol data
   */
  validateSymbol(symbol: Partial<ElectricalSymbol>): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required field validation
    if (!symbol.name || symbol.name.trim().length === 0) {
      errors.push('Symbol name is required');
    }

    if (!symbol.symbol_code || symbol.symbol_code.trim().length === 0) {
      errors.push('Symbol code is required');
    }

    if (!symbol.category_id) {
      errors.push('Category ID is required');
    }

    // Cost validation
    if (symbol.base_material_cost !== undefined && symbol.base_material_cost !== null) {
      if (symbol.base_material_cost < 0) {
        errors.push('Base material cost cannot be negative');
      }
      if (symbol.base_material_cost > 10000) {
        warnings.push('Base material cost is unusually high (>$10,000)');
      }
    }

    // Installation time validation
    if (symbol.installation_time_minutes !== undefined && symbol.installation_time_minutes !== null) {
      if (symbol.installation_time_minutes < 0) {
        errors.push('Installation time cannot be negative');
      }
      if (symbol.installation_time_minutes > 480) { // 8 hours
        warnings.push('Installation time is unusually long (>8 hours)');
      }
    }

    // Electrical specifications validation
    if (symbol.voltage && !this.isValidVoltage(symbol.voltage)) {
      errors.push('Invalid voltage specification');
    }

    if (symbol.amperage && !this.isValidAmperage(symbol.amperage)) {
      errors.push('Invalid amperage specification');
    }

    if (symbol.wattage !== undefined && symbol.wattage !== null && symbol.wattage < 0) {
      errors.push('Wattage cannot be negative');
    }

    // Labor complexity validation
    if (symbol.labor_complexity && !['simple', 'moderate', 'complex', 'expert'].includes(symbol.labor_complexity)) {
      errors.push('Invalid labor complexity level');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate voltage specification
   */
  private isValidVoltage(voltage: string): boolean {
    const validVoltages = ['12V', '24V', '120V', '240V', '277V', '480V', '208V', '120/240V', '277/480V'];
    return validVoltages.includes(voltage) || /^\d+V$/.test(voltage);
  }

  /**
   * Validate amperage specification
   */
  private isValidAmperage(amperage: string): boolean {
    return /^\d+(\.\d+)?A$/.test(amperage) || /^\d+(\.\d+)?\s?amp(s)?$/i.test(amperage);
  }

  /**
   * Check if symbol exists by code
   */
  async checkSymbolExists(symbolCode: string): Promise<boolean> {
    try {
      const symbol = await this.getSymbolByCode(symbolCode);
      return symbol !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate symbol material mapping
   */
  validateMaterialMapping(mapping: Partial<SymbolMaterialMapping>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!mapping.symbol_id) {
      errors.push('Symbol ID is required');
    }

    if (!mapping.material_type || mapping.material_type.trim().length === 0) {
      errors.push('Material type is required');
    }

    if (!mapping.material_name || mapping.material_name.trim().length === 0) {
      errors.push('Material name is required');
    }

    if (mapping.quantity !== undefined && mapping.quantity !== null && mapping.quantity <= 0) {
      errors.push('Quantity must be greater than 0');
    }

    if (mapping.unit_cost !== undefined && mapping.unit_cost !== null && mapping.unit_cost < 0) {
      errors.push('Unit cost cannot be negative');
    }

    if (mapping.total_cost !== undefined && mapping.total_cost !== null && mapping.total_cost < 0) {
      errors.push('Total cost cannot be negative');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Export singleton factory
const symbolServices = new Map<string, ElectricalSymbolService>();

export function getElectricalSymbolService(organizationId: string): ElectricalSymbolService {
  if (!symbolServices.has(organizationId)) {
    symbolServices.set(organizationId, new ElectricalSymbolService(organizationId));
  }
  return symbolServices.get(organizationId)!;
}

export default ElectricalSymbolService;
