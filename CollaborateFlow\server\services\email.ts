import { MailService } from '@sendgrid/mail';

if (!process.env.SENDGRID_API_KEY) {
  console.warn("SENDGRID_API_KEY environment variable is not set. Email functionality will not work.");
}

// Using environment variables or default values for email configuration
const SENDGRID_API_KEY = process.env.SENDGRID_API_KEY || '';
const FROM_EMAIL = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';

// Check if the API key has the expected format
let apiKeyValid = false;
if (SENDGRID_API_KEY) {
  // We're not checking for SG. prefix here - just checking that we have some value
  apiKeyValid = SENDGRID_API_KEY.length > 10;
}

// Initialize mail service
const mailService = new MailService();
try {
  if (apiKeyValid) {
    mailService.setApiKey(SENDGRID_API_KEY);
    console.log("SendGrid initialized successfully");
  } else {
    console.warn("SendGrid API key is missing or invalid - email sending will be disabled");
  }
} catch (error) {
  console.error("Error initializing SendGrid:", error);
}

interface EmailParams {
  to: string;
  from: string;
  subject: string;
  text?: string;
  html?: string;
  cc?: string | undefined;
  bcc?: string | undefined;
}

export async function sendEmail(params: EmailParams): Promise<boolean> {
  try {
    if (!apiKeyValid) {
      console.error('SendGrid API key not set or invalid');
      return false;
    }

    // Use default FROM_EMAIL if not specified
    const from = params.from || FROM_EMAIL;

    const msg = {
      to: params.to,
      from,
      subject: params.subject,
      text: params.text || '',
      html: params.html || '',
      cc: params.cc,
      bcc: params.bcc,
    };

    await mailService.send(msg);
    console.log(`Email sent successfully to ${params.to}`);
    return true;
  } catch (error) {
    console.error('SendGrid email error:', error);
    return false;
  }
}

export async function sendQuoteEmail(
  to: string,
  cc: string | undefined,
  subject: string,
  message: string,
  clientName: string,
  quoteNumber: string,
  quoteToken: string,
  baseUrl: string
): Promise<boolean> {
  const quoteUrl = `${baseUrl}/quote/${quoteNumber}/view/${quoteToken}`;
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6;">
      <p>Dear ${clientName},</p>
      <p>${message}</p>
      <div style="margin: 30px 0;">
        <a href="${quoteUrl}" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          View Quote
        </a>
      </div>
      <p>
        If the button above doesn't work, you can copy and paste this link into your browser:<br>
        <a href="${quoteUrl}">${quoteUrl}</a>
      </p>
      <p>This link will allow you to review the quote, provide feedback, and approve or reject it.</p>
    </div>
  `;
  
  return sendEmail({
    to,
    from: FROM_EMAIL,
    subject,
    text: message + `\n\nView Quote: ${quoteUrl}`,
    html: htmlContent,
    cc
  });
}