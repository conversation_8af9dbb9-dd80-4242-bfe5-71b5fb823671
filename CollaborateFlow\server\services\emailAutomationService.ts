/**
 * EMAIL AUTOMATION SERVICE
 * Handles automated email workflows and triggers
 */

import { supabase } from './supabase';
import EmailService from './emailService';

export interface AutomationRule {
  id: string;
  organization_id: string;
  rule_name: string;
  description?: string;
  trigger_event: string;
  is_active: boolean;
  priority: number;
  trigger_conditions: Record<string, any>;
  delay_minutes: number;
  template_id: string;
  recipient_type: 'client' | 'team_member' | 'contractor' | 'custom';
  custom_recipients?: string[];
  max_executions_per_day?: number;
  max_executions_total?: number;
  executions_today: number;
  total_executions: number;
  last_executed?: string;
}

export interface AutomationExecution {
  id?: string;
  organization_id: string;
  automation_rule_id: string;
  trigger_event: string;
  trigger_data: Record<string, any>;
  status: 'pending' | 'processing' | 'sent' | 'failed' | 'skipped';
  recipient_email?: string;
  email_activity_id?: string;
  executed_at?: string;
  processing_time_ms?: number;
  error_message?: string;
  context_data: Record<string, any>;
}

export class EmailAutomationService {
  private organizationId: string;
  private emailService: EmailService;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.emailService = new EmailService(organizationId);
  }

  /**
   * Process automation trigger
   */
  async processTrigger(
    triggerEvent: string,
    triggerData: Record<string, any>,
    contextData: Record<string, any> = {}
  ): Promise<void> {
    try {
      console.log(`📧 Processing automation trigger: ${triggerEvent}`);

      // Get active automation rules for this trigger
      const { data: rules, error } = await supabase
        .from('email_automation_rules')
        .select('*')
        .eq('organization_id', this.organizationId)
        .eq('trigger_event', triggerEvent)
        .eq('is_active', true)
        .order('priority', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch automation rules: ${error.message}`);
      }

      if (!rules || rules.length === 0) {
        console.log(`No automation rules found for trigger: ${triggerEvent}`);
        return;
      }

      // Process each rule
      for (const rule of rules) {
        await this.processRule(rule, triggerEvent, triggerData, contextData);
      }

    } catch (error) {
      console.error('Automation trigger processing failed:', error);
    }
  }

  /**
   * Process individual automation rule
   */
  private async processRule(
    rule: AutomationRule,
    triggerEvent: string,
    triggerData: Record<string, any>,
    contextData: Record<string, any>
  ): Promise<void> {
    try {
      // Check execution limits
      if (!this.checkExecutionLimits(rule)) {
        console.log(`Execution limits reached for rule: ${rule.rule_name}`);
        return;
      }

      // Check trigger conditions
      if (!this.evaluateConditions(rule.trigger_conditions, triggerData)) {
        console.log(`Conditions not met for rule: ${rule.rule_name}`);
        return;
      }

      // Create execution record
      const execution: AutomationExecution = {
        organization_id: this.organizationId,
        automation_rule_id: rule.id,
        trigger_event: triggerEvent,
        trigger_data: triggerData,
        status: 'pending',
        context_data: contextData
      };

      const { data: executionRecord, error: executionError } = await supabase
        .from('email_automation_executions')
        .insert(execution)
        .select()
        .single();

      if (executionError) {
        throw new Error(`Failed to create execution record: ${executionError.message}`);
      }

      // Schedule or execute immediately
      if (rule.delay_minutes > 0) {
        await this.scheduleExecution(executionRecord.id, rule.delay_minutes);
      } else {
        await this.executeRule(executionRecord.id, rule, triggerData, contextData);
      }

    } catch (error) {
      console.error(`Rule processing failed for ${rule.rule_name}:`, error);
    }
  }

  /**
   * Execute automation rule
   */
  async executeRule(
    executionId: string,
    rule: AutomationRule,
    triggerData: Record<string, any>,
    contextData: Record<string, any>
  ): Promise<void> {
    const startTime = Date.now();

    try {
      // Update execution status
      await supabase
        .from('email_automation_executions')
        .update({ 
          status: 'processing',
          executed_at: new Date().toISOString()
        })
        .eq('id', executionId);

      // Determine recipients
      const recipients = await this.getRecipients(rule, triggerData, contextData);

      if (recipients.length === 0) {
        await this.markExecutionSkipped(executionId, 'No recipients found');
        return;
      }

      // Send emails to each recipient
      let emailsSent = 0;
      let lastEmailActivityId: string | undefined;

      for (const recipient of recipients) {
        try {
          const emailResult = await this.sendAutomatedEmail(
            rule,
            recipient,
            triggerData,
            contextData
          );

          if (emailResult.status === 'sent') {
            emailsSent++;
            lastEmailActivityId = emailResult.messageId;
          }
        } catch (emailError) {
          console.error(`Failed to send email to ${recipient}:`, emailError);
        }
      }

      // Update execution record
      const processingTime = Date.now() - startTime;
      await supabase
        .from('email_automation_executions')
        .update({
          status: emailsSent > 0 ? 'sent' : 'failed',
          recipient_email: recipients[0], // Store first recipient
          email_activity_id: lastEmailActivityId,
          processing_time_ms: processingTime
        })
        .eq('id', executionId);

      // Update rule execution counts
      await this.updateRuleExecutionCounts(rule.id);

      console.log(`✅ Automation rule executed: ${rule.rule_name} (${emailsSent}/${recipients.length} emails sent)`);

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      await supabase
        .from('email_automation_executions')
        .update({
          status: 'failed',
          processing_time_ms: processingTime,
          error_message: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', executionId);

      console.error(`Automation rule execution failed: ${rule.rule_name}`, error);
    }
  }

  /**
   * Send automated email
   */
  private async sendAutomatedEmail(
    rule: AutomationRule,
    recipientEmail: string,
    triggerData: Record<string, any>,
    contextData: Record<string, any>
  ): Promise<{ status: string; messageId: string }> {
    // Map trigger data to email data based on trigger event
    switch (rule.trigger_event) {
      case 'quote_created':
        return this.emailService.sendQuoteNotification({
          clientEmail: recipientEmail,
          clientName: triggerData.client_name || 'Valued Client',
          quoteName: triggerData.quote_name || 'New Quote',
          quoteNumber: triggerData.quote_number || 'N/A',
          totalAmount: triggerData.total_amount || 0,
          currency: triggerData.currency || 'USD',
          validUntil: triggerData.valid_until || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          viewUrl: triggerData.view_url || '#',
          contractorName: triggerData.contractor_name || 'Your Contractor',
          contractorEmail: triggerData.contractor_email || '<EMAIL>',
          quoteId: triggerData.quote_id || ''
        });

      case 'quote_approved':
        return this.emailService.sendQuoteApproved({
          clientEmail: recipientEmail,
          clientName: triggerData.client_name || 'Valued Client',
          quoteName: triggerData.quote_name || 'Quote',
          totalAmount: triggerData.total_amount || 0,
          currency: triggerData.currency || 'USD',
          contractorName: triggerData.contractor_name || 'Your Contractor',
          quoteId: triggerData.quote_id || ''
        });

      case 'signature_request_created':
        return this.emailService.sendSignatureRequest({
          signerEmail: recipientEmail,
          signerName: triggerData.signer_name || 'Valued Client',
          documentTitle: triggerData.document_title || 'Document',
          signingUrl: triggerData.signing_url || '#',
          expirationDate: triggerData.expiration_date || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          requestedBy: triggerData.requested_by || 'Your Contractor',
          requestId: triggerData.request_id || ''
        });

      case 'change_request_submitted':
        return this.emailService.sendChangeRequestNotification({
          recipientEmail: recipientEmail,
          clientName: triggerData.client_name || 'Client',
          quoteName: triggerData.quote_name || 'Quote',
          changeDescription: triggerData.change_description || 'Change request submitted',
          reviewUrl: triggerData.review_url || '#',
          changeRequestId: triggerData.change_request_id || ''
        });

      case 'project_updated':
        return this.emailService.sendProjectUpdate({
          clientEmail: recipientEmail,
          clientName: triggerData.client_name || 'Valued Client',
          projectName: triggerData.project_name || 'Project',
          updateMessage: triggerData.update_message || 'Project has been updated',
          projectUrl: triggerData.project_url || '#',
          projectId: triggerData.project_id || ''
        });

      default:
        throw new Error(`Unsupported trigger event: ${rule.trigger_event}`);
    }
  }

  /**
   * Get recipients for automation rule
   */
  private async getRecipients(
    rule: AutomationRule,
    triggerData: Record<string, any>,
    contextData: Record<string, any>
  ): Promise<string[]> {
    const recipients: string[] = [];

    switch (rule.recipient_type) {
      case 'client':
        if (triggerData.client_email) {
          recipients.push(triggerData.client_email);
        }
        break;

      case 'team_member':
        // Get team members from context or organization
        if (contextData.team_member_emails) {
          recipients.push(...contextData.team_member_emails);
        }
        break;

      case 'contractor':
        if (triggerData.contractor_email) {
          recipients.push(triggerData.contractor_email);
        }
        break;

      case 'custom':
        if (rule.custom_recipients) {
          recipients.push(...rule.custom_recipients);
        }
        break;
    }

    return recipients.filter(email => email && this.isValidEmail(email));
  }

  /**
   * Check execution limits
   */
  private checkExecutionLimits(rule: AutomationRule): boolean {
    // Check daily limit
    if (rule.max_executions_per_day && rule.executions_today >= rule.max_executions_per_day) {
      return false;
    }

    // Check total limit
    if (rule.max_executions_total && rule.total_executions >= rule.max_executions_total) {
      return false;
    }

    return true;
  }

  /**
   * Evaluate trigger conditions
   */
  private evaluateConditions(
    conditions: Record<string, any>,
    triggerData: Record<string, any>
  ): boolean {
    if (!conditions || Object.keys(conditions).length === 0) {
      return true; // No conditions means always execute
    }

    // Simple condition evaluation
    for (const [key, expectedValue] of Object.entries(conditions)) {
      const actualValue = triggerData[key];
      
      if (typeof expectedValue === 'object' && expectedValue !== null) {
        // Handle complex conditions (gt, lt, contains, etc.)
        if (expectedValue.gt !== undefined && actualValue <= expectedValue.gt) return false;
        if (expectedValue.lt !== undefined && actualValue >= expectedValue.lt) return false;
        if (expectedValue.contains !== undefined && !String(actualValue).includes(expectedValue.contains)) return false;
        if (expectedValue.equals !== undefined && actualValue !== expectedValue.equals) return false;
      } else {
        // Simple equality check
        if (actualValue !== expectedValue) return false;
      }
    }

    return true;
  }

  /**
   * Schedule execution for later
   */
  private async scheduleExecution(executionId: string, delayMinutes: number): Promise<void> {
    // In a production environment, this would integrate with a job queue
    // For now, we'll use a simple setTimeout (not recommended for production)
    console.log(`📅 Scheduling execution ${executionId} for ${delayMinutes} minutes from now`);
    
    // This is a simplified implementation
    // In production, use a proper job queue like Bull, Agenda, or similar
    setTimeout(async () => {
      try {
        const { data: execution, error } = await supabase
          .from('email_automation_executions')
          .select(`
            *,
            email_automation_rules(*)
          `)
          .eq('id', executionId)
          .single();

        if (!error && execution && execution.status === 'pending') {
          await this.executeRule(
            executionId,
            execution.email_automation_rules,
            execution.trigger_data,
            execution.context_data
          );
        }
      } catch (error) {
        console.error('Scheduled execution failed:', error);
      }
    }, delayMinutes * 60 * 1000);
  }

  /**
   * Mark execution as skipped
   */
  private async markExecutionSkipped(executionId: string, reason: string): Promise<void> {
    await supabase
      .from('email_automation_executions')
      .update({
        status: 'skipped',
        error_message: reason,
        executed_at: new Date().toISOString()
      })
      .eq('id', executionId);
  }

  /**
   * Update rule execution counts
   */
  private async updateRuleExecutionCounts(ruleId: string): Promise<void> {
    await supabase
      .from('email_automation_rules')
      .update({
        executions_today: supabase.rpc('increment_executions_today'),
        total_executions: supabase.rpc('increment_total_executions'),
        last_executed: new Date().toISOString()
      })
      .eq('id', ruleId);
  }

  /**
   * Validate email address
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Create automation rule
   */
  async createAutomationRule(ruleData: Partial<AutomationRule>): Promise<string> {
    const { data: rule, error } = await supabase
      .from('email_automation_rules')
      .insert({
        organization_id: this.organizationId,
        ...ruleData
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create automation rule: ${error.message}`);
    }

    return rule.id;
  }

  /**
   * Get automation rules
   */
  async getAutomationRules(): Promise<AutomationRule[]> {
    const { data: rules, error } = await supabase
      .from('email_automation_rules')
      .select('*')
      .eq('organization_id', this.organizationId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch automation rules: ${error.message}`);
    }

    return rules || [];
  }

  /**
   * Update automation rule
   */
  async updateAutomationRule(ruleId: string, updates: Partial<AutomationRule>): Promise<void> {
    const { error } = await supabase
      .from('email_automation_rules')
      .update(updates)
      .eq('id', ruleId)
      .eq('organization_id', this.organizationId);

    if (error) {
      throw new Error(`Failed to update automation rule: ${error.message}`);
    }
  }

  /**
   * Delete automation rule
   */
  async deleteAutomationRule(ruleId: string): Promise<void> {
    const { error } = await supabase
      .from('email_automation_rules')
      .delete()
      .eq('id', ruleId)
      .eq('organization_id', this.organizationId);

    if (error) {
      throw new Error(`Failed to delete automation rule: ${error.message}`);
    }
  }
}

export default EmailAutomationService;
