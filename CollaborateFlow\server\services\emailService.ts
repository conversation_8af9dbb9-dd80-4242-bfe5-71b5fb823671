/**
 * EMAIL SERVICE
 * Enhanced email automation with SendGrid integration and templates
 */

import { MailService } from '@sendgrid/mail';
import { supabase } from './supabase';
import fs from 'fs';
import path from 'path';

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
}

export interface EmailResult {
  messageId: string;
  status: 'sent' | 'failed' | 'queued';
  timestamp: Date;
  error?: string;
}

export interface EmailActivity {
  id?: string;
  organization_id: string;
  email_type: string;
  recipient_email: string;
  subject: string;
  message_id?: string;
  status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'failed';
  reference_type?: string;
  reference_id?: string;
  template_used?: string;
  sent_at: string;
  delivered_at?: string;
  opened_at?: string;
  clicked_at?: string;
  error_message?: string;
}

export class EmailService {
  private mailService: MailService;
  private organizationId: string;
  private apiKeyValid: boolean = false;
  private templates: Map<string, EmailTemplate> = new Map();

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.mailService = new MailService();
    this.initializeService();
    this.loadTemplates();
  }

  /**
   * Initialize SendGrid service
   */
  private initializeService(): void {
    const apiKey = process.env.SENDGRID_API_KEY;
    
    if (apiKey && apiKey.length > 10) {
      try {
        this.mailService.setApiKey(apiKey);
        this.apiKeyValid = true;
        console.log('✅ SendGrid email service initialized');
      } catch (error) {
        console.error('❌ SendGrid initialization failed:', error);
        this.apiKeyValid = false;
      }
    } else {
      console.warn('⚠️ SendGrid API key not configured - using mock email service');
      this.apiKeyValid = false;
    }
  }

  /**
   * Load email templates
   */
  private loadTemplates(): void {
    const templates: EmailTemplate[] = [
      {
        id: 'quote_notification',
        name: 'Quote Notification',
        subject: 'New Quote Available: {{quoteName}}',
        htmlContent: this.getQuoteNotificationTemplate(),
        textContent: this.getQuoteNotificationTextTemplate(),
        variables: ['clientName', 'quoteName', 'totalAmount', 'validUntil', 'viewUrl', 'contractorName']
      },
      {
        id: 'quote_approved',
        name: 'Quote Approved',
        subject: 'Quote Approved: {{quoteName}}',
        htmlContent: this.getQuoteApprovedTemplate(),
        textContent: this.getQuoteApprovedTextTemplate(),
        variables: ['clientName', 'quoteName', 'totalAmount', 'contractorName']
      },
      {
        id: 'signature_request',
        name: 'Signature Request',
        subject: 'Document Ready for Signature: {{documentTitle}}',
        htmlContent: this.getSignatureRequestTemplate(),
        textContent: this.getSignatureRequestTextTemplate(),
        variables: ['signerName', 'documentTitle', 'signingUrl', 'expirationDate', 'requestedBy']
      },
      {
        id: 'change_request',
        name: 'Change Request Notification',
        subject: 'Change Request Submitted: {{quoteName}}',
        htmlContent: this.getChangeRequestTemplate(),
        textContent: this.getChangeRequestTextTemplate(),
        variables: ['clientName', 'quoteName', 'changeDescription', 'reviewUrl']
      },
      {
        id: 'project_update',
        name: 'Project Update',
        subject: 'Project Update: {{projectName}}',
        htmlContent: this.getProjectUpdateTemplate(),
        textContent: this.getProjectUpdateTextTemplate(),
        variables: ['clientName', 'projectName', 'updateMessage', 'projectUrl']
      }
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });

    console.log(`📧 Loaded ${templates.length} email templates`);
  }

  /**
   * Send quote notification email
   */
  async sendQuoteNotification(data: {
    clientEmail: string;
    clientName: string;
    quoteName: string;
    quoteNumber: string;
    totalAmount: number;
    currency: string;
    validUntil: string;
    viewUrl: string;
    contractorName: string;
    contractorEmail: string;
    quoteId: string;
  }): Promise<EmailResult> {
    const template = this.templates.get('quote_notification');
    if (!template) {
      throw new Error('Quote notification template not found');
    }

    const templateData = {
      clientName: data.clientName,
      quoteName: data.quoteName,
      quoteNumber: data.quoteNumber,
      totalAmount: this.formatCurrency(data.totalAmount, data.currency),
      validUntil: this.formatDate(data.validUntil),
      viewUrl: data.viewUrl,
      contractorName: data.contractorName,
      contractorEmail: data.contractorEmail,
      companyName: process.env.COMPANY_NAME || 'CoElec'
    };

    return this.sendTemplatedEmail({
      to: data.clientEmail,
      template: template,
      templateData: templateData,
      emailType: 'quote_notification',
      referenceType: 'quote',
      referenceId: data.quoteId
    });
  }

  /**
   * Send quote approved confirmation
   */
  async sendQuoteApproved(data: {
    clientEmail: string;
    clientName: string;
    quoteName: string;
    totalAmount: number;
    currency: string;
    contractorName: string;
    quoteId: string;
  }): Promise<EmailResult> {
    const template = this.templates.get('quote_approved');
    if (!template) {
      throw new Error('Quote approved template not found');
    }

    const templateData = {
      clientName: data.clientName,
      quoteName: data.quoteName,
      totalAmount: this.formatCurrency(data.totalAmount, data.currency),
      contractorName: data.contractorName,
      companyName: process.env.COMPANY_NAME || 'CoElec'
    };

    return this.sendTemplatedEmail({
      to: data.clientEmail,
      template: template,
      templateData: templateData,
      emailType: 'quote_approved',
      referenceType: 'quote',
      referenceId: data.quoteId
    });
  }

  /**
   * Send signature request email
   */
  async sendSignatureRequest(data: {
    signerEmail: string;
    signerName: string;
    documentTitle: string;
    signingUrl: string;
    expirationDate: string;
    requestedBy: string;
    requestId: string;
  }): Promise<EmailResult> {
    const template = this.templates.get('signature_request');
    if (!template) {
      throw new Error('Signature request template not found');
    }

    const templateData = {
      signerName: data.signerName,
      documentTitle: data.documentTitle,
      signingUrl: data.signingUrl,
      expirationDate: this.formatDate(data.expirationDate),
      requestedBy: data.requestedBy,
      companyName: process.env.COMPANY_NAME || 'CoElec'
    };

    return this.sendTemplatedEmail({
      to: data.signerEmail,
      template: template,
      templateData: templateData,
      emailType: 'signature_request',
      referenceType: 'signature_request',
      referenceId: data.requestId
    });
  }

  /**
   * Send change request notification
   */
  async sendChangeRequestNotification(data: {
    recipientEmail: string;
    clientName: string;
    quoteName: string;
    changeDescription: string;
    reviewUrl: string;
    changeRequestId: string;
  }): Promise<EmailResult> {
    const template = this.templates.get('change_request');
    if (!template) {
      throw new Error('Change request template not found');
    }

    const templateData = {
      clientName: data.clientName,
      quoteName: data.quoteName,
      changeDescription: data.changeDescription,
      reviewUrl: data.reviewUrl,
      companyName: process.env.COMPANY_NAME || 'CoElec'
    };

    return this.sendTemplatedEmail({
      to: data.recipientEmail,
      template: template,
      templateData: templateData,
      emailType: 'change_request',
      referenceType: 'change_request',
      referenceId: data.changeRequestId
    });
  }

  /**
   * Send project update email
   */
  async sendProjectUpdate(data: {
    clientEmail: string;
    clientName: string;
    projectName: string;
    updateMessage: string;
    projectUrl: string;
    projectId: string;
  }): Promise<EmailResult> {
    const template = this.templates.get('project_update');
    if (!template) {
      throw new Error('Project update template not found');
    }

    const templateData = {
      clientName: data.clientName,
      projectName: data.projectName,
      updateMessage: data.updateMessage,
      projectUrl: data.projectUrl,
      companyName: process.env.COMPANY_NAME || 'CoElec'
    };

    return this.sendTemplatedEmail({
      to: data.clientEmail,
      template: template,
      templateData: templateData,
      emailType: 'project_update',
      referenceType: 'project',
      referenceId: data.projectId
    });
  }

  /**
   * Send templated email
   */
  private async sendTemplatedEmail(params: {
    to: string;
    template: EmailTemplate;
    templateData: Record<string, any>;
    emailType: string;
    referenceType?: string;
    referenceId?: string;
    cc?: string;
    bcc?: string;
  }): Promise<EmailResult> {
    try {
      // Replace template variables
      const subject = this.replaceTemplateVariables(params.template.subject, params.templateData);
      const htmlContent = this.replaceTemplateVariables(params.template.htmlContent, params.templateData);
      const textContent = this.replaceTemplateVariables(params.template.textContent, params.templateData);

      const emailData = {
        to: params.to,
        from: {
          email: process.env.FROM_EMAIL || '<EMAIL>',
          name: process.env.COMPANY_NAME || 'CoElec'
        },
        subject: subject,
        html: htmlContent,
        text: textContent,
        cc: params.cc,
        bcc: params.bcc,
        trackingSettings: {
          clickTracking: { enable: true },
          openTracking: { enable: true }
        }
      };

      let result: EmailResult;

      if (this.apiKeyValid) {
        // Send via SendGrid
        const response = await this.mailService.send(emailData);
        const messageId = response[0].headers['x-message-id'] || `mock_${Date.now()}`;
        
        result = {
          messageId: messageId,
          status: 'sent',
          timestamp: new Date()
        };
      } else {
        // Mock email sending for development
        console.log('📧 Mock Email Sent:', {
          to: params.to,
          subject: subject,
          type: params.emailType
        });
        
        result = {
          messageId: `mock_${Date.now()}`,
          status: 'sent',
          timestamp: new Date()
        };
      }

      // Log email activity
      await this.logEmailActivity({
        organization_id: this.organizationId,
        email_type: params.emailType,
        recipient_email: params.to,
        subject: subject,
        message_id: result.messageId,
        status: 'sent',
        reference_type: params.referenceType,
        reference_id: params.referenceId,
        template_used: params.template.id,
        sent_at: new Date().toISOString()
      });

      return result;

    } catch (error) {
      console.error('Email sending failed:', error);
      
      const errorResult: EmailResult = {
        messageId: '',
        status: 'failed',
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      // Log failed email
      await this.logEmailActivity({
        organization_id: this.organizationId,
        email_type: params.emailType,
        recipient_email: params.to,
        subject: params.template.subject,
        status: 'failed',
        reference_type: params.referenceType,
        reference_id: params.referenceId,
        template_used: params.template.id,
        sent_at: new Date().toISOString(),
        error_message: errorResult.error
      });

      return errorResult;
    }
  }

  /**
   * Replace template variables
   */
  private replaceTemplateVariables(template: string, data: Record<string, any>): string {
    let result = template;
    
    Object.entries(data).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value || ''));
    });
    
    return result;
  }

  /**
   * Format currency
   */
  private formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  /**
   * Format date
   */
  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Log email activity
   */
  private async logEmailActivity(activity: EmailActivity): Promise<void> {
    try {
      await supabase
        .from('email_activity_log')
        .insert(activity);
    } catch (error) {
      console.error('Failed to log email activity:', error);
    }
  }

  // Template methods will be added in the next step due to size constraints
  private getQuoteNotificationTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4F46E5; color: white; padding: 20px; text-align: center;">
          <h1>{{companyName}}</h1>
          <h2>New Quote Available</h2>
        </div>
        <div style="padding: 30px;">
          <p>Dear {{clientName}},</p>
          <p>We have prepared a new quote for your electrical project:</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>{{quoteName}}</h3>
            <p><strong>Quote Number:</strong> {{quoteNumber}}</p>
            <p><strong>Total Amount:</strong> {{totalAmount}}</p>
            <p><strong>Valid Until:</strong> {{validUntil}}</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{viewUrl}}" style="background-color: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              View & Approve Quote
            </a>
          </div>
          <p>If you have any questions, please contact {{contractorName}} at {{contractorEmail}}.</p>
          <p>Best regards,<br>{{companyName}} Team</p>
        </div>
      </div>
    `;
  }

  private getQuoteNotificationTextTemplate(): string {
    return `
Dear {{clientName}},

We have prepared a new quote for your electrical project:

{{quoteName}}
Quote Number: {{quoteNumber}}
Total Amount: {{totalAmount}}
Valid Until: {{validUntil}}

View and approve your quote: {{viewUrl}}

If you have any questions, please contact {{contractorName}} at {{contractorEmail}}.

Best regards,
{{companyName}} Team
    `;
  }

  private getQuoteApprovedTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #10B981; color: white; padding: 20px; text-align: center;">
          <h1>{{companyName}}</h1>
          <h2>Quote Approved!</h2>
        </div>
        <div style="padding: 30px;">
          <p>Dear {{clientName}},</p>
          <p>Thank you for approving your quote:</p>
          <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10B981;">
            <h3>{{quoteName}}</h3>
            <p><strong>Approved Amount:</strong> {{totalAmount}}</p>
          </div>
          <p>We will begin work on your project shortly. {{contractorName}} will contact you to schedule the start date.</p>
          <p>Thank you for choosing {{companyName}}!</p>
          <p>Best regards,<br>{{companyName}} Team</p>
        </div>
      </div>
    `;
  }

  private getQuoteApprovedTextTemplate(): string {
    return `
Dear {{clientName}},

Thank you for approving your quote:

{{quoteName}}
Approved Amount: {{totalAmount}}

We will begin work on your project shortly. {{contractorName}} will contact you to schedule the start date.

Thank you for choosing {{companyName}}!

Best regards,
{{companyName}} Team
    `;
  }

  private getSignatureRequestTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #7C3AED; color: white; padding: 20px; text-align: center;">
          <h1>{{companyName}}</h1>
          <h2>Document Ready for Signature</h2>
        </div>
        <div style="padding: 30px;">
          <p>Dear {{signerName}},</p>
          <p>{{requestedBy}} has requested your signature on the following document:</p>
          <div style="background-color: #faf5ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #7C3AED;">
            <h3>{{documentTitle}}</h3>
            <p><strong>Expires:</strong> {{expirationDate}}</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{signingUrl}}" style="background-color: #7C3AED; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              Sign Document
            </a>
          </div>
          <p>Please sign the document before the expiration date.</p>
          <p>Best regards,<br>{{companyName}} Team</p>
        </div>
      </div>
    `;
  }

  private getSignatureRequestTextTemplate(): string {
    return `
Dear {{signerName}},

{{requestedBy}} has requested your signature on the following document:

{{documentTitle}}
Expires: {{expirationDate}}

Sign document: {{signingUrl}}

Please sign the document before the expiration date.

Best regards,
{{companyName}} Team
    `;
  }

  private getChangeRequestTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #F59E0B; color: white; padding: 20px; text-align: center;">
          <h1>{{companyName}}</h1>
          <h2>Change Request Submitted</h2>
        </div>
        <div style="padding: 30px;">
          <p>A change request has been submitted for:</p>
          <div style="background-color: #fffbeb; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #F59E0B;">
            <h3>{{quoteName}}</h3>
            <p><strong>Client:</strong> {{clientName}}</p>
            <p><strong>Change Description:</strong></p>
            <p>{{changeDescription}}</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{reviewUrl}}" style="background-color: #F59E0B; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              Review Change Request
            </a>
          </div>
          <p>Please review and respond to this change request.</p>
          <p>Best regards,<br>{{companyName}} Team</p>
        </div>
      </div>
    `;
  }

  private getChangeRequestTextTemplate(): string {
    return `
A change request has been submitted for:

{{quoteName}}
Client: {{clientName}}

Change Description:
{{changeDescription}}

Review change request: {{reviewUrl}}

Please review and respond to this change request.

Best regards,
{{companyName}} Team
    `;
  }

  private getProjectUpdateTemplate(): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #3B82F6; color: white; padding: 20px; text-align: center;">
          <h1>{{companyName}}</h1>
          <h2>Project Update</h2>
        </div>
        <div style="padding: 30px;">
          <p>Dear {{clientName}},</p>
          <p>We have an update on your project:</p>
          <div style="background-color: #eff6ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3B82F6;">
            <h3>{{projectName}}</h3>
            <p>{{updateMessage}}</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{projectUrl}}" style="background-color: #3B82F6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              View Project Details
            </a>
          </div>
          <p>Thank you for choosing {{companyName}}!</p>
          <p>Best regards,<br>{{companyName}} Team</p>
        </div>
      </div>
    `;
  }

  private getProjectUpdateTextTemplate(): string {
    return `
Dear {{clientName}},

We have an update on your project:

{{projectName}}
{{updateMessage}}

View project details: {{projectUrl}}

Thank you for choosing {{companyName}}!

Best regards,
{{companyName}} Team
    `;
  }
}

export default EmailService;
