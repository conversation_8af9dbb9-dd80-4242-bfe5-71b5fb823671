/**
 * Feature Flags Service for CollaborateFlow
 *
 * This module provides a central place to manage feature flags,
 * allowing gradual transition from mock data to real database implementation.
 */

// Feature flag configuration
interface FeatureFlags {
  useRealDatabase: boolean;
  useRealTeamService: boolean;
  useRealProjectService: boolean;
  useRealColumnService: boolean;
  useRealTaskService: boolean;
  useRealFloorPlanService: boolean;
  useRealQuoteService: boolean;
  useRealSymbolService: boolean;
  useRealMaterialService: boolean;
  useRealLaborService: boolean;
  useRealAI: boolean;
}

// Default configuration based on environment
const defaultFlags: FeatureFlags = {
  useRealDatabase: process.env.USE_REAL_DATABASE === 'true',
  useRealTeamService: process.env.USE_REAL_TEAM_SERVICE === 'true',
  useRealProjectService: process.env.USE_REAL_PROJECT_SERVICE === 'true',
  useRealColumnService: process.env.USE_REAL_COLUMN_SERVICE === 'true',
  useRealTaskService: process.env.USE_REAL_TASK_SERVICE === 'true',
  useRealFloorPlanService: process.env.USE_REAL_FLOOR_PLAN_SERVICE === 'true',
  useRealQuoteService: process.env.USE_REAL_QUOTE_SERVICE === 'true',
  useRealSymbolService: process.env.USE_REAL_SYMBOL_SERVICE === 'true',
  useRealMaterialService: process.env.USE_REAL_MATERIAL_SERVICE === 'true',
  useRealLaborService: process.env.USE_REAL_LABOR_SERVICE === 'true',
  useRealAI: process.env.USE_REAL_AI === 'true',
};

// Current feature flags (can be modified at runtime)
let currentFlags: FeatureFlags = { ...defaultFlags };

/**
 * Get the current state of a feature flag
 * @param flagName The name of the flag
 * @returns The current state of the flag (true or false)
 */
export function getFeatureFlag<K extends keyof FeatureFlags>(flagName: K): boolean {
  return currentFlags[flagName];
}

/**
 * Set a feature flag value
 * @param flagName The name of the flag
 * @param value The new value for the flag
 */
export function setFeatureFlag<K extends keyof FeatureFlags>(flagName: K, value: boolean): void {
  currentFlags[flagName] = value;
  console.log(`Feature flag ${flagName} set to ${value}`);
}

/**
 * Enable all real database services
 */
export function enableAllRealServices(): void {
  Object.keys(currentFlags).forEach(key => {
    currentFlags[key as keyof FeatureFlags] = true;
  });
  console.log('All real database services enabled');
}

/**
 * Disable all real database services (use mock data)
 */
export function disableAllRealServices(): void {
  Object.keys(currentFlags).forEach(key => {
    currentFlags[key as keyof FeatureFlags] = false;
  });
  console.log('All real database services disabled, using mock data');
}

/**
 * Get the complete feature flag configuration
 * @returns The current feature flag configuration
 */
export function getAllFeatureFlags(): FeatureFlags {
  return { ...currentFlags };
}

/**
 * Reset feature flags to default values from environment
 */
export function resetFeatureFlagsToDefault(): void {
  currentFlags = { ...defaultFlags };
  console.log('Feature flags reset to default values');
}
