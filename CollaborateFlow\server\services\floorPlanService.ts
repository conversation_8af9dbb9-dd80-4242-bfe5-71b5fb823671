import { supabase, handleSupabaseError } from './supabase';
import { FloorPlan, Symbol } from '../types';

/**
 * Get all floor plans for a project
 * @param projectId Project ID
 * @returns Promise resolving to an array of floor plans
 */
export async function getFloorPlans(projectId: number): Promise<FloorPlan[]> {
  try {
    const { data, error } = await supabase
      .from('floor_plans')
      .select('*')
      .eq('project_id', projectId)
      .order('version', { ascending: false });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getFloorPlans');
  }
}

/**
 * Get a specific floor plan
 * @param floorPlanId Floor plan ID
 * @returns Promise resolving to a floor plan or null if not found
 */
export async function getFloorPlan(floorPlanId: number): Promise<FloorPlan | null> {
  try {
    const { data, error } = await supabase
      .from('floor_plans')
      .select('*')
      .eq('id', floorPlanId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') { // Code for no rows returned
        return null;
      }
      throw error;
    }
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'getFloorPlan');
  }
}

/**
 * Create a new floor plan
 * @param floorPlan Floor plan data without ID
 * @returns Promise resolving to the created floor plan
 */
export async function createFloorPlan(floorPlan: Omit<FloorPlan, 'id' | 'createdAt' | 'updatedAt'>): Promise<FloorPlan> {
  try {
    // Get the latest version for this project
    const { data: latestVersions, error: versionError } = await supabase
      .from('floor_plans')
      .select('version')
      .eq('project_id', floorPlan.projectId)
      .order('version', { ascending: false })
      .limit(1);
    
    if (versionError) throw versionError;
    
    // Determine the new version number
    const newVersion = latestVersions && latestVersions.length > 0 ? latestVersions[0].version + 1 : 1;
    
    // Create the floor plan with the new version
    const { data, error } = await supabase
      .from('floor_plans')
      .insert({
        ...floorPlan,
        version: newVersion,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createFloorPlan');
  }
}

/**
 * Update floor plan details
 * @param floorPlanId Floor plan ID
 * @param data Updated floor plan data
 * @returns Promise resolving to the updated floor plan
 */
export async function updateFloorPlan(floorPlanId: number, data: Partial<Omit<FloorPlan, 'id' | 'createdAt' | 'updatedAt' | 'version'>>): Promise<FloorPlan> {
  try {
    const { data: updatedFloorPlan, error } = await supabase
      .from('floor_plans')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', floorPlanId)
      .select()
      .single();
    
    if (error) throw error;
    
    return updatedFloorPlan;
  } catch (error) {
    return handleSupabaseError(error, 'updateFloorPlan');
  }
}

/**
 * Delete a floor plan
 * @param floorPlanId Floor plan ID
 * @returns Promise resolving to true if successful
 */
export async function deleteFloorPlan(floorPlanId: number): Promise<boolean> {
  try {
    // First delete all symbols associated with this floor plan
    const { error: symbolsError } = await supabase
      .from('symbols')
      .delete()
      .eq('floor_plan_id', floorPlanId);
    
    if (symbolsError) throw symbolsError;
    
    // Then delete the floor plan
    const { error } = await supabase
      .from('floor_plans')
      .delete()
      .eq('id', floorPlanId);
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    return handleSupabaseError(error, 'deleteFloorPlan');
  }
}

/**
 * Get all symbols for a floor plan
 * @param floorPlanId Floor plan ID
 * @returns Promise resolving to an array of symbols
 */
export async function getSymbols(floorPlanId: number): Promise<Symbol[]> {
  try {
    const { data, error } = await supabase
      .from('symbols')
      .select('*')
      .eq('floor_plan_id', floorPlanId);
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getSymbols');
  }
}

/**
 * Create a new symbol
 * @param symbol Symbol data without ID
 * @returns Promise resolving to the created symbol
 */
export async function createSymbol(symbol: Omit<Symbol, 'id' | 'createdAt'>): Promise<Symbol> {
  try {
    const { data, error } = await supabase
      .from('symbols')
      .insert({
        ...symbol,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createSymbol');
  }
}

/**
 * Update symbol details
 * @param symbolId Symbol ID
 * @param data Updated symbol data
 * @returns Promise resolving to the updated symbol
 */
export async function updateSymbol(symbolId: number, data: Partial<Omit<Symbol, 'id' | 'createdAt'>>): Promise<Symbol> {
  try {
    const { data: updatedSymbol, error } = await supabase
      .from('symbols')
      .update(data)
      .eq('id', symbolId)
      .select()
      .single();
    
    if (error) throw error;
    
    return updatedSymbol;
  } catch (error) {
    return handleSupabaseError(error, 'updateSymbol');
  }
}

/**
 * Delete a symbol
 * @param symbolId Symbol ID
 * @returns Promise resolving to true if successful
 */
export async function deleteSymbol(symbolId: number): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('symbols')
      .delete()
      .eq('id', symbolId);
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    return handleSupabaseError(error, 'deleteSymbol');
  }
}

/**
 * Bulk create symbols (for AI-detected symbols)
 * @param symbols Array of symbol data
 * @returns Promise resolving to the created symbols
 */
export async function bulkCreateSymbols(symbols: Omit<Symbol, 'id' | 'createdAt'>[]): Promise<Symbol[]> {
  try {
    // Add created_at to all symbols
    const symbolsWithTimestamp = symbols.map(symbol => ({
      ...symbol,
      created_at: new Date().toISOString()
    }));
    
    const { data, error } = await supabase
      .from('symbols')
      .insert(symbolsWithTimestamp)
      .select();
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'bulkCreateSymbols');
  }
}
