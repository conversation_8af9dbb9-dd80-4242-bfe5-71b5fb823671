import { supabase, handleSupabaseError } from './supabase';
import { Project } from '../types';

/**
 * Get all projects a user has access to
 * @param userId User ID
 * @returns Promise resolving to an array of projects
 */
export async function getProjects(userId: number): Promise<Project[]> {
  try {
    // Get team IDs where the user is a member
    const { data: teamMembers, error: memberError } = await supabase
      .from('team_members')
      .select('team_id')
      .eq('user_id', userId);
    
    if (memberError) throw memberError;
    
    if (!teamMembers || teamMembers.length === 0) {
      return [];
    }
    
    const teamIds = teamMembers.map(member => member.team_id);
    
    // Get projects belonging to those teams
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .in('team_id', teamIds);
    
    if (projectsError) throw projectsError;
    
    return projects || [];
  } catch (error) {
    return handleSupabaseError(error, 'getProjects');
  }
}

/**
 * Get a specific project by ID
 * @param projectId Project ID
 * @returns Promise resolving to a project or null if not found
 */
export async function getProject(projectId: number): Promise<Project | null> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') { // Code for no rows returned
        return null;
      }
      throw error;
    }
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'getProject');
  }
}

/**
 * Create a new project
 * @param project Project data without ID
 * @returns Promise resolving to the created project
 */
export async function createProject(project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .insert({
        ...project,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createProject');
  }
}

/**
 * Update project details
 * @param projectId Project ID
 * @param data Updated project data
 * @returns Promise resolving to the updated project
 */
export async function updateProject(projectId: number, data: Partial<Omit<Project, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Project> {
  try {
    const { data: updatedProject, error } = await supabase
      .from('projects')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId)
      .select()
      .single();
    
    if (error) throw error;
    
    return updatedProject;
  } catch (error) {
    return handleSupabaseError(error, 'updateProject');
  }
}

/**
 * Delete a project
 * @param projectId Project ID
 * @returns Promise resolving to true if successful
 */
export async function deleteProject(projectId: number): Promise<boolean> {
  try {
    // First delete all related entities (this would be expanded in a full implementation)
    // For example, delete tasks, columns, floor plans, etc. related to this project
    
    // Then delete the project
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    return handleSupabaseError(error, 'deleteProject');
  }
}

/**
 * Get projects for a specific team
 * @param teamId Team ID
 * @returns Promise resolving to an array of projects
 */
export async function getTeamProjects(teamId: number): Promise<Project[]> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('team_id', teamId);
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getTeamProjects');
  }
}
