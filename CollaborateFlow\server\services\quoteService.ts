import { supabase, handleSupabaseError } from './supabase';
import { Quote, Material, Labor } from '../types';

/**
 * Get all quotes for a project
 * @param projectId Project ID
 * @returns Promise resolving to an array of quotes
 */
export async function getQuotes(projectId: number): Promise<Quote[]> {
  try {
    const { data, error } = await supabase
      .from('quotes')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getQuotes');
  }
}

/**
 * Get a specific quote
 * @param quoteId Quote ID
 * @returns Promise resolving to a quote or null if not found
 */
export async function getQuote(quoteId: number): Promise<Quote | null> {
  try {
    const { data, error } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') { // Code for no rows returned
        return null;
      }
      throw error;
    }
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'getQuote');
  }
}

/**
 * Create a new quote
 * @param quote Quote data without ID
 * @returns Promise resolving to the created quote
 */
export async function createQuote(quote: Omit<Quote, 'id' | 'createdAt' | 'updatedAt'>): Promise<Quote> {
  try {
    const { data, error } = await supabase
      .from('quotes')
      .insert({
        ...quote,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createQuote');
  }
}

/**
 * Update quote details
 * @param quoteId Quote ID
 * @param data Updated quote data
 * @returns Promise resolving to the updated quote
 */
export async function updateQuote(quoteId: number, data: Partial<Omit<Quote, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Quote> {
  try {
    const { data: updatedQuote, error } = await supabase
      .from('quotes')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', quoteId)
      .select()
      .single();
    
    if (error) throw error;
    
    return updatedQuote;
  } catch (error) {
    return handleSupabaseError(error, 'updateQuote');
  }
}

/**
 * Delete a quote
 * @param quoteId Quote ID
 * @returns Promise resolving to true if successful
 */
export async function deleteQuote(quoteId: number): Promise<boolean> {
  try {
    // First delete all materials and labor associated with this quote
    const { error: materialsError } = await supabase
      .from('materials')
      .delete()
      .eq('quote_id', quoteId);
    
    if (materialsError) throw materialsError;
    
    const { error: laborError } = await supabase
      .from('labor')
      .delete()
      .eq('quote_id', quoteId);
    
    if (laborError) throw laborError;
    
    // Then delete the quote
    const { error } = await supabase
      .from('quotes')
      .delete()
      .eq('id', quoteId);
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    return handleSupabaseError(error, 'deleteQuote');
  }
}

/**
 * Get all materials for a quote
 * @param quoteId Quote ID
 * @returns Promise resolving to an array of materials
 */
export async function getMaterials(quoteId: number): Promise<Material[]> {
  try {
    const { data, error } = await supabase
      .from('materials')
      .select('*')
      .eq('quote_id', quoteId);
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getMaterials');
  }
}

/**
 * Create a new material
 * @param material Material data without ID
 * @returns Promise resolving to the created material
 */
export async function createMaterial(material: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>): Promise<Material> {
  try {
    const { data, error } = await supabase
      .from('materials')
      .insert({
        ...material,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createMaterial');
  }
}

/**
 * Bulk create materials
 * @param materials Array of material data
 * @returns Promise resolving to the created materials
 */
export async function bulkCreateMaterials(materials: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<Material[]> {
  try {
    // Add timestamps to all materials
    const materialsWithTimestamps = materials.map(material => ({
      ...material,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
    
    const { data, error } = await supabase
      .from('materials')
      .insert(materialsWithTimestamps)
      .select();
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'bulkCreateMaterials');
  }
}

/**
 * Get all labor items for a quote
 * @param quoteId Quote ID
 * @returns Promise resolving to an array of labor items
 */
export async function getLaborItems(quoteId: number): Promise<Labor[]> {
  try {
    const { data, error } = await supabase
      .from('labor')
      .select('*')
      .eq('quote_id', quoteId);
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getLaborItems');
  }
}

/**
 * Create a new labor item
 * @param labor Labor data without ID
 * @returns Promise resolving to the created labor item
 */
export async function createLaborItem(labor: Omit<Labor, 'id' | 'createdAt' | 'updatedAt'>): Promise<Labor> {
  try {
    const { data, error } = await supabase
      .from('labor')
      .insert({
        ...labor,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createLaborItem');
  }
}

/**
 * Bulk create labor items
 * @param laborItems Array of labor data
 * @returns Promise resolving to the created labor items
 */
export async function bulkCreateLaborItems(laborItems: Omit<Labor, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<Labor[]> {
  try {
    // Add timestamps to all labor items
    const laborItemsWithTimestamps = laborItems.map(item => ({
      ...item,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
    
    const { data, error } = await supabase
      .from('labor')
      .insert(laborItemsWithTimestamps)
      .select();
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'bulkCreateLaborItems');
  }
}
