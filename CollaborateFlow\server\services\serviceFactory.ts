/**
 * Service Factory for CollaborateFlow
 * 
 * This module provides a factory pattern to switch between mock and real implementations
 * of services based on feature flags. This allows for gradual migration from mock
 * to real database implementations.
 */

import { getFeatureFlag } from './featureFlags';

// Import mock services
import { mockTeams, mockProjects, mockTeamMembers, mockColumns, mockTasks, mockFloorPlans, mockQuotes } from '../mocks/mockData';

// Import real services
import * as RealTeamService from './teamService';
import * as RealProjectService from './projectService';
import * as RealColumnService from './columnService';
import * as RealTaskService from './taskService';
import * as RealFloorPlanService from './floorPlanService';
import * as RealQuoteService from './quoteService';

// Mock implementations
const MockTeamService = {
  getTeams: async (userId: number) => mockTeams,
  getTeam: async (teamId: number) => mockTeams.find(t => t.id === teamId) || null,
  createTeam: async (team: any) => ({ ...team, id: Math.max(...mockTeams.map(t => t.id)) + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  updateTeam: async (teamId: number, data: any) => ({ ...mockTeams.find(t => t.id === teamId), ...data, updatedAt: new Date().toISOString() }),
  deleteTeam: async (teamId: number) => true,
  getTeamMembers: async (teamId: number) => mockTeamMembers.filter(m => m.teamId === teamId),
  addTeamMember: async (teamMember: any) => ({ ...teamMember, id: Math.max(...mockTeamMembers.map(m => m.id)) + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  removeTeamMember: async (teamId: number, userId: number) => true,
  updateTeamMemberRole: async (teamId: number, userId: number, role: string) => {
    const member = mockTeamMembers.find(m => m.teamId === teamId && m.userId === userId);
    return { ...member, role, updatedAt: new Date().toISOString() };
  },
};

const MockProjectService = {
  getProjects: async (userId: number) => mockProjects,
  getProject: async (projectId: number) => mockProjects.find(p => p.id === projectId) || null,
  createProject: async (project: any) => ({ ...project, id: Math.max(...mockProjects.map(p => p.id)) + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  updateProject: async (projectId: number, data: any) => ({ ...mockProjects.find(p => p.id === projectId), ...data, updatedAt: new Date().toISOString() }),
  deleteProject: async (projectId: number) => true,
  getTeamProjects: async (teamId: number) => mockProjects.filter(p => p.teamId === teamId),
};

const MockColumnService = {
  getColumns: async (projectId: number) => mockColumns.filter(c => c.projectId === projectId).sort((a, b) => a.order - b.order),
  createColumn: async (column: any) => ({ ...column, id: Math.max(...mockColumns.map(c => c.id)) + 1 }),
  updateColumn: async (columnId: number, data: any) => ({ ...mockColumns.find(c => c.id === columnId), ...data }),
  deleteColumn: async (columnId: number) => true,
  reorderColumns: async (projectId: number, columnOrders: any[]) => true,
};

const MockTaskService = {
  getTasks: async (projectId: number) => mockTasks.filter(t => t.projectId === projectId),
  getTask: async (taskId: number) => mockTasks.find(t => t.id === taskId) || null,
  createTask: async (task: any) => ({ ...task, id: Math.max(...mockTasks.map(t => t.id)) + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  updateTask: async (taskId: number, data: any) => ({ ...mockTasks.find(t => t.id === taskId), ...data, updatedAt: new Date().toISOString() }),
  deleteTask: async (taskId: number) => true,
  getColumnTasks: async (columnId: number) => mockTasks.filter(t => t.columnId === columnId).sort((a, b) => a.order - b.order),
  moveTask: async (taskId: number, newColumnId: number, newOrder: number) => {
    const task = mockTasks.find(t => t.id === taskId);
    return { ...task, columnId: newColumnId, order: newOrder, updatedAt: new Date().toISOString() };
  },
};

const MockFloorPlanService = {
  getFloorPlans: async (projectId: number) => mockFloorPlans.filter(f => f.projectId === projectId),
  getFloorPlan: async (floorPlanId: number) => mockFloorPlans.find(f => f.id === floorPlanId) || null,
  createFloorPlan: async (floorPlan: any) => ({ ...floorPlan, id: Math.max(...mockFloorPlans.map(f => f.id)) + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  updateFloorPlan: async (floorPlanId: number, data: any) => ({ ...mockFloorPlans.find(f => f.id === floorPlanId), ...data, updatedAt: new Date().toISOString() }),
  deleteFloorPlan: async (floorPlanId: number) => true,
  getSymbols: async (floorPlanId: number) => [], // Mock empty symbols array
  createSymbol: async (symbol: any) => ({ ...symbol, id: 1, createdAt: new Date().toISOString() }),
  updateSymbol: async (symbolId: number, data: any) => ({ ...data, id: symbolId, createdAt: new Date().toISOString() }),
  deleteSymbol: async (symbolId: number) => true,
  bulkCreateSymbols: async (symbols: any[]) => symbols.map((s, i) => ({ ...s, id: i + 1, createdAt: new Date().toISOString() })),
};

const MockQuoteService = {
  getQuotes: async (projectId: number) => mockQuotes.filter(q => q.projectId === projectId),
  getQuote: async (quoteId: number) => mockQuotes.find(q => q.id === quoteId) || null,
  createQuote: async (quote: any) => ({ ...quote, id: Math.max(...mockQuotes.map(q => q.id)) + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  updateQuote: async (quoteId: number, data: any) => ({ ...mockQuotes.find(q => q.id === quoteId), ...data, updatedAt: new Date().toISOString() }),
  deleteQuote: async (quoteId: number) => true,
  getMaterials: async (quoteId: number) => [], // Mock empty materials array
  createMaterial: async (material: any) => ({ ...material, id: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  bulkCreateMaterials: async (materials: any[]) => materials.map((m, i) => ({ ...m, id: i + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() })),
  getLaborItems: async (quoteId: number) => [], // Mock empty labor array
  createLaborItem: async (labor: any) => ({ ...labor, id: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }),
  bulkCreateLaborItems: async (laborItems: any[]) => laborItems.map((l, i) => ({ ...l, id: i + 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() })),
};

// Service factory functions
export const TeamService = {
  getTeams: async (userId: number) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.getTeams(userId)
      : await MockTeamService.getTeams(userId);
  },
  getTeam: async (teamId: number) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.getTeam(teamId)
      : await MockTeamService.getTeam(teamId);
  },
  createTeam: async (team: any) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.createTeam(team)
      : await MockTeamService.createTeam(team);
  },
  updateTeam: async (teamId: number, data: any) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.updateTeam(teamId, data)
      : await MockTeamService.updateTeam(teamId, data);
  },
  deleteTeam: async (teamId: number) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.deleteTeam(teamId)
      : await MockTeamService.deleteTeam(teamId);
  },
  getTeamMembers: async (teamId: number) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.getTeamMembers(teamId)
      : await MockTeamService.getTeamMembers(teamId);
  },
  addTeamMember: async (teamMember: any) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.addTeamMember(teamMember)
      : await MockTeamService.addTeamMember(teamMember);
  },
  removeTeamMember: async (teamId: number, userId: number) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.removeTeamMember(teamId, userId)
      : await MockTeamService.removeTeamMember(teamId, userId);
  },
  updateTeamMemberRole: async (teamId: number, userId: number, role: string) => {
    return getFeatureFlag('useRealTeamService')
      ? await RealTeamService.updateTeamMemberRole(teamId, userId, role as any)
      : await MockTeamService.updateTeamMemberRole(teamId, userId, role);
  },
};

export const ProjectService = {
  getProjects: async (userId: number) => {
    return getFeatureFlag('useRealProjectService')
      ? await RealProjectService.getProjects(userId)
      : await MockProjectService.getProjects(userId);
  },
  getProject: async (projectId: number) => {
    return getFeatureFlag('useRealProjectService')
      ? await RealProjectService.getProject(projectId)
      : await MockProjectService.getProject(projectId);
  },
  createProject: async (project: any) => {
    return getFeatureFlag('useRealProjectService')
      ? await RealProjectService.createProject(project)
      : await MockProjectService.createProject(project);
  },
  updateProject: async (projectId: number, data: any) => {
    return getFeatureFlag('useRealProjectService')
      ? await RealProjectService.updateProject(projectId, data)
      : await MockProjectService.updateProject(projectId, data);
  },
  deleteProject: async (projectId: number) => {
    return getFeatureFlag('useRealProjectService')
      ? await RealProjectService.deleteProject(projectId)
      : await MockProjectService.deleteProject(projectId);
  },
  getTeamProjects: async (teamId: number) => {
    return getFeatureFlag('useRealProjectService')
      ? await RealProjectService.getTeamProjects(teamId)
      : await MockProjectService.getTeamProjects(teamId);
  },
};

export const ColumnService = {
  getColumns: async (projectId: number) => {
    return getFeatureFlag('useRealColumnService')
      ? await RealColumnService.getColumns(projectId)
      : await MockColumnService.getColumns(projectId);
  },
  createColumn: async (column: any) => {
    return getFeatureFlag('useRealColumnService')
      ? await RealColumnService.createColumn(column)
      : await MockColumnService.createColumn(column);
  },
  updateColumn: async (columnId: number, data: any) => {
    return getFeatureFlag('useRealColumnService')
      ? await RealColumnService.updateColumn(columnId, data)
      : await MockColumnService.updateColumn(columnId, data);
  },
  deleteColumn: async (columnId: number) => {
    return getFeatureFlag('useRealColumnService')
      ? await RealColumnService.deleteColumn(columnId)
      : await MockColumnService.deleteColumn(columnId);
  },
  reorderColumns: async (projectId: number, columnOrders: any[]) => {
    return getFeatureFlag('useRealColumnService')
      ? await RealColumnService.reorderColumns(projectId, columnOrders)
      : await MockColumnService.reorderColumns(projectId, columnOrders);
  },
};

export const TaskService = {
  getTasks: async (projectId: number) => {
    return getFeatureFlag('useRealTaskService')
      ? await RealTaskService.getTasks(projectId)
      : await MockTaskService.getTasks(projectId);
  },
  getTask: async (taskId: number) => {
    return getFeatureFlag('useRealTaskService')
      ? await RealTaskService.getTask(taskId)
      : await MockTaskService.getTask(taskId);
  },
  createTask: async (task: any) => {
    return getFeatureFlag('useRealTaskService')
      ? await RealTaskService.createTask(task)
      : await MockTaskService.createTask(task);
  },
  updateTask: async (taskId: number, data: any) => {
    return getFeatureFlag('useRealTaskService')
      ? await RealTaskService.updateTask(taskId, data)
      : await MockTaskService.updateTask(taskId, data);
  },
  deleteTask: async (taskId: number) => {
    return getFeatureFlag('useRealTaskService')
      ? await RealTaskService.deleteTask(taskId)
      : await MockTaskService.deleteTask(taskId);
  },
  getColumnTasks: async (columnId: number) => {
    return getFeatureFlag('useRealTaskService')
      ? await RealTaskService.getColumnTasks(columnId)
      : await MockTaskService.getColumnTasks(columnId);
  },
  moveTask: async (taskId: number, newColumnId: number, newOrder: number) => {
    return getFeatureFlag('useRealTaskService')
      ? await RealTaskService.moveTask(taskId, newColumnId, newOrder)
      : await MockTaskService.moveTask(taskId, newColumnId, newOrder);
  },
};

export const FloorPlanService = {
  getFloorPlans: async (projectId: number) => {
    return getFeatureFlag('useRealFloorPlanService')
      ? await RealFloorPlanService.getFloorPlans(projectId)
      : await MockFloorPlanService.getFloorPlans(projectId);
  },
  getFloorPlan: async (floorPlanId: number) => {
    return getFeatureFlag('useRealFloorPlanService')
      ? await RealFloorPlanService.getFloorPlan(floorPlanId)
      : await MockFloorPlanService.getFloorPlan(floorPlanId);
  },
  createFloorPlan: async (floorPlan: any) => {
    return getFeatureFlag('useRealFloorPlanService')
      ? await RealFloorPlanService.createFloorPlan(floorPlan)
      : await MockFloorPlanService.createFloorPlan(floorPlan);
  },
  updateFloorPlan: async (floorPlanId: number, data: any) => {
    return getFeatureFlag('useRealFloorPlanService')
      ? await RealFloorPlanService.updateFloorPlan(floorPlanId, data)
      : await MockFloorPlanService.updateFloorPlan(floorPlanId, data);
  },
  deleteFloorPlan: async (floorPlanId: number) => {
    return getFeatureFlag('useRealFloorPlanService')
      ? await RealFloorPlanService.deleteFloorPlan(floorPlanId)
      : await MockFloorPlanService.deleteFloorPlan(floorPlanId);
  },
  getSymbols: async (floorPlanId: number) => {
    return getFeatureFlag('useRealSymbolService')
      ? await RealFloorPlanService.getSymbols(floorPlanId)
      : await MockFloorPlanService.getSymbols(floorPlanId);
  },
  createSymbol: async (symbol: any) => {
    return getFeatureFlag('useRealSymbolService')
      ? await RealFloorPlanService.createSymbol(symbol)
      : await MockFloorPlanService.createSymbol(symbol);
  },
  updateSymbol: async (symbolId: number, data: any) => {
    return getFeatureFlag('useRealSymbolService')
      ? await RealFloorPlanService.updateSymbol(symbolId, data)
      : await MockFloorPlanService.updateSymbol(symbolId, data);
  },
  deleteSymbol: async (symbolId: number) => {
    return getFeatureFlag('useRealSymbolService')
      ? await RealFloorPlanService.deleteSymbol(symbolId)
      : await MockFloorPlanService.deleteSymbol(symbolId);
  },
  bulkCreateSymbols: async (symbols: any[]) => {
    return getFeatureFlag('useRealSymbolService')
      ? await RealFloorPlanService.bulkCreateSymbols(symbols)
      : await MockFloorPlanService.bulkCreateSymbols(symbols);
  },
};

export const QuoteService = {
  getQuotes: async (projectId: number) => {
    return getFeatureFlag('useRealQuoteService')
      ? await RealQuoteService.getQuotes(projectId)
      : await MockQuoteService.getQuotes(projectId);
  },
  getQuote: async (quoteId: number) => {
    return getFeatureFlag('useRealQuoteService')
      ? await RealQuoteService.getQuote(quoteId)
      : await MockQuoteService.getQuote(quoteId);
  },
  createQuote: async (quote: any) => {
    return getFeatureFlag('useRealQuoteService')
      ? await RealQuoteService.createQuote(quote)
      : await MockQuoteService.createQuote(quote);
  },
  updateQuote: async (quoteId: number, data: any) => {
    return getFeatureFlag('useRealQuoteService')
      ? await RealQuoteService.updateQuote(quoteId, data)
      : await MockQuoteService.updateQuote(quoteId, data);
  },
  deleteQuote: async (quoteId: number) => {
    return getFeatureFlag('useRealQuoteService')
      ? await RealQuoteService.deleteQuote(quoteId)
      : await MockQuoteService.deleteQuote(quoteId);
  },
  getMaterials: async (quoteId: number) => {
    return getFeatureFlag('useRealMaterialService')
      ? await RealQuoteService.getMaterials(quoteId)
      : await MockQuoteService.getMaterials(quoteId);
  },
  createMaterial: async (material: any) => {
    return getFeatureFlag('useRealMaterialService')
      ? await RealQuoteService.createMaterial(material)
      : await MockQuoteService.createMaterial(material);
  },
  bulkCreateMaterials: async (materials: any[]) => {
    return getFeatureFlag('useRealMaterialService')
      ? await RealQuoteService.bulkCreateMaterials(materials)
      : await MockQuoteService.bulkCreateMaterials(materials);
  },
  getLaborItems: async (quoteId: number) => {
    return getFeatureFlag('useRealLaborService')
      ? await RealQuoteService.getLaborItems(quoteId)
      : await MockQuoteService.getLaborItems(quoteId);
  },
  createLaborItem: async (labor: any) => {
    return getFeatureFlag('useRealLaborService')
      ? await RealQuoteService.createLaborItem(labor)
      : await MockQuoteService.createLaborItem(labor);
  },
  bulkCreateLaborItems: async (laborItems: any[]) => {
    return getFeatureFlag('useRealLaborService')
      ? await RealQuoteService.bulkCreateLaborItems(laborItems)
      : await MockQuoteService.bulkCreateLaborItems(laborItems);
  },
};
