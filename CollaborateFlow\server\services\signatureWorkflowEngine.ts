/**
 * SIGNATURE WORKFLOW ENGINE
 * Orchestrates end-to-end signature workflows with DocuSign integration
 */

import { supabase } from '../supabase';
import { DigitalSignatureService, SignatureRequest, SignatureSigner } from './digitalSignatureService';
import DocuSignAdapter, { DocuSignConfig } from './digitalSignature/docusignAdapter';

export interface SignatureWorkflow {
  id: string;
  organization_id: string;
  workflow_type: 'quote_approval' | 'contract_signing' | 'change_order' | 'completion_certificate';
  workflow_name: string;
  workflow_description?: string;

  // Workflow Configuration
  auto_send: boolean;
  require_all_signatures: boolean;
  signing_order_enforced: boolean;
  expiration_days: number;
  reminder_frequency_days: number;

  // Document Configuration
  document_template_id?: string;
  signature_fields_template: any[];

  // Notification Configuration
  email_template_id?: string;
  notification_settings: {
    send_completion_email: boolean;
    send_reminder_emails: boolean;
    cc_organization_admin: boolean;
    custom_message?: string;
  };

  // Status and Tracking
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  signature_request_id: string;
  organization_id: string;

  // Execution Context
  context_type: 'quote' | 'project' | 'contract' | 'change_order';
  context_id: string;
  context_data: Record<string, any>;

  // Execution Status
  execution_status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  error_message?: string;

  // Progress Tracking
  total_steps: number;
  completed_steps: number;
  current_step: string;

  // Results
  signed_document_urls: string[];
  completion_certificate_url?: string;

  created_at: string;
  updated_at: string;
}

export interface QuoteApprovalWorkflowData {
  quote_id: string;
  client_contact: {
    name: string;
    email: string;
    company?: string;
    phone?: string;
  };
  quote_details: {
    title: string;
    total_amount: number;
    currency: string;
    valid_until: string;
    description?: string;
  };
  approval_deadline?: string;
  custom_terms?: string[];
}

export class SignatureWorkflowEngine {
  private organizationId: string;
  private digitalSignatureService: DigitalSignatureService;
  private docusignAdapter: DocuSignAdapter | null = null;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.digitalSignatureService = new DigitalSignatureService(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      organizationId
    );
    this.initializeDocuSignAdapter();
  }

  /**
   * Initialize DocuSign adapter with organization configuration
   */
  private async initializeDocuSignAdapter(): Promise<void> {
    try {
      // Get DocuSign configuration for organization
      const { data: config, error } = await supabase
        .from('signature_providers')
        .select('provider_config')
        .eq('organization_id', this.organizationId)
        .eq('provider_name', 'docusign')
        .eq('is_active', true)
        .single();

      if (error || !config) {
        console.warn('DocuSign configuration not found, using mock adapter');
        // Use mock configuration for testing
        const mockConfig: DocuSignConfig = {
          integration_key: process.env.DOCUSIGN_INTEGRATION_KEY || 'mock_integration_key',
          client_secret: process.env.DOCUSIGN_CLIENT_SECRET || 'mock_client_secret',
          user_id: process.env.DOCUSIGN_USER_ID || 'mock_user_id',
          account_id: process.env.DOCUSIGN_ACCOUNT_ID || 'mock_account_id',
          base_url: process.env.DOCUSIGN_BASE_URL || 'https://demo.docusign.net',
          redirect_uri: process.env.DOCUSIGN_REDIRECT_URI || 'http://localhost:3000/signature-complete'
        };
        this.docusignAdapter = new DocuSignAdapter(mockConfig);
        return;
      }

      this.docusignAdapter = new DocuSignAdapter(config.provider_config as DocuSignConfig);
    } catch (error) {
      console.error('Failed to initialize DocuSign adapter:', error);
    }
  }

  /**
   * Execute quote approval workflow
   */
  async executeQuoteApprovalWorkflow(workflowData: QuoteApprovalWorkflowData): Promise<WorkflowExecution> {
    try {
      console.log(`🔄 Starting quote approval workflow for quote ${workflowData.quote_id}`);

      // Step 1: Create workflow execution record
      const execution = await this.createWorkflowExecution({
        workflow_type: 'quote_approval',
        context_type: 'quote',
        context_id: workflowData.quote_id,
        context_data: workflowData,
        total_steps: 5
      });

      await this.updateExecutionProgress(execution.id, 1, 'generating_document');

      // Step 2: Generate quote PDF document
      const quoteDocument = await this.generateQuoteDocument(workflowData);

      await this.updateExecutionProgress(execution.id, 2, 'creating_signature_request');

      // Step 3: Create signature request
      const signatureRequest = await this.createSignatureRequest({
        request_title: `Quote Approval - ${workflowData.quote_details.title}`,
        request_message: this.generateQuoteApprovalMessage(workflowData),
        document_data: quoteDocument,
        workflow_type: 'quote_approval'
      });

      await this.updateExecutionProgress(execution.id, 3, 'adding_signers');

      // Step 4: Add client as signer
      await this.addSignersToRequest(signatureRequest.id, [{
        signer_name: workflowData.client_contact.name,
        signer_email: workflowData.client_contact.email,
        signer_role: 'client',
        signing_order: 1,
        authentication_method: 'email',
        signature_fields: this.getQuoteSignatureFields(),
        form_data: {
          company: workflowData.client_contact.company,
          phone: workflowData.client_contact.phone
        }
      }]);

      await this.updateExecutionProgress(execution.id, 4, 'sending_for_signature');

      // Step 5: Send signature request
      if (this.docusignAdapter) {
        await this.sendSignatureRequestViaDocuSign(signatureRequest.id, workflowData);
      } else {
        await this.digitalSignatureService.sendSignatureRequest(signatureRequest.id);
      }

      await this.updateExecutionProgress(execution.id, 5, 'completed');

      // Update execution as completed
      const { data: completedExecution, error: updateError } = await supabase
        .from('workflow_executions')
        .update({
          execution_status: 'in_progress',
          completed_steps: 5,
          current_step: 'awaiting_signatures',
          signature_request_id: signatureRequest.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', execution.id)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Failed to update execution: ${updateError.message}`);
      }

      console.log(`✅ Quote approval workflow started successfully for quote ${workflowData.quote_id}`);
      return completedExecution;

    } catch (error) {
      console.error('Quote approval workflow failed:', error);
      throw new Error(`Workflow execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process signature completion webhook
   */
  async processSignatureCompletion(
    signatureRequestId: string,
    webhookData?: any
  ): Promise<void> {
    try {
      console.log(`📝 Processing signature completion for request ${signatureRequestId}`);

      // Get workflow execution
      const { data: execution, error } = await supabase
        .from('workflow_executions')
        .select('*')
        .eq('signature_request_id', signatureRequestId)
        .single();

      if (error || !execution) {
        throw new Error('Workflow execution not found');
      }

      // Update signature request status
      await this.digitalSignatureService.updateSignatureStatus(
        signatureRequestId,
        'completed',
        'All signatures completed'
      );

      // Download signed documents
      let signedDocumentUrls: string[] = [];
      if (this.docusignAdapter && webhookData?.envelopeId) {
        const signedDocument = await this.docusignAdapter.downloadDocuments(webhookData.envelopeId);
        const documentUrl = await this.storeSignedDocument(
          signatureRequestId,
          signedDocument,
          'signed_quote.pdf'
        );
        signedDocumentUrls = [documentUrl];
      }

      // Generate completion certificate
      const certificateUrl = await this.generateCompletionCertificate(execution);

      // Update workflow execution
      await supabase
        .from('workflow_executions')
        .update({
          execution_status: 'completed',
          completed_at: new Date().toISOString(),
          signed_document_urls: signedDocumentUrls,
          completion_certificate_url: certificateUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', execution.id);

      // Send completion notifications
      await this.sendCompletionNotifications(execution, signedDocumentUrls);

      console.log(`✅ Signature completion processed successfully for request ${signatureRequestId}`);

    } catch (error) {
      console.error('Signature completion processing failed:', error);
      throw error;
    }
  }

  /**
   * Get workflow execution status
   */
  async getWorkflowStatus(executionId: string): Promise<WorkflowExecution | null> {
    const { data: execution, error } = await supabase
      .from('workflow_executions')
      .select('*')
      .eq('id', executionId)
      .eq('organization_id', this.organizationId)
      .single();

    if (error) {
      console.error('Failed to get workflow status:', error);
      return null;
    }

    return execution;
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflow(executionId: string, reason: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('workflow_executions')
        .update({
          execution_status: 'cancelled',
          error_message: reason,
          updated_at: new Date().toISOString()
        })
        .eq('id', executionId)
        .eq('organization_id', this.organizationId);

      if (error) {
        throw new Error(`Failed to cancel workflow: ${error.message}`);
      }

      return true;
    } catch (error) {
      console.error('Workflow cancellation failed:', error);
      return false;
    }
  }

  // Private helper methods

  private async createWorkflowExecution(data: {
    workflow_type: string;
    context_type: string;
    context_id: string;
    context_data: any;
    total_steps: number;
  }): Promise<WorkflowExecution> {
    const { data: execution, error } = await supabase
      .from('workflow_executions')
      .insert({
        organization_id: this.organizationId,
        workflow_type: data.workflow_type,
        context_type: data.context_type,
        context_id: data.context_id,
        context_data: data.context_data,
        execution_status: 'pending',
        total_steps: data.total_steps,
        completed_steps: 0,
        current_step: 'initializing',
        started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create workflow execution: ${error.message}`);
    }

    return execution;
  }

  private async updateExecutionProgress(
    executionId: string,
    completedSteps: number,
    currentStep: string
  ): Promise<void> {
    await supabase
      .from('workflow_executions')
      .update({
        completed_steps: completedSteps,
        current_step: currentStep,
        updated_at: new Date().toISOString()
      })
      .eq('id', executionId);
  }

  private async generateQuoteDocument(workflowData: QuoteApprovalWorkflowData): Promise<Buffer> {
    // Mock PDF generation - in production, use a PDF library
    const mockPdfContent = `
      QUOTE APPROVAL DOCUMENT

      Quote ID: ${workflowData.quote_id}
      Client: ${workflowData.client_contact.name}
      Company: ${workflowData.client_contact.company || 'N/A'}

      Quote Details:
      Title: ${workflowData.quote_details.title}
      Amount: ${workflowData.quote_details.currency} ${workflowData.quote_details.total_amount}
      Valid Until: ${workflowData.quote_details.valid_until}

      Description: ${workflowData.quote_details.description || 'N/A'}

      By signing below, you approve this quote and authorize work to begin.

      Client Signature: _____________________ Date: _________

      ${workflowData.custom_terms?.join('\n') || ''}
    `;

    return Buffer.from(mockPdfContent, 'utf8');
  }

  private async createSignatureRequest(data: {
    request_title: string;
    request_message: string;
    document_data: Buffer;
    workflow_type: string;
  }): Promise<{ id: string }> {
    const requestId = await this.digitalSignatureService.createSignatureRequest({
      organization_id: this.organizationId,
      request_title: data.request_title,
      request_message: data.request_message,
      request_type: data.workflow_type,
      request_status: 'draft',
      document_count: 1,
      signer_count: 1,
      completion_percentage: 0
    });

    if (!requestId) {
      throw new Error('Failed to create signature request');
    }

    return { id: requestId };
  }

  private async addSignersToRequest(requestId: string, signers: Omit<SignatureSigner, 'id' | 'signature_request_id'>[]): Promise<void> {
    await this.digitalSignatureService.addSigners(requestId, signers);
  }

  private async sendSignatureRequestViaDocuSign(
    requestId: string,
    workflowData: QuoteApprovalWorkflowData
  ): Promise<void> {
    if (!this.docusignAdapter) {
      throw new Error('DocuSign adapter not initialized');
    }

    // Get signature request and signers
    const { data: request, error: requestError } = await supabase
      .from('signature_requests')
      .select(`
        *,
        signature_signers(*)
      `)
      .eq('id', requestId)
      .single();

    if (requestError || !request) {
      throw new Error('Signature request not found');
    }

    // Generate document
    const documentBuffer = await this.generateQuoteDocument(workflowData);

    // Convert to DocuSign format
    const { documents, signers, options } = DocuSignAdapter.convertToDocuSignFormat(
      request,
      request.signature_signers,
      documentBuffer,
      `quote_${workflowData.quote_id}.pdf`
    );

    // Create envelope in DocuSign
    const envelope = await this.docusignAdapter.createSignatureRequest(documents, signers, options);

    // Update request with provider ID
    await supabase
      .from('signature_requests')
      .update({
        provider_request_id: envelope.envelopeId,
        request_status: 'sent',
        sent_at: new Date().toISOString()
      })
      .eq('id', requestId);
  }

  private generateQuoteApprovalMessage(workflowData: QuoteApprovalWorkflowData): string {
    return `
Dear ${workflowData.client_contact.name},

Please review and approve the attached quote for "${workflowData.quote_details.title}".

Quote Details:
- Total Amount: ${workflowData.quote_details.currency} ${workflowData.quote_details.total_amount}
- Valid Until: ${workflowData.quote_details.valid_until}

To approve this quote, please review the document and provide your digital signature.

Thank you for your business!

Best regards,
CoElec Team
    `.trim();
  }

  private getQuoteSignatureFields(): any[] {
    return [
      {
        type: 'signature',
        page: 1,
        x: 100,
        y: 400,
        width: 200,
        height: 50,
        required: true,
        label: 'Client Signature'
      },
      {
        type: 'date',
        page: 1,
        x: 350,
        y: 400,
        width: 100,
        height: 30,
        required: true,
        label: 'Date Signed'
      }
    ];
  }

  private async storeSignedDocument(
    requestId: string,
    documentBuffer: Buffer,
    fileName: string
  ): Promise<string> {
    // Mock document storage - in production, use Supabase Storage
    const mockUrl = `https://storage.coelec.com/signed-documents/${requestId}/${fileName}`;
    console.log(`Storing signed document: ${fileName} (${documentBuffer.length} bytes)`);
    return mockUrl;
  }

  private async generateCompletionCertificate(execution: WorkflowExecution): Promise<string> {
    // Mock certificate generation
    const mockCertificateUrl = `https://storage.coelec.com/certificates/${execution.id}/completion-certificate.pdf`;
    console.log(`Generated completion certificate for execution ${execution.id}`);
    return mockCertificateUrl;
  }

  private async sendCompletionNotifications(
    execution: WorkflowExecution,
    signedDocumentUrls: string[]
  ): Promise<void> {
    console.log(`Sending completion notifications for execution ${execution.id}`);
    console.log(`Signed documents: ${signedDocumentUrls.join(', ')}`);
    // In production, integrate with email service
  }
}

  /**
   * Track signature status
   */
  async trackStatus(envelopeId: string): Promise<any> {
    try {
      console.log(`📊 Tracking status for envelope: ${envelopeId}`);

      // Mock status tracking
      const statuses = ['sent', 'delivered', 'signed', 'completed'];
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

      return {
        envelopeId,
        status: randomStatus,
        lastUpdated: new Date().toISOString(),
        signers: [
          {
            name: 'John Doe',
            email: '<EMAIL>',
            status: randomStatus,
            signedAt: randomStatus === 'signed' || randomStatus === 'completed' ? new Date().toISOString() : null
          }
        ]
      };
    } catch (error) {
      console.error('❌ Status tracking failed:', error);
      throw error;
    }
  }

  /**
   * Handle completion notification
   */
  async handleCompletion(envelopeId: string): Promise<void> {
    try {
      console.log(`🎉 Handling completion for envelope: ${envelopeId}`);

      // Mock completion handling
      await this.updateWorkflowStatus(envelopeId, 'completed');

      // Send notifications
      console.log(`📧 Sending completion notifications for envelope: ${envelopeId}`);

    } catch (error) {
      console.error('❌ Completion handling failed:', error);
      throw error;
    }
  }
}

export default SignatureWorkflowEngine;
