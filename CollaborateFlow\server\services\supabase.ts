import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { log } from '../vite';

// Load environment variables explicitly
dotenv.config();

// Define the RUNTIME_CONFIG interface for TypeScript
declare global {
  interface Window {
    RUNTIME_CONFIG?: {
      SUPABASE_URL: string;
      SUPABASE_KEY: string;
    }
  }
}

// Initialize the Supabase client
let supabaseUrl: string;
let supabaseKey: string;

// Check if we're in a browser environment
if (typeof window !== 'undefined' && window.RUNTIME_CONFIG) {
  supabaseUrl = window.RUNTIME_CONFIG.SUPABASE_URL;
  supabaseKey = window.RUNTIME_CONFIG.SUPABASE_KEY;
} else {
  // In Node.js environment
  supabaseUrl = process.env.SUPABASE_URL || '';
  supabaseKey = process.env.SUPABASE_KEY || '';
}

// Add logging to debug environment variables
log(`Supabase URL: ${supabaseUrl ? 'Found' : 'Missing'}`);
log(`Supabase Key: ${supabaseKey ? 'Found' : 'Missing'}`);

// Use default values from config.js if environment variables are missing
if (!supabaseUrl) {
  log('SUPABASE_URL not found in environment, using default from config');
  supabaseUrl = 'https://nzhvukfaolebykcquedd.supabase.co';
}

if (!supabaseKey) {
  log('SUPABASE_KEY not found in environment, using default from config');
  supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56aHZ1a2Zhb2xlYnlrY3F1ZWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNjQ3NDYsImV4cCI6MjA1Nzg0MDc0Nn0.3Lqr_vtji_XazHH_oJpJUwUoGzkJ2KR4eOnVlFNOw1U';
}

// Create the client
export const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to handle Supabase errors consistently
export function handleSupabaseError(error: any, operation: string): never {
  console.error(`Supabase ${operation} error:`, error);
  throw new Error(`Error during ${operation}: ${error.message || 'Unknown error'}`);
}

// Connection test function
export async function testSupabaseConnection(): Promise<boolean> {
  try {
    // Test the connection by querying the teams table
    const { data, error } = await supabase.from('teams').select('id').limit(1);
    
    if (error) {
      // Check if the error is related to the table not existing yet
      if (error.code === '42P01') { // PostgreSQL code for undefined_table
        console.warn('Teams table does not exist yet. Database might not be initialized.');
        // Try an alternative query to just check connection
        const { error: authError } = await supabase.auth.getSession();
        if (authError) throw authError;
        console.log('Supabase connection successful, but schema might not be initialized');
        return true;
      }
      throw error;
    }
    
    console.log('Supabase connection successful');
    return true;
  } catch (error) {
    console.error('Supabase connection test failed:', error);
    return false;
  }
}
