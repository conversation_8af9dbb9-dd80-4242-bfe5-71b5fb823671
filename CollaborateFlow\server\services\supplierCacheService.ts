/**
 * SUPPLIER CACHE SERVICE
 * High-performance caching for supplier pricing and product data
 */

import { supabase } from '../supabase';
import { SupplierProduct, PricingResponse } from './supplierIntegrationService';

export interface CacheEntry<T> {
  key: string;
  data: T;
  created_at: string;
  expires_at: string;
  supplier_id: string;
  organization_id: string;
  cache_type: 'product_search' | 'pricing' | 'availability' | 'product_details';
  hit_count: number;
  last_accessed: string;
}

export interface CacheStats {
  total_entries: number;
  hit_rate: number;
  miss_rate: number;
  cache_size_mb: number;
  expired_entries: number;
  most_accessed_keys: Array<{
    key: string;
    hit_count: number;
    supplier_id: string;
  }>;
}

export class SupplierCacheService {
  private organizationId: string;
  private memoryCache: Map<string, CacheEntry<any>> = new Map();
  private maxMemoryCacheSize: number = 1000; // Maximum entries in memory
  private defaultTtlHours: number = 24;

  constructor(organizationId: string) {
    this.organizationId = organizationId;
    this.startCleanupInterval();
  }

  /**
   * Get cached product search results
   */
  async getProductSearch(
    supplierId: string,
    query: string,
    options: any
  ): Promise<SupplierProduct[] | null> {
    const key = this.generateSearchKey(supplierId, query, options);
    return this.get<SupplierProduct[]>(key, 'product_search');
  }

  /**
   * Cache product search results
   */
  async setProductSearch(
    supplierId: string,
    query: string,
    options: any,
    products: SupplierProduct[],
    ttlHours?: number
  ): Promise<void> {
    const key = this.generateSearchKey(supplierId, query, options);
    await this.set(key, products, 'product_search', supplierId, ttlHours);
  }

  /**
   * Get cached pricing data
   */
  async getPricing(
    supplierId: string,
    requestHash: string
  ): Promise<PricingResponse | null> {
    const key = `pricing_${supplierId}_${requestHash}`;
    return this.get<PricingResponse>(key, 'pricing');
  }

  /**
   * Cache pricing data
   */
  async setPricing(
    supplierId: string,
    requestHash: string,
    pricing: PricingResponse,
    ttlHours?: number
  ): Promise<void> {
    const key = `pricing_${supplierId}_${requestHash}`;
    await this.set(key, pricing, 'pricing', supplierId, ttlHours);
  }

  /**
   * Get cached product details
   */
  async getProductDetails(
    supplierId: string,
    productId: string
  ): Promise<SupplierProduct | null> {
    const key = `product_${supplierId}_${productId}`;
    return this.get<SupplierProduct>(key, 'product_details');
  }

  /**
   * Cache product details
   */
  async setProductDetails(
    supplierId: string,
    productId: string,
    product: SupplierProduct,
    ttlHours?: number
  ): Promise<void> {
    const key = `product_${supplierId}_${productId}`;
    await this.set(key, product, 'product_details', supplierId, ttlHours);
  }

  /**
   * Get cached availability data
   */
  async getAvailability(
    supplierId: string,
    productIds: string[]
  ): Promise<Record<string, any> | null> {
    const key = `availability_${supplierId}_${productIds.sort().join('_')}`;
    return this.get<Record<string, any>>(key, 'availability');
  }

  /**
   * Cache availability data
   */
  async setAvailability(
    supplierId: string,
    productIds: string[],
    availability: Record<string, any>,
    ttlHours?: number
  ): Promise<void> {
    const key = `availability_${supplierId}_${productIds.sort().join('_')}`;
    await this.set(key, availability, 'availability', supplierId, ttlHours || 1); // Shorter TTL for availability
  }

  /**
   * Clear all cache entries for a supplier
   */
  async clearSupplierCache(supplierId: string): Promise<void> {
    // Clear from memory cache
    const keysToDelete = Array.from(this.memoryCache.keys()).filter(key => 
      this.memoryCache.get(key)?.supplier_id === supplierId
    );
    keysToDelete.forEach(key => this.memoryCache.delete(key));

    // Clear from database cache
    const { error } = await supabase
      .from('supplier_cache')
      .delete()
      .eq('organization_id', this.organizationId)
      .eq('supplier_id', supplierId);

    if (error) {
      console.error('Failed to clear supplier cache from database:', error);
    }
  }

  /**
   * Clear expired cache entries
   */
  async clearExpiredEntries(): Promise<number> {
    const now = new Date().toISOString();
    let clearedCount = 0;

    // Clear from memory cache
    const expiredKeys = Array.from(this.memoryCache.entries())
      .filter(([key, entry]) => entry.expires_at < now)
      .map(([key]) => key);
    
    expiredKeys.forEach(key => {
      this.memoryCache.delete(key);
      clearedCount++;
    });

    // Clear from database cache
    const { data, error } = await supabase
      .from('supplier_cache')
      .delete()
      .eq('organization_id', this.organizationId)
      .lt('expires_at', now)
      .select('key');

    if (!error && data) {
      clearedCount += data.length;
    }

    return clearedCount;
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<CacheStats> {
    const { data: dbStats, error } = await supabase
      .from('supplier_cache')
      .select('key, hit_count, supplier_id, cache_type, created_at, expires_at')
      .eq('organization_id', this.organizationId);

    if (error) {
      console.error('Failed to get cache stats:', error);
      return this.getEmptyStats();
    }

    const now = new Date().toISOString();
    const totalEntries = (dbStats?.length || 0) + this.memoryCache.size;
    const expiredEntries = dbStats?.filter(entry => entry.expires_at < now).length || 0;

    // Calculate hit rate (simplified)
    const totalHits = dbStats?.reduce((sum, entry) => sum + entry.hit_count, 0) || 0;
    const hitRate = totalEntries > 0 ? (totalHits / (totalHits + totalEntries)) * 100 : 0;

    // Get most accessed keys
    const mostAccessed = dbStats
      ?.sort((a, b) => b.hit_count - a.hit_count)
      .slice(0, 10)
      .map(entry => ({
        key: entry.key,
        hit_count: entry.hit_count,
        supplier_id: entry.supplier_id
      })) || [];

    return {
      total_entries: totalEntries,
      hit_rate: Math.round(hitRate * 100) / 100,
      miss_rate: Math.round((100 - hitRate) * 100) / 100,
      cache_size_mb: this.estimateCacheSize(),
      expired_entries: expiredEntries,
      most_accessed_keys: mostAccessed
    };
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmUpCache(supplierId?: string): Promise<void> {
    const query = supabase
      .from('supplier_cache')
      .select('*')
      .eq('organization_id', this.organizationId)
      .order('hit_count', { ascending: false })
      .limit(100);

    if (supplierId) {
      query.eq('supplier_id', supplierId);
    }

    const { data: frequentEntries, error } = await query;

    if (error || !frequentEntries) {
      console.error('Failed to warm up cache:', error);
      return;
    }

    // Load into memory cache
    frequentEntries.forEach(entry => {
      if (!this.memoryCache.has(entry.key) && entry.expires_at > new Date().toISOString()) {
        this.memoryCache.set(entry.key, entry);
      }
    });
  }

  /**
   * Generic get method
   */
  private async get<T>(
    key: string,
    cacheType: CacheEntry<T>['cache_type']
  ): Promise<T | null> {
    // Try memory cache first
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && memoryEntry.expires_at > new Date().toISOString()) {
      await this.incrementHitCount(key);
      return memoryEntry.data as T;
    }

    // Try database cache
    const { data: dbEntry, error } = await supabase
      .from('supplier_cache')
      .select('*')
      .eq('organization_id', this.organizationId)
      .eq('key', key)
      .eq('cache_type', cacheType)
      .single();

    if (error || !dbEntry || dbEntry.expires_at <= new Date().toISOString()) {
      return null;
    }

    // Load into memory cache for faster access
    this.memoryCache.set(key, dbEntry);
    this.enforceMemoryCacheLimit();

    await this.incrementHitCount(key);
    return dbEntry.data as T;
  }

  /**
   * Generic set method
   */
  private async set<T>(
    key: string,
    data: T,
    cacheType: CacheEntry<T>['cache_type'],
    supplierId: string,
    ttlHours?: number
  ): Promise<void> {
    const ttl = ttlHours || this.defaultTtlHours;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + ttl * 60 * 60 * 1000);

    const entry: CacheEntry<T> = {
      key,
      data,
      created_at: now.toISOString(),
      expires_at: expiresAt.toISOString(),
      supplier_id: supplierId,
      organization_id: this.organizationId,
      cache_type: cacheType,
      hit_count: 0,
      last_accessed: now.toISOString()
    };

    // Set in memory cache
    this.memoryCache.set(key, entry);
    this.enforceMemoryCacheLimit();

    // Set in database cache
    const { error } = await supabase
      .from('supplier_cache')
      .upsert(entry, { onConflict: 'organization_id,key' });

    if (error) {
      console.error('Failed to cache data in database:', error);
    }
  }

  /**
   * Generate search cache key
   */
  private generateSearchKey(supplierId: string, query: string, options: any): string {
    const optionsHash = this.hashObject(options);
    return `search_${supplierId}_${query.toLowerCase().replace(/\s+/g, '_')}_${optionsHash}`;
  }

  /**
   * Hash object for cache key generation
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Increment hit count for cache entry
   */
  private async incrementHitCount(key: string): Promise<void> {
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry) {
      memoryEntry.hit_count++;
      memoryEntry.last_accessed = new Date().toISOString();
    }

    // Update database asynchronously
    supabase
      .from('supplier_cache')
      .update({ 
        hit_count: supabase.rpc('increment_hit_count'),
        last_accessed: new Date().toISOString()
      })
      .eq('organization_id', this.organizationId)
      .eq('key', key)
      .then(({ error }) => {
        if (error) {
          console.error('Failed to increment hit count:', error);
        }
      });
  }

  /**
   * Enforce memory cache size limit
   */
  private enforceMemoryCacheLimit(): void {
    if (this.memoryCache.size <= this.maxMemoryCacheSize) {
      return;
    }

    // Remove least recently used entries
    const entries = Array.from(this.memoryCache.entries())
      .sort(([, a], [, b]) => a.last_accessed.localeCompare(b.last_accessed));

    const toRemove = entries.slice(0, this.memoryCache.size - this.maxMemoryCacheSize);
    toRemove.forEach(([key]) => this.memoryCache.delete(key));
  }

  /**
   * Estimate cache size in MB
   */
  private estimateCacheSize(): number {
    let totalSize = 0;
    this.memoryCache.forEach(entry => {
      totalSize += JSON.stringify(entry).length;
    });
    return Math.round((totalSize / (1024 * 1024)) * 100) / 100;
  }

  /**
   * Get empty stats object
   */
  private getEmptyStats(): CacheStats {
    return {
      total_entries: 0,
      hit_rate: 0,
      miss_rate: 100,
      cache_size_mb: 0,
      expired_entries: 0,
      most_accessed_keys: []
    };
  }

  /**
   * Start cleanup interval for expired entries
   */
  private startCleanupInterval(): void {
    // Clean up expired entries every hour
    setInterval(async () => {
      try {
        const clearedCount = await this.clearExpiredEntries();
        if (clearedCount > 0) {
          console.log(`Cleared ${clearedCount} expired cache entries`);
        }
      } catch (error) {
        console.error('Cache cleanup failed:', error);
      }
    }, 60 * 60 * 1000); // 1 hour
  }
}

export default SupplierCacheService;
