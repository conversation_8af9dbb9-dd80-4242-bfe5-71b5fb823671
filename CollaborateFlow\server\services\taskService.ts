import { supabase, handleSupabaseError } from './supabase';
import { Task } from '../types';

/**
 * Get tasks for a project
 * @param projectId Project ID
 * @returns Promise resolving to an array of tasks
 */
export async function getTasks(projectId: number): Promise<Task[]> {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('project_id', projectId)
      .order('order', { ascending: true });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getTasks');
  }
}

/**
 * Get a specific task
 * @param taskId Task ID
 * @returns Promise resolving to a task or null if not found
 */
export async function getTask(taskId: number): Promise<Task | null> {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') { // Code for no rows returned
        return null;
      }
      throw error;
    }
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'getTask');
  }
}

/**
 * Create a new task
 * @param task Task data without ID
 * @returns Promise resolving to the created task
 */
export async function createTask(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .insert({
        ...task,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    return handleSupabaseError(error, 'createTask');
  }
}

/**
 * Update task details
 * @param taskId Task ID
 * @param data Updated task data
 * @returns Promise resolving to the updated task
 */
export async function updateTask(taskId: number, data: Partial<Omit<Task, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Task> {
  try {
    const { data: updatedTask, error } = await supabase
      .from('tasks')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)
      .select()
      .single();
    
    if (error) throw error;
    
    return updatedTask;
  } catch (error) {
    return handleSupabaseError(error, 'updateTask');
  }
}

/**
 * Delete a task
 * @param taskId Task ID
 * @returns Promise resolving to true if successful
 */
export async function deleteTask(taskId: number): Promise<boolean> {
  try {
    // Get the task to determine its column and order for reordering remaining tasks
    const { data: task, error: getError } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();
    
    if (getError) throw getError;
    
    // Delete the task
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId);
    
    if (error) throw error;
    
    // Reorder the remaining tasks in the same column
    const { error: reorderError } = await supabase
      .rpc('reorder_tasks_after_delete', { 
        p_column_id: task.columnId, 
        p_deleted_order: task.order 
      });
    
    if (reorderError) throw reorderError;
    
    return true;
  } catch (error) {
    return handleSupabaseError(error, 'deleteTask');
  }
}

/**
 * Get tasks for a specific column
 * @param columnId Column ID
 * @returns Promise resolving to an array of tasks
 */
export async function getColumnTasks(columnId: number): Promise<Task[]> {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('column_id', columnId)
      .order('order', { ascending: true });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    return handleSupabaseError(error, 'getColumnTasks');
  }
}

/**
 * Move a task to a different column
 * @param taskId Task ID
 * @param newColumnId New column ID
 * @param newOrder New order in the column
 * @returns Promise resolving to the updated task
 */
export async function moveTask(taskId: number, newColumnId: number, newOrder: number): Promise<Task> {
  try {
    // Get the current task details
    const { data: task, error: getError } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();
    
    if (getError) throw getError;
    
    // If moving to a different column, handle reordering in both columns
    if (task.columnId !== newColumnId) {
      // Call a stored procedure to handle the complex reordering logic
      const { error } = await supabase.rpc('move_task_between_columns', { 
        p_task_id: taskId,
        p_old_column_id: task.columnId,
        p_new_column_id: newColumnId,
        p_new_order: newOrder
      });
      
      if (error) throw error;
    } else {
      // Moving within the same column
      const { error } = await supabase.rpc('move_task_within_column', { 
        p_task_id: taskId,
        p_column_id: newColumnId,
        p_old_order: task.order,
        p_new_order: newOrder
      });
      
      if (error) throw error;
    }
    
    // Get the updated task
    const { data: updatedTask, error: updateError } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();
    
    if (updateError) throw updateError;
    
    return updatedTask;
  } catch (error) {
    return handleSupabaseError(error, 'moveTask');
  }
}
