import { randomBytes } from 'crypto';
import { supabase } from './services/supabase';
import { 
  users, 
  teams, 
  teamMembers, 
  projects, 
  columns, 
  tasks,
  organizations,
  quotes,
  quoteFeedback,
  type User, 
  type InsertUser, 
  type Team, 
  type InsertTeam, 
  type TeamMember, 
  type InsertTeamMember, 
  type Project, 
  type InsertProject, 
  type Column, 
  type InsertColumn, 
  type Task, 
  type InsertTask,
  type Quote,
  type InsertQuote,
  type QuoteFeedback,
  type InsertQuoteFeedback,
  type Organization,
  type InsertOrganization
} from "@shared/schema";
import session from "express-session";
import createMemoryStore from "memorystore";
import connectPg from "connect-pg-simple";
import { db, pool } from "./db";
import { eq, and, inArray } from "drizzle-orm";

const MemoryStore = createMemoryStore(session);
const PostgresSessionStore = connectPg(session);

export interface IStorage {
  // Organization operations
  getOrganization(id: number): Promise<Organization | undefined>;
  getOrganizationByCreator(userId: number): Promise<Organization | undefined>;
  createOrganization(organization: InsertOrganization): Promise<Organization>;
  updateOrganization(id: number, updates: Partial<Organization>): Promise<Organization | undefined>;
  
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserBySupabaseId(supabaseId: string): Promise<User | undefined>;
  createUserFromSupabase(supabaseUser: any): Promise<User>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;
  
  // Team operations
  getTeam(id: number): Promise<Team | undefined>;
  getTeamsByUserId(userId: number): Promise<Team[]>;
  getTeamsByOrganization(organizationId: number): Promise<Team[]>;
  createTeam(team: InsertTeam): Promise<Team>;
  
  // Team Member operations
  getTeamMembers(teamId: number): Promise<User[]>;
  addTeamMember(teamMember: InsertTeamMember): Promise<TeamMember>;
  
  // Project operations
  getProject(id: number): Promise<Project | undefined>;
  getProjects(userId: number): Promise<Project[]>;
  createProject(project: InsertProject): Promise<Project>;
  
  // Column operations
  getColumn(id: number): Promise<Column | undefined>;
  getColumnsByProject(projectId: number): Promise<Column[]>;
  createColumn(column: InsertColumn): Promise<Column>;
  
  // Task operations
  getTask(id: number): Promise<Task | undefined>;
  getTasksByProject(projectId: number): Promise<Task[]>;
  createTask(task: InsertTask): Promise<Task>;
  updateTask(id: number, updates: Partial<Task>): Promise<Task | undefined>;
  deleteTask(id: number): Promise<void>;
  
  // Quote operations
  getQuote(id: number): Promise<Quote | undefined>;
  getQuoteByToken(token: string): Promise<Quote | undefined>;
  getQuotesByProject(projectId: number): Promise<Quote[]>;
  getQuotesByUser(userId: number): Promise<Quote[]>;
  createQuote(quote: InsertQuote): Promise<Quote>;
  updateQuote(id: number, updates: Partial<Quote>): Promise<Quote | undefined>;
  
  // Quote Feedback operations
  getQuoteFeedback(id: number): Promise<QuoteFeedback | undefined>;
  getQuoteFeedbackByQuote(quoteId: number): Promise<QuoteFeedback[]>;
  createQuoteFeedback(feedback: InsertQuoteFeedback): Promise<QuoteFeedback>;
  
  // Session store
  sessionStore: any;
}

export class DatabaseStorage implements IStorage {
  sessionStore: any;

  constructor() {
    this.sessionStore = new PostgresSessionStore({ 
      pool, 
      createTableIfMissing: true 
    });
  }
  
  // Organization operations
  async getOrganization(id: number): Promise<Organization | undefined> {
    const result = await db.select().from(organizations).where(eq(organizations.id, id));
    return result[0] || undefined;
  }
  
  async getOrganizationByCreator(userId: number): Promise<Organization | undefined> {
    const result = await db.select().from(organizations).where(eq(organizations.createdById, userId));
    return result[0] || undefined;
  }
  
  async createOrganization(organization: InsertOrganization): Promise<Organization> {
    const result = await db.insert(organizations).values(organization).returning();
    return result[0];
  }
  
  async updateOrganization(id: number, updates: Partial<Organization>): Promise<Organization | undefined> {
    const result = await db
      .update(organizations)
      .set(updates)
      .where(eq(organizations.id, id))
      .returning();
    return result[0] || undefined;
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id));
    return result[0] || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result.length > 0 ? result[0] : undefined;
  }

  async getUserBySupabaseId(supabaseId: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.supabaseId, supabaseId)).limit(1);
    return result.length > 0 ? result[0] : undefined;
  }

  async createUserFromSupabase(supabaseUser: any): Promise<User> {
    // Extract user information from Supabase user object
    const { id: supabaseId, email, user_metadata } = supabaseUser;
    const fullName = user_metadata?.full_name || 'User';
    
    // Generate a username from email (before the @ symbol)
    const username = email.split('@')[0];
    
    // Create a random password since we'll be using Supabase for authentication
    const password = randomBytes(16).toString('hex');
    
    // Create the user record
    const newUser: InsertUser = {
      username,
      email,
      password,
      fullName,
      supabaseId,
      hasCompletedSetup: false
    };
    
    const result = await db.insert(users).values(newUser).returning();
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    const result = await db.insert(users).values(user).returning();
    return result[0];
  }
  
  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const result = await db
      .update(users)
      .set(updates)
      .where(eq(users.id, id))
      .returning();
    return result[0] || undefined;
  }

  // Team operations
  async getTeam(id: number): Promise<Team | undefined> {
    const result = await db.select().from(teams).where(eq(teams.id, id));
    return result[0] || undefined;
  }

  async getTeamsByUserId(userId: number): Promise<Team[]> {
    try {
      console.log(`Getting teams for user ${userId} using Supabase client`);
      
      // Find all team memberships for the user using Supabase client
      const { data: memberships, error: membershipError } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId);
      
      if (membershipError) {
        console.error('Error fetching team memberships:', membershipError);
        throw membershipError;
      }
      
      if (!memberships || memberships.length === 0) {
        console.log(`No team memberships found for user ${userId}`);
        return [];
      }
      
      // Get the teams for those memberships
      const teamIds = memberships.map(membership => membership.team_id);
      console.log(`Found team memberships for teams: ${teamIds.join(', ')}`);
      
      const { data: teams, error: teamsError } = await supabase
        .from('teams')
        .select('*')
        .in('id', teamIds);
      
      if (teamsError) {
        console.error('Error fetching teams:', teamsError);
        throw teamsError;
      }
      
      // Convert from snake_case to camelCase for frontend
      return teams ? teams.map(team => ({
        id: team.id,
        name: team.name,
        description: team.description,
        createdById: team.created_by_id,
        organizationId: team.organization_id,
        createdAt: team.created_at
      })) : [];
    } catch (error) {
      console.error('Error in getTeamsByUserId:', error);
      throw error;
    }
  }
  
  async getTeamsByOrganization(organizationId: number): Promise<Team[]> {
    return await db
      .select()
      .from(teams)
      .where(eq(teams.organizationId, organizationId));
  }

  async createTeam(team: InsertTeam): Promise<Team> {
    try {
      // Use Supabase client instead of direct PostgreSQL connection
      const { data, error } = await supabase
        .from('teams')
        .insert({
          name: team.name,
          description: team.description || null,
          created_by_id: team.createdById,
          organization_id: team.organizationId || null
        })
        .select()
        .single();
      
      if (error) {
        console.error('Error creating team with Supabase:', error);
        throw error;
      }
      
      // Convert from snake_case to camelCase for frontend
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        createdById: data.created_by_id,
        organizationId: data.organization_id
        // We'll handle createdAt in our type system later if needed
      };
    } catch (error) {
      console.error('Error in createTeam:', error);
      throw error;
    }
  }

  // Team Member operations
  async getTeamMembers(teamId: number): Promise<User[]> {
    // Find all memberships for the team
    const memberships = await db
      .select()
      .from(teamMembers)
      .where(eq(teamMembers.teamId, teamId));
    
    if (memberships.length === 0) {
      return [];
    }
    
    // Get the users for those memberships
    const userIds = memberships.map(membership => membership.userId);
    return await db
      .select()
      .from(users)
      .where(inArray(users.id, userIds));
  }

  async addTeamMember(teamMember: InsertTeamMember): Promise<TeamMember> {
    const result = await db.insert(teamMembers).values({
      teamId: teamMember.teamId,
      userId: teamMember.userId,
      role: teamMember.role || 'member'
    }).returning();
    return result[0];
  }

  // Project operations
  async getProject(id: number): Promise<Project | undefined> {
    const result = await db.select().from(projects).where(eq(projects.id, id));
    return result[0] || undefined;
  }

  async getProjects(userId: number): Promise<Project[]> {
    // Get all teams the user is a member of
    const teams = await this.getTeamsByUserId(userId);
    
    if (teams.length === 0) {
      return [];
    }
    
    const teamIds = teams.map(team => team.id);
    
    // Get all projects for those teams
    return await db
      .select()
      .from(projects)
      .where(inArray(projects.teamId, teamIds));
  }

  async createProject(project: InsertProject): Promise<Project> {
    const result = await db.insert(projects).values({
      name: project.name,
      description: project.description || null,
      startDate: project.startDate || null,
      endDate: project.endDate || null,
      teamId: project.teamId,
      createdById: project.createdById
    }).returning();
    return result[0];
  }

  // Column operations
  async getColumn(id: number): Promise<Column | undefined> {
    const result = await db.select().from(columns).where(eq(columns.id, id));
    return result[0] || undefined;
  }

  async getColumnsByProject(projectId: number): Promise<Column[]> {
    return await db
      .select()
      .from(columns)
      .where(eq(columns.projectId, projectId))
      .orderBy(columns.order);
  }

  async createColumn(column: InsertColumn): Promise<Column> {
    const result = await db.insert(columns).values(column).returning();
    return result[0];
  }

  // Task operations
  async getTask(id: number): Promise<Task | undefined> {
    const result = await db.select().from(tasks).where(eq(tasks.id, id));
    return result[0] || undefined;
  }

  async getTasksByProject(projectId: number): Promise<Task[]> {
    return await db
      .select()
      .from(tasks)
      .where(eq(tasks.projectId, projectId));
  }

  async createTask(task: InsertTask): Promise<Task> {
    const result = await db.insert(tasks).values({
      title: task.title,
      description: task.description || null,
      status: task.status,
      priority: task.priority || null,
      category: task.category || null,
      dueDate: task.dueDate || null,
      columnId: task.columnId,
      projectId: task.projectId,
      assignees: task.assignees || [],
      createdById: task.createdById
    }).returning();
    return result[0];
  }

  async updateTask(id: number, updates: Partial<Task>): Promise<Task | undefined> {
    const result = await db
      .update(tasks)
      .set(updates)
      .where(eq(tasks.id, id))
      .returning();
    return result[0] || undefined;
  }

  async deleteTask(id: number): Promise<void> {
    await db.delete(tasks).where(eq(tasks.id, id));
  }

  // Quote operations
  async getQuote(id: number): Promise<Quote | undefined> {
    const result = await db.select().from(quotes).where(eq(quotes.id, id)).limit(1);
    return result[0];
  }
  
  async getQuoteByToken(token: string): Promise<Quote | undefined> {
    const result = await db.select().from(quotes).where(eq(quotes.token, token)).limit(1);
    return result[0];
  }
  
  async getQuotesByProject(projectId: number): Promise<Quote[]> {
    return db.select().from(quotes).where(eq(quotes.projectId, projectId));
  }
  
  async getQuotesByUser(userId: number): Promise<Quote[]> {
    return db.select().from(quotes).where(eq(quotes.createdById, userId));
  }
  
  async createQuote(quote: InsertQuote): Promise<Quote> {
    const result = await db.insert(quotes).values(quote).returning();
    return result[0];
  }
  
  async updateQuote(id: number, updates: Partial<Quote>): Promise<Quote | undefined> {
    const result = await db.update(quotes).set(updates).where(eq(quotes.id, id)).returning();
    return result[0];
  }
  
  // Quote Feedback operations
  async getQuoteFeedback(id: number): Promise<QuoteFeedback | undefined> {
    const result = await db.select().from(quoteFeedback).where(eq(quoteFeedback.id, id)).limit(1);
    return result[0];
  }
  
  async getQuoteFeedbackByQuote(quoteId: number): Promise<QuoteFeedback[]> {
    return db.select().from(quoteFeedback).where(eq(quoteFeedback.quoteId, quoteId));
  }
  
  async createQuoteFeedback(feedback: InsertQuoteFeedback): Promise<QuoteFeedback> {
    const result = await db.insert(quoteFeedback).values(feedback).returning();
    return result[0];
  }
}

// Import the Supabase storage implementation
import { SupabaseStorage } from './supabase-storage';

// Determine which storage implementation to use based on environment
const useDirectPgConnections = process.env.USE_DIRECT_PG_CONNECTIONS === 'true';

// For Docker environments, we'll use the Supabase REST API implementation
// This avoids direct PostgreSQL connection issues in containerized environments
console.log(`Using ${useDirectPgConnections ? 'DatabaseStorage (direct PostgreSQL)' : 'SupabaseStorage (REST API)'} implementation`);

// Use Supabase REST API storage to avoid direct PostgreSQL connection issues
export const storage = new SupabaseStorage();
