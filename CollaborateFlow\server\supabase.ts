import { createClient } from '@supabase/supabase-js';

// Check for required environment variables
if (!process.env.SUPABASE_URL) {
  throw new Error('SUPABASE_URL is required. Please provide your Supabase project URL.');
}

if (!process.env.SUPABASE_KEY) {
  throw new Error('SUPABASE_KEY is required. Please provide your Supabase service role key.');
}

// Create Supabase client with additional configuration for Docker environment
export const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY,
  {
    auth: {
      persistSession: false
    },
    global: {
      // Force IPv4 connections and add more retries
      fetch: (url, options) => {
        console.log(`Making Supabase request to: ${url}`);
        // Use native fetch with additional options
        return fetch(url, {
          ...options,
          // Add longer timeout for Docker environment
          signal: AbortSignal.timeout(10000)
        });
      }
    }
  }
);

// Test Supabase connection on startup
supabase.auth.getSession().then(() => {
  console.log('✅ Supabase REST API connection verified successfully');
}).catch(err => {
  console.error('❌ Error connecting to Supabase REST API:', err.message);
});