// TypeScript interfaces for all entities in CollaborateFlow

// Base interface with common fields
interface BaseEntity {
  id: number;
  createdAt: string;
  updatedAt?: string; // Made optional since not all tables have this field
}

// User entity
export interface User extends BaseEntity {
  username: string;
  email: string;
  fullName: string;
  hasCompletedSetup: boolean;
  // Added fields needed for Supabase integration
  supabaseId?: string;
  password?: string;
  avatarUrl?: string | null;
  role?: string | null;
  organizationId?: number | null;
}

// Team entity
export interface Team extends BaseEntity {
  name: string;
  description: string;
  createdById: number;
  organizationId: number; // Required field to match Supabase schema
}

// Team member entity
export interface TeamMember extends BaseEntity {
  teamId: number;
  userId: number;
  role: 'admin' | 'member' | 'viewer';
}

// Project entity
export interface Project extends BaseEntity {
  name: string;
  description: string;
  teamId: number;
  createdById: number;
  status: 'planning' | 'in_progress' | 'completed' | 'on_hold';
}

// Column entity (for Kanban board)
export interface Column {
  id: number;
  name: string;
  order: number;
  projectId: number;
}

// Task entity
export interface Task extends BaseEntity {
  title: string;
  description: string;
  columnId: number;
  projectId: number;
  assigneeId: number | null;
  order: number;
}

// Floor Plan entity
export interface FloorPlan extends BaseEntity {
  projectId: number;
  fileName: string;
  fileUrl: string;
  version: number;
  status: 'uploaded' | 'processing' | 'processed' | 'failed';
}

// Quote entity
export interface Quote extends BaseEntity {
  projectId: number;
  name: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected';
  totalMaterialCost: number;
  totalLaborCost: number;
  markupPercentage: number;
  totalCost: number;
  createdById: number;
}

// Symbol entity (detected in floor plans)
export interface Symbol {
  id: number;
  floorPlanId: number;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
  properties: Record<string, any>;
  createdAt: string;
}

// Material entity
export interface Material extends BaseEntity {
  quoteId: number;
  name: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

// Labor entity
export interface Labor extends BaseEntity {
  quoteId: number;
  description: string;
  hours: number;
  rate: number;
  totalPrice: number;
}
