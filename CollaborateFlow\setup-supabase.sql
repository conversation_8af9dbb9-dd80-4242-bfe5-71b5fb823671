-- Create tables if they don't exist
-- Organizations table
CREATE TABLE IF NOT EXISTS "organizations" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "email" TEXT,
  "phone" TEXT,
  "address" TEXT,
  "city" TEXT,
  "state" TEXT,
  "zip" TEXT,
  "country" TEXT,
  "tax_id" TEXT,
  "license_number" TEXT,
  "website" TEXT,
  "logo" TEXT,
  "created_by_id" INTEGER NOT NULL,
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Users table
CREATE TABLE IF NOT EXISTS "users" (
  "id" SERIAL PRIMARY KEY,
  "username" TEXT NOT NULL,
  "password" TEXT NOT NULL,
  "full_name" TEXT,
  "email" TEXT,
  "avatar_url" TEXT,
  "organization_id" INTEGER,
  "has_completed_setup" BOOLEAN DEFAULT FALSE,
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Teams table
CREATE TABLE IF NOT EXISTS "teams" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "created_by_id" INTEGER NOT NULL,
  "organization_id" INTEGER NOT NULL,
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Team members table
CREATE TABLE IF NOT EXISTS "team_members" (
  "id" SERIAL PRIMARY KEY,
  "team_id" INTEGER NOT NULL,
  "user_id" INTEGER NOT NULL,
  "role" TEXT DEFAULT 'member'
);

-- Projects table
CREATE TABLE IF NOT EXISTS "projects" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "start_date" TEXT,
  "end_date" TEXT,
  "status" TEXT DEFAULT 'planning',
  "client_name" TEXT,
  "client_email" TEXT,
  "client_phone" TEXT,
  "estimated_budget" TEXT,
  "tags" JSONB DEFAULT '[]',
  "created_at" TIMESTAMP DEFAULT NOW(),
  "team_id" INTEGER NOT NULL,
  "created_by_id" INTEGER NOT NULL
);

-- Columns table
CREATE TABLE IF NOT EXISTS "columns" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "order" INTEGER NOT NULL,
  "project_id" INTEGER NOT NULL
);

-- Tasks table
CREATE TABLE IF NOT EXISTS "tasks" (
  "id" SERIAL PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "status" TEXT NOT NULL,
  "priority" TEXT DEFAULT 'medium',
  "category" TEXT,
  "due_date" TEXT,
  "column_id" INTEGER NOT NULL,
  "project_id" INTEGER NOT NULL,
  "assignees" JSONB DEFAULT '[]',
  "created_by_id" INTEGER NOT NULL,
  "assigned_to" INTEGER,
  "order" INTEGER DEFAULT 0 NOT NULL
);

-- Quotes table
CREATE TABLE IF NOT EXISTS "quotes" (
  "id" SERIAL PRIMARY KEY,
  "quote_number" TEXT NOT NULL,
  "project_id" INTEGER NOT NULL,
  "project_name" TEXT NOT NULL,
  "client_name" TEXT NOT NULL,
  "client_email" TEXT NOT NULL,
  "client_phone" TEXT,
  "client_company" TEXT,
  "client_address" TEXT,
  "issue_date" TIMESTAMP NOT NULL,
  "expiry_date" TIMESTAMP NOT NULL,
  "subtotal" NUMERIC NOT NULL,
  "tax_rate" NUMERIC NOT NULL,
  "tax" NUMERIC NOT NULL,
  "total" NUMERIC NOT NULL,
  "labor_total" NUMERIC NOT NULL,
  "materials_total" NUMERIC NOT NULL,
  "status" TEXT DEFAULT 'draft',
  "payment_terms" TEXT,
  "notes" TEXT,
  "items" JSONB NOT NULL,
  "token" TEXT,
  "created_by_id" INTEGER NOT NULL,
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW(),
  "logo_url" TEXT,
  "organization_id" INTEGER,
  "signature_data" TEXT,
  "approved_at" TIMESTAMP,
  "rejected_at" TIMESTAMP,
  "viewed_at" TIMESTAMP
);

-- Quote feedback table
CREATE TABLE IF NOT EXISTS "quote_feedback" (
  "id" SERIAL PRIMARY KEY,
  "quote_id" INTEGER NOT NULL,
  "message" TEXT NOT NULL,
  "section" TEXT,
  "item_index" INTEGER,
  "is_client" BOOLEAN NOT NULL,
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Session table (only create if it doesn't exist)
CREATE TABLE IF NOT EXISTS "session" (
  "sid" VARCHAR NOT NULL PRIMARY KEY,
  "sess" JSON NOT NULL,
  "expire" TIMESTAMP(6) NOT NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS "idx_users_username" ON "users" ("username");
CREATE INDEX IF NOT EXISTS "idx_team_members_team_id" ON "team_members" ("team_id");
CREATE INDEX IF NOT EXISTS "idx_team_members_user_id" ON "team_members" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_projects_team_id" ON "projects" ("team_id");
CREATE INDEX IF NOT EXISTS "idx_columns_project_id" ON "columns" ("project_id");
CREATE INDEX IF NOT EXISTS "idx_tasks_project_id" ON "tasks" ("project_id");
CREATE INDEX IF NOT EXISTS "idx_tasks_column_id" ON "tasks" ("column_id");
CREATE INDEX IF NOT EXISTS "idx_quotes_project_id" ON "quotes" ("project_id");
CREATE INDEX IF NOT EXISTS "idx_quote_feedback_quote_id" ON "quote_feedback" ("quote_id");
CREATE INDEX IF NOT EXISTS "idx_session_expire" ON "session" ("expire");