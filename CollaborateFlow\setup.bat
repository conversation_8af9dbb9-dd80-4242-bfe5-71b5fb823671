@echo off
setlocal

echo ====================================
echo   CollaborateFlow Setup Assistant
echo ====================================
echo.

echo Checking for Docker installation...
docker --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Docker is not installed! Please install Docker first:
    echo Visit: https://www.docker.com/products/docker-desktop
    exit /b 1
)
echo [OK] Docker is installed

echo Checking for Docker Compose...
docker-compose --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Docker Compose is not installed or not in PATH!
    echo Docker Compose is usually included with Docker Desktop.
    exit /b 1
)
echo [OK] Docker Compose is installed

echo Checking for environment file...
if not exist ".env" (
    echo Environment file (.env) not found. Creating from example...
    
    if not exist ".env.example" (
        echo ERROR: .env.example file not found!
        exit /b 1
    )
    
    copy .env.example .env > nul
    echo [OK] Created .env file from example
    echo Note: You may want to review .env file and update any values if needed
) else (
    echo [OK] Environment file (.env) already exists
)

echo.
echo Building Docker containers (this may take a few minutes)...
docker-compose build
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Error building Docker containers!
    exit /b 1
)
echo [OK] Docker containers built successfully

echo.
echo Starting Docker containers...
docker-compose up -d
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Error starting Docker containers!
    exit /b 1
)
echo [OK] Docker containers started successfully

echo Waiting for services to start...
timeout /t 5 /nobreak > nul

echo.
echo ==================================================
echo   CollaborateFlow is now running!
echo ==================================================
echo.
echo Access the application:
echo   * Web Interface: http://localhost:5001
echo   * Database: localhost:5432 (username: postgres, password: postgres)
echo.
echo Common commands:
echo   * View logs: docker-compose logs
echo   * Stop application: docker-compose down
echo   * Create a user: Run create-user.bat with appropriate parameters
echo.
echo For more information, see the README.md file.
echo ==================================================
