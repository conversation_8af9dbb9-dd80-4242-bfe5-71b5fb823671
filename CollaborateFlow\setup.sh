#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
BLUE="\033[0;34m"
YELLOW="\033[1;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${BLUE}====================================${NC}"
echo -e "${BLUE}  CollaborateFlow Setup Assistant${NC}"
echo -e "${BLUE}====================================${NC}"
echo 

# Check for Docker installation
echo -e "${YELLOW}Checking for Docker installation...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed! Please install Docker first:${NC}"
    echo -e "Visit: https://www.docker.com/products/docker-desktop"
    exit 1
fi
echo -e "${GREEN}✓ Docker is installed${NC}"

# Check for Docker Compose installation
echo -e "${YELLOW}Checking for Docker Compose...${NC}"
if ! docker-compose --version &> /dev/null; then
    echo -e "${RED}Docker Compose is not installed or not in PATH!${NC}"
    echo -e "Docker Compose is usually included with Docker Desktop."
    exit 1
fi
echo -e "${GREEN}✓ Docker Compose is installed${NC}"

# Check for .env file and create if it doesn't exist
echo -e "${YELLOW}Checking for environment file...${NC}"
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}Environment file (.env) not found. Creating from example...${NC}"
    
    if [ ! -f ".env.example" ]; then
        echo -e "${RED}Error: .env.example file not found!${NC}"
        exit 1
    fi
    
    cp .env.example .env
    echo -e "${GREEN}✓ Created .env file from example${NC}"
    echo -e "${YELLOW}Note: You may want to review .env file and update any values if needed${NC}"
else
    echo -e "${GREEN}✓ Environment file (.env) already exists${NC}"
fi

# Build the Docker containers
echo -e "\n${YELLOW}Building Docker containers (this may take a few minutes)...${NC}"
if ! docker-compose build; then
    echo -e "${RED}Error building Docker containers!${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Docker containers built successfully${NC}"

# Start the Docker containers
echo -e "\n${YELLOW}Starting Docker containers...${NC}"
if ! docker-compose up -d; then
    echo -e "${RED}Error starting Docker containers!${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Docker containers started successfully${NC}"

# Wait a moment for services to fully start
echo -e "${YELLOW}Waiting for services to start...${NC}"
sleep 5

# Check if containers are running
if [ $(docker-compose ps -q | wc -l) -lt 2 ]; then
    echo -e "${RED}Warning: Not all containers appear to be running!${NC}"
    echo -e "Run 'docker-compose logs' to see what went wrong."
else
    echo -e "${GREEN}✓ All services are running${NC}"
fi

# Final instructions
echo -e "\n${GREEN}==================================================${NC}"
echo -e "${GREEN}  CollaborateFlow is now running!${NC}"
echo -e "${GREEN}==================================================${NC}"
echo -e "\n${BLUE}Access the application:${NC}"
echo -e "  • Web Interface: ${YELLOW}http://localhost:5001${NC}"
echo -e "  • Database: ${YELLOW}localhost:5432${NC} (username: postgres, password: postgres)"
echo -e "\n${BLUE}Common commands:${NC}"
echo -e "  • View logs: ${YELLOW}docker-compose logs${NC}"
echo -e "  • Stop application: ${YELLOW}docker-compose down${NC}"
echo -e "  • Create a user: ${YELLOW}./create-user.sh <EMAIL> password\"Full Name\"${NC}"

echo -e "\n${BLUE}For more information, see the README.md file.${NC}"
echo -e "${GREEN}==================================================${NC}"
