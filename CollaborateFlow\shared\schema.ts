import { pgTable, text, serial, integer, boolean, jsonb, timestamp, numeric, uuid } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Organization model
export const organizations = pgTable("organizations", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email"),
  phone: text("phone"),
  address: text("address"),
  city: text("city"),
  state: text("state"),
  zip: text("zip"),
  country: text("country"),
  taxId: text("tax_id"),
  licenseNumber: text("license_number"),
  website: text("website"),
  logo: text("logo"),
  createdById: integer("created_by_id").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

// User model
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  fullName: text("full_name").notNull(),
  email: text("email").notNull(),
  avatarUrl: text("avatar_url"),
  role: text("role").default("user"),
  organizationId: integer("organization_id"),
  hasCompletedSetup: boolean("has_completed_setup").default(false),
  supabaseId: text("supabase_id").unique(), // Add supabaseId for Supabase Auth integration
});

// Team model
export const teams = pgTable("teams", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  createdById: integer("created_by_id").notNull(),
  organizationId: integer("organization_id").notNull(), // This is required in the Supabase database
});

// Team membership model
export const teamMembers = pgTable("team_members", {
  id: serial("id").primaryKey(),
  teamId: integer("team_id").notNull(),
  userId: integer("user_id").notNull(),
  role: text("role").default("member"),
});

// Project model
export const projects = pgTable("projects", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  startDate: text("start_date"),
  endDate: text("end_date"),
  status: text("status").default("planning"), // planning, in-progress, completed, on-hold, cancelled
  clientName: text("client_name"),
  clientEmail: text("client_email"),
  clientPhone: text("client_phone"),
  estimatedBudget: text("estimated_budget"),
  tags: jsonb("tags").default([]),
  createdAt: timestamp("created_at").defaultNow(),
  teamId: integer("team_id").notNull(),
  createdById: integer("created_by_id").notNull(),
});

// Column (for Kanban) model
export const columns = pgTable("columns", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  order: integer("order").notNull(),
  projectId: integer("project_id").notNull(),
});

// Task model
export const tasks = pgTable("tasks", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  status: text("status").notNull(),
  priority: text("priority").default("medium"),
  category: text("category"),
  dueDate: text("due_date"),
  columnId: integer("column_id").notNull(),
  projectId: integer("project_id").notNull(),
  assignedTo: integer("assigned_to").references(() => users.id, { onDelete: "set null" }),
  order: integer("order").default(0).notNull(),
  assignees: jsonb("assignees").default([]),
  createdById: integer("created_by_id").notNull(),
});

// Quote model
export const quotes = pgTable("quotes", {
  id: serial("id").primaryKey(),
  quoteNumber: text("quote_number").notNull(),
  projectId: integer("project_id").notNull(),
  projectName: text("project_name").notNull(),
  clientName: text("client_name").notNull(),
  clientEmail: text("client_email").notNull(),
  clientPhone: text("client_phone"),
  clientCompany: text("client_company"),
  clientAddress: text("client_address"),
  issueDate: timestamp("issue_date").notNull(),
  expiryDate: timestamp("expiry_date").notNull(),
  subtotal: numeric("subtotal").notNull(),
  taxRate: numeric("tax_rate").notNull(),
  tax: numeric("tax").notNull(),
  total: numeric("total").notNull(),
  laborTotal: numeric("labor_total").notNull(),
  materialsTotal: numeric("materials_total").notNull(),
  status: text("status").default("draft"), // draft, sent, viewed, approved, rejected, expired
  paymentTerms: text("payment_terms"),
  notes: text("notes"),
  items: jsonb("items").notNull(),
  token: text("token"), // For client access
  createdById: integer("created_by_id").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  logoUrl: text("logo_url"),
  organizationId: integer("organization_id"),
  signatureData: text("signature_data"), // Base64 encoded signature image
  approvedAt: timestamp("approved_at"), // When the quote was approved
  rejectedAt: timestamp("rejected_at"), // When the quote was rejected
  viewedAt: timestamp("viewed_at"), // When the quote was first viewed
});

// Quote Feedback model
export const quoteFeedback = pgTable("quote_feedback", {
  id: serial("id").primaryKey(),
  quoteId: integer("quote_id").notNull(),
  message: text("message").notNull(),
  section: text("section"),
  itemIndex: integer("item_index"),
  isClient: boolean("is_client").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Schema definitions
export const insertOrganizationSchema = createInsertSchema(organizations).pick({
  name: true,
  email: true,
  phone: true,
  address: true,
  city: true,
  state: true,
  zip: true,
  country: true,
  taxId: true,
  licenseNumber: true,
  website: true,
  logo: true,
  createdById: true,
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  fullName: true,
  email: true,
  avatarUrl: true,
  organizationId: true,
  hasCompletedSetup: true,
  supabaseId: true, // Add supabaseId for Supabase Auth integration
});

export const insertTeamSchema = createInsertSchema(teams).pick({
  name: true,
  description: true,
  createdById: true,
  organizationId: true,
});

export const insertTeamMemberSchema = createInsertSchema(teamMembers).pick({
  teamId: true,
  userId: true,
  role: true,
});

export const insertProjectSchema = createInsertSchema(projects).pick({
  name: true,
  description: true,
  startDate: true,
  endDate: true,
  status: true,
  clientName: true,
  clientEmail: true,
  clientPhone: true,
  estimatedBudget: true,
  tags: true,
  teamId: true,
  createdById: true,
});

export const insertColumnSchema = createInsertSchema(columns).pick({
  name: true,
  order: true,
  projectId: true,
});

export const insertTaskSchema = createInsertSchema(tasks).pick({
  title: true,
  description: true,
  status: true,
  priority: true,
  category: true,
  dueDate: true,
  columnId: true,
  projectId: true,
  assignees: true,
  createdById: true,
});

export const insertQuoteSchema = createInsertSchema(quotes).pick({
  quoteNumber: true,
  projectId: true,
  projectName: true,
  clientName: true,
  clientEmail: true,
  clientPhone: true,
  clientCompany: true,
  clientAddress: true,
  issueDate: true,
  expiryDate: true,
  subtotal: true,
  taxRate: true,
  tax: true,
  total: true,
  laborTotal: true,
  materialsTotal: true,
  status: true,
  paymentTerms: true,
  notes: true,
  items: true,
  token: true,
  createdById: true,
  logoUrl: true,
  organizationId: true,
  signatureData: true,
  approvedAt: true,
  rejectedAt: true,
  viewedAt: true,
});

export const insertQuoteFeedbackSchema = createInsertSchema(quoteFeedback).pick({
  quoteId: true,
  message: true,
  section: true,
  itemIndex: true,
  isClient: true,
  createdAt: true,
});

// Types
export type InsertOrganization = z.infer<typeof insertOrganizationSchema>;
export type Organization = typeof organizations.$inferSelect;

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertTeam = z.infer<typeof insertTeamSchema>;
export type Team = typeof teams.$inferSelect;

export type InsertTeamMember = z.infer<typeof insertTeamMemberSchema>;
export type TeamMember = typeof teamMembers.$inferSelect;

export type InsertProject = z.infer<typeof insertProjectSchema>;
export type Project = typeof projects.$inferSelect;

export type InsertColumn = z.infer<typeof insertColumnSchema>;
export type Column = typeof columns.$inferSelect;

export type InsertTask = z.infer<typeof insertTaskSchema>;
export type Task = typeof tasks.$inferSelect;

export type InsertQuote = z.infer<typeof insertQuoteSchema>;
export type Quote = typeof quotes.$inferSelect;

export type InsertQuoteFeedback = z.infer<typeof insertQuoteFeedbackSchema>;
export type QuoteFeedback = typeof quoteFeedback.$inferSelect;
