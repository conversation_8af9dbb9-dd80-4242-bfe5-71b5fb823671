-- Import script for Supabase database
-- This script uses INSERT statements that are compatible with Supabase

-- Insert sample data into organizations
INSERT INTO organizations (id, name, email, phone, address, city, state, zip, country, tax_id, license_number, website, logo, created_by_id, created_at)
VALUES (1, 'Her<PERSON>', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 'United States', NULL, NULL, NULL, '/uploads/file-1747129561415-*********.webp', 1, '2025-05-13 09:46:01.769371')
ON CONFLICT (id) DO NOTHING;

-- Insert data into users
INSERT INTO users (id, username, password, full_name, email, avatar_url, organization_id, has_completed_setup, role)
VALUES 
(1, 'johndoe', 'bd7c972f48f1fdacd7fc2febb300f1f7a851c8a1c3af4bfdf5a3b146381f7d8ce9be03140e5c1fda54ae17735d624e0f9ce90af8a71e2f34caf6a6750351099a.d4db1a0c4d2c84a7', '<PERSON>', '<EMAIL>', '/avatars/john.jpg', 1, true, 'admin'),
(2, 'janedoe', '85dc7c4a58c5df4ffc83db78ea6d6dbf0e0eed97b1999b4e9a3133bcaef1855566e1d7dff3b100e1b21d321d85893fb62f0174b6456870bd9a26ca1b147ee4ee.8dd35b76b560d92a', '<PERSON>', '<EMAIL>', '/avatars/jane.jpg', 1, true, 'user')
ON CONFLICT (id) DO NOTHING;

-- Insert data into teams
INSERT INTO teams (id, name, description, created_by_id, organization_id, created_at)
VALUES 
(1, 'Engineering', 'Software engineering team', 1, 1, '2025-05-10 13:00:00'),
(2, 'Design', 'UI/UX design team', 1, 1, '2025-05-10 13:15:00'),
(3, 'Marketing', 'Marketing and sales team', 2, 1, '2025-05-10 13:30:00')
ON CONFLICT (id) DO NOTHING;

-- Insert data into team_members
INSERT INTO team_members (id, team_id, user_id, role)
VALUES 
(1, 1, 1, 'owner'),
(2, 2, 1, 'owner'),
(3, 3, 2, 'owner')
ON CONFLICT (id) DO NOTHING;

-- Insert sample data into columns
INSERT INTO columns (id, name, "order", project_id)
VALUES 
(1, 'To Do', 0, 1),
(2, 'In Progress', 1, 1),
(3, 'Review', 2, 1),
(4, 'Done', 3, 1),
(5, 'To Do', 0, 2),
(6, 'In Progress', 1, 2),
(7, 'Done', 2, 2),
(8, 'To Do', 0, 3),
(9, 'In Progress', 1, 3),
(10, 'Done', 2, 3),
(11, 'Backlog', 0, 4),
(12, 'Planning', 1, 4),
(13, 'Materials Ready', 2, 4),
(14, 'Installation', 3, 4),
(15, 'Testing', 4, 4),
(16, 'Complete', 5, 4)
ON CONFLICT (id) DO NOTHING;

-- Insert sample data into projects
INSERT INTO projects (id, name, description, start_date, end_date, team_id, created_by_id, status, client_name, client_email, client_phone, estimated_budget, tags, created_at)
VALUES 
(1, 'Hermes', 'fedf', '2025-05-13', NULL, 1, 2, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 06:46:26.452284'),
(2, 'Heremes 2', 'testing', '2025-05-13', '2025-06-04', 1, 2, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 06:56:10.110725'),
(3, 'Test', 'wguwg', '2025-05-13', '2025-06-08', 1, 2, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 07:21:30.214806'),
(4, 'Electrical Installation Demo', 'Demo project for testing the Kanban board features', '2025-05-13', '2025-06-12', 2, 1, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-13 10:56:27.174317'),
(5, 'test', 'sdfdv', '2025-05-14', '2025-06-08', 3, 1, 'planning', NULL, NULL, NULL, NULL, '[]', '2025-05-14 06:00:21.603995')
ON CONFLICT (id) DO NOTHING;

-- Insert data into tasks
INSERT INTO tasks (id, title, description, status, priority, category, due_date, column_id, project_id, assignees, created_by_id, assigned_to, "order")
VALUES 
(1, 'Setup project structure', 'Create initial project structure and repositories', 'to-do', 'high', 'setup', '2025-05-20', 1, 1, '[]', 1, 1, 0),
(2, 'Design mockups', 'Create UI/UX mockups for the main dashboard', 'in-progress', 'medium', 'design', '2025-05-25', 2, 1, '[]', 1, 2, 0),
(3, 'Install electrical panel', 'Purchase and install the main electrical panel', 'to-do', 'high', 'installation', '2025-05-22', 11, 4, '[]', 1, 1, 0),
(4, 'Run wires for outlets', 'Install wiring for all power outlets', 'to-do', 'medium', 'installation', '2025-05-24', 11, 4, '[]', 1, 1, 1),
(5, 'Install light fixtures', 'Mount and connect all light fixtures', 'planning', 'medium', 'installation', '2025-05-26', 12, 4, '[]', 1, 2, 0),
(6, 'Test circuits', 'Test all electrical circuits for safety', 'planning', 'high', 'testing', '2025-05-28', 12, 4, '[]', 1, 1, 1),
(7, 'Final inspection', 'Conduct final inspection and documentation', 'planning', 'high', 'testing', '2025-05-30', 12, 4, '[]', 1, 1, 2),
(8, 'Create marketing plan', 'Develop comprehensive marketing strategy', 'to-do', 'high', 'marketing', '2025-05-22', 8, 3, '[]', 2, 2, 0),
(9, 'Social media campaign', 'Launch and monitor social media campaign', 'to-do', 'medium', 'marketing', '2025-05-25', 8, 3, '[]', 2, 2, 1)
ON CONFLICT (id) DO NOTHING;

-- Create sample quote template
INSERT INTO quotes (id, quote_number, project_id, project_name, client_name, client_email, client_phone, client_company, client_address, issue_date, expiry_date, subtotal, tax_rate, tax, total, labor_total, materials_total, status, payment_terms, notes, items, token, created_by_id, created_at, updated_at, logo_url, organization_id)
VALUES 
(1, 'Q-2025-001', 4, 'Electrical Installation Demo', 'John Smith', '<EMAIL>', '************', 'Smith Enterprises', '123 Main St, Anytown, USA', '2025-05-14 00:00:00', '2025-06-14 00:00:00', 2500.00, 7.5, 187.50, 2687.50, 1500.00, 1000.00, 'draft', 'Net 30', 'Thank you for your business!', '[{"description":"Electrical panel installation","category":"Labor","quantity":1,"unitPrice":750,"total":750},{"description":"Outlet and switch installation","category":"Labor","quantity":1,"unitPrice":500,"total":500},{"description":"Light fixture installation","category":"Labor","quantity":1,"unitPrice":250,"total":250},{"description":"Main electrical panel","category":"Materials","quantity":1,"unitPrice":500,"total":500},{"description":"Outlets and switches","category":"Materials","quantity":20,"unitPrice":15,"total":300},{"description":"Light fixtures","category":"Materials","quantity":4,"unitPrice":50,"total":200}]', 'abc123token', 1, '2025-05-14 09:00:00', '2025-05-14 09:00:00', '/uploads/file-1747129561415-*********.webp', 1)
ON CONFLICT (id) DO NOTHING;

-- Reset sequences to continue after the highest ID
SELECT setval('organizations_id_seq', (SELECT MAX(id) FROM organizations));
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('teams_id_seq', (SELECT MAX(id) FROM teams));
SELECT setval('team_members_id_seq', (SELECT MAX(id) FROM team_members));
SELECT setval('projects_id_seq', (SELECT MAX(id) FROM projects));
SELECT setval('columns_id_seq', (SELECT MAX(id) FROM columns));
SELECT setval('tasks_id_seq', (SELECT MAX(id) FROM tasks));
SELECT setval('quotes_id_seq', (SELECT MAX(id) FROM quotes));