import { supabase, db } from './server/db';
import { scrypt, timingSafeEqual } from "crypto";
import { promisify } from "util";

const scryptAsync = promisify(scrypt);

// Function to compare passwords (copied from auth.ts for testing)
async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

async function testAuthentication() {
  console.log('Testing user authentication with Supabase...');
  
  try {
    // 1. First, retrieve all users from the database to check what's available
    const users = await getAllUsers();
    console.log('Found users:', users);
    
    if (!users || users.length === 0) {
      console.error('No users found');
      return;
    }
    
    // 2. Focus on testing with the first user we find
    const testUser = users[0];
    console.log('Testing authentication with user:', testUser.username || 'User ID: ' + testUser.id);
    
    // 3. Test direct Supabase authentication endpoint
    console.log('Testing direct API authentication with Supabase...');
    try {
      // This is a POST request to Supabase's auth endpoint
      const { data, error } = await supabase.auth.signInWithPassword({
        email: testUser.email,
        password: 'testpassword' // We don't know the actual password
      });
      
      if (error) {
        console.log('Expected auth error (we don\'t know the actual password):', error.message);
      } else {
        console.log('Auth successful:', data);
      }
    } catch (error) {
      console.error('Supabase auth API error:', error);
    }
    
    // 4. Test our application's internal authentication logic
    console.log('\nTesting our application\'s internal authentication logic...');
    // This uses our application's password comparison logic
    console.log('Note: These tests will likely fail as we don\'t know the original passwords');
    try {
      const testPassword = 'test123';
      const result = await comparePasswords(testPassword, testUser.password);
      console.log('Password comparison result:', result);
    } catch (error) {
      console.error('Password comparison error:', error);
    }
    
  } catch (error) {
    console.error('Authentication test error:', error);
  }
}

async function getAllUsers() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*');
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error retrieving users:', error);
    return [];
  }
}

testAuthentication();