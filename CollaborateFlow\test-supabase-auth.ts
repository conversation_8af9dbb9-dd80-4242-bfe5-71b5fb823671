import { supabase } from './server/db';

async function testSupabaseAuth() {
  console.log('Testing Supabase authentication...');

  try {
    // 1. Create a test user with Supabase Auth
    console.log('\nAttempting to create a test user...');
    const email = '<EMAIL>';
    const password = 'Password123!';
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
    });
    
    if (signUpError) {
      console.log('Sign-up error (might be expected if user already exists):', signUpError.message);
    } else {
      console.log('Sign-up successful:', signUpData);
    }

    // 2. Sign in with the test user
    console.log('\nAttempting to sign in...');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (signInError) {
      console.log('Sign-in error:', signInError.message);
    } else {
      console.log('Sign-in successful!');
      console.log('User ID:', signInData.user?.id);
      console.log('Session:', signInData.session ? 'Active' : 'None');
      
      // 3. Test authenticated access to protected data
      console.log('\nTesting authenticated access to data...');
      const { data: userData, error: userError } = await supabase
        .from('teams')  // Using 'teams' as we know it exists
        .select('*')
        .limit(1);
        
      if (userError) {
        console.log('Data access error:', userError.message);
      } else {
        console.log('Successfully accessed data with authenticated user!');
        console.log('Sample data:', userData);
      }
      
      // 4. Sign out
      console.log('\nSigning out...');
      const { error: signOutError } = await supabase.auth.signOut();
      
      if (signOutError) {
        console.log('Sign-out error:', signOutError.message);
      } else {
        console.log('Successfully signed out');
      }
    }
    
  } catch (error) {
    console.error('Supabase authentication test error:', error);
  }
}

testSupabaseAuth();