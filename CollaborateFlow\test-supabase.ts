import { supabase, db } from './server/db';

async function testSupabaseConnection() {
  console.log('Testing Supabase connection...');
  
  // Test direct Supabase client connection
  try {
    const { data, error } = await supabase.from('users').select('*').limit(1);
    if (error) throw error;
    console.log('Supabase client connection successful!');
    console.log('User data:', data);
  } catch (error) {
    console.error('Supabase client connection error:', error);
  }
  
  // Test Drizzle ORM connection
  try {
    const users = await db.query.users.findMany({ limit: 1 });
    console.log('Drizzle ORM connection successful!');
    console.log('User data via Drizzle:', users);
  } catch (error) {
    console.error('Drizzle ORM connection error:', error);
  }
}

testSupabaseConnection();