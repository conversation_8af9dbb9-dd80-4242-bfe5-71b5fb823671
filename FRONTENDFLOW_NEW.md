Okay, I will rewrite the **CoElec Frontend Flow Architecture** document. The core UI structure and flows will largely remain the same as they describe user interactions, but references to the backend technology will be updated to **Supabase**. We'll also ensure the frontend flow for AI symbol recognition clearly outlines the user's interaction with this asynchronous process, and acknowledge that the existing UI (React, TypeScript, shadcn.ui, Tailwind CSS) is the foundation, with development in the **Windsurf IDE**.

Here's the revised Frontend Flow Architecture:

---

## # CoElec Frontend Flow Architecture

## ## Overview

This document outlines the comprehensive frontend architecture for the CoElec electrical estimation platform, detailing user flows, component hierarchy, and interaction patterns. The architecture is designed to provide a seamless experience for electrical contractors. The frontend is built with **React, TypeScript, shadcn.ui, and Tailwind CSS**, serving as the starting point for further development using the **Windsurf IDE**, connecting to a **Supabase** backend.

## ## User Authentication Flow

```mermaid
graph TD
    Landing[Landing Page] --> Auth{Authentication}
    Auth -->|Sign Up| Register[Registration Form]
    Auth -->|Sign In| Login[Login Form]
    Register --> OrgSetup[Organization Setup]
    Login --> Dashboard
    OrgSetup --> Dashboard
```

### ### Implementation Notes

*   **Landing Page**: First-time visitor entry point with value proposition, feature overview, and authentication options.
*   **Authentication**: Implement with **Supabase Auth**, supporting email/password and optional OAuth providers (Google, Microsoft). The frontend will handle UI for these flows, interacting with Supabase Auth endpoints.
*   **Registration Form**: Capture essential user information with progressive disclosure for additional fields. On submission, create user in **Supabase Auth** and associated profile data in the database.
*   **Organization Setup**: First-time organization configuration including company details, branding, and initial user roles, stored in **Supabase Database**.
*   **Login Form**: Clean interface with proper validation, error handling, and "Remember Me" functionality, authenticating against **Supabase Auth**.

## ## Dashboard and Main Navigation

```mermaid
graph TD
    Dashboard --> ProjectHub[Project Hub]
    Dashboard --> Analytics[Analytics Dashboard]
    Dashboard --> Settings[Settings & Administration]
    Dashboard --> UserProfile[User Profile]
```

### ### Implementation Notes

*   **Dashboard**: Personalized homepage with activity summary, quick actions, and key metrics fetched from **Supabase**.
*   **Navigation System**: Implement using React Router with role-based access control (roles fetched from **Supabase** user metadata/claims) and breadcrumb support.
*   **Responsive Design**: Ensure seamless transition between desktop, tablet, and mobile views with adaptive layouts.
*   **State Management**: Use Zustand for global application state and React Query (TanStack Query) for server state management with **Supabase** (fetching, caching, and synchronization).

## ## Project Hub Flow

```mermaid
graph TD
    ProjectHub --> CreateProject[Create New Project]
    ProjectHub --> ProjectList[Project List]
    ProjectList --> ProjectDetail[Project Details]
```

### ### Implementation Notes

*   **Project Hub**: Central location for managing all projects with filtering, sorting, and search capabilities, querying data from **Supabase**.
*   **Create Project**: Step-by-step wizard interface with validation for each stage, creating project records in **Supabase**.
*   **Project List**: Implement virtualized list for performance with over 50 items, displaying projects fetched from **Supabase**.
*   **List Filtering**: Add filtering by status, date, client, and custom tags, applied to queries against **Supabase**.

## ## Project Creation and AI Symbol Detection Flow

```mermaid
graph TD
    CreateProject --> ProjectSetup[Project Setup]
    ProjectSetup --> FloorPlanUpload[Upload Floor Plans]
    FloorPlanUpload --> ProcessingInitiation[Initiate AI Processing]
    ProcessingInitiation --> ProcessingQueueView[View Processing Queue & Status]
    ProcessingQueueView --> SymbolDetectionFeedback[AI Symbol Detection In Progress...]
    SymbolDetectionFeedback --> SymbolReview[Symbol Review & Editing on Canvas]
    SymbolReview --> MaterialEstimation[Material Estimation]
    MaterialEstimation --> LaborEstimation[Labor Estimation]
    LaborEstimation --> QuoteGeneration[Quote Generation]
    QuoteGeneration --> ClientApproval[Client Approval Flow]
```

### ### Implementation Notes

*   **Project Setup**: Capture project metadata, client information, and project type; saved to **Supabase**.
*   **Floor Plan Upload**: Implement drag-and-drop interface with progress indicators and file validation. Files are uploaded to **Supabase Storage**.
*   **Processing Initiation**: After upload, the frontend triggers a **Supabase Edge Function**. This function will be responsible for initiating the AI symbol detection pipeline, which involves calling the `SymbolDetectionMCP` server.
*   **Processing Queue View & Status**: The frontend will display a visual indicator of the floor plan's processing status (e.g., "Queued," "Processing," "Completed," "Error"). This status will be updated in **Supabase DB** by backend processes and can be listened to via **Supabase Realtime** subscriptions or periodic polling. Estimated completion time may be displayed if available.
*   **AI Symbol Detection In Progress...**: The frontend provides clear user feedback that AI analysis is underway. This is an asynchronous process.
*   **Symbol Review & Editing on Canvas**:
    *   Once processing is complete (notified via **Supabase Realtime** or polling), the frontend fetches detected symbols (including coordinates, types, confidence scores) from **Supabase DB**.
    *   A rich canvas interface allows users to:
        *   View detected symbols overlaid on the floor plan.
        *   Manually add, delete, or modify symbols and their properties.
        *   Verify or correct AI detections, especially those with low confidence scores.
    *   User corrections are saved back to **Supabase DB**, forming a crucial feedback loop for improving AI models/prompts over time.
*   **Material & Labor Estimation**: Interactive interface for adjusting quantities and rates, based on symbols and rules, with data managed in **Supabase**.
*   **Quote Generation**: Template-based system with preview capability. Quote data is assembled and can be generated via a **Supabase Edge Function** if server-side PDF generation is chosen.
*   **Client Approval**: Workflow for sending quotes and tracking client responses.

## ## Project Details Navigation

```mermaid
graph TD
    ProjectDetail --> ViewFloorPlans[View Floor Plans]
    ProjectDetail --> EditSymbols[Edit Detected Symbols]
    ProjectDetail --> ManageEstimates[Manage Estimates]
    ProjectDetail --> ManageQuotes[Manage Quotes]
    ProjectDetail --> ProjectTimeline[Project Timeline]
    ProjectDetail --> ProjectNotes[Project Notes & Comments]
```

### ### Implementation Notes

*   **Project Detail**: Comprehensive dashboard for a specific project with contextual actions, all data fetched from **Supabase**.
*   **Tab Navigation**: Implement using tabs or segmented controls for efficient space utilization.
*   **Context Preservation**: Maintain state when navigating between tabs for seamless user experience.
*   **Action Permissions**: Implement role-based action availability based on user roles from **Supabase**.

## ## Floor Plan Management

```mermaid
graph TD
    ViewFloorPlans --> UploadNewVersion[Upload New Version]
    ViewFloorPlans --> CompareVersions[Compare Versions]
    ViewFloorPlans --> AnnotateFloorPlan[Annotate Floor Plan]
```

### ### Implementation Notes

*   **View Floor Plans**: Interactive viewer (canvas-based) with zoom, pan, and measurement tools. Floor plans loaded from **Supabase Storage**.
*   **Version Management**: Track and display multiple floor plan versions with change indicators. Version metadata stored in **Supabase DB**.
*   **Comparison Tool**: Visual diff tool for comparing versions (frontend logic).
*   **Annotation**: Layer-based annotation system with collaboration features (annotations stored in **Supabase DB**, real-time updates via **Supabase Realtime**).

## ## Symbol Editing (Post AI-Detection)

```mermaid
graph TD
    EditSymbols --> AddSymbol[Add Symbol Manually]
    EditSymbols --> RemoveSymbol[Remove Detected/Manual Symbol]
    EditSymbols --> ModifySymbol[Modify Symbol Properties]
    EditSymbols --> BatchEdit[Batch Edit Symbols]
```

### ### Implementation Notes

*   **Interactive Canvas**: Canvas-based editor with high-performance rendering for displaying floor plans and symbols.
*   **Symbol Library**: Categorized library of standard electrical symbols (data from **Supabase DB**) with search functionality for manual additions.
*   **Direct Manipulation**: Drag, resize, and rotate capabilities with snapping for symbols on the canvas.
*   **Batch Operations**: Tools for selecting and editing multiple symbols simultaneously.
*   **Undo/Redo**: Comprehensive history management for all editing operations on the frontend. Changes are persisted to **Supabase DB**.

## ## Estimate Management

```mermaid
graph TD
    ManageEstimates --> ViewMaterials[View Materials List]
    ManageEstimates --> AdjustQuantities[Adjust Quantities]
    ManageEstimates --> UpdatePricing[Update Pricing from Suppliers]
    ManageEstimates --> AddCustomItems[Add Custom Items]
```

### ### Implementation Notes

*   **Materials List**: Categorized display of all required materials with quantity and cost, data from **Supabase DB**.
*   **Editable Grid**: High-performance data grid for viewing and editing quantities.
*   **Pricing Integration**: Frontend displays prices. Real-time price updates are fetched from **Supabase DB**, which is updated by backend processes interacting with the `SupplierIntegrationMCP`.
*   **Custom Items**: Interface for adding items not derived from symbols.
*   **What-If Analysis**: Tools for exploring different pricing and quantity scenarios (frontend calculations based on data from **Supabase**).

## ## Quote Management

```mermaid
graph TD
    ManageQuotes --> GenerateNewQuote[Generate New Quote]
    ManageQuotes --> EditExistingQuote[Edit Existing Quote]
    ManageQuotes --> SendQuoteToClient[Send Quote to Client]
    ManageQuotes --> TrackQuoteStatus[Track Quote Status]
```

### ### Implementation Notes

*   **Quote Templates**: Customizable templates with company branding (templates potentially stored in **Supabase DB** or as static assets).
*   **Quote Editor**: WYSIWYG editor for fine-tuning quote appearance.
*   **PDF Generation**: Client-side PDF generation or server-side via a **Supabase Edge Function**, with preview.
*   **Delivery Options**: Email delivery (via **Supabase Edge Function** calling an email service) with tracking and secure client portal access.
*   **Status Tracking**: Visual indicators of quote status (Draft, Sent, Viewed, Approved, Declined), status stored in **Supabase DB**.

## ## Client Approval Flow

```mermaid
graph TD
    ClientApproval --> EmailQuoteLink[Email Quote Link]
    EmailQuoteLink --> ClientPortalView[Client Portal View]
    ClientPortalView --> ClientFeedback[Client Feedback]
    ClientPortalView --> ClientApproves[Client Approves]
    ClientPortalView --> ClientRejects[Client Rejects]
    ClientFeedback --> ReviseQuote[Revise Quote by Contractor]
    ClientApproves --> ProjectExecution[Project Execution]
    ClientRejects --> ReviseQuote
```

### ### Implementation Notes

*   **Email Delivery**: Customizable email templates with tracking capabilities, sent via **Supabase Edge Function**.
*   **Client Portal**: Simplified interface for clients to review quotes without requiring full account creation (e.g., using secure links generated by **Supabase**).
*   **Feedback Mechanism**: Structured feedback collection with annotation capabilities.
*   **Digital Signature**: Secure approval process with audit trail (integration with a third-party service, managed via **Supabase Edge Functions**).
*   **Revision Workflow**: Process for tracking and implementing requested changes.

## ## Settings and User Management

```mermaid
graph TD
    Settings --> CompanyProfile[Company Profile]
    Settings --> UserManagement[User Management]
    Settings --> SubscriptionPlan[Subscription Plan]
    Settings --> IntegrationSettings[Integration Settings]
    Settings --> NotificationPrefs[Notification Preferences]
```

### ### Implementation Notes

*   **Company Profile**: Management of company details, logo, and branding, stored in **Supabase DB**.
*   **User Management**: Interface for inviting, managing, and assigning roles to team members (interacts with **Supabase Auth** and user profile tables).
*   **Role Permissions**: Granular permission configuration by role (UI reflects permissions, enforced by **Supabase RLS**).
*   **Integration Management**: Configuration of external service connections (e.g., API keys for digital signature service, stored securely).
*   **Notification System**: Customizable notifications for different event types.

## ## User Profile

```mermaid
graph TD
    UserProfile --> PersonalInfo[Personal Information]
    UserProfile --> ChangePassword[Change Password]
    UserProfile --> LinkedAccounts[Linked OAuth Accounts]
    UserProfile --> ActivityHistory[Activity History]
```

### ### Implementation Notes

*   **Personal Information**: User profile management with avatar support, data in **Supabase DB** linked to **Supabase Auth** user.
*   **Security Settings**: Password management (via **Supabase Auth**) with 2FA options.
*   **Account Linking**: OAuth provider connections managed via **Supabase Auth**.
*   **Activity Tracking**: Chronological display of user actions with filtering, based on audit logs in **Supabase DB**.

## ## Analytics Dashboard

```mermaid
graph TD
    Analytics --> ProjectMetrics[Project Metrics]
    Analytics --> FinancialReports[Financial Reports]
    Analytics --> EstimationAccuracy[Estimation Accuracy]
    Analytics --> UserActivity[User Activity]
    Analytics --> ClientEngagement[Client Engagement]
```

### ### Implementation Notes

*   **Data Visualization**: Interactive charts and graphs using a lightweight charting library, displaying data queried from **Supabase DB**.
*   **Filtering Capabilities**: Time-period selection and dimension filtering for analytics queries.
*   **Export Options**: CSV, Excel, and PDF export of reports (data fetched from **Supabase**, formatting/export handled client-side or via **Supabase Edge Function**).
*   **Custom Reports**: User-defined report configuration and saving.
*   **Real-time Updates**: Live data refreshing for active metrics where applicable (using **Supabase Realtime**).

## ## Mobile Responsive Views

```mermaid
graph TD
    MobileViews[Mobile Views] --> MobileDashboard[Mobile Dashboard]
    MobileViews --> MobileProjectView[Mobile Project View]
    MobileViews --> MobileQuoteApproval[Mobile Quote Approval]
```

### ### Implementation Notes

*   **Responsive Design**: Mobile-first approach using Tailwind CSS for responsive layouts.
*   **Touch Optimization**: Larger touch targets and swipe gestures for mobile interfaces.
*   **Offline Capabilities**: Progressive enhancement with offline mode for key features (e.g., using service workers and local storage, synchronizing with **Supabase** when online).
*   **Performance Optimization**: Lazy loading and code splitting for mobile performance.

## ## Technical Implementation Guidelines

### ### Component Architecture
*   Implement components following the Atomic Design methodology.
*   Create shared UI components based on shadcn.ui with consistent styling.
*   Follow strict type definitions with TypeScript interfaces for all props.
*   Implement error boundaries for fault isolation.

### ### State Management
*   **Global state**: Zustand for application-wide state.
*   **Server state**: React Query (TanStack Query) for data fetching, caching, and synchronization with **Supabase**.
*   **Form state**: React Hook Form with Zod schema validation.
*   **Component state**: React `useState` for component-specific state.

### ### Performance Considerations
*   Implement virtualization for all lists with more than 50 items.
*   Use `React.memo` and `useMemo` for computationally expensive operations.
*   Implement code splitting for all routes and large components.
*   Optimize images and assets for faster loading.

### ### Accessibility
*   Follow WCAG 2.1 AA standards for all components.
*   Implement keyboard navigation for all interactive elements.
*   Provide appropriate ARIA attributes for complex components.
*   Ensure proper contrast ratios and text scaling.

This frontend architecture provides a comprehensive framework for implementing the CoElec platform with a focus on user experience, performance, and maintainability, leveraging the existing UI codebase and connecting to a **Supabase** backend. The detailed flow diagrams and implementation notes should guide **Windsurf IDE** users in creating a robust frontend application.