## # CoElec Product Requirements Document (PRD)

## ## 1. Introduction

### ### 1.1 Purpose

CoElec is an AI-powered application designed to streamline the electrical estimation process by automating the detection of electrical symbols from floor plans, generating accurate material and labor estimates, and creating professional client quotes. This platform will revolutionize how electrical contractors estimate projects, reducing the time and effort required while improving accuracy and professionalism.

### ### 1.2 Product Vision

To create the industry-leading electrical estimation platform that transforms how electrical contractors work, by leveraging cutting-edge AI to automate tedious manual processes, provide accurate estimates, and enable seamless collaboration between team members and clients.

### ### 1.3 Scope

CoElec will cover the entire workflow from floor plan upload through client approval:

*   Floor plan processing and electrical symbol detection using multimodal AI.
*   Material takeoff and labor estimation based on detected symbols.
*   Supplier price integration and real-time cost updates via MCP servers.
*   Quote generation with professional templates and branding.
*   Client approval workflow with digital signatures.
*   Project management and team collaboration tools.

### ### 1.4 Target Audience

*   **Primary**: Electrical contractors and estimators who create quotes for projects.
*   **Secondary**: Project managers, electricians, and business owners who oversee estimation.
*   **Tertiary**: Clients receiving electrical quotes who need to review and approve them.

## ## 2. User Personas

### ### 2.1 Electrical Estimator (Primary)

**Name**: <PERSON> **Age**: 42 **Role**: Senior Estimator at Mid-Size Electrical Contractor **Technical Proficiency**: Moderate

**Goals**:
*   Produce accurate estimates quickly to bid on more projects.
*   Reduce manual counting and calculation errors that affect profitability.
*   Stay competitive with pricing while maintaining profit margins.
*   Manage multiple projects simultaneously with limited time.

**Pain Points**:
*   Spends hours manually counting symbols on floor plans (6-8 hours per medium project).
*   Struggles with inconsistent symbols across different architects and drawing standards.
*   Manual price updates from suppliers are time-consuming and often delayed.
*   Difficult to track changes and revisions when clients request modifications.

**Scenario**: Michael receives a set of floor plans for a 50,000 sq ft commercial building. Using CoElec, he uploads the plans and receives an AI-processed takeoff within minutes instead of spending a full day counting symbols. He reviews the detection results, makes a few manual corrections, and generates an accurate quote that he can send to the client the same day.

### ### 2.2 Electrical Business Owner

**Name**: Sarah Rodriguez **Age**: 53 **Role**: Owner of Rodriguez Electrical (25 employees) **Technical Proficiency**: Low to Moderate

**Goals**:
*   Grow business without proportional increase in overhead costs.
*   Ensure consistent, profitable pricing across all projects.
*   Present professional image to clients with polished documentation.
*   Track team performance and project profitability metrics.

**Pain Points**:
*   Concerned about estimation accuracy affecting profitability (average 15% error rate).
*   Limited oversight of estimating team's work and methodologies.
*   Inconsistent presentation of quotes to clients affecting brand perception.
*   Difficulty scaling estimation process with business growth.

**Scenario**: Sarah reviews the monthly estimation performance dashboard in CoElec, identifying that her team has increased quote throughput by 60% while maintaining a 92% win rate on bids. She can see which types of projects are most profitable and direct her team to focus on these opportunities, while also identifying training needs based on estimation accuracy metrics.

### ### 2.3 Project Manager

**Name**: David Chen **Age**: 38 **Role**: Project Manager at Electrical Contractor **Technical Proficiency**: Moderate

**Goals**:
*   Ensure seamless transition from estimate to project execution.
*   Maintain clear communication with clients throughout the project.
*   Track project changes and approvals with proper documentation.
*   Coordinate responsibilities between estimators and field teams.

**Pain Points**:
*   Disconnection between estimation and project execution causing material shortages.
*   Lack of visibility into estimation assumptions that affect project planning.
*   Difficulty tracking client change requests and their financial impact.
*   Manual coordination between estimators and field teams creating delays.

**Scenario**: David receives notification that a client has approved a quote. In CoElec, he can immediately review the detailed breakdown of materials and labor, understand the assumptions made during estimation, and begin planning resource allocation. When the client requests a change to add emergency lighting, he can quickly see the impact on materials, labor, and timeline within the platform.

### ### 2.4 Client (Property Developer)

**Name**: Amanda Lewis **Age**: 45 **Role**: Development Manager at Commercial Property Developer **Technical Proficiency**: Moderate

**Goals**:
*   Receive clear, detailed electrical quotes that align with project requirements.
*   Understand what's included in the pricing to avoid surprises.
*   Easily request and track changes to the scope of work.
*   Compare quotes between contractors efficiently.

**Pain Points**:
*   Quotes vary widely in format and detail between contractors.
*   Difficult to understand what's included without electrical expertise.
*   Change requests lead to delays and confusion about revised pricing.
*   Limited visibility into project status once work begins.

**Scenario**: Amanda receives a quote link from Rodriguez Electrical via email. She opens the client portal without needing to create an account and views a professionally formatted quote with detailed breakdown of materials and labor. She can easily see what's included, request changes through the platform, and digitally approve the quote once she's satisfied, all while tracking the communication history in one place.

## ## 3. User Requirements

### ### 3.1 Electrical Estimator Requirements

#### #### 3.1.1 Floor Plan Processing
*   Upload floor plans in multiple formats (PDF, JPG, PNG, DWG, DXF).
*   Automatic detection of electrical symbols with high accuracy (>90% target).
*   Manual correction tools for symbol detection errors.
*   Measurement scaling tools for accurate dimensions.
*   Ability to manage multiple floor plan versions with change tracking.

#### #### 3.1.2 Symbol Management
*   Comprehensive library of standard electrical symbols.
*   Custom symbol creation and management for non-standard items.
*   Symbol categorization by type (outlets, switches, fixtures, panels, etc.).
*   Symbol counting and reporting with filtering options.
*   Symbol placement validation against electrical codes (potential future enhancement).

#### #### 3.1.3 Estimation Features
*   Automatic material takeoff from detected symbols.
*   Labor calculation based on industry standards and customizable rates.
*   Material cost updates from integrated supplier catalogs (via MCP servers).
*   Markup and overhead calculation with customizable rules.
*   What-if scenario comparison for different materials or approaches.

#### #### 3.1.4 Project Management
*   Project status tracking from creation to completion.
*   Version control for estimates with comparison features.
*   Notes and annotations attached to specific project elements.
*   Task management and assignment to team members.
*   Notification system for updates and approvals.

### ### 3.2 Business Owner Requirements

#### #### 3.2.1 Business Management
*   Company profile and branding management.
*   Team management with role-based permissions.
*   Performance analytics and reporting dashboards.
*   Client relationship management and history.
*   Business process customization and templates.

#### #### 3.2.2 Financial Controls
*   Pricing strategy management with approval workflows.
*   Markup and margin controls with minimum thresholds.
*   Approval workflows for estimates above specified thresholds.
*   Financial reporting and analytics on estimation accuracy.
*   Optional integration with accounting systems.

#### #### 3.2.3 Client Presentation
*   Professional quote templates with company branding.
*   Customizable document formatting and sections.
*   Client portal for quote review and communication.
*   Digital signature and approval process.
*   Comprehensive client communication history.

### ### 3.3 Project Manager Requirements

#### #### 3.3.1 Project Coordination
*   Seamless transition from estimate to project execution.
*   Material ordering integration with quantity verification.
*   Labor scheduling tools based on estimation data.
*   Project timeline visualization and milestone tracking.
*   Change order management with approval workflow.

#### #### 3.3.2 Team Collaboration
*   Real-time collaboration on projects across team members.
*   Comment and feedback system attached to specific elements.
*   Task assignment and tracking with due dates.
*   File sharing and documentation management.
*   Notification system for updates and changes.

#### #### 3.3.3 Client Management
*   Client communication history with searchable archives.
*   Change request tracking with version comparison.
*   Approval workflow management with audit trail.
*   Project status updates customizable for client visibility.
*   Issue resolution tracking and documentation.

### ### 3.4 Client Requirements

#### #### 3.4.1 Quote Review
*   Clear, professional quote presentation without technical jargon.
*   Detailed breakdown of materials and labor in understandable terms.
*   Visual representation of electrical layout when appropriate.
*   Easy comparison between quote versions when changes are made.
*   Simple approval process with digital signature option.

#### #### 3.4.2 Communication
*   Direct messaging with contractor through the platform.
*   Change request submission with clear description fields.
*   Question and answer capability for specific quote items.
*   Notification system for updates and required actions.
*   Document sharing and storage for project-related files.

## ## 4. Functional Specifications

### ### 4.1 User Authentication and Management

#### #### 4.1.1 Authentication
*   Utilize **Supabase Auth** for email/password authentication with strong password requirements.
*   Support for Multi-Factor Authentication (MFA) via Supabase Auth for enhanced security.
*   Role-based access control (Admin, Manager, Estimator, Client) managed via custom claims or user metadata in Supabase.
*   Secure password recovery workflow.
*   Session management with appropriate timeout settings configurable via Supabase.

#### #### 4.1.2 Organization Management
*   Multi-tenant architecture with complete data isolation using Row Level Security (RLS) in Supabase PostgreSQL.
*   Organization profile management (name, address, logo, etc.) stored in dedicated Supabase tables.
*   Team member management with an invite system (leveraging Supabase Auth and custom tables).
*   Role and permission assignment with granular controls, enforced by RLS policies.
*   Organization settings and preferences customization.

#### #### 4.1.3 User Profiles
*   User profile management with personal information linked to Supabase Auth users.
*   User preferences for notifications and display options.
*   Notification settings by type and delivery method.
*   Activity history with audit trail (custom implementation using Supabase tables).
*   Personal dashboard with relevant metrics and tasks.

### ### 4.2 Floor Plan Processing

#### #### 4.2.1 File Upload and Management
*   Drag-and-drop file upload interface with progress indicators.
*   Support for multiple file formats (PDF, JPG, PNG, DWG, DXF) with client-side and server-side (Supabase Edge Function) validation.
*   Batch upload capability for multi-sheet projects.
*   File organization with folders and metadata within **Supabase Storage**, with metadata stored in Supabase Database.
*   Version control for floor plans with comparison tools.

#### #### 4.2.2 AI-Powered Symbol Detection

*   **Core Strategy:** Leverage state-of-the-art multimodal AI models accessed via **OpenRouter** and managed through dedicated **MCP (Model Context Protocol) servers**. The goal is high accuracy, adaptability, and cost-effectiveness.
*   **Model Selection (via OpenRouter & MCP Server):**
    *   **Primary High-Quality:** Claude 3.5 Sonnet for complex symbols and nuanced detection.
    *   **Versatile/Complex Plans:** GPT-4o for mixed elements or non-standard symbols.
    *   **Standard Plans/Fallback:** Gemini Pro Vision for common patterns.
    *   **Simple Tasks/Cost-Efficiency:** Claude 3 Haiku or GPT-4o mini for pre-processing, simple checks, or as a rapid fallback.
    *   The MCP server will abstract these model choices, allowing dynamic selection based on plan complexity, user settings, or cost considerations.
*   **Implementation Approach ("Vibe Coding" & Iterative Refinement):**
    *   **Rapid Prototyping with MCPs:** Quickly stand up MCP server endpoints to test different models and prompts with sample floor plans.
    *   **Iterative Prompt Engineering:** Start with foundational prompts. Use a "vibe coding" approach: rapidly iterate on prompt variations, test small changes frequently, and observe results visually. Employ few-shot learning by including examples of symbols directly in prompts.
    *   **AI-Assisted Development:** Utilize AI code assistants (e.g., integrated into Windsurf IDE or standalone) to generate boilerplate for API calls to MCP servers, JSON parsing, and initial prompt structures.
    *   **Visual Feedback Loop:** Develop internal tools or integrate with the UI to provide immediate visual feedback on how prompt/model changes affect symbol detection on test images. This accelerates the "vibe" and intuition-driven refinement process.
*   **Key Technical Steps & Best Practices:**
    *   **Image Preprocessing:** Standardize images (format conversion, resolution adjustment, noise reduction) potentially using simpler AI models or traditional CV techniques before sending to more expensive multimodal models. This can be an MCP tool.
    *   **Tiling Strategy:** For large floor plans, implement an overlapping tiling strategy (e.g., 640x640px tiles with 50% overlap) to fit within model context windows and ensure symbols at tile edges are captured.
    *   **Contextual Information:** Pass relevant metadata (e.g., project type, region-specific symbol sets if known) to the AI models via the MCP server to improve detection accuracy.
    *   **Symbol Recognition & Classification:** Models should return symbol type, coordinates, and a confidence score.
    *   **Result Parsing & Normalization:** Implement robust parsing for JSON or other structured output from the models. Normalize coordinates and symbol properties.
    *   **Non-Maximum Suppression (NMS):** Apply NMS (e.g., 0.5 IoU threshold) to merge duplicate detections from overlapping tiles, prioritizing higher confidence scores.
    *   **Confidence Scoring & Manual Review Flagging:** Utilize model-generated confidence scores. Flag detections below a configurable threshold (e.g., <0.7-0.8) for mandatory manual review.
    *   **Scale Detection and Calibration:** Implement tools for users to calibrate the scale from drawing references if not automatically detectable.
*   **Feedback Loop for Continuous Improvement:**
    *   User corrections from the manual review interface (Section 4.2.3) must be logged. This data is invaluable for:
        *   Identifying common failure modes of specific models/prompts.
        *   Refining prompts iteratively.
        *   Potentially fine-tuning custom models in the future (long-term).

#### #### 4.2.3 Manual Correction Interface
*   Interactive canvas (existing UI component) for symbol viewing and editing.
*   Tools for adding, moving, deleting, and modifying symbols (type, rating, specifications).
*   Bulk symbol operations for efficient editing.
*   Verification workflow for AI detections, especially those flagged with low confidence.

#### #### 4.2.4 Floor Plan Analysis
*   Room identification and labeling (AI-assisted, if feasible).
*   Circuit and panel identification from drawing information (AI-assisted).
*   Electrical load calculation based on symbols.
*   Code compliance checking against relevant standards (potential future AI feature).
*   Analysis reporting with summary and details.

### ### 4.3 Estimation Engine

#### #### 4.3.1 Material Database
*   Comprehensive electrical material catalog with specifications, stored in Supabase Database.
*   Material categorization by type, use, and specifications.
*   Material specifications and documentation links.
*   Alternative material options with substitution rules.
*   Custom material creation and management.

#### #### 4.3.2 Symbol-to-Material Mapping
*   Automatic mapping of symbols to required materials based on configurable rules.
*   Assembly creation and management for common groupings (e.g., an outlet assembly includes the outlet, box, cover plate, and estimated wire).
*   Material quantity calculation based on symbol properties and assembly definitions.
*   Dependency tracking between related materials.
*   Rule-based mapping configuration for different scenarios.

#### #### 4.3.3 Labor Calculation
*   Industry-standard labor units for installation tasks.
*   Labor rate management by region and classification.
*   Productivity factors and adjustments for conditions.
*   Crew composition configuration for different work types.
*   Labor hour calculation with breakdown by task.

#### #### 4.3.4 Pricing Management
*   Material cost database with regular updates (see Supplier Integration).
*   Supplier price integration via MCP connections (see Section 4.4).
*   Price update management with change tracking.
*   Regional price variation handling.
*   Historical price tracking for trend analysis.

#### #### 4.3.5 Estimation Calculation
*   Material cost calculation with quantity-based pricing.
*   Labor cost calculation with appropriate rates.
*   Equipment cost calculation when applicable.
*   Tax calculation based on location and rules.
*   Markup and overhead application with flexibility.
*   Total project cost compilation with summaries.

### ### 4.4 Supplier Integration

#### #### 4.4.1 Supplier Management
*   Supplier database with contact information stored in Supabase.
*   Secure credential storage for API and portal access (e.g., using Supabase Vault or encrypted fields).
*   Supplier preference configuration and ranking.
*   Comparative tools for supplier selection.
*   Supplier performance tracking and metrics.

#### #### 4.4.2 Price Update System (via MCP Servers)
*   **MCP Server Responsibility:** An MCP server dedicated to supplier integration will handle interactions with various supplier systems.
*   Manual price entry option for flexibility.
*   Automated price updates via MCP connections to supplier APIs or robust web scraping modules (if APIs are unavailable). Each supplier might have its own "tool" on the MCP server.
*   Price verification workflow with validation rules.
*   Price history tracking for reference and auditing.
*   Price change notifications for significant variations.

#### #### 4.4.3 Material Ordering
*   Material list generation from estimates.
*   Order preparation workflow with quantity verification.
*   Order submission to preferred suppliers (potentially via MCP server if suppliers offer ordering APIs).
*   Order status tracking and notifications.
*   Order history and reporting for analysis.

### ### 4.5 Project Management

#### #### 4.5.1 Project Dashboard
*   Project overview with key metrics and status.
*   Project status tracking with customizable stages.
*   Task and activity tracking with assignments.
*   Timeline visualization with milestone tracking.
*   Resource allocation tracking and planning.

#### #### 4.5.2 Workflow Management
*   Custom workflow creation for different project types.
*   Status transition rules and requirements.
*   Approval and review processes with role assignments.
*   Milestone tracking with notifications.
*   Dependency management between tasks and phases.

#### #### 4.5.3 Team Collaboration
*   Real-time collaboration tools for concurrent work (leveraging Supabase Realtime for relevant features).
*   Comment and discussion system attached to elements.
*   @mention functionality for team member notifications.
*   Task assignment and tracking with due dates.
*   Activity feed and notifications for project updates.

#### #### 4.5.4 Document Management
*   Document repository with categorization within Supabase Storage.
*   Document metadata and searching capabilities (leveraging Supabase Database for metadata).
*   Version control for all documents.
*   Document sharing with permission controls (using Supabase Storage policies).
*   Document preview and annotation tools.

### ### 4.6 Client-Facing Features

#### #### 4.6.1 Quote Generation
*   Quote template management with customization.
*   Brand customization with logo and colors.
*   Line item management with grouping options.
*   Terms and conditions management with templates.
*   Quote preview and formatting controls. Generation can be handled by Supabase Edge Functions.

#### #### 4.6.2 Client Portal
*   Secure client access without requiring full account creation (e.g., magic links via Supabase Auth, or limited-access roles).
*   Project dashboard tailored for client view.
*   Quote review interface with clear explanations.
*   Document access with permission controls.
*   Communication tools integrated with the portal.

#### #### 4.6.3 Approval Process
*   Digital signature integration with legal compliance (via third-party service integration).
*   Approval workflow with sequential steps if needed.
*   Change request management and tracking.
*   Approval history and audit trail.
*   Email notifications for approval activities (via Supabase Edge Functions and email provider).

#### #### 4.6.4 Client Communication
*   In-app messaging system with threading (custom build using Supabase Realtime and Database).
*   Email notification integration for external alerts.
*   Communication history with searchable archive.
*   File sharing capabilities within messages.
*   Read receipts and activity tracking.

### ### 4.7 Analytics and Reporting

#### #### 4.7.1 Estimation Analytics
*   Estimation accuracy tracking against actuals.
*   Win/loss analysis for quoted projects.
*   Pricing trend analysis over time.
*   Material usage analysis across projects.
*   Labor efficiency analysis with benchmarks.
    *(Data for analytics will reside in Supabase Database, queries and aggregations can be performed directly or via Edge Functions)*

#### #### 4.7.2 Business Analytics
*   Project profitability analysis with drill-down.
*   Team performance metrics and comparison.
*   Client acquisition and retention metrics.
*   Sales pipeline analysis with forecasting.
*   Growth and trend reporting with visualization.

#### #### 4.7.3 Report Generation
*   Standard report templates for common needs.
*   Custom report builder with flexible options.
*   Scheduled report generation and delivery (via Supabase Edge Functions and cron jobs).
*   Export options in multiple formats (PDF, Excel, CSV).
*   Interactive dashboards with filtering (built into the frontend, data from Supabase).

## ## 5. Non-Functional Requirements

### ### 5.1 Performance

#### #### 5.1.1 Response Time
*   Page load time < 2 seconds for standard operations.
*   Floor plan AI processing time < 30-60 seconds for standard plans (dependent on AI model choice and complexity, managed via asynchronous processing).
*   Database query results display < 1 second after query.
*   UI interactions < 100ms response for feedback.
*   Real-time updates < 500ms for collaborative features.

#### #### 5.1.2 Scalability
*   Support for organizations with 100+ simultaneous users.
*   Handling of 1000+ concurrent users across the platform (Supabase is designed for scalability).
*   Processing of 10,000+ floor plans per day (asynchronous processing essential).
*   Management of 100,000+ projects in the Supabase database.
*   Storage for millions of detected symbols and terabytes of floor plans in Supabase Storage.

#### #### 5.1.3 Availability
*   99.9% uptime guarantee for Supabase services (refer to Supabase SLA).
*   Planned maintenance windows outside business hours.
*   Redundancy for critical components is handled by Supabase infrastructure.
*   Geographic distribution for global access and resilience (Supabase offers multi-region capabilities).
*   Graceful degradation when external AI services or other integrations face issues.

### ### 5.2 Security

#### #### 5.2.1 Data Protection
*   End-to-end encryption for sensitive data transmission (HTTPS).
*   Encryption at rest for Supabase Storage and Database.
*   Secure credential storage for external services (e.g., Supabase Vault if available, or encrypted environment variables for Edge Functions).
*   Regular security audits and penetration testing.
*   Privacy compliance with regulations (GDPR, CCPA, etc.) through careful data handling and Supabase features.

#### #### 5.2.2 Authentication and Authorization
*   Multi-factor authentication support via Supabase Auth.
*   Role-based access control using Supabase user roles/metadata and PostgreSQL Row Level Security (RLS).
*   Fine-grained permissions system for resource access via RLS.
*   Session management with appropriate timeout and renewal (Supabase JWTs).
*   Secure password policies enforced by Supabase Auth.

#### #### 5.2.3 Application Security
*   Protection against OWASP Top 10 vulnerabilities.
*   API security with Supabase's built-in mechanisms (PostgREST authorization, Edge Function security).
*   Audit logging for security-relevant actions (custom implementation).
*   Intrusion detection and prevention measures (largely handled by Supabase infrastructure).
*   Regular vulnerability scanning and remediation.

### ### 5.3 Reliability

#### #### 5.3.1 Error Handling
*   Graceful error recovery without data loss.
*   Detailed error logging (e.g., to Supabase logs or a third-party logging service) with context for troubleshooting.
*   User-friendly error messages with actionable guidance.
*   Automatic retry mechanisms for transient failures in Edge Functions or MCP server calls.
*   Failure isolation to prevent cascading issues.

#### #### 5.3.2 Backup and Recovery
*   Automated daily backups with point-in-time recovery (PITR) capabilities provided by Supabase for the database.
*   Disaster recovery plan leveraging Supabase's infrastructure resilience.
*   Data integrity checks.
*   Backup verification process.

#### #### 5.3.3 Monitoring
*   Real-time performance monitoring with alerting (using Supabase monitoring tools and/or third-party integrations).
*   Error and exception tracking.
*   User experience monitoring.
*   Infrastructure health monitoring (provided by Supabase).
*   Alerts and notifications system for critical issues.

### ### 5.4 Usability

#### #### 5.4.1 User Interface
*   The existing UI (React, TypeScript, shadcn.ui, Tailwind CSS) serves as the foundation. It should embody a clean, intuitive design following modern web standards.
*   Mobile-responsive layouts for different device sizes.
*   Consistent navigation patterns throughout the application.
*   Accessibility compliance with WCAG 2.1 AA standards.
*   Cross-browser compatibility with major browsers.

#### #### 5.4.2 User Experience
*   Streamlined workflows with minimal steps.
*   Contextual help and tooltips for complex features.
*   Progressive disclosure of advanced functionality.
*   Consistent feedback for user actions.
*   Keyboard shortcuts for power users.

#### #### 5.4.3 Internationalization
*   Support for multiple languages (initially English).
*   Regional formatting for dates, currencies, and units.
*   Time zone handling for global organizations.
*   Cultural adaptations when needed.
*   Right-to-left language support for future expansion.

### ### 5.5 Compatibility

#### #### 5.5.1 Browsers
*   Support for Chrome, Firefox, Safari, Edge (latest 2 versions).
*   Graceful degradation for older browsers when possible.
*   Performance optimization for different browsers.
*   Consistent rendering across supported platforms.
*   Feature detection and appropriate fallbacks.

#### #### 5.5.2 Devices
*   Desktop optimization (primary focus).
*   Tablet support for field use and presentations.
*   Limited mobile functionality for on-the-go access (responsive web design).
*   Touch interface support for interactive elements.
*   Screen size adaptation with responsive design.

#### #### 5.5.3 Integration
*   REST API provided by Supabase (PostgREST) for direct data access where appropriate, and custom REST/GraphQL APIs via Edge Functions.
*   Webhook support for event notifications (implemented via Supabase Edge Functions).
*   Standard authentication protocols (OAuth 2.0 via Supabase Auth).
*   Import/export capabilities for data migration.
*   Third-party integration points with clear documentation.

## ## 6. System Architecture

### ### 6.1 Frontend Architecture

#### #### 6.1.1 Technology Stack
*   **UI Framework/Library:** React (existing codebase).
*   **Language:** TypeScript (existing codebase).
*   **Build Tooling:** Vite (or as per existing codebase).
*   **Component Library:** shadcn.ui (existing codebase).
*   **Styling:** Tailwind CSS (existing codebase).
*   **Routing:** React Router (or as per existing codebase).
*   **Server State Management:** React Query (or TanStack Query).
*   **Global State Management:** Zustand (or as per existing codebase).
*   The frontend application is a pre-existing asset that will be adapted to connect with the Supabase backend.

#### #### 6.1.2 Component Architecture
*   Atomic design principles for component organization (as per existing codebase).
*   Shared component library for consistency.
*   Feature-based organization for maintainability.
*   Lazy loading for optimal performance.
*   Responsive design system with breakpoints.

#### #### 6.1.3 State Management
*   Global application state for cross-component data (Zustand or existing solution).
*   Component-level state for isolated concerns.
*   Server state management with caching and synchronization (React Query).
*   Persistent state handling for user preferences.
*   Form state management with validation (e.g., React Hook Form with Zod).

### ### 6.2 Backend Architecture

#### #### 6.2.1 Supabase Services
*   **Authentication:** **Supabase Auth** for user management, JWTs, OAuth providers, RLS integration.
*   **Database:** **Supabase Database (PostgreSQL)** for all persistent data. Row Level Security (RLS) will be heavily utilized for multi-tenancy and fine-grained access control. Realtime capabilities for collaborative features.
*   **Storage:** **Supabase Storage** for file storage (floor plans, generated quotes, project documents) with permission controls integrated with RLS.
*   **Serverless Functions:** **Supabase Edge Functions** (Deno runtime) for custom business logic, API endpoints not covered by PostgREST, integrations with third-party services (e.g., email, digital signatures), and backend processing tasks (e.g., PDF generation, scheduled jobs).
*   **Hosting:** Frontend will be hosted on a static hosting provider (e.g., Vercel, Netlify, or Supabase's own static hosting if suitable). Supabase handles backend hosting.

#### #### 6.2.2 API Design
*   **Primary Data Access:** Leverage **PostgREST** (provided by Supabase) for automatic RESTful API generation based on the PostgreSQL schema. Security enforced by RLS.
*   **Custom Business Logic:** **Supabase Edge Functions** will expose custom RESTful or GraphQL endpoints for operations requiring complex logic, integrations, or specific processing not suitable for direct database exposure.
*   API versioning strategy for future compatibility (e.g., `/v1/...` in Edge Function routes).
*   Rate limiting and throttling for security (configurable at CDN/proxy level or within Edge Functions).
*   API documentation using OpenAPI/Swagger (can be auto-generated for PostgREST and manually created for Edge Functions).

#### #### 6.2.3 Data Model (Supabase PostgreSQL)
*   Multi-tenant data isolation enforced by **Row Level Security (RLS)** policies based on `organization_id` in most tables.
*   Normalized collections for shared reference data (e.g., standard symbol libraries).
*   Denormalized data for performance where appropriate (e.g., aggregated counts, cached summaries), managed with PostgreSQL views or triggers.
*   Hierarchical data for organizational structure.
*   Strategic use of foreign key relationships, JSONB for flexible data, and appropriate indexing for query performance.

### ### 6.3 AI Integration

#### #### 6.3.1 Multimodal AI Architecture (via MCP Servers)
*   **Orchestration:** **OpenRouter** will be used as an aggregator to access various multimodal AI models.
*   **Interface Layer:** Dedicated **MCP (Model Context Protocol) servers** will act as an abstraction layer between CoElec's backend (Supabase Edge Functions) and OpenRouter. This allows for:
    *   Standardized request/response formats for AI tasks.
    *   Centralized prompt management and versioning.
    *   Dynamic model selection logic (based on task, cost, performance).
    *   Caching of AI responses where appropriate.
    *   Simplified maintenance and model swapping.
*   **Prompt Engineering:** A systematic approach to designing, testing, and refining prompts. Prompts will be version-controlled and managed, possibly within the MCP server configuration or a dedicated service.
*   **Result Parsing and Validation:** Robust mechanisms within the MCP server or Supabase Edge Functions to parse responses from AI models and validate their structure and content.
*   **Feedback Loop:** User corrections and feedback will be captured and used to iteratively refine prompts and model selection strategies.
*   **"Vibe Coding" in AI Development:** The development of AI interactions (especially prompt engineering and initial model testing) will embrace rapid iteration. Developers are encouraged to quickly test hypotheses, visually inspect results, and intuitively refine approaches, supported by the MCP framework for easy experimentation.

#### #### 6.3.2 MCP Server Architecture
*   **Core Components:** Implement distinct MCP servers or clearly defined "toolsets" within a larger MCP framework for different domains:
    *   `SymbolDetectionMCP`: Handles all interactions for floor plan analysis and symbol detection.
    *   `SupplierIntegrationMCP`: Manages connections to supplier catalogs for pricing and availability.
    *   `EstimationMCP` (Optional): Could house complex estimation logic or AI-assisted estimation refinement tools.
    *   `DocumentMCP` (Optional): For AI-assisted document analysis or generation beyond simple templating.
*   **Tool and Resource Definitions:** Clearly define "tools" (actions) and "resources" (data access points) within each MCP server, following established MCP best practices.
*   **Authentication & Security:** Secure access to MCP servers (e.g., API keys, JWTs if called from Edge Functions).
*   **Error Handling & Resilience:** Implement robust error handling, retry logic, and circuit breakers for calls to external AI models or supplier systems.

#### #### 6.3.3 A2A Protocol Implementation
*   For complex, multi-step AI workflows or distributed tasks, an Agent-to-Agent (A2A) communication protocol can be implemented.
*   Agents (specialized Supabase Edge Functions or MCP tools) would communicate via standardized messages, possibly orchestrated through a Supabase table acting as a task queue or a dedicated messaging service if needed.
*   Context sharing and task delegation between agents for seamless workflows.

### ### 6.4 Integration Architecture

#### #### 6.4.1 Supplier Integration (via MCP Server)
*   The `SupplierIntegrationMCP` will be the primary interface.
*   **Adapters:** Develop supplier-specific adapters (tools within the MCP) that can:
    *   Connect to supplier APIs if available.
    *   Implement robust web scraping modules for suppliers without APIs.
*   **Authentication:** Securely manage supplier credentials.
*   **Data Normalization:** Standardize pricing, product, and availability data obtained from various suppliers into a consistent format.

#### #### 6.4.2 External Service Integration
*   **Email Service:** Integrate with services like SendGrid, Postmark (via Supabase Edge Functions) for notifications.
*   **Digital Signature:** Integrate with services like DocuSign, HelloSign (via Supabase Edge Functions).
*   **Payment Processing:** For subscriptions, integrate with Stripe, Paddle (via Supabase Edge Functions).
*   **Accounting/CRM Systems:** Future integrations via custom Edge Functions and third-party APIs.

#### #### 6.4.3 Extensibility Framework
*   **API-First Design:** Prioritize well-defined APIs (PostgREST and custom Edge Functions) to allow for future integrations and extensions.
*   **Webhook System:** Implement outgoing webhooks from Supabase Edge Functions (triggered by database changes or events) to notify external systems.
*   **Feature Flags:** Utilize a feature flag system for controlled rollout of new features.

## ## 7. User Interface Design

### ### 7.1 Design System
*   The existing UI (React, TypeScript, shadcn.ui, Tailwind CSS) establishes the visual language.
*   **Visual Language:** Clean, professional aesthetic appropriate for the electrical contracting industry. Clear visual hierarchy.
*   **Component Library:** Utilize and extend the shadcn.ui library for consistent form elements, navigation, data display, and feedback components.
*   **Layout System:** Responsive grid system, page templates, consistent spacing, and breakpoints.

### ### 7.2 Key Screens and Workflows
*   (As per original PRD - Dashboard, Floor Plan Processing, Estimation Interface, Quote Generation, Project Management)
*   The design should prioritize clarity and efficiency for these core tasks, building upon the existing UI foundation.

### ### 7.3 User Interaction Patterns
*   (As per original PRD - Navigation, Data Entry, Feedback and Messaging)
*   Ensure these patterns are consistently applied in the existing UI and any new features.

## ## 8. Feature Prioritization

(Unchanged from original PRD - Core, High, Medium, Low priority features remain the same)

### ### 8.1 Core Features (Must Have)
*   User authentication (Supabase Auth) and role management.
*   Floor plan upload (Supabase Storage) and basic processing.
*   AI-powered symbol detection (MCP/OpenRouter) with manual correction.
*   Basic material and labor estimation.
*   Simple quote generation.
*   Project management basics.

### ### 8.2 High Priority Features (Should Have)
*   Supplier price integration via `SupplierIntegrationMCP`.
*   Team collaboration tools (Supabase Realtime).
*   Document management system (Supabase Storage & Database).
*   Client portal with approval workflow.
*   Estimation analytics and reporting.
*   Advanced symbol detection with context awareness.

### ### 8.3 Medium Priority Features (Could Have)
*   Digital signature integration.
*   Material ordering workflow.
*   Advanced estimation scenarios and what-if analysis.
*   Custom report builder.
*   Project timeline visualization.
*   Mobile application for field use (responsive web initially).

### ### 8.4 Low Priority Features (Won't Have in MVP)
*   Accounting system integration.
*   Advanced project planning tools.
*   Field service management.
*   Inventory tracking system.
*   Customer relationship management.
*   Machine learning for estimation optimization (beyond current AI detection).

## ## 9. Implementation Considerations

### ### 9.1 Development Approach

#### #### 9.1.1 Agile Methodology
*   Two-week sprint cycles for incremental delivery.
*   Daily stand-up meetings for coordination.
*   Sprint planning and retrospectives for continuous improvement.
*   Continuous integration and delivery (CI/CD) pipeline.
*   Iterative feature development with feedback incorporation, especially for AI components where "vibe coding" and rapid experimentation are key.
*   **IDE:** Development will primarily use **Windsurf IDE**, leveraging its features for efficient coding and potentially AI-assisted development.

#### #### 9.1.2 Team Structure
*   (Unchanged from original PRD - Product owner, Technical lead, Frontend developers, Backend developers (Supabase focused), ML/AI specialists, UX/UI designers, QA engineers)

#### #### 9.1.3 Development Phases
*   (Unchanged from original PRD - Discovery, MVP, Alpha/Beta, Production, Continuous Improvement)

### ### 9.2 Testing Strategy

#### #### 9.2.1 Testing Levels
*   Unit testing for components (React Testing Library), Supabase Edge Functions, and MCP server tools.
*   Integration testing for feature combinations, API interactions (frontend to Supabase, Supabase to MCPs).
*   End-to-end testing for complete workflows (e.g., using Cypress).
*   **AI Model Testing:**
    *   Accuracy testing on diverse datasets of floor plans.
    *   Robustness testing against varied image quality and symbol types.
    *   Prompt effectiveness testing and A/B testing of different prompts/models.
    *   Performance (latency, cost) testing of AI calls.
*   Performance testing for scalability verification.
*   Security testing for vulnerability identification (including RLS policies).

#### #### 9.2.2 Testing Tools
*   Jest for unit testing.
*   React Testing Library for frontend components.
*   Cypress for end-to-end testing.
*   Lighthouse for performance and accessibility.
*   **Supabase Local Development:** Utilize Supabase CLI for local development and testing of database schema, RLS, and Edge Functions.
*   Manual testing protocols for user experience verification.
*   Custom scripts or frameworks for testing MCP server responses and AI model outputs.

#### #### 9.2.3 Quality Assurance
*   (Unchanged from original PRD - Code review, Automated testing in CI/CD, Bug tracking, UAT, Beta testing)

### ### 9.3 Deployment Strategy

#### #### 9.3.1 Environment Setup
*   **Development:** Local Supabase instances via CLI, or shared dev Supabase project.
*   **Staging:** Dedicated Supabase project for pre-release testing, mirroring production setup.
*   **Production:** Dedicated Supabase project for the live application.
*   Frontend deployments via Vercel/Netlify or similar, connected to respective Supabase environments.

#### #### 9.3.2 Release Process
*   Feature branching workflow with pull requests.
*   Continuous integration with automated testing.
*   Staged rollout using feature flags where appropriate.
*   Monitoring and alerting during deployment.
*   Rollback capabilities for Supabase schema (migrations) and Edge Functions.

#### #### 9.3.3 DevOps Practices
*   Infrastructure as Code for Supabase schema (migrations).
*   Automated deployments for frontend and Supabase Edge Functions.
*   Monitoring and alerting for system health (Supabase dashboards, custom alerts).
*   Logging and diagnostics.
*   Performance optimization and tuning.

### ### 9.4 Maintenance and Support
*   (Unchanged from original PRD - Support Levels, Maintenance Schedule, Monitoring and Analytics)

## ## 10. Success Metrics
(Unchanged from original PRD - Business, Product, Technical, Long-term Indicators)

### ### 10.1 Business Metrics
*   Customer acquisition and retention rates.
*   Revenue growth month-over-month.
*   Customer lifetime value calculation.
*   Expansion within existing organizations.
*   Market share in electrical estimation space.

### ### 10.2 Product Metrics
*   User engagement and active usage statistics.
*   Feature adoption rates across customer base.
*   Time savings compared to manual methods (target: 80%).
*   Estimation accuracy improvement (target: 15% better).
*   AI symbol detection accuracy (target: >90% on defined symbol sets).
*   User satisfaction scores (target: 4.5/5).

### ### 10.3 Technical Metrics
*   System uptime and reliability percentages (Supabase SLA + application layer).
*   Response time and performance benchmarks.
*   Error rates and resolution time.
*   API usage and performance statistics (Supabase and MCP servers).
*   Infrastructure cost efficiency (Supabase and AI model usage).

### ### 10.4 Long-term Success Indicators
*   Industry adoption rate among electrical contractors.
*   Customer testimonials and case studies.
*   Expansion to adjacent industries (plumbing, HVAC).
*   Partnership opportunities with suppliers and software vendors.
*   Platform ecosystem growth with integrations.

## ## 11. Future Roadmap
(Unchanged from original PRD - Phase 1-4 plans remain the same)

## ### 11.1 Phase 1: Core Platform (Months 1-6)
*   Launch MVP with essential features.
*   Establish core user base with early adopters.
*   Gather initial feedback and usage data.
*   Refine AI symbol detection accuracy.
*   Stabilize foundation for growth.

## ### 11.2 Phase 2: Enhanced Capabilities (Months 7-12)
*   Advanced estimation features with what-if analysis.
*   Expanded supplier integrations with major vendors (via `SupplierIntegrationMCP`).
*   Improved client collaboration tools.
*   Mobile application for field use (responsive web first).
*   API platform for custom integrations.

## ### 11.3 Phase 3: Ecosystem Growth (Months 13-18)
*   Expansion to adjacent trades (plumbing, HVAC).
*   Advanced analytics and business intelligence.
*   Field service integration for project execution.
*   Marketplace for supplies and services.
*   Partner integration program.

## ### 11.4 Phase 4: Enterprise and Innovation (Months 19-24)
*   Enterprise-grade features and controls.
*   Advanced AI capabilities for predictive estimation.
*   International expansion with localization.
*   Custom vertical solutions for specific industries.
*   Construction ecosystem integration.

## ## 12. Appendices
(Unchanged from original PRD - Technical Specifications, User Research, Legal and Compliance, Glossary)

### ### 12.1 Technical Specifications
*   Detailed API documentation (PostgREST, custom Edge Functions, MCP servers).
*   Supabase database schema diagrams.
*   Authentication flow diagrams (Supabase Auth).
*   Infrastructure architecture diagrams (Supabase, MCPs, Frontend hosting).
*   Integration specifications.

### ### 12.2 User Research
*   User interview summaries.
*   Market research findings.
*   Competitor analysis.
*   User testing results.
*   Feature prioritization data.

### ### 12.3 Legal and Compliance
*   Terms of service template.
*   Privacy policy requirements.
*   Data handling procedures.
*   Industry compliance requirements.
*   Security protocols and standards.

### ### 12.4 Glossary
*   Electrical terminology for development reference.
*   Construction industry terms and definitions.
*   Technical acronyms and abbreviations (Supabase, MCP, AI models).
*   Product-specific terminology.
*   AI and estimation concepts explanation.

---

This revised PRD reflects the shift to Supabase for the backend, acknowledges the pre-existing UI, mentions Windsurf as the IDE, and provides more detailed guidance on implementing AI-powered symbol recognition using best practices, "vibe coding" principles for iterative development, and robust MCP server utilization. All other sections have been carried over with necessary adjustments for consistency.