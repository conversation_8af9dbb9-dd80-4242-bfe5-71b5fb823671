
## # CoElec Project Development Rules

## ## 1. Architectural Principles

### ### 1.1 Component Structure (Frontend)

*   **Atomic Design Methodology**: Build the UI (which is a pre-existing asset using React, TypeScript, shadcn.ui, Tailwind CSS) following a hierarchical structure of atoms, molecules, organisms, templates, and pages. Each component should have a single responsibility and be reusable.
*   **Feature-Based Organization**: Structure frontend code by business domain features. Group related components, hooks, services, and utilities within feature directories.
*   **Single Responsibility**: Ensure each component, service, or function has exactly one responsibility.
*   **DRY (Don't Repeat Yourself)**: Identify and extract common functionality into shared utilities and components.
*   **SOLID Principles**: Apply SOLID principles throughout the codebase.

### ### 1.2 State Management (Frontend)

*   **Centralized State**: Implement Zustand for global application state (or continue with the existing solution if different and effective). Create separate stores for distinct domains.
*   **Local State**: Keep component-specific state local using <PERSON><PERSON>'s `useState` and `useReducer`.
*   **Server State**: Use React Query (or TanStack Query) for all server state management (data fetching from **Supabase**, caching, background updates, error handling). Configure appropriate stale times and retry strategies. Implement optimistic updates for critical workflows.
*   **Immutable Data**: Never directly mutate state objects.
*   **State Slices**: Organize state into logical domain-driven slices.

### ### 1.3 Performance Standards (Frontend & Backend Interaction)

*   **Lazy Loading**: Implement code splitting for all routes and large frontend components.
*   **Virtualization**: Apply virtualized lists for any data display showing more than 50 items.
*   **Memoization**: Strategically use `React.memo()` and `useMemo()`.
*   **Asset Optimization**: Compress and optimize all images.
*   **Bundle Size**: Keep the initial frontend bundle size under 500KB (compressed).
*   **API Response Times**: **Supabase** query and Edge Function response times should be optimized for sub-second delivery for most operations. AI processing via MCPs will be asynchronous with clear user feedback.

### ### 1.4 Project Structure (Frontend Example)

```
src/
├── components/         # Shared UI components
│   ├── ui/              # Base UI components (shadcn)
│   └── [feature]/       # Feature-specific components
├── features/          # Feature modules
│   ├── auth/           # Authentication feature
│   ├── floorPlans/     # Floor plan processing
│   ├── estimation/     # Estimation feature
│   └── [feature]/      # Other features
├── hooks/             # Custom React hooks
├── services/          # API service layers (interacting with Supabase/MCPs)
├── utils/             # Utility functions
├── stores/            # Zustand state stores
├── types/             # TypeScript type definitions
└── pages/             # Page components
```
*(Backend structure will involve Supabase project configuration, database schema migrations, and Edge Functions organized by domain/feature.)*

## ## 2. Code Standards

### ### 2.1 Naming Conventions
(Unchanged from original - PascalCase for components/types, camelCase for functions/variables, UPPER_SNAKE_CASE for constants)

### ### 2.2 TypeScript Usage
(Unchanged from original - Strict mode, explicit types, interface vs type, generics, avoid `any`, type guards, `readonly`)

### ### 2.3 Documentation
(Unchanged from original - JSDoc, component props, usage examples, edge cases, inline comments, TODO/FIXME)

### ### 2.4 Code Formatting
(Unchanged from original - Prettier, ESLint, max line length, indentation, semicolons, quotes, trailing commas)
*   **IDE**: Development will primarily use **Windsurf IDE**, leveraging its formatting and linting integrations.

## ## 3. AI Implementation Guidelines (Symbol Recognition Focus)

### ### 3.1 Multimodal AI Integration Strategy

*   **Core Principle**: Symbol recognition is paramount. We will employ a flexible, iterative, and robust approach using state-of-the-art multimodal AI models accessed via **OpenRouter**, with **MCP (Model Context Protocol) servers** acting as the crucial abstraction and orchestration layer.
*   **OpenRouter Usage**: All AI model interactions for symbol detection will be routed through OpenRouter, managed by a dedicated `SymbolDetectionMCP` server. This provides unified access, monitoring, and easier model swapping.
*   **Model Selection Logic (within `SymbolDetectionMCP`)**: Implement a priority-based and adaptive selection strategy:
    1.  **Claude 3.5 Sonnet**: Primary for highest quality, precise symbol identification, and complex layouts.
    2.  **GPT-4o**: For highly complex floor plans with mixed elements, non-standard symbols, or when Sonnet yields suboptimal results.
    3.  **Gemini Pro Vision**: For standard floor plans with common symbol patterns, good balance of cost/performance, or as a primary fallback.
    4.  **Claude 3 Haiku / GPT-4o mini**: For simpler tasks like image pre-classification (e.g., identifying if a plan is suitable for detailed analysis), basic validation, or as a rapid, cost-effective fallback.
    *   The `SymbolDetectionMCP` will dynamically select models based on factors like image complexity (potentially pre-assessed), user subscription tier, or specific task requirements.
*   **Context7 MCP Reference**: Design of all MCP servers (especially `SymbolDetectionMCP` and `SupplierIntegrationMCP`) must heavily reference and adhere to patterns from Context7 MCP implementations for consistency, interoperability, and best practices.
*   **Prompt Engineering ("Vibe Coding" & Best Practices):**
    *   **Prompt Templates & Versioning**: Store all prompts as versioned templates within the `SymbolDetectionMCP` or a dedicated, easily updatable configuration store. Implement semantic versioning for prompts.
    *   **Iterative Refinement ("Vibe Coding")**:
        *   **Start Simple, Iterate Rapidly**: Begin with foundational prompts. Encourage developers to make small, frequent changes to prompts and immediately observe their impact.
        *   **Visual Feedback Loop**: Crucial for "vibe coding." The **Windsurf IDE** environment should facilitate, or internal tools must provide, a rapid way to upload test images, run them through the `SymbolDetectionMCP` with specific prompt versions/models, and visually inspect the detected symbols overlaid on the image. This tight feedback loop builds intuition and accelerates refinement.
        *   **Hypothesis-Driven Experimentation**: Formulate hypotheses about why a prompt isn't working (e.g., "the model is confused by handwritten notes") and test targeted prompt modifications.
        *   **Few-Shot Learning**: Incorporate clear examples of correctly identified symbols (and perhaps common misidentifications to avoid) directly into prompts where beneficial.
    *   **AI-Assisted Development**: Leverage AI code assistants (e.g., within **Windsurf IDE**) for generating boilerplate for API calls to MCPs, JSON parsing for model outputs, and drafting initial prompt structures.
*   **Error Handling & Fallbacks**: The `SymbolDetectionMCP` must implement comprehensive error handling for AI operations (rate limits, token limits, content policy violations, service disruptions from OpenRouter). Implement intelligent fallback chains (e.g., if Sonnet fails, try GPT-4o, then Gemini).
*   **Token Management & Cost Optimization**:
    *   Track token usage per model, per request, and per user/project via the `SymbolDetectionMCP`.
    *   Implement prompt optimization techniques (e.g., concise language, context summarization).
    *   Utilize caching for AI responses within the `SymbolDetectionMCP` where appropriate (e.g., for identical images and prompt versions).

### ### 3.2 Symbol Detection Implementation Pipeline

1.  **Image Upload & Preprocessing (Client -> Supabase Edge Function -> `SymbolDetectionMCP` tool):**
    *   User uploads floor plan (PDF, JPG, PNG, DWG, DXF).
    *   A **Supabase Edge Function** handles initial validation and may trigger a `SymbolDetectionMCP` tool for preprocessing:
        *   Format conversion to a consistent image type (e.g., PNG).
        *   Resolution adjustment and optimization.
        *   Noise reduction, deskewing, and basic image enhancement.
2.  **Tiling Strategy (within `SymbolDetectionMCP` or orchestrated by Edge Function):**
    *   For large floor plans, implement an overlapping tiling strategy (e.g., 640x640 or 1024x1024 pixel tiles with ~30-50% overlap) to fit model context windows and accurately capture symbols spanning tile boundaries.
3.  **AI Symbol Detection (via `SymbolDetectionMCP` tools):**
    *   Each tile is sent to the selected AI model via OpenRouter, orchestrated by the `SymbolDetectionMCP`.
    *   The MCP passes the image tile and the current best-versioned prompt (with any necessary contextual information like project type or regional symbol variations if known).
    *   The AI model returns identified symbols with coordinates, type, and a confidence score.
4.  **Result Aggregation & Post-Processing (within `SymbolDetectionMCP` or Supabase Edge Function):**
    *   **Non-Maximum Suppression (NMS)**: Apply NMS with an appropriate IoU (Intersection over Union) threshold (e.g., 0.5) to merge duplicate detections from overlapping tiles, prioritizing detections with higher confidence scores.
    *   **Coordinate Normalization**: Convert tile-local coordinates to global plan coordinates.
    *   **Symbol Data Standardization**: Ensure consistent formatting for symbol types and properties.
5.  **Confidence Scoring & Manual Review Flagging:**
    *   Store symbols with their confidence scores in **Supabase DB**.
    *   Flag detections below a configurable threshold (e.g., <0.75) for mandatory manual review in the UI. Provide visual indicators for confidence levels.
6.  **Contextual Analysis (Advanced Feature):**
    *   Future iterations may incorporate surrounding elements or known architectural features (if detectable) in the analysis to improve classification accuracy (e.g., a switch is likely near a door). This could be a separate AI call or a more complex prompt.
7.  **Manual Override & Feedback Loop:**
    *   The UI (existing asset) must allow users to add, delete, or modify AI-detected symbols.
    *   All user corrections (discrepancies between AI output and final user-approved symbols) **must** be logged systematically in **Supabase DB**. This data is critical for:
        *   Identifying common AI failure modes (e.g., specific symbols, image types).
        *   Iteratively refining prompts and model selection strategies within the `SymbolDetectionMCP`.
        *   Building datasets for potential future fine-tuning or RAG (Retrieval Augmented Generation) systems.

### ### 3.3 MCP Integration

*   **Context7 Reference**: Thoroughly study and adhere to Context7 MCP server implementation patterns for design, error handling, security, tool/resource definitions, and naming conventions.
*   **Supabase Edge Functions as MCP Clients**: Frontend interactions with MCPs will typically be proxied through **Supabase Edge Functions**. These Edge Functions will securely authenticate and call the relevant MCP server tools.
*   **Error Handling**: Implement comprehensive, tiered error handling: within the MCP tool (e.g., AI model errors), in the Supabase Edge Function calling the MCP, and in the frontend.
*   **Authentication**: MCP servers must be secured (e.g., API keys, mutual TLS if deployed in a private network). Supabase Edge Functions will use secure methods to authenticate with MCPs.
*   **Rate Limiting & Caching**: MCP servers should implement rate limiting for external services (like OpenRouter) and robust caching strategies for frequently requested data or AI model responses to improve performance and reduce costs.
*   **Logging**: Log all significant MCP operations (request, parameters (anonymized if sensitive), response status, execution time, costs) for debugging, monitoring, and auditing.

### ### 3.4 A2A Protocol Usage

*   **Agent Specialization**: For complex, multi-step workflows (e.g., a full floor plan processing pipeline involving preprocessing, multiple AI calls, and post-processing), define specialized agents. These can be implemented as:
    *   Distinct **Supabase Edge Functions**.
    *   Granular tools within an MCP server.
*   **Orchestration**: Use **Supabase Database** tables as task queues or state machines to orchestrate agent interactions. **Supabase Realtime** can provide progress updates to the frontend.
*   **Message Format**: Use a consistent JSON message format for inter-agent communication.
*   **Task Delegation & Context Sharing**: Clearly define interfaces and ensure necessary context is passed between agents.
*   **Error Recovery & Timeout Handling**: Implement robust error recovery, retry logic (with exponential backoff), and appropriate timeouts for agent operations.

## ## 4. Supabase Implementation Rules

### ### 4.1 Supabase Database (PostgreSQL) Design

*   **Table Naming**: Use plural nouns, `snake_case` for tables and columns (e.g., `users`, `projects`, `floor_plans`, `project_id`).
*   **Primary Keys**: Use UUIDs (`uuid` type) as primary keys for most tables, generated by `gen_random_uuid()`.
*   **Relationships**: Use foreign keys to enforce relational integrity. Define `ON DELETE` cascade/restrict/set null behavior appropriately.
*   **Data Types**: Utilize appropriate PostgreSQL data types (e.g., `TEXT`, `VARCHAR`, `TIMESTAMPZ`, `JSONB` for flexible data, `NUMERIC` for financial data, PostGIS types for spatial data if applicable).
*   **Indexing**: Create indexes on columns frequently used in `WHERE` clauses, `JOIN` conditions, and `ORDER BY` clauses. Use `EXPLAIN ANALYZE` to identify slow queries and optimize.
*   **RLS for Multi-Tenancy**: Design schema with `organization_id` (or similar tenant identifier) in almost all tables. This column will be central to Row Level Security policies.
*   **Normalization/Denormalization**: Start with a normalized design. Strategically denormalize for performance where necessary (e.g., caching counts, pre-calculating summaries), potentially using PostgreSQL views or materialized views.
*   **Schema Migrations**: Manage all schema changes using Supabase's migration system. Store migrations in version control.

### ### 4.2 Supabase Security (RLS & Policies)

*   **Principle of Least Privilege**: Grant only the minimum access necessary. Deny by default.
*   **Row Level Security (RLS)**: **RLS is mandatory for multi-tenant data isolation.**
    *   Enable RLS on all tables containing tenant-specific or user-specific data.
    *   Create RLS policies that filter data based on `auth.uid()`, `auth.jwt() ->> 'user_role'`, and the organization/tenant ID (often stored in a custom claim within the JWT).
    *   Define policies for `SELECT`, `INSERT`, `UPDATE`, `DELETE` operations.
*   **Function Security**: Define PostgreSQL functions with `SECURITY DEFINER` judiciously and only when necessary, ensuring they are secure against misuse. Prefer `SECURITY INVOKER` where possible.
*   **Storage Policies**: Implement security policies for **Supabase Storage** buckets and objects, aligning them with user roles and organization membership.
*   **Policy Testing**: Write comprehensive tests for RLS policies and Storage policies to verify correct behavior for different user roles and scenarios (both allowed and denied). Test using Supabase's local development environment.
*   **Custom Claims (JWT)**: Use Supabase Auth custom claims (set via Edge Functions or database triggers) to store user roles and organization membership for efficient RLS.

### ### 4.3 Supabase Edge Functions (Deno Runtime)

*   **Function Size & Focus**: Keep Edge Functions small, focused on specific tasks (Single Responsibility Principle). Decompose complex operations.
*   **Idempotency**: Design functions to be idempotent where possible, especially if they might be retried.
*   **Error Handling**: Implement comprehensive error handling. Return meaningful HTTP status codes and JSON error responses. Log errors effectively (Supabase logs or external service).
*   **Retry Logic**: For calls to external services (like MCPs or third-party APIs), implement retry logic with exponential backoff.
*   **Environment Variables**: Use Supabase project environment variables for all configurations (API keys, external service URLs, feature flags). Never hardcode sensitive information.
*   **Cold Start Optimization**: Minimize initialization code and dependencies. Be mindful of Deno's module loading.
*   **Authentication & Authorization**:
    *   Validate the JWT in all Edge Functions that require authentication.
    *   Extract user ID, role, and organization ID from the JWT.
    *   Perform explicit authorization checks before executing operations, even if RLS is also in place (defense in depth).
*   **Database Interaction**: Use the Supabase client library within Edge Functions to interact with the database, respecting RLS.
*   **Triggers**: Use PostgreSQL database triggers to invoke Edge Functions for reactive logic (e.g., on new user signup, on project status change).

### ### 4.4 Supabase Storage

*   **Folder Structure**: Organize files in a logical folder structure, often mirroring organization/project structure (e.g., `organizations/{org_id}/projects/{project_id}/floor_plans/{file_id_version}`).
*   **File Naming**: Use consistent file naming, including relevant identifiers (UUIDs are good for uniqueness) and preventing collisions.
*   **Metadata**: Store file metadata (content type, size, user-defined tags, version info) in associated Supabase Database tables, linked to the storage object path.
*   **Security Policies**: Implement granular security policies on buckets and objects, based on user roles, organization, and RLS principles.
*   **Content Disposition**: Set appropriate `Content-Disposition` headers for downloadable files.
*   **Cache Control**: Configure `Cache-Control` headers based on file mutability.
*   **CORS Configuration**: Configure CORS for buckets if direct browser access from different origins is needed.
*   **Cleanup**: Implement cleanup processes for temporary or orphaned files (e.g., a scheduled Supabase Edge Function that checks for unreferenced storage objects).

## ## 5. Frontend Development Rules

(Largely unchanged, acknowledging the pre-existing UI and its stack: React, TypeScript, shadcn.ui, Tailwind CSS, Zustand, React Query)

### ### 5.1 React Best Practices
(Unchanged)

### ### 5.2 Component Design
(Unchanged)

### ### 5.3 Form Handling
(Unchanged - React Hook Form with Zod recommended)

### ### 5.4 Styling Approach
(Unchanged - Tailwind CSS and shadcn.ui)

## ## 6. Testing Requirements

### ### 6.1 Unit Testing
*   **Test Coverage**: Maintain >= 80% code coverage.
*   **Component Testing**: React Testing Library for UI components.
*   **Hook Testing**: React Hooks Testing Library for custom hooks.
*   **Utility Testing**: Test utility functions.
*   **Store Testing**: Test Zustand (or existing global state) stores.
*   **Supabase Edge Function Testing**: Use Deno's built-in test runner. Mock Supabase client and external dependencies.
*   **MCP Tool Testing**: Unit test individual tools within MCP servers.
*   **Mock Services**: Mock Supabase client, MCP responses, and other external APIs.
*   **Jest/Vitest**: Use Jest or Vitest for unit tests.

### ### 6.2 Integration Testing
*   **Critical Paths**: Test critical user workflows end-to-end.
*   **API Integration**:
    *   Frontend to **Supabase** (PostgREST and Edge Functions).
    *   **Supabase Edge Functions** to **MCP Servers**.
    *   **MCP Servers** to **OpenRouter/AI Models**.
    *   Use MSW or similar for mocking API layers.
*   **RLS Policy Testing**: Test RLS policies by executing queries as different mock users/roles against a test Supabase instance.
*   **State Changes**: Test complex state changes.
*   **Error Scenarios**: Test error handling across layers.
*   **End-to-End**: Use Cypress for E2E tests of critical user journeys.
*   **Visual Testing**: Implement visual regression testing for key UI components.

### ### 6.3 Performance Testing
*   **Load Time**: Measure frontend page load times.
*   **API Latency**: Measure latency of Supabase queries, Edge Functions, and MCP calls (especially AI model responses).
*   **First Contentful Paint / Time to Interactive**: Frontend metrics.
*   **Bundle Size**: Monitor frontend bundle size.
*   **Memory Usage**: Frontend and backend (Edge Functions, MCPs if applicable).
*   **Lighthouse**: Run Lighthouse tests.
*   **Core Web Vitals**: Meet good scores.
*   **Database Performance**: Use `EXPLAIN ANALYZE` on Supabase queries under load.

## ## 7. MCP Reference Guidelines

### ### 7.1 Context7 MCP Integration
(Unchanged - Library Reference, Pattern Consistency, Function Naming, Parameter Structure, Response Format, Error Handling, Version Compatibility)

### ### 7.2 MCP Tool Implementation
(Pattern unchanged, but note `symbolDetectionService.detect` would be the core logic within the MCP tool, potentially calling OpenRouter)

```typescript
// Always follow this Context7 MCP tool implementation pattern
import { FastMCP } from 'fastmcp'; // Assuming fastmcp or similar library

// Initialize MCP server (e.g., SymbolDetectionMCP)
const mcp = new FastMCP("CoElec-SymbolDetectionMCP");

mcp.tool({
    name: "detectSymbolsInTile",
    description: "Detect electrical symbols from a floor plan image tile",
    parameters: {
        imageTileUrl: { type: "string", description: "URL or base64 data of the floor plan image tile" },
        promptVersion: { type: "string", description: "Version of the prompt to use" },
        modelPreference: { type: "string", required: false, description: "Preferred AI model" },
        // ... other options
    },
    handler: async ({ imageTileUrl, promptVersion, modelPreference, options = {} }) => {
        try {
            // Internal logic:
            // 1. Select appropriate AI model via OpenRouter (based on modelPreference or internal logic)
            // 2. Retrieve specific prompt based on promptVersion
            // 3. Call AI model with imageTile and prompt
            // 4. Parse and standardize results
            const result = await internalAISymbolService.detectSymbols(imageTileUrl, promptVersion, modelPreference, options);
            return { success: true, symbols: result.symbols, confidence: result.confidence_scores, model_used: result.model_used };
        } catch (error: any) { // Type error appropriately
            // Log error internally
            return { success: false, error: error.message, errorCode: error.code, errorDetail: error };
        }
    }
});
```

### ### 7.3 MCP Resource Implementation
(Pattern unchanged - useful for accessing shared data like symbol libraries if not directly in Supabase DB)

## ## 8. API Design Guidelines

### ### 8.1 RESTful Endpoints (Supabase PostgREST & Edge Functions)

*   **Supabase PostgREST**: Provides resource-based URLs, standard HTTP methods, and status codes automatically for database tables/views. Secure with RLS.
*   **Supabase Edge Functions**: For custom logic, use resource-based URLs (e.g., `/api/v1/projects/{id}/initiate-analysis`). Adhere to standard HTTP methods and status codes.
*   **Query Parameters**: Standardize for pagination, sorting, filtering (PostgREST has its own syntax, be consistent for Edge Functions).
*   **Versioning**: For Edge Function APIs, include version in URL path (e.g., `/api/v1/...`). PostgREST versioning is tied to Supabase releases.

### ### 8.2 Response Format (for Custom Edge Function APIs)
(Standard SuccessResponse/ErrorResponse format unchanged)

### ### 8.3 Error Handling (for Custom Edge Function APIs)
(Consistent Format, Error Codes, Helpful Messages, Validation Errors, Security Considerations, Logging, Client Handling - unchanged principles)

### ### 8.4 API Documentation
*   **Supabase PostgREST**: Supabase auto-generates OpenAPI specs.
*   **Supabase Edge Functions**: Manually create OpenAPI/Swagger specs or use tools to generate them.
*   **MCP Server APIs**: Document MCP tool/resource contracts clearly.
*   Include examples, authentication details, error responses, rate limits, deprecation policy, and changelog.

## ## 9. Collaboration and Review Process
(Unchanged - Git Workflow, Code Review, Documentation Updates)

## ## 10. Deployment Rules

### ### 10.1 Environment Configuration
*   **Environment Variables**: Use Supabase project environment variables for backend and Edge Functions. Use build-time environment variables for the frontend.
*   **Secret Management**: Utilize Supabase Vault (if available and suitable) or encrypted environment variables for sensitive keys (e.g., OpenRouter API key, MCP server keys). Never commit secrets.
*   **Environment Parity**: Strive for similarity between dev, staging, and production Supabase project configurations.
*   **Configuration Validation**: Validate critical env vars at Edge Function startup.
*   (Default Values, Documentation, Environment-Specific Behavior - unchanged)

### ### 10.2 Release Process
*   **Semantic Versioning**: For application releases and APIs.
*   **Supabase Migrations**: Manage database schema changes through Supabase migrations, applied sequentially.
*   **Edge Function Deployment**: Deploy updated Edge Functions via Supabase CLI or GitHub Actions.
*   (Release Notes, Release Approval, Rollback Plan, Feature Flags, Phased Rollout, Smoke Testing, Post-Deployment Verification - unchanged principles)

### ### 10.3 Monitoring and Alerts
*   Utilize **Supabase's built-in monitoring** for database performance, API usage, and Edge Function logs/metrics.
*   Implement custom alerting based on Supabase logs or by integrating with third-party monitoring services.
*   Monitor MCP server performance and costs.
*   (Error Tracking, Performance Monitoring, Usage Analytics, Alerting Thresholds, Alert Routing, False Positive Reduction, Dashboard, Incident Response - unchanged principles)

## ## 11. Security and Compliance Rules

### ### 11.1 Authentication & Authorization
*   **Authentication Method**: Use **Supabase Auth** (JWT-based).
*   **Password Requirements**: Enforce strong password policies via Supabase Auth settings.
*   **MFA Support**: Enable and encourage MFA via Supabase Auth.
*   **Session Management**: Utilize Supabase Auth's session handling (JWT refresh).
*   **Token Handling**: Securely handle JWTs on the client; validate them in Edge Functions and RLS policies.
*   **Permission Model**: Granular permissions implemented via **Supabase Row Level Security (RLS)** policies and user roles (stored in JWT custom claims).
*   **Authorization Checks**: Enforce authorization checks consistently in RLS, Edge Functions, and frontend logic.
*   **Login Throttling**: Provided by Supabase Auth.

### ### 11.2 Data Protection
*   **Sensitive Data**: Encrypt sensitive data at rest (Supabase handles this for DB/Storage) and in transit (HTTPS).
*   **Data Classification**: Classify data by sensitivity.
*   **PII Handling**: Handle PII according to regulations (GDPR, CCPA). Implement RLS to restrict PII access.
*   **Data Minimization**: Collect only necessary data.
*   **Data Retention**: Implement appropriate retention policies (may require custom scripts/Edge Functions for automated cleanup).
*   **Secure Deletion**: Utilize Supabase mechanisms for data deletion.
*   **Backup Encryption**: Supabase handles backup encryption.
*   **Data Access Logs**: Implement custom audit trails for sensitive data access if Supabase default logging is insufficient.

### ### 11.3 Secure Development
*   (Dependency Scanning, OWASP Top 10, Static Analysis, Secret Scanning, Security Headers, CSP, CORS, Input Validation - unchanged principles, apply to frontend, Edge Functions, and MCP server code)

## ## 12. Performance Rules

### ### 12.1 Frontend Performance
(Unchanged - Bundle Size, Tree Shaking, Image Optimization, Lazy Loading, Caching, Critical CSS, Font Loading, Script Loading)

### ### 12.2 Backend Performance (Supabase)
*   **Query Optimization**: Write efficient SQL queries. Use `EXPLAIN ANALYZE`. Optimize Supabase client calls from Edge Functions.
*   **Indexing**: Create appropriate PostgreSQL indexes in Supabase DB.
*   **Caching**: Implement server-side caching in Supabase Edge Functions or MCPs where beneficial (e.g., for frequently accessed, rarely changing data or expensive computations).
*   **Connection Pooling**: Handled by Supabase.
*   **Resource Limits**: Be mindful of Supabase plan limits and Edge Function execution limits.
*   **Batching**: Batch database operations from Edge Functions when possible.
*   **Asynchronous Processing**: Use asynchronous patterns for time-consuming tasks like AI analysis (e.g., trigger Edge Function, store task status in DB, client polls or uses Realtime for updates).
*   **Horizontal Scaling**: Supabase is designed for horizontal scaling. Design Edge Functions to be stateless.

### ### 12.3 Network Performance
(Unchanged - Compression, HTTP/2, Reduced Payloads, CDN Usage, API Optimization, Connection Reuse, Prefetching, Service Worker)

## ## 13. Extensibility Rules
(Unchanged - Plugin Architecture, Multi-Vertical Support, Integration Capabilities)

## ## 14. Maintenance Guidelines
(Unchanged - Technical Debt Management, Dependency Management, Documentation Maintenance)

---

This rewritten "RULES" document now aligns with the use of Supabase and Windsurf, integrates the UI as a starting point, and provides more specific, actionable guidance for the critical AI symbol recognition feature, emphasizing MCP servers and iterative "vibe coding" development.